<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.IotDeviceMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.IotDevicePojo">
        <include refid="selectIotDeviceVo"/>
        where Iot_Device.id = #{key}
    </select>
    <sql id="selectIotDeviceVo">
        select Iot_Device.id,
               Iot_Device.AdditionalInfo,
               Iot_Device.Customerid,
               Iot_Device.DeviceProfileid,
               Iot_Device.DeviceData,
               Iot_Device.DevType,
               Iot_Device.DevSN,
               Iot_Device.DevName,
               Iot_Device.<PERSON><PERSON><PERSON>l,
               Iot_Device.Token,
               Iot_Device.Firmwareid,
               Iot_Device.Softwareid,
               Iot_Device.Externalid,
               Iot_Device.Version,
               Iot_Device.Remark,
               Iot_Device.RowNum,
               Iot_Device.CreateBy,
               Iot_Device.CreateByid,
               Iot_Device.CreateDate,
               Iot_Device.Lister,
               Iot_Device.Listerid,
               Iot_Device.ModifyDate,
               Iot_Device.Custom1,
               Iot_Device.Custom2,
               Iot_Device.Custom3,
               Iot_Device.Custom4,
               Iot_Device.Custom5,
               Iot_Device.Custom6,
               Iot_Device.Custom7,
               Iot_Device.Custom8,
               Iot_Device.Custom9,
               Iot_Device.Custom10,
               Iot_Device.Tenantid,
               Iot_Device.TenantName,
               Iot_Device.Revision,
               Iot_DeviceProfile.ProfName
        from Iot_Device
        LEFT join Iot_DeviceProfile on Iot_Device.DeviceProfileid = Iot_DeviceProfile.Id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.IotDevicePojo">
        <include refid="selectIotDeviceVo"/>
         where 1 = 1 and Iot_Device.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Iot_Device.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.additionalinfo != null ">
   and Iot_Device.AdditionalInfo like concat('%', #{SearchPojo.additionalinfo}, '%')
</if>
<if test="SearchPojo.customerid != null ">
   and Iot_Device.Customerid like concat('%', #{SearchPojo.customerid}, '%')
</if>
<if test="SearchPojo.deviceprofileid != null ">
   and Iot_Device.DeviceProfileid like concat('%', #{SearchPojo.deviceprofileid}, '%')
</if>
<if test="SearchPojo.devicedata != null ">
   and Iot_Device.DeviceData like concat('%', #{SearchPojo.devicedata}, '%')
</if>
<if test="SearchPojo.devtype != null ">
   and Iot_Device.DevType like concat('%', #{SearchPojo.devtype}, '%')
</if>
<if test="SearchPojo.devsn != null ">
   and Iot_Device.DevSN like concat('%', #{SearchPojo.devsn}, '%')
</if>
<if test="SearchPojo.devname != null ">
   and Iot_Device.DevName like concat('%', #{SearchPojo.devname}, '%')
</if>
<if test="SearchPojo.devlabel != null ">
   and Iot_Device.DevLabel like concat('%', #{SearchPojo.devlabel}, '%')
</if>
<if test="SearchPojo.firmwareid != null ">
   and Iot_Device.Firmwareid like concat('%', #{SearchPojo.firmwareid}, '%')
</if>
<if test="SearchPojo.softwareid != null ">
   and Iot_Device.Softwareid like concat('%', #{SearchPojo.softwareid}, '%')
</if>
<if test="SearchPojo.externalid != null ">
   and Iot_Device.Externalid like concat('%', #{SearchPojo.externalid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Iot_Device.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Iot_Device.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Iot_Device.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Iot_Device.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Iot_Device.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Iot_Device.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Iot_Device.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Iot_Device.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Iot_Device.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Iot_Device.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Iot_Device.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Iot_Device.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Iot_Device.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Iot_Device.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Iot_Device.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Iot_Device.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.additionalinfo != null ">
   or Iot_Device.AdditionalInfo like concat('%', #{SearchPojo.additionalinfo}, '%')
</if>
<if test="SearchPojo.customerid != null ">
   or Iot_Device.Customerid like concat('%', #{SearchPojo.customerid}, '%')
</if>
<if test="SearchPojo.deviceprofileid != null ">
   or Iot_Device.DeviceProfileid like concat('%', #{SearchPojo.deviceprofileid}, '%')
</if>
<if test="SearchPojo.devicedata != null ">
   or Iot_Device.DeviceData like concat('%', #{SearchPojo.devicedata}, '%')
</if>
<if test="SearchPojo.devtype != null ">
   or Iot_Device.DevType like concat('%', #{SearchPojo.devtype}, '%')
</if>
<if test="SearchPojo.devsn != null ">
   or Iot_Device.DevSN like concat('%', #{SearchPojo.devsn}, '%')
</if>
<if test="SearchPojo.devname != null ">
   or Iot_Device.DevName like concat('%', #{SearchPojo.devname}, '%')
</if>
<if test="SearchPojo.devlabel != null ">
   or Iot_Device.DevLabel like concat('%', #{SearchPojo.devlabel}, '%')
</if>
<if test="SearchPojo.firmwareid != null ">
   or Iot_Device.Firmwareid like concat('%', #{SearchPojo.firmwareid}, '%')
</if>
<if test="SearchPojo.softwareid != null ">
   or Iot_Device.Softwareid like concat('%', #{SearchPojo.softwareid}, '%')
</if>
<if test="SearchPojo.externalid != null ">
   or Iot_Device.Externalid like concat('%', #{SearchPojo.externalid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Iot_Device.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Iot_Device.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Iot_Device.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Iot_Device.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Iot_Device.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Iot_Device.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Iot_Device.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Iot_Device.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Iot_Device.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Iot_Device.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Iot_Device.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Iot_Device.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Iot_Device.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Iot_Device.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Iot_Device.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Iot_Device.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Iot_Device(id, AdditionalInfo, Customerid, DeviceProfileid, DeviceData, DevType, DevSN, DevName, DevLabel, Token, Firmwareid, Softwareid, Externalid, Version, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{additionalinfo}, #{customerid}, #{deviceprofileid}, #{devicedata}, #{devtype}, #{devsn}, #{devname}, #{devlabel}, #{token}, #{firmwareid}, #{softwareid}, #{externalid}, #{version}, #{remark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Iot_Device
        <set>
            <if test="additionalinfo != null ">
                AdditionalInfo =#{additionalinfo},
            </if>
            <if test="customerid != null ">
                Customerid =#{customerid},
            </if>
            <if test="deviceprofileid != null ">
                DeviceProfileid =#{deviceprofileid},
            </if>
            <if test="devicedata != null ">
                DeviceData =#{devicedata},
            </if>
            <if test="devtype != null ">
                DevType =#{devtype},
            </if>
            <if test="devsn != null ">
                DevSN =#{devsn},
            </if>
            <if test="devname != null ">
                DevName =#{devname},
            </if>
            <if test="devlabel != null ">
                DevLabel =#{devlabel},
            </if>
            <if test="token != null ">
                Token =#{token},
            </if>
            <if test="firmwareid != null ">
                Firmwareid =#{firmwareid},
            </if>
            <if test="softwareid != null ">
                Softwareid =#{softwareid},
            </if>
            <if test="externalid != null ">
                Externalid =#{externalid},
            </if>
            <if test="version != null">
                Version =#{version},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Iot_Device where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getEntitybySn" resultType="inks.service.std.eam.domain.pojo.IotDevicePojo">
        <include refid="selectIotDeviceVo"/>
        where Iot_Device.DevSN = #{sn}
    </select>

    <select id="getEntityByToken" resultType="inks.service.std.eam.domain.pojo.IotDevicePojo">
        <include refid="selectIotDeviceVo"/>
        where Iot_Device.Token = #{token} and Iot_Device.Token is not null and Iot_Device.Token != ''
    </select>

    <select id="getAllToken" resultType="inks.service.std.eam.domain.vo.DeviceTokenVO">
        select id, Token, Tenantid from Iot_Device
    </select>

    <select id="getAllDevicesWithToken" resultType="inks.service.std.eam.domain.vo.DeviceTokenVO">
        select id, Token, Tenantid from Iot_Device
        where Token is not null and Token != ''
    </select>

</mapper>

