<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.IotAssetprofileMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.IotAssetprofilePojo">
        <include refid="selectIotAssetprofileVo"/>
        where Iot_AssetProfile.id = #{key} and Iot_AssetProfile.Tenantid=#{tid}
    </select>
    <sql id="selectIotAssetprofileVo">
        select Iot_AssetProfile.id,
               Iot_AssetProfile.ProfName,
               Iot_AssetProfile.Image,
               Iot_AssetProfile.Description,
               Iot_AssetProfile.IsDefault,
               Iot_AssetProfile.DefaultRuleChainid,
               Iot_AssetProfile.DefaultDashboardid,
               Iot_AssetProfile.DefaultQueueName,
               Iot_AssetProfile.DefaultEdgeRuleChainid,
               Iot_AssetProfile.Externalid,
               Iot_AssetProfile.Version,
               Iot_AssetProfile.Remark,
               Iot_AssetProfile.RowNum,
               Iot_AssetProfile.CreateBy,
               Iot_AssetProfile.CreateByid,
               Iot_AssetProfile.CreateDate,
               Iot_AssetProfile.Lister,
               Iot_AssetProfile.Listerid,
               Iot_AssetProfile.ModifyDate,
               Iot_AssetProfile.Custom1,
               Iot_AssetProfile.Custom2,
               Iot_AssetProfile.Custom3,
               Iot_AssetProfile.Custom4,
               Iot_AssetProfile.Custom5,
               Iot_AssetProfile.Custom6,
               Iot_AssetProfile.Custom7,
               Iot_AssetProfile.Custom8,
               Iot_AssetProfile.Custom9,
               Iot_AssetProfile.Custom10,
               Iot_AssetProfile.Tenantid,
               Iot_AssetProfile.TenantName,
               Iot_AssetProfile.Revision,
               Iot_RuleChain.RuleChainName,
               Iot_Dashboard.Title as DashboardTitle
        from Iot_AssetProfile
                 Left join Iot_RuleChain on Iot_AssetProfile.DefaultRuleChainid = Iot_RuleChain.id
                 Left join Iot_Dashboard on Iot_AssetProfile.DefaultDashboardid = Iot_Dashboard.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.IotAssetprofilePojo">
        <include refid="selectIotAssetprofileVo"/>
        where 1 = 1 and Iot_AssetProfile.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Iot_AssetProfile.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.profname != null ">
            and Iot_AssetProfile.ProfName like concat('%', #{SearchPojo.profname}, '%')
        </if>
        <if test="SearchPojo.image != null ">
            and Iot_AssetProfile.Image like concat('%', #{SearchPojo.image}, '%')
        </if>
        <if test="SearchPojo.description != null ">
            and Iot_AssetProfile.Description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.defaultrulechainid != null ">
            and Iot_AssetProfile.DefaultRuleChainid like concat('%', #{SearchPojo.defaultrulechainid}, '%')
        </if>
        <if test="SearchPojo.defaultdashboardid != null ">
            and Iot_AssetProfile.DefaultDashboardid like concat('%', #{SearchPojo.defaultdashboardid}, '%')
        </if>
        <if test="SearchPojo.defaultqueuename != null ">
            and Iot_AssetProfile.DefaultQueueName like concat('%', #{SearchPojo.defaultqueuename}, '%')
        </if>
        <if test="SearchPojo.defaultedgerulechainid != null ">
            and Iot_AssetProfile.DefaultEdgeRuleChainid like concat('%', #{SearchPojo.defaultedgerulechainid}, '%')
        </if>
        <if test="SearchPojo.externalid != null ">
            and Iot_AssetProfile.Externalid like concat('%', #{SearchPojo.externalid}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and Iot_AssetProfile.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Iot_AssetProfile.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Iot_AssetProfile.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Iot_AssetProfile.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Iot_AssetProfile.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Iot_AssetProfile.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Iot_AssetProfile.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Iot_AssetProfile.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Iot_AssetProfile.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Iot_AssetProfile.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Iot_AssetProfile.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Iot_AssetProfile.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Iot_AssetProfile.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Iot_AssetProfile.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Iot_AssetProfile.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Iot_AssetProfile.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.profname != null ">
                or Iot_AssetProfile.ProfName like concat('%', #{SearchPojo.profname}, '%')
            </if>
            <if test="SearchPojo.image != null ">
                or Iot_AssetProfile.Image like concat('%', #{SearchPojo.image}, '%')
            </if>
            <if test="SearchPojo.description != null ">
                or Iot_AssetProfile.Description like concat('%', #{SearchPojo.description}, '%')
            </if>
            <if test="SearchPojo.defaultrulechainid != null ">
                or Iot_AssetProfile.DefaultRuleChainid like concat('%', #{SearchPojo.defaultrulechainid}, '%')
            </if>
            <if test="SearchPojo.defaultdashboardid != null ">
                or Iot_AssetProfile.DefaultDashboardid like concat('%', #{SearchPojo.defaultdashboardid}, '%')
            </if>
            <if test="SearchPojo.defaultqueuename != null ">
                or Iot_AssetProfile.DefaultQueueName like concat('%', #{SearchPojo.defaultqueuename}, '%')
            </if>
            <if test="SearchPojo.defaultedgerulechainid != null ">
                or Iot_AssetProfile.DefaultEdgeRuleChainid like concat('%', #{SearchPojo.defaultedgerulechainid}, '%')
            </if>
            <if test="SearchPojo.externalid != null ">
                or Iot_AssetProfile.Externalid like concat('%', #{SearchPojo.externalid}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or Iot_AssetProfile.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Iot_AssetProfile.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Iot_AssetProfile.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Iot_AssetProfile.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Iot_AssetProfile.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Iot_AssetProfile.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Iot_AssetProfile.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Iot_AssetProfile.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Iot_AssetProfile.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Iot_AssetProfile.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Iot_AssetProfile.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Iot_AssetProfile.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Iot_AssetProfile.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Iot_AssetProfile.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Iot_AssetProfile.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Iot_AssetProfile.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Iot_AssetProfile(id, ProfName, Image, Description, IsDefault, DefaultRuleChainid, DefaultDashboardid, DefaultQueueName, DefaultEdgeRuleChainid, Externalid, Version, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{profname}, #{image}, #{description}, #{isdefault}, #{defaultrulechainid}, #{defaultdashboardid}, #{defaultqueuename}, #{defaultedgerulechainid}, #{externalid}, #{version}, #{remark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Iot_AssetProfile
        <set>
            <if test="profname != null ">
                ProfName =#{profname},
            </if>
            <if test="image != null ">
                Image =#{image},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="isdefault != null">
                IsDefault =#{isdefault},
            </if>
            <if test="defaultrulechainid != null ">
                DefaultRuleChainid =#{defaultrulechainid},
            </if>
            <if test="defaultdashboardid != null ">
                DefaultDashboardid =#{defaultdashboardid},
            </if>
            <if test="defaultqueuename != null ">
                DefaultQueueName =#{defaultqueuename},
            </if>
            <if test="defaultedgerulechainid != null ">
                DefaultEdgeRuleChainid =#{defaultedgerulechainid},
            </if>
            <if test="externalid != null ">
                Externalid =#{externalid},
            </if>
            <if test="version != null">
                Version =#{version},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Iot_AssetProfile where id = #{key} and Tenantid=#{tid}
    </delete>
</mapper>

