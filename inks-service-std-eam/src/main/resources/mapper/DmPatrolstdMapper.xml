<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmPatrolstdMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmPatrolstdPojo">
        select
          id, StdGroupid, StdCode, StdName, Operator, Summary, Listerid, Lister, CreateByid, CreateDate, CreateBy, ModifyDate, Assessor, Assessorid, AssessDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision
        from Dm_PatrolStd
        where Dm_PatrolStd.id = #{key} and Dm_PatrolStd.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
          id, StdGroupid, StdCode, StdName, Operator, Summary, Listerid, Lister, CreateByid, CreateDate, CreateBy, ModifyDate, Assessor, Assessorid, AssessDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolStd
    </sql>
    <sql id="selectdetailVo">
         select
          id, StdGroupid, StdCode, StdName, Operator, Summary, Listerid, Lister, CreateByid, CreateDate, CreateBy, ModifyDate, Assessor, Assessorid, AssessDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolStd
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmPatrolstditemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 and Dm_PatrolStd.Tenantid =#{tenantid}
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_PatrolStd.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.stdgroupid != null ">
   and Dm_PatrolStd.stdgroupid like concat('%', #{SearchPojo.stdgroupid}, '%')
</if>
<if test="SearchPojo.stdcode != null ">
   and Dm_PatrolStd.stdcode like concat('%', #{SearchPojo.stdcode}, '%')
</if>
<if test="SearchPojo.stdname != null ">
   and Dm_PatrolStd.stdname like concat('%', #{SearchPojo.stdname}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Dm_PatrolStd.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Dm_PatrolStd.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_PatrolStd.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_PatrolStd.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_PatrolStd.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_PatrolStd.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Dm_PatrolStd.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Dm_PatrolStd.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Dm_PatrolStd.deletelister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_PatrolStd.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_PatrolStd.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_PatrolStd.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_PatrolStd.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_PatrolStd.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_PatrolStd.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_PatrolStd.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_PatrolStd.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Dm_PatrolStd.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Dm_PatrolStd.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.stdgroupid != null ">
   or Dm_PatrolStd.StdGroupid like concat('%', #{SearchPojo.stdgroupid}, '%')
</if>
<if test="SearchPojo.stdcode != null ">
   or Dm_PatrolStd.StdCode like concat('%', #{SearchPojo.stdcode}, '%')
</if>
<if test="SearchPojo.stdname != null ">
   or Dm_PatrolStd.StdName like concat('%', #{SearchPojo.stdname}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Dm_PatrolStd.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Dm_PatrolStd.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_PatrolStd.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_PatrolStd.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_PatrolStd.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_PatrolStd.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Dm_PatrolStd.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Dm_PatrolStd.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Dm_PatrolStd.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_PatrolStd.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_PatrolStd.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_PatrolStd.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_PatrolStd.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_PatrolStd.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_PatrolStd.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_PatrolStd.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_PatrolStd.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Dm_PatrolStd.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Dm_PatrolStd.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmPatrolstdPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 and Dm_PatrolStd.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_PatrolStd.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.stdgroupid != null ">
   and Dm_PatrolStd.StdGroupid like concat('%', #{SearchPojo.stdgroupid}, '%')
</if>
<if test="SearchPojo.stdcode != null ">
   and Dm_PatrolStd.StdCode like concat('%', #{SearchPojo.stdcode}, '%')
</if>
<if test="SearchPojo.stdname != null ">
   and Dm_PatrolStd.StdName like concat('%', #{SearchPojo.stdname}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Dm_PatrolStd.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Dm_PatrolStd.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_PatrolStd.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_PatrolStd.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_PatrolStd.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_PatrolStd.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Dm_PatrolStd.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Dm_PatrolStd.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Dm_PatrolStd.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_PatrolStd.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_PatrolStd.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_PatrolStd.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_PatrolStd.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_PatrolStd.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_PatrolStd.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_PatrolStd.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_PatrolStd.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Dm_PatrolStd.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Dm_PatrolStd.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.stdgroupid != null ">
   or Dm_PatrolStd.StdGroupid like concat('%', #{SearchPojo.stdgroupid}, '%')
</if>
<if test="SearchPojo.stdcode != null ">
   or Dm_PatrolStd.StdCode like concat('%', #{SearchPojo.stdcode}, '%')
</if>
<if test="SearchPojo.stdname != null ">
   or Dm_PatrolStd.StdName like concat('%', #{SearchPojo.stdname}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Dm_PatrolStd.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Dm_PatrolStd.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_PatrolStd.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_PatrolStd.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_PatrolStd.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_PatrolStd.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Dm_PatrolStd.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Dm_PatrolStd.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Dm_PatrolStd.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_PatrolStd.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_PatrolStd.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_PatrolStd.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_PatrolStd.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_PatrolStd.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_PatrolStd.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_PatrolStd.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_PatrolStd.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Dm_PatrolStd.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Dm_PatrolStd.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_PatrolStd(id, StdGroupid, StdCode, StdName, Operator, Summary, Listerid, Lister, CreateByid, CreateDate, CreateBy, ModifyDate, Assessor, Assessorid, AssessDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{stdgroupid}, #{stdcode}, #{stdname}, #{operator}, #{summary}, #{listerid}, #{lister}, #{createbyid}, #{createdate}, #{createby}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{enabledmark}, #{deletemark}, #{deletelister}, #{deletedate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_PatrolStd
        <set>
            <if test="stdgroupid != null ">
                StdGroupid =#{stdgroupid},
            </if>
            <if test="stdcode != null ">
                StdCode =#{stdcode},
            </if>
            <if test="stdname != null ">
                StdName =#{stdname},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_PatrolStd where id = #{key} and Tenantid=#{tid}
    </delete>
                                                        <!--通过主键审核数据-->
    <update id="approval">
        update Dm_PatrolStd SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
                                                                                <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.std.eam.domain.pojo.DmPatrolstdPojo">
        select
          id
        from Dm_PatrolStdItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

</mapper>

