<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmToolborrowitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmToolborrowitemPojo">
        <include refid="selectDmToolborrowitemVo"/>
        where Dm_ToolBorrowItem.id = #{key} and Dm_ToolBorrowItem.Tenantid=#{tid}
    </select>
    <sql id="selectDmToolborrowitemVo">
        select Dm_ToolBorrowItem.id,
               Dm_ToolBorrowItem.Pid,
               Dm_ToolBorrowItem.Toolid,
               Dm_ToolBorrowItem.BorrowQty,
               Dm_ToolBorrowItem.ReturnedQty,
               Dm_ToolBorrowItem.Status,
               Dm_ToolBorrowItem.RowNum,
               Dm_ToolBorrowItem.Remark,
               Dm_ToolBorrowItem.Custom1,
               Dm_ToolBorrowItem.Custom2,
               Dm_ToolBorrowItem.Custom3,
               Dm_ToolBorrowItem.Custom4,
               Dm_ToolBorrowItem.Custom5,
               Dm_ToolBorrowItem.Custom6,
               Dm_ToolBorrowItem.Custom7,
               Dm_ToolBorrowItem.Custom8,
               Dm_ToolBorrowItem.Custom9,
               Dm_ToolBorrowItem.Custom10,
               Dm_ToolBorrowItem.Tenantid,
               Dm_ToolBorrowItem.Revision,
               Dm_ToolInfo.ToolName,
               Dm_ToolInfo.ToolCode
        from Dm_ToolBorrowItem
                 left join Dm_ToolInfo on Dm_ToolBorrowItem.Toolid = Dm_ToolInfo.id
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmToolborrowitemPojo">
        <include refid="selectDmToolborrowitemVo"/>
        where Dm_ToolBorrowItem.Pid = #{Pid} and Dm_ToolBorrowItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmToolborrowitemPojo">
        <include refid="selectDmToolborrowitemVo"/>
         where 1 = 1 and Dm_ToolBorrowItem.Tenantid =#{tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_ToolBorrowItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Dm_ToolBorrowItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.toolid != null and SearchPojo.toolid != ''">
   and Dm_ToolBorrowItem.toolid like concat('%', #{SearchPojo.toolid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Dm_ToolBorrowItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Dm_ToolBorrowItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Dm_ToolBorrowItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Dm_ToolBorrowItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Dm_ToolBorrowItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Dm_ToolBorrowItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Dm_ToolBorrowItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Dm_ToolBorrowItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Dm_ToolBorrowItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Dm_ToolBorrowItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Dm_ToolBorrowItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Dm_ToolBorrowItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.toolid != null and SearchPojo.toolid != ''">
   or Dm_ToolBorrowItem.Toolid like concat('%', #{SearchPojo.toolid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Dm_ToolBorrowItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Dm_ToolBorrowItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Dm_ToolBorrowItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Dm_ToolBorrowItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Dm_ToolBorrowItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Dm_ToolBorrowItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Dm_ToolBorrowItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Dm_ToolBorrowItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Dm_ToolBorrowItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Dm_ToolBorrowItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Dm_ToolBorrowItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_ToolBorrowItem(id, Pid, Toolid, BorrowQty, ReturnedQty, Status, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{toolid}, #{borrowqty}, #{returnedqty}, #{status}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_ToolBorrowItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="toolid != null ">
                Toolid = #{toolid},
            </if>
            <if test="borrowqty != null">
                BorrowQty = #{borrowqty},
            </if>
            <if test="returnedqty != null">
                ReturnedQty = #{returnedqty},
            </if>
            <if test="status != null">
                Status = #{status},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_ToolBorrowItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

