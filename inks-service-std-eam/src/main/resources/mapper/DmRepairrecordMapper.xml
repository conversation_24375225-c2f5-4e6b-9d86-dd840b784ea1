<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmRepairrecordMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmRepairrecordPojo">
        select Dm_RepairRecord.id,
               Dm_RepairRecord.RefNo,
               Dm_RepairRecord.BillDate,
               Dm_RepairRecord.BillType,
               Dm_RepairRecord.BillTitle,
               Dm_RepairRecord.Deviceid,
               Dm_RepairRecord.Repairid,
               Dm_RepairRecord.FailureCause,
               Dm_RepairRecord.Solution,
               Dm_RepairRecord.WorkTime,
               Dm_RepairRecord.SignImage,
               Dm_RepairRecord.Operator,
               Dm_RepairRecord.Summary,
               Dm_RepairRecord.Custom1,
               Dm_RepairRecord.Custom2,
               Dm_RepairRecord.Custom3,
               Dm_RepairRecord.Custom4,
               Dm_RepairRecord.Custom5,
               Dm_RepairRecord.Custom6,
               Dm_RepairRecord.Custom7,
               Dm_RepairRecord.Custom8,
               Dm_RepairRecord.Lister,
               Dm_RepairRecord.Listerid,
               Dm_RepairRecord.CreateBy,
               Dm_RepairRecord.CreateByid,
               Dm_RepairRecord.CreateDate,
               Dm_RepairRecord.ModifyDate,
               Dm_RepairRecord.Assessorid,
               Dm_RepairRecord.AssessDate,
               Dm_RepairRecord.Tenantid,
               Dm_RepairRecord.Revision,
               Dm_Device.DevName,
               Dm_Device.DevCode,
               Dm_Device.DevSpec,
               Dm_Device.DevUnit,
               Dm_RepairService.RefNo as RepairRefNo
        from Dm_RepairRecord
                 join Dm_Device on Dm_RepairRecord.Deviceid = Dm_Device.id
                 join Dm_RepairService on Dm_RepairRecord.Repairid = Dm_RepairService.id
        where Dm_RepairRecord.id = #{key}
          and Dm_RepairRecord.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Dm_RepairRecord.id,
               Dm_RepairRecord.RefNo,
               Dm_RepairRecord.BillDate,
               Dm_RepairRecord.BillType,
               Dm_RepairRecord.BillTitle,
               Dm_RepairRecord.Deviceid,
               Dm_RepairRecord.Repairid,
               Dm_RepairRecord.FailureCause,
               Dm_RepairRecord.Solution,
               Dm_RepairRecord.WorkTime,
               Dm_RepairRecord.SignImage,
               Dm_RepairRecord.Operator,
               Dm_RepairRecord.Summary,
               Dm_RepairRecord.Custom1,
               Dm_RepairRecord.Custom2,
               Dm_RepairRecord.Custom3,
               Dm_RepairRecord.Custom4,
               Dm_RepairRecord.Custom5,
               Dm_RepairRecord.Custom6,
               Dm_RepairRecord.Custom7,
               Dm_RepairRecord.Custom8,
               Dm_RepairRecord.Lister,
               Dm_RepairRecord.Listerid,
               Dm_RepairRecord.CreateBy,
               Dm_RepairRecord.CreateByid,
               Dm_RepairRecord.CreateDate,
               Dm_RepairRecord.ModifyDate,
               Dm_RepairRecord.Assessorid,
               Dm_RepairRecord.AssessDate,
               Dm_RepairRecord.Tenantid,
               Dm_RepairRecord.Revision,
               Dm_Device.DevName,
               Dm_Device.DevCode,
               Dm_Device.DevSpec,
               Dm_Device.DevUnit,
               Dm_RepairService.RefNo        as RepairRefNo,
               Dm_RepairService.BillTitle    as RepairTitle,
               Dm_RepairService.Operator     as RepairOperator,
               Dm_RepairService.RepairReason as RepairReason,
               Dm_RepairService.BillDate as RepairDate
        from Dm_RepairRecord
                 join Dm_Device on Dm_RepairRecord.Deviceid = Dm_Device.id
                 join Dm_RepairService on Dm_RepairRecord.Repairid = Dm_RepairService.id
    </sql>
    <sql id="selectdetailVo">
        select Dm_RepairRecordItem.id,
               Dm_RepairRecordItem.Pid,
               Dm_RepairRecordItem.Spareid,
               Dm_RepairRecordItem.PlanQty,
               Dm_RepairRecordItem.Quantity,
               Dm_RepairRecordItem.Price,
               Dm_RepairRecordItem.Amount,
               Dm_RepairRecordItem.Remark,
               Dm_RepairRecordItem.RowNum,
               Dm_RepairRecordItem.Storeid,
               Dm_RepairRecordItem.Location,
               Dm_RepairRecordItem.BatchNo,
               Dm_RepairRecordItem.ExpiryDate,
               Dm_RepairRecordItem.TaxPrice,
               Dm_RepairRecordItem.TaxAmount,
               Dm_RepairRecordItem.ItemTaxrate,
               Dm_RepairRecordItem.Custom1,
               Dm_RepairRecordItem.Custom2,
               Dm_RepairRecordItem.Custom3,
               Dm_RepairRecordItem.Custom4,
               Dm_RepairRecordItem.Custom5,
               Dm_RepairRecordItem.Custom6,
               Dm_RepairRecordItem.Custom7,
               Dm_RepairRecordItem.Custom8,
               Dm_RepairRecordItem.Tenantid,
               Dm_RepairRecordItem.Revision,
               Dm_Spare.SpareCode,
               Dm_Spare.SpareName,
               Dm_Spare.SpareUnit,
               Dm_Spare.SpareSpec,
               Dm_RepairRecord.RefNo,
               Dm_RepairRecord.BillDate,
               Dm_RepairRecord.BillType,
               Dm_RepairRecord.BillTitle,
               Dm_Device.DevName,
               Dm_Device.DevCode,
               Dm_Device.DevSpec,
               Dm_Device.DevUnit,
               Dm_RepairService.RefNo as RepairRefNo,
               Dm_RepairService.BillDate as RepairDate
        from Dm_RepairRecord
                 Right Join Dm_RepairRecordItem on Dm_RepairRecord.id = Dm_RepairRecordItem.Pid
                 left join Dm_Device on Dm_RepairRecord.Deviceid = Dm_Device.id
                 left join Dm_RepairService on Dm_RepairRecord.Repairid = Dm_RepairService.id
                 left join Dm_Spare on Dm_RepairRecordItem.Spareid = Dm_Spare.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmRepairrecorditemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Dm_RepairRecord.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Dm_RepairRecord.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Dm_RepairRecord.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Dm_RepairRecord.billtype like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Dm_RepairRecord.billtitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.deviceid != null">
            and Dm_RepairRecord.deviceid like concat('%',
                #{SearchPojo.deviceid}, '%')
        </if>
        <if test="SearchPojo.repairid != null">
            and Dm_RepairRecord.repairid like concat('%',
                #{SearchPojo.repairid}, '%')
        </if>
        <if test="SearchPojo.failurecause != null">
            and Dm_RepairRecord.failurecause like concat('%',
                #{SearchPojo.failurecause}, '%')
        </if>
        <if test="SearchPojo.solution != null">
            and Dm_RepairRecord.solution like concat('%',
                #{SearchPojo.solution}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Dm_RepairRecord.operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Dm_RepairRecord.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Dm_RepairRecord.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Dm_RepairRecord.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Dm_RepairRecord.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Dm_RepairRecord.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Dm_RepairRecord.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Dm_RepairRecord.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Dm_RepairRecord.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Dm_RepairRecord.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Dm_RepairRecord.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Dm_RepairRecord.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Dm_RepairRecord.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Dm_RepairRecord.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Dm_RepairRecord.assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Dm_RepairRecord.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Dm_RepairRecord.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Dm_RepairRecord.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.deviceid != null">
                or Dm_RepairRecord.Deviceid like concat('%', #{SearchPojo.deviceid}, '%')
            </if>
            <if test="SearchPojo.repairid != null">
                or Dm_RepairRecord.Repairid like concat('%', #{SearchPojo.repairid}, '%')
            </if>
            <if test="SearchPojo.failurecause != null">
                or Dm_RepairRecord.FailureCause like concat('%', #{SearchPojo.failurecause}, '%')
            </if>
            <if test="SearchPojo.solution != null">
                or Dm_RepairRecord.Solution like concat('%', #{SearchPojo.solution}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Dm_RepairRecord.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Dm_RepairRecord.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Dm_RepairRecord.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Dm_RepairRecord.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Dm_RepairRecord.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Dm_RepairRecord.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Dm_RepairRecord.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Dm_RepairRecord.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Dm_RepairRecord.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Dm_RepairRecord.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Dm_RepairRecord.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Dm_RepairRecord.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Dm_RepairRecord.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Dm_RepairRecord.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Dm_RepairRecord.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmRepairrecordPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Dm_RepairRecord.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Dm_RepairRecord.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Dm_RepairRecord.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Dm_RepairRecord.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Dm_RepairRecord.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.deviceid != null">
            and Dm_RepairRecord.Deviceid like concat('%',
                #{SearchPojo.deviceid}, '%')
        </if>
        <if test="SearchPojo.repairid != null">
            and Dm_RepairRecord.Repairid like concat('%',
                #{SearchPojo.repairid}, '%')
        </if>
        <if test="SearchPojo.failurecause != null">
            and Dm_RepairRecord.FailureCause like concat('%',
                #{SearchPojo.failurecause}, '%')
        </if>
        <if test="SearchPojo.solution != null">
            and Dm_RepairRecord.Solution like concat('%',
                #{SearchPojo.solution}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Dm_RepairRecord.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Dm_RepairRecord.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Dm_RepairRecord.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Dm_RepairRecord.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Dm_RepairRecord.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Dm_RepairRecord.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Dm_RepairRecord.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Dm_RepairRecord.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Dm_RepairRecord.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Dm_RepairRecord.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Dm_RepairRecord.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Dm_RepairRecord.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Dm_RepairRecord.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Dm_RepairRecord.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Dm_RepairRecord.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Dm_RepairRecord.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Dm_RepairRecord.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Dm_RepairRecord.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.deviceid != null">
                or Dm_RepairRecord.Deviceid like concat('%', #{SearchPojo.deviceid}, '%')
            </if>
            <if test="SearchPojo.repairid != null">
                or Dm_RepairRecord.Repairid like concat('%', #{SearchPojo.repairid}, '%')
            </if>
            <if test="SearchPojo.failurecause != null">
                or Dm_RepairRecord.FailureCause like concat('%', #{SearchPojo.failurecause}, '%')
            </if>
            <if test="SearchPojo.solution != null">
                or Dm_RepairRecord.Solution like concat('%', #{SearchPojo.solution}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Dm_RepairRecord.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Dm_RepairRecord.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Dm_RepairRecord.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Dm_RepairRecord.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Dm_RepairRecord.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Dm_RepairRecord.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Dm_RepairRecord.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Dm_RepairRecord.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Dm_RepairRecord.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Dm_RepairRecord.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Dm_RepairRecord.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Dm_RepairRecord.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Dm_RepairRecord.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Dm_RepairRecord.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Dm_RepairRecord.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Dm_RepairRecord(id, RefNo, BillDate, BillType, BillTitle, Deviceid, Repairid, FailureCause,
                                    Solution, WorkTime, SignImage, Operator, Summary, Custom1, Custom2, Custom3,
                                    Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateBy, CreateByid,
                                    CreateDate, ModifyDate, Assessorid, AssessDate, Tenantid, Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{deviceid}, #{repairid}, #{failurecause},
                #{solution}, #{worktime}, #{signimage}, #{operator}, #{summary}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{lister}, #{listerid}, #{createby},
                #{createbyid}, #{createdate}, #{modifydate}, #{assessorid}, #{assessdate}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_RepairRecord
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="deviceid != null">
                Deviceid =#{deviceid},
            </if>
            <if test="repairid != null">
                Repairid =#{repairid},
            </if>
            <if test="failurecause != null">
                FailureCause =#{failurecause},
            </if>
            <if test="solution != null">
                Solution =#{solution},
            </if>
            <if test="worktime != null">
                WorkTime =#{worktime},
            </if>
            <if test="signimage != null">
                SignImage =#{signimage},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Dm_RepairRecord
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.eam.domain.pojo.DmRepairrecordPojo">
        select id
        from Dm_RepairRecordItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <update id="updateRepairStatus">
        update Dm_RepairService
        set BillResult = #{billresult}
        where id = #{repairid}
          and Tenantid = #{tid}
    </update>

    <select id="checkRepair" resultType="int">
        select count(1)
        from Dm_RepairRecord
        where Repairid = #{repairid}
          and Tenantid = #{tid}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
</mapper>

