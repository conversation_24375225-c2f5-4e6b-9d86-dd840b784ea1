<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.IotDeviceprofileMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.IotDeviceprofilePojo">
        <include refid="selectIotDeviceprofileVo"/>
        where Iot_DeviceProfile.id = #{key} and Iot_DeviceProfile.Tenantid=#{tid}
    </select>
    <sql id="selectIotDeviceprofileVo">
        select Iot_DeviceProfile.id,
               Iot_DeviceProfile.ProfName,
               Iot_DeviceProfile.ProfType,
               Iot_DeviceProfile.Image,
               Iot_DeviceProfile.TransportType,
               Iot_DeviceProfile.ProvisionType,
               Iot_DeviceProfile.ProfileData,
               Iot_DeviceProfile.Description,
               Iot_DeviceProfile.IsDefault,
               Iot_DeviceProfile.Firmwareid,
               Iot_DeviceProfile.Softwareid,
               Iot_DeviceProfile.DefaultRuleChainid,
               Iot_DeviceProfile.DefaultDashboardid,
               Iot_DeviceProfile.DefaultQueueName,
               Iot_DeviceProfile.ProvisionDeviceKey,
               Iot_DeviceProfile.DefaultEdgeRuleChainid,
               Iot_DeviceProfile.Externalid,
               Iot_DeviceProfile.Version,
               Iot_DeviceProfile.Remark,
               Iot_DeviceProfile.RowNum,
               Iot_DeviceProfile.CreateBy,
               Iot_DeviceProfile.CreateByid,
               Iot_DeviceProfile.CreateDate,
               Iot_DeviceProfile.Lister,
               Iot_DeviceProfile.Listerid,
               Iot_DeviceProfile.ModifyDate,
               Iot_DeviceProfile.Custom1,
               Iot_DeviceProfile.Custom2,
               Iot_DeviceProfile.Custom3,
               Iot_DeviceProfile.Custom4,
               Iot_DeviceProfile.Custom5,
               Iot_DeviceProfile.Custom6,
               Iot_DeviceProfile.Custom7,
               Iot_DeviceProfile.Custom8,
               Iot_DeviceProfile.Custom9,
               Iot_DeviceProfile.Custom10,
               Iot_DeviceProfile.Tenantid,
               Iot_DeviceProfile.TenantName,
               Iot_DeviceProfile.Revision,
               Iot_RuleChain.RuleChainName,
               Iot_Dashboard.Title as DashboardTitle
        from Iot_DeviceProfile
        Left join Iot_RuleChain on Iot_DeviceProfile.DefaultRuleChainid = Iot_RuleChain.id
        Left join Iot_Dashboard on Iot_DeviceProfile.DefaultDashboardid = Iot_Dashboard.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.IotDeviceprofilePojo">
        <include refid="selectIotDeviceprofileVo"/>
         where 1 = 1 and Iot_DeviceProfile.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Iot_DeviceProfile.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.profname != null ">
   and Iot_DeviceProfile.ProfName like concat('%', #{SearchPojo.profname}, '%')
</if>
<if test="SearchPojo.proftype != null ">
   and Iot_DeviceProfile.ProfType like concat('%', #{SearchPojo.proftype}, '%')
</if>
<if test="SearchPojo.image != null ">
   and Iot_DeviceProfile.Image like concat('%', #{SearchPojo.image}, '%')
</if>
<if test="SearchPojo.transporttype != null ">
   and Iot_DeviceProfile.TransportType like concat('%', #{SearchPojo.transporttype}, '%')
</if>
<if test="SearchPojo.provisiontype != null ">
   and Iot_DeviceProfile.ProvisionType like concat('%', #{SearchPojo.provisiontype}, '%')
</if>
<if test="SearchPojo.profiledata != null ">
   and Iot_DeviceProfile.ProfileData like concat('%', #{SearchPojo.profiledata}, '%')
</if>
<if test="SearchPojo.description != null ">
   and Iot_DeviceProfile.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.firmwareid != null ">
   and Iot_DeviceProfile.Firmwareid like concat('%', #{SearchPojo.firmwareid}, '%')
</if>
<if test="SearchPojo.softwareid != null ">
   and Iot_DeviceProfile.Softwareid like concat('%', #{SearchPojo.softwareid}, '%')
</if>
<if test="SearchPojo.defaultrulechainid != null ">
   and Iot_DeviceProfile.DefaultRuleChainid like concat('%', #{SearchPojo.defaultrulechainid}, '%')
</if>
<if test="SearchPojo.defaultdashboardid != null ">
   and Iot_DeviceProfile.DefaultDashboardid like concat('%', #{SearchPojo.defaultdashboardid}, '%')
</if>
<if test="SearchPojo.defaultqueuename != null ">
   and Iot_DeviceProfile.DefaultQueueName like concat('%', #{SearchPojo.defaultqueuename}, '%')
</if>
<if test="SearchPojo.provisiondevicekey != null ">
   and Iot_DeviceProfile.ProvisionDeviceKey like concat('%', #{SearchPojo.provisiondevicekey}, '%')
</if>
<if test="SearchPojo.defaultedgerulechainid != null ">
   and Iot_DeviceProfile.DefaultEdgeRuleChainid like concat('%', #{SearchPojo.defaultedgerulechainid}, '%')
</if>
<if test="SearchPojo.externalid != null ">
   and Iot_DeviceProfile.Externalid like concat('%', #{SearchPojo.externalid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Iot_DeviceProfile.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Iot_DeviceProfile.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Iot_DeviceProfile.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Iot_DeviceProfile.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Iot_DeviceProfile.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Iot_DeviceProfile.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Iot_DeviceProfile.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Iot_DeviceProfile.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Iot_DeviceProfile.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Iot_DeviceProfile.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Iot_DeviceProfile.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Iot_DeviceProfile.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Iot_DeviceProfile.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Iot_DeviceProfile.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Iot_DeviceProfile.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Iot_DeviceProfile.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.profname != null ">
   or Iot_DeviceProfile.ProfName like concat('%', #{SearchPojo.profname}, '%')
</if>
<if test="SearchPojo.proftype != null ">
   or Iot_DeviceProfile.ProfType like concat('%', #{SearchPojo.proftype}, '%')
</if>
<if test="SearchPojo.image != null ">
   or Iot_DeviceProfile.Image like concat('%', #{SearchPojo.image}, '%')
</if>
<if test="SearchPojo.transporttype != null ">
   or Iot_DeviceProfile.TransportType like concat('%', #{SearchPojo.transporttype}, '%')
</if>
<if test="SearchPojo.provisiontype != null ">
   or Iot_DeviceProfile.ProvisionType like concat('%', #{SearchPojo.provisiontype}, '%')
</if>
<if test="SearchPojo.profiledata != null ">
   or Iot_DeviceProfile.ProfileData like concat('%', #{SearchPojo.profiledata}, '%')
</if>
<if test="SearchPojo.description != null ">
   or Iot_DeviceProfile.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.firmwareid != null ">
   or Iot_DeviceProfile.Firmwareid like concat('%', #{SearchPojo.firmwareid}, '%')
</if>
<if test="SearchPojo.softwareid != null ">
   or Iot_DeviceProfile.Softwareid like concat('%', #{SearchPojo.softwareid}, '%')
</if>
<if test="SearchPojo.defaultrulechainid != null ">
   or Iot_DeviceProfile.DefaultRuleChainid like concat('%', #{SearchPojo.defaultrulechainid}, '%')
</if>
<if test="SearchPojo.defaultdashboardid != null ">
   or Iot_DeviceProfile.DefaultDashboardid like concat('%', #{SearchPojo.defaultdashboardid}, '%')
</if>
<if test="SearchPojo.defaultqueuename != null ">
   or Iot_DeviceProfile.DefaultQueueName like concat('%', #{SearchPojo.defaultqueuename}, '%')
</if>
<if test="SearchPojo.provisiondevicekey != null ">
   or Iot_DeviceProfile.ProvisionDeviceKey like concat('%', #{SearchPojo.provisiondevicekey}, '%')
</if>
<if test="SearchPojo.defaultedgerulechainid != null ">
   or Iot_DeviceProfile.DefaultEdgeRuleChainid like concat('%', #{SearchPojo.defaultedgerulechainid}, '%')
</if>
<if test="SearchPojo.externalid != null ">
   or Iot_DeviceProfile.Externalid like concat('%', #{SearchPojo.externalid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Iot_DeviceProfile.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Iot_DeviceProfile.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Iot_DeviceProfile.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Iot_DeviceProfile.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Iot_DeviceProfile.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Iot_DeviceProfile.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Iot_DeviceProfile.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Iot_DeviceProfile.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Iot_DeviceProfile.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Iot_DeviceProfile.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Iot_DeviceProfile.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Iot_DeviceProfile.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Iot_DeviceProfile.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Iot_DeviceProfile.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Iot_DeviceProfile.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Iot_DeviceProfile.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Iot_DeviceProfile(id, ProfName, ProfType, Image, TransportType, ProvisionType, ProfileData, Description, IsDefault, Firmwareid, Softwareid, DefaultRuleChainid, DefaultDashboardid, DefaultQueueName, ProvisionDeviceKey, DefaultEdgeRuleChainid, Externalid, Version, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{profname}, #{proftype}, #{image}, #{transporttype}, #{provisiontype}, #{profiledata}, #{description}, #{isdefault}, #{firmwareid}, #{softwareid}, #{defaultrulechainid}, #{defaultdashboardid}, #{defaultqueuename}, #{provisiondevicekey}, #{defaultedgerulechainid}, #{externalid}, #{version}, #{remark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Iot_DeviceProfile
        <set>
            <if test="profname != null ">
                ProfName =#{profname},
            </if>
            <if test="proftype != null ">
                ProfType =#{proftype},
            </if>
            <if test="image != null ">
                Image =#{image},
            </if>
            <if test="transporttype != null ">
                TransportType =#{transporttype},
            </if>
            <if test="provisiontype != null ">
                ProvisionType =#{provisiontype},
            </if>
            <if test="profiledata != null ">
                ProfileData =#{profiledata},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="isdefault != null">
                IsDefault =#{isdefault},
            </if>
            <if test="firmwareid != null ">
                Firmwareid =#{firmwareid},
            </if>
            <if test="softwareid != null ">
                Softwareid =#{softwareid},
            </if>
            <if test="defaultrulechainid != null ">
                DefaultRuleChainid =#{defaultrulechainid},
            </if>
            <if test="defaultdashboardid != null ">
                DefaultDashboardid =#{defaultdashboardid},
            </if>
            <if test="defaultqueuename != null ">
                DefaultQueueName =#{defaultqueuename},
            </if>
            <if test="provisiondevicekey != null ">
                ProvisionDeviceKey =#{provisiondevicekey},
            </if>
            <if test="defaultedgerulechainid != null ">
                DefaultEdgeRuleChainid =#{defaultedgerulechainid},
            </if>
            <if test="externalid != null ">
                Externalid =#{externalid},
            </if>
            <if test="version != null">
                Version =#{version},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Iot_DeviceProfile where id = #{key} and Tenantid=#{tid}
    </delete>
</mapper>

