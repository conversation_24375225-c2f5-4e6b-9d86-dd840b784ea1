<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmRepairrecorditemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmRepairrecorditemPojo">
        select id,
               Pid,
               Spareid,
               PlanQty,
               Quantity,
               Price,
               Amount,
               Remark,
               RowNum,
               Storeid,
               Location,
               BatchNo,
               ExpiryDate,
               TaxPrice,
               TaxAmount,
               ItemTaxrate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Tenantid,
               Revision
        from Dm_RepairRecordItem
        where Dm_RepairRecordItem.id = #{key}
          and Dm_RepairRecordItem.Tenantid = #{tid}
    </select>
    <sql id="selectDmRepairrecorditemVo">
        select id,
               Pid,
               Spareid,
               PlanQty,
               Quantity,
               Price,
               Amount,
               Remark,
               RowNum,
               Storeid,
               Location,
               BatchNo,
               ExpiryDate,
               TaxPrice,
               TaxAmount,
               ItemTaxrate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Tenantid,
               Revision
        from Dm_RepairRecordItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmRepairrecorditemPojo">
        <include refid="selectDmRepairrecorditemVo"/>
        where 1 = 1 and Dm_RepairRecordItem.Tenantid =#{Tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_RepairRecordItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Dm_RepairRecordItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.spareid != null and SearchPojo.spareid != ''">
            and Dm_RepairRecordItem.spareid like concat('%', #{SearchPojo.spareid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Dm_RepairRecordItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
            and Dm_RepairRecordItem.storeid like concat('%', #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Dm_RepairRecordItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Dm_RepairRecordItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Dm_RepairRecordItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Dm_RepairRecordItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Dm_RepairRecordItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Dm_RepairRecordItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Dm_RepairRecordItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Dm_RepairRecordItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Dm_RepairRecordItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Dm_RepairRecordItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Dm_RepairRecordItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.spareid != null and SearchPojo.spareid != ''">
                or Dm_RepairRecordItem.Spareid like concat('%', #{SearchPojo.spareid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Dm_RepairRecordItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
                or Dm_RepairRecordItem.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Dm_RepairRecordItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Dm_RepairRecordItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Dm_RepairRecordItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Dm_RepairRecordItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Dm_RepairRecordItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Dm_RepairRecordItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Dm_RepairRecordItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Dm_RepairRecordItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Dm_RepairRecordItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Dm_RepairRecordItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmRepairrecorditemPojo">
        select Dm_RepairRecordItem.id,
               Dm_RepairRecordItem.Pid,
               Dm_RepairRecordItem.Spareid,
               Dm_RepairRecordItem.PlanQty,
               Dm_RepairRecordItem.Quantity,
               Dm_RepairRecordItem.Price,
               Dm_RepairRecordItem.Amount,
               Dm_RepairRecordItem.Remark,
               Dm_RepairRecordItem.RowNum,
               Dm_RepairRecordItem.Storeid,
               Dm_RepairRecordItem.Location,
               Dm_RepairRecordItem.BatchNo,
               Dm_RepairRecordItem.ExpiryDate,
               Dm_RepairRecordItem.TaxPrice,
               Dm_RepairRecordItem.TaxAmount,
               Dm_RepairRecordItem.ItemTaxrate,
               Dm_RepairRecordItem.Custom1,
               Dm_RepairRecordItem.Custom2,
               Dm_RepairRecordItem.Custom3,
               Dm_RepairRecordItem.Custom4,
               Dm_RepairRecordItem.Custom5,
               Dm_RepairRecordItem.Custom6,
               Dm_RepairRecordItem.Custom7,
               Dm_RepairRecordItem.Custom8,
               Dm_RepairRecordItem.Tenantid,
               Dm_RepairRecordItem.Revision,
               Dm_Spare.SpareCode,
               Dm_Spare.SpareName,
               Dm_Spare.SpareUnit,
               Dm_Spare.SpareSpec
        from Dm_RepairRecordItem
        join Dm_Spare on Dm_RepairRecordItem.Spareid = Dm_Spare.id
        where Dm_RepairRecordItem.Pid = #{Pid}
          and Dm_RepairRecordItem.Tenantid = #{tid}
        order by Dm_RepairRecordItem.RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Dm_RepairRecordItem(id, Pid, Spareid, PlanQty, Quantity, Price, Amount, Remark, RowNum, Storeid,
                                        Location, BatchNo, ExpiryDate, TaxPrice, TaxAmount, ItemTaxrate, Custom1,
                                        Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Tenantid,
                                        Revision)
        values (#{id}, #{pid}, #{spareid}, #{planqty}, #{quantity}, #{price}, #{amount}, #{remark}, #{rownum},
                #{storeid}, #{location}, #{batchno}, #{expirydate}, #{taxprice}, #{taxamount}, #{itemtaxrate},
                #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
                #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_RepairRecordItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="spareid != null ">
                Spareid = #{spareid},
            </if>
            <if test="planqty != null">
                PlanQty = #{planqty},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="storeid != null ">
                Storeid = #{storeid},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="expirydate != null">
                ExpiryDate = #{expirydate},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Dm_RepairRecordItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

