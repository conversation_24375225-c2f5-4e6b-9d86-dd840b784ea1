<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmDeviceMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmDevicePojo">
        select
          id, DevGroupid, DevCode, DevName, DevSpec, DevUnit, DevPinyin, GroupCode, GroupName, GroupNo, DevPhoto1, DevPhoto2, MakeDate, StartDate, InPrice, NowPrice, Depreciation, UseState, UseLocation, UseBranch, UseStaff, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, <PERSON>er, <PERSON><PERSON>d, <PERSON>reate<PERSON>ate, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>yid, <PERSON>difyDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, <PERSON>sannulMark, DisannulDate, Tenantid, Revision
        from Dm_Device
        where Dm_Device.id = #{key} and Dm_Device.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
          id, DevGroupid, DevCode, DevName, DevSpec, DevUnit, DevPinyin, GroupCode, GroupName, GroupNo, DevPhoto1, DevPhoto2, MakeDate, StartDate, InPrice, NowPrice, Depreciation, UseState, UseLocation, UseBranch, UseStaff, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, DisannulMark, DisannulDate, Tenantid, Revision        from Dm_Device
    </sql>
    <sql id="selectdetailVo">
         select
          id, DevGroupid, DevCode, DevName, DevSpec, DevUnit, DevPinyin, GroupCode, GroupName, GroupNo, DevPhoto1, DevPhoto2, MakeDate, StartDate, InPrice, NowPrice, Depreciation, UseState, UseLocation, UseBranch, UseStaff, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, DisannulMark, DisannulDate, Tenantid, Revision        from Dm_Device
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmDeviceitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 and Dm_Device.Tenantid =#{tenantid}
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_Device.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.devgroupid != null ">
   and Dm_Device.devgroupid like concat('%', #{SearchPojo.devgroupid}, '%')
</if>
<if test="SearchPojo.devcode != null ">
   and Dm_Device.devcode like concat('%', #{SearchPojo.devcode}, '%')
</if>
<if test="SearchPojo.devname != null ">
   and Dm_Device.devname like concat('%', #{SearchPojo.devname}, '%')
</if>
<if test="SearchPojo.devspec != null ">
   and Dm_Device.devspec like concat('%', #{SearchPojo.devspec}, '%')
</if>
<if test="SearchPojo.devunit != null ">
   and Dm_Device.devunit like concat('%', #{SearchPojo.devunit}, '%')
</if>
<if test="SearchPojo.devpinyin != null ">
   and Dm_Device.devpinyin like concat('%', #{SearchPojo.devpinyin}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   and Dm_Device.groupcode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Dm_Device.groupname like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.devphoto1 != null ">
   and Dm_Device.devphoto1 like concat('%', #{SearchPojo.devphoto1}, '%')
</if>
<if test="SearchPojo.devphoto2 != null ">
   and Dm_Device.devphoto2 like concat('%', #{SearchPojo.devphoto2}, '%')
</if>
<if test="SearchPojo.usestate != null ">
   and Dm_Device.usestate like concat('%', #{SearchPojo.usestate}, '%')
</if>
<if test="SearchPojo.uselocation != null ">
   and Dm_Device.uselocation like concat('%', #{SearchPojo.uselocation}, '%')
</if>
<if test="SearchPojo.usebranch != null ">
   and Dm_Device.usebranch like concat('%', #{SearchPojo.usebranch}, '%')
</if>
<if test="SearchPojo.usestaff != null ">
   and Dm_Device.usestaff like concat('%', #{SearchPojo.usestaff}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Dm_Device.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_Device.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_Device.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_Device.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_Device.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_Device.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_Device.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_Device.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_Device.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_Device.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_Device.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_Device.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_Device.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Dm_Device.deletelister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.devgroupid != null ">
   or Dm_Device.DevGroupid like concat('%', #{SearchPojo.devgroupid}, '%')
</if>
<if test="SearchPojo.devcode != null ">
   or Dm_Device.DevCode like concat('%', #{SearchPojo.devcode}, '%')
</if>
<if test="SearchPojo.devname != null ">
   or Dm_Device.DevName like concat('%', #{SearchPojo.devname}, '%')
</if>
<if test="SearchPojo.devspec != null ">
   or Dm_Device.DevSpec like concat('%', #{SearchPojo.devspec}, '%')
</if>
<if test="SearchPojo.devunit != null ">
   or Dm_Device.DevUnit like concat('%', #{SearchPojo.devunit}, '%')
</if>
<if test="SearchPojo.devpinyin != null ">
   or Dm_Device.DevPinyin like concat('%', #{SearchPojo.devpinyin}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   or Dm_Device.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Dm_Device.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.devphoto1 != null ">
   or Dm_Device.DevPhoto1 like concat('%', #{SearchPojo.devphoto1}, '%')
</if>
<if test="SearchPojo.devphoto2 != null ">
   or Dm_Device.DevPhoto2 like concat('%', #{SearchPojo.devphoto2}, '%')
</if>
<if test="SearchPojo.usestate != null ">
   or Dm_Device.UseState like concat('%', #{SearchPojo.usestate}, '%')
</if>
<if test="SearchPojo.uselocation != null ">
   or Dm_Device.UseLocation like concat('%', #{SearchPojo.uselocation}, '%')
</if>
<if test="SearchPojo.usebranch != null ">
   or Dm_Device.UseBranch like concat('%', #{SearchPojo.usebranch}, '%')
</if>
<if test="SearchPojo.usestaff != null ">
   or Dm_Device.UseStaff like concat('%', #{SearchPojo.usestaff}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Dm_Device.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_Device.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_Device.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_Device.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_Device.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_Device.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_Device.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_Device.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_Device.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_Device.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_Device.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_Device.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_Device.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Dm_Device.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmDevicePojo">
        <include refid="selectbillVo"/>
         where 1 = 1 and Dm_Device.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_Device.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.devgroupid != null ">
   and Dm_Device.DevGroupid like concat('%', #{SearchPojo.devgroupid}, '%')
</if>
<if test="SearchPojo.devcode != null ">
   and Dm_Device.DevCode like concat('%', #{SearchPojo.devcode}, '%')
</if>
<if test="SearchPojo.devname != null ">
   and Dm_Device.DevName like concat('%', #{SearchPojo.devname}, '%')
</if>
<if test="SearchPojo.devspec != null ">
   and Dm_Device.DevSpec like concat('%', #{SearchPojo.devspec}, '%')
</if>
<if test="SearchPojo.devunit != null ">
   and Dm_Device.DevUnit like concat('%', #{SearchPojo.devunit}, '%')
</if>
<if test="SearchPojo.devpinyin != null ">
   and Dm_Device.DevPinyin like concat('%', #{SearchPojo.devpinyin}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   and Dm_Device.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Dm_Device.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.devphoto1 != null ">
   and Dm_Device.DevPhoto1 like concat('%', #{SearchPojo.devphoto1}, '%')
</if>
<if test="SearchPojo.devphoto2 != null ">
   and Dm_Device.DevPhoto2 like concat('%', #{SearchPojo.devphoto2}, '%')
</if>
<if test="SearchPojo.usestate != null ">
   and Dm_Device.UseState like concat('%', #{SearchPojo.usestate}, '%')
</if>
<if test="SearchPojo.uselocation != null ">
   and Dm_Device.UseLocation like concat('%', #{SearchPojo.uselocation}, '%')
</if>
<if test="SearchPojo.usebranch != null ">
   and Dm_Device.UseBranch like concat('%', #{SearchPojo.usebranch}, '%')
</if>
<if test="SearchPojo.usestaff != null ">
   and Dm_Device.UseStaff like concat('%', #{SearchPojo.usestaff}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Dm_Device.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_Device.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_Device.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_Device.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_Device.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_Device.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_Device.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_Device.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_Device.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_Device.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_Device.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_Device.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_Device.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Dm_Device.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.devgroupid != null ">
   or Dm_Device.DevGroupid like concat('%', #{SearchPojo.devgroupid}, '%')
</if>
<if test="SearchPojo.devcode != null ">
   or Dm_Device.DevCode like concat('%', #{SearchPojo.devcode}, '%')
</if>
<if test="SearchPojo.devname != null ">
   or Dm_Device.DevName like concat('%', #{SearchPojo.devname}, '%')
</if>
<if test="SearchPojo.devspec != null ">
   or Dm_Device.DevSpec like concat('%', #{SearchPojo.devspec}, '%')
</if>
<if test="SearchPojo.devunit != null ">
   or Dm_Device.DevUnit like concat('%', #{SearchPojo.devunit}, '%')
</if>
<if test="SearchPojo.devpinyin != null ">
   or Dm_Device.DevPinyin like concat('%', #{SearchPojo.devpinyin}, '%')
</if>
<if test="SearchPojo.groupcode != null ">
   or Dm_Device.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Dm_Device.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.devphoto1 != null ">
   or Dm_Device.DevPhoto1 like concat('%', #{SearchPojo.devphoto1}, '%')
</if>
<if test="SearchPojo.devphoto2 != null ">
   or Dm_Device.DevPhoto2 like concat('%', #{SearchPojo.devphoto2}, '%')
</if>
<if test="SearchPojo.usestate != null ">
   or Dm_Device.UseState like concat('%', #{SearchPojo.usestate}, '%')
</if>
<if test="SearchPojo.uselocation != null ">
   or Dm_Device.UseLocation like concat('%', #{SearchPojo.uselocation}, '%')
</if>
<if test="SearchPojo.usebranch != null ">
   or Dm_Device.UseBranch like concat('%', #{SearchPojo.usebranch}, '%')
</if>
<if test="SearchPojo.usestaff != null ">
   or Dm_Device.UseStaff like concat('%', #{SearchPojo.usestaff}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Dm_Device.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_Device.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_Device.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_Device.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_Device.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_Device.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_Device.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_Device.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_Device.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_Device.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_Device.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_Device.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_Device.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Dm_Device.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_Device(id, DevGroupid, DevCode, DevName, DevSpec, DevUnit, DevPinyin, GroupCode, GroupName, GroupNo, DevPhoto1, DevPhoto2, MakeDate, StartDate, InPrice, NowPrice, Depreciation, UseState, UseLocation, UseBranch, UseStaff, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, DisannulMark, DisannulDate, Tenantid, Revision)
        values (#{id}, #{devgroupid}, #{devcode}, #{devname}, #{devspec}, #{devunit}, #{devpinyin}, #{groupcode}, #{groupname}, #{groupno}, #{devphoto1}, #{devphoto2}, #{makedate}, #{startdate}, #{inprice}, #{nowprice}, #{depreciation}, #{usestate}, #{uselocation}, #{usebranch}, #{usestaff}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{lister}, #{listerid}, #{createdate}, #{createby}, #{createbyid}, #{modifydate}, #{enabledmark}, #{deletemark}, #{deletelister}, #{deletedate}, #{disannulmark}, #{disannuldate}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_Device
        <set>
            <if test="devgroupid != null ">
                DevGroupid =#{devgroupid},
            </if>
            <if test="devcode != null ">
                DevCode =#{devcode},
            </if>
            <if test="devname != null ">
                DevName =#{devname},
            </if>
            <if test="devspec != null ">
                DevSpec =#{devspec},
            </if>
            <if test="devunit != null ">
                DevUnit =#{devunit},
            </if>
            <if test="devpinyin != null ">
                DevPinyin =#{devpinyin},
            </if>
            <if test="groupcode != null ">
                GroupCode =#{groupcode},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="groupno != null">
                GroupNo =#{groupno},
            </if>
            <if test="devphoto1 != null ">
                DevPhoto1 =#{devphoto1},
            </if>
            <if test="devphoto2 != null ">
                DevPhoto2 =#{devphoto2},
            </if>
            <if test="makedate != null">
                MakeDate =#{makedate},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="inprice != null">
                InPrice =#{inprice},
            </if>
            <if test="nowprice != null">
                NowPrice =#{nowprice},
            </if>
            <if test="depreciation != null">
                Depreciation =#{depreciation},
            </if>
            <if test="usestate != null ">
                UseState =#{usestate},
            </if>
            <if test="uselocation != null ">
                UseLocation =#{uselocation},
            </if>
            <if test="usebranch != null ">
                UseBranch =#{usebranch},
            </if>
            <if test="usestaff != null ">
                UseStaff =#{usestaff},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="disannulmark != null">
                DisannulMark =#{disannulmark},
            </if>
            <if test="disannuldate != null">
                DisannulDate =#{disannuldate},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_Device where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                                                                                                                    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.std.eam.domain.pojo.DmDevicePojo">
        select
          id
        from Dm_DeviceItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>
                                                                <!--    返回的是DevicePojo！！！！-->

    <select id="getDeviceByDevids" resultType="inks.service.std.eam.domain.extend.DevicePojo">
        select id as devid,
               DevName,
               DevCode,
               DevSpec,
               DevUnit
        from Dm_Device
        where Tenantid = #{tid}
        <if test="devIds != null and devIds.size() > 0">
            and id
            <foreach collection="devIds" open="in (" close=")" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <update id="updateUseState">
        update Dm_Device
        <set>
            UseState =#{usestate}
        </set>
        where id
        <foreach collection="unFinishDevIdsTodaySet" open="in (" close=")" separator="," item="id">
            #{id}
        </foreach>
        and Tenantid =#{tid}
    </update>

    <select id="getDevNamesByDevids" resultType="java.lang.String">
        select DevName
        from Dm_Device
        where Tenantid = #{tid}
        <if test="unFinishDevsInPlan != null and unFinishDevsInPlan.size() > 0">
            and id
            <foreach collection="unFinishDevsInPlan" open="in (" close=")" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>


    <select id="checkDevcode" resultType="int">
        select count(1)
        from Dm_Device
        where DevCode = #{devcode} and Tenantid = #{tenantid}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <select id="getMaxDevCode" resultType="java.lang.String">
        select max(DevCode) as devcode
        from Dm_Device
        where Tenantid = #{tenantid}
    </select>
</mapper>

