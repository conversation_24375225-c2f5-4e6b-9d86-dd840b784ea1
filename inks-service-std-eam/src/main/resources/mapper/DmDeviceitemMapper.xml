<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmDeviceitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmDeviceitemPojo">
        select
          id, Pid, ItemCode, ItemName, ItemSpec, InspectMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Tenantid, Revision        from Dm_DeviceItem
        where Dm_DeviceItem.id = #{key} and Dm_DeviceItem.Tenantid=#{tid}
    </select>
    <sql id="selectDmDeviceitemVo">
         select
          id, Pid, ItemCode, ItemName, ItemS<PERSON>, InspectMark, RowN<PERSON>, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Tenantid, Revision        from Dm_DeviceItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmDeviceitemPojo">
        <include refid="selectDmDeviceitemVo"/>
         where 1 = 1 and Dm_DeviceItem.Tenantid =#{Tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_DeviceItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Dm_DeviceItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   and Dm_DeviceItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   and Dm_DeviceItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   and Dm_DeviceItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Dm_DeviceItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Dm_DeviceItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Dm_DeviceItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Dm_DeviceItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Dm_DeviceItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Dm_DeviceItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Dm_DeviceItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Dm_DeviceItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Dm_DeviceItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Dm_DeviceItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   or Dm_DeviceItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   or Dm_DeviceItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   or Dm_DeviceItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Dm_DeviceItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Dm_DeviceItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Dm_DeviceItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Dm_DeviceItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Dm_DeviceItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Dm_DeviceItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Dm_DeviceItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Dm_DeviceItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Dm_DeviceItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmDeviceitemPojo">
        select
          id, Pid, ItemCode, ItemName, ItemSpec, InspectMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Tenantid, Revision        from Dm_DeviceItem
        where Dm_DeviceItem.Pid = #{Pid} and Dm_DeviceItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_DeviceItem(id, Pid, ItemCode, ItemName, ItemSpec, InspectMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Tenantid, Revision)
        values (#{id}, #{pid}, #{itemcode}, #{itemname}, #{itemspec}, #{inspectmark}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_DeviceItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="inspectmark != null">
                InspectMark = #{inspectmark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_DeviceItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

