<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmKnowledgeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmKnowledgePojo">
        select
          id, RefNo, Deviceid, DeviceName, BillType, BillTitle, Operator, BillDate, Reason, Details, Remark, VideoUrl, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateBy, CreateByid, CreateDate, ModifyDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Tenantid, Revision        from Dm_Knowledge
        where Dm_Knowledge. = #{key} and Dm_Knowledge.Tenantid=#{tid}
    </select>
    <sql id="selectDmKnowledgeVo">
         select
          id, RefNo, Deviceid, DeviceName, BillType, BillTitle, Operator, BillDate, Reason, Details, Remark, VideoUrl, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateBy, CreateByid, CreateDate, ModifyDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Tenantid, Revision        from Dm_Knowledge
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmKnowledgePojo">
        <include refid="selectDmKnowledgeVo"/>
         where 1 = 1 and Dm_Knowledge.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_Knowledge.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.id != null ">
   and Dm_Knowledge.id like concat('%', #{SearchPojo.id}, '%')
</if>
<if test="SearchPojo.refno != null ">
   and Dm_Knowledge.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.deviceid != null ">
   and Dm_Knowledge.Deviceid like concat('%', #{SearchPojo.deviceid}, '%')
</if>
<if test="SearchPojo.devicename != null ">
   and Dm_Knowledge.DeviceName like concat('%', #{SearchPojo.devicename}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Dm_Knowledge.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Dm_Knowledge.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Dm_Knowledge.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.reason != null ">
   and Dm_Knowledge.Reason like concat('%', #{SearchPojo.reason}, '%')
</if>
<if test="SearchPojo.details != null ">
   and Dm_Knowledge.Details like concat('%', #{SearchPojo.details}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Dm_Knowledge.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.videourl != null ">
   and Dm_Knowledge.VideoUrl like concat('%', #{SearchPojo.videourl}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_Knowledge.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_Knowledge.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_Knowledge.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_Knowledge.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_Knowledge.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_Knowledge.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_Knowledge.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_Knowledge.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_Knowledge.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_Knowledge.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_Knowledge.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_Knowledge.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Dm_Knowledge.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.id != null ">
   or Dm_Knowledge.id like concat('%', #{SearchPojo.id}, '%')
</if>
<if test="SearchPojo.refno != null ">
   or Dm_Knowledge.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.deviceid != null ">
   or Dm_Knowledge.Deviceid like concat('%', #{SearchPojo.deviceid}, '%')
</if>
<if test="SearchPojo.devicename != null ">
   or Dm_Knowledge.DeviceName like concat('%', #{SearchPojo.devicename}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Dm_Knowledge.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Dm_Knowledge.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Dm_Knowledge.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.reason != null ">
   or Dm_Knowledge.Reason like concat('%', #{SearchPojo.reason}, '%')
</if>
<if test="SearchPojo.details != null ">
   or Dm_Knowledge.Details like concat('%', #{SearchPojo.details}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Dm_Knowledge.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.videourl != null ">
   or Dm_Knowledge.VideoUrl like concat('%', #{SearchPojo.videourl}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_Knowledge.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_Knowledge.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_Knowledge.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_Knowledge.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_Knowledge.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_Knowledge.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_Knowledge.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_Knowledge.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_Knowledge.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_Knowledge.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_Knowledge.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_Knowledge.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Dm_Knowledge.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_Knowledge(id, RefNo, Deviceid, DeviceName, BillType, BillTitle, Operator, BillDate, Reason, Details, Remark, VideoUrl, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateBy, CreateByid, CreateDate, ModifyDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Tenantid, Revision)
        values (#{id}, #{refno}, #{deviceid}, #{devicename}, #{billtype}, #{billtitle}, #{operator}, #{billdate}, #{reason}, #{details}, #{remark}, #{videourl}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{lister}, #{listerid}, #{createby}, #{createbyid}, #{createdate}, #{modifydate}, #{enabledmark}, #{deletemark}, #{deletelister}, #{deletedate}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_Knowledge
        <set>
            <if test="id != null ">
                id =#{id},
            </if>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="deviceid != null ">
                Deviceid =#{deviceid},
            </if>
            <if test="devicename != null ">
                DeviceName =#{devicename},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="reason != null ">
                Reason =#{reason},
            </if>
            <if test="details != null ">
                Details =#{details},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="videourl != null ">
                VideoUrl =#{videourl},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
                Revision=Revision+1
        </set>
        where  = #{$pk.name} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_Knowledge where  = #{key} and Tenantid=#{tid}
    </delete>
                                                                                                                                </mapper>

