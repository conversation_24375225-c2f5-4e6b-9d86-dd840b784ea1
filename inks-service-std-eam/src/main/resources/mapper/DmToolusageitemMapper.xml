<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmToolusageitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmToolusageitemPojo">
        <include refid="selectDmToolusageitemVo"/>
        where Dm_ToolUsageItem.id = #{key} and Dm_ToolUsageItem.Tenantid=#{tid}
    </select>
    <sql id="selectDmToolusageitemVo">
        select Dm_ToolUsageItem.id,
               Dm_ToolUsageItem.Pid,
               Dm_ToolUsageItem.Toolid,
               Dm_ToolUsageItem.Quantity,
               Dm_ToolUsageItem.Workid,
               Dm_ToolUsageItem.WorkUid,
               Dm_ToolUsageItem.UssgeDate,
               Dm_ToolUsageItem.RowNum,
               Dm_ToolUsageItem.Remark,
               Dm_ToolUsageItem.Custom1,
               Dm_ToolUsageItem.Custom2,
               Dm_ToolUsageItem.Custom3,
               Dm_ToolUsageItem.Custom4,
               Dm_ToolUsageItem.Custom5,
               Dm_ToolUsageItem.Custom6,
               Dm_ToolUsageItem.Custom7,
               Dm_ToolUsageItem.Custom8,
               Dm_ToolUsageItem.Custom9,
               Dm_ToolUsageItem.Custom10,
               Dm_ToolUsageItem.Deptid,
               Dm_ToolUsageItem.Tenantid,
               Dm_ToolUsageItem.TenantName,
               Dm_ToolUsageItem.Revision,
               Dm_ToolInfo.ToolName,
               Dm_ToolInfo.ToolCode
        from Dm_ToolUsageItem
        left join Dm_ToolInfo on Dm_ToolUsageItem.Toolid = Dm_ToolInfo.id
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmToolusageitemPojo">
        <include refid="selectDmToolusageitemVo"/>
        where Dm_ToolUsageItem.Pid = #{Pid} and Dm_ToolUsageItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmToolusageitemPojo">
        <include refid="selectDmToolusageitemVo"/>
         where 1 = 1 and Dm_ToolUsageItem.Tenantid =#{tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_ToolUsageItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Dm_ToolUsageItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.toolid != null and SearchPojo.toolid != ''">
   and Dm_ToolUsageItem.toolid like concat('%', #{SearchPojo.toolid}, '%')
</if>
<if test="SearchPojo.workid != null and SearchPojo.workid != ''">
   and Dm_ToolUsageItem.workid like concat('%', #{SearchPojo.workid}, '%')
</if>
<if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
   and Dm_ToolUsageItem.workuid like concat('%', #{SearchPojo.workuid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Dm_ToolUsageItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Dm_ToolUsageItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Dm_ToolUsageItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Dm_ToolUsageItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Dm_ToolUsageItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Dm_ToolUsageItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Dm_ToolUsageItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Dm_ToolUsageItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Dm_ToolUsageItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Dm_ToolUsageItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Dm_ToolUsageItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null and SearchPojo.deptid != ''">
   and Dm_ToolUsageItem.deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   and Dm_ToolUsageItem.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Dm_ToolUsageItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.toolid != null and SearchPojo.toolid != ''">
   or Dm_ToolUsageItem.Toolid like concat('%', #{SearchPojo.toolid}, '%')
</if>
<if test="SearchPojo.workid != null and SearchPojo.workid != ''">
   or Dm_ToolUsageItem.Workid like concat('%', #{SearchPojo.workid}, '%')
</if>
<if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
   or Dm_ToolUsageItem.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Dm_ToolUsageItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Dm_ToolUsageItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Dm_ToolUsageItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Dm_ToolUsageItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Dm_ToolUsageItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Dm_ToolUsageItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Dm_ToolUsageItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Dm_ToolUsageItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Dm_ToolUsageItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Dm_ToolUsageItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Dm_ToolUsageItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null and SearchPojo.deptid != ''">
   or Dm_ToolUsageItem.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   or Dm_ToolUsageItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_ToolUsageItem(id, Pid, Toolid, Quantity, Workid, WorkUid, UssgeDate, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{toolid}, #{quantity}, #{workid}, #{workuid}, #{ussgedate}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_ToolUsageItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="toolid != null ">
                Toolid = #{toolid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="workid != null ">
                Workid = #{workid},
            </if>
            <if test="workuid != null ">
                WorkUid = #{workuid},
            </if>
            <if test="ussgedate != null">
                UssgeDate = #{ussgedate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="deptid != null ">
                Deptid = #{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_ToolUsageItem where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getSumUsedQtyAfterDate" resultType="int">
        select IFNULL(sum(Quantity),0)
        from Dm_ToolUsageItem
        where Tenantid=#{tid}
        and Toolid=#{toolid}
        and UssgeDate > #{maintdate}
    </select>
</mapper>

