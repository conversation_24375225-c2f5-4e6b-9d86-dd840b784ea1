<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmUpkeeprecordMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmUpkeeprecordPojo">
        select Dm_UpkeepRecord.id,
               Dm_UpkeepRecord.RefNo,
               Dm_UpkeepRecord.BillDate,
               Dm_UpkeepRecord.BillType,
               Dm_UpkeepRecord.BillTitle,
               Dm_UpkeepRecord.Planid,
               Dm_UpkeepRecord.PlanDate,
               Dm_UpkeepRecord.ItemLevel,
               Dm_UpkeepRecord.ItemName,
               Dm_UpkeepRecord.WorkTime,
               Dm_UpkeepRecord.Operator,
               Dm_UpkeepRecord.Solution,
               Dm_UpkeepRecord.ItemCount,
               Dm_UpkeepRecord.FinishCount,
               Dm_UpkeepRecord.Summary,
               Dm_UpkeepRecord.Confirmer,
               Dm_UpkeepRecord.Custom1,
               Dm_UpkeepRecord.Custom2,
               Dm_UpkeepRecord.Custom3,
               Dm_UpkeepRecord.Custom4,
               Dm_UpkeepRecord.Custom5,
               Dm_UpkeepRecord.Custom6,
               Dm_UpkeepRecord.Custom7,
               Dm_UpkeepRecord.Custom8,
               Dm_UpkeepRecord.Listerid,
               Dm_UpkeepRecord.Lister,
               Dm_UpkeepRecord.CreateBy,
               Dm_UpkeepRecord.CreateByid,
               Dm_UpkeepRecord.CreateDate,
               Dm_UpkeepRecord.ModifyDate,
               Dm_UpkeepRecord.Assessorid,
               Dm_UpkeepRecord.Assessor,
               Dm_UpkeepRecord.AssessDate,
               Dm_UpkeepRecord.Tenantid,
               Dm_UpkeepRecord.Revision,
               Dm_UpkeepFlow.BillTitle as flowname,
               Dm_UpkeepFlow.id        as flowid
        from Dm_UpkeepRecord
                 left join Dm_UpkeepPlan on Dm_UpkeepRecord.Planid = Dm_UpkeepPlan.id
                 left join Dm_UpkeepFlow on Dm_UpkeepPlan.Flowid = Dm_UpkeepFlow.id
        where Dm_UpkeepRecord.id = #{key}
          and Dm_UpkeepRecord.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Dm_UpkeepRecord.id,
               Dm_UpkeepRecord.RefNo,
               Dm_UpkeepRecord.BillDate,
               Dm_UpkeepRecord.BillType,
               Dm_UpkeepRecord.BillTitle,
               Dm_UpkeepRecord.Planid,
               Dm_UpkeepRecord.PlanDate,
               Dm_UpkeepRecord.ItemLevel,
               Dm_UpkeepRecord.ItemName,
               Dm_UpkeepRecord.WorkTime,
               Dm_UpkeepRecord.Operator,
               Dm_UpkeepRecord.Solution,
               Dm_UpkeepRecord.ItemCount,
               Dm_UpkeepRecord.FinishCount,
               Dm_UpkeepRecord.Summary,
               Dm_UpkeepRecord.Confirmer,
               Dm_UpkeepRecord.Custom1,
               Dm_UpkeepRecord.Custom2,
               Dm_UpkeepRecord.Custom3,
               Dm_UpkeepRecord.Custom4,
               Dm_UpkeepRecord.Custom5,
               Dm_UpkeepRecord.Custom6,
               Dm_UpkeepRecord.Custom7,
               Dm_UpkeepRecord.Custom8,
               Dm_UpkeepRecord.Listerid,
               Dm_UpkeepRecord.Lister,
               Dm_UpkeepRecord.CreateBy,
               Dm_UpkeepRecord.CreateByid,
               Dm_UpkeepRecord.CreateDate,
               Dm_UpkeepRecord.ModifyDate,
               Dm_UpkeepRecord.Assessorid,
               Dm_UpkeepRecord.Assessor,
               Dm_UpkeepRecord.AssessDate,
               Dm_UpkeepRecord.Tenantid,
               Dm_UpkeepRecord.Revision,
               Dm_UpkeepFlow.BillTitle as flowname,
               Dm_UpkeepFlow.id        as flowid
        from Dm_UpkeepRecord
                 left join Dm_UpkeepPlan on Dm_UpkeepRecord.Planid = Dm_UpkeepPlan.id
                 left join Dm_UpkeepFlow on Dm_UpkeepPlan.Flowid = Dm_UpkeepFlow.id
    </sql>
    <sql id="selectdetailVo">
        select Dm_UpkeepRecordItem.id,
               Dm_UpkeepRecordItem.Pid,
               Dm_UpkeepRecordItem.Deviceid,
               Dm_UpkeepRecordItem.Remark,
               Dm_UpkeepRecordItem.RowNum,
               Dm_UpkeepRecordItem.ExpiryDate,
               Dm_UpkeepRecordItem.FinishMark,
               Dm_UpkeepRecordItem.Recordid,
               Dm_UpkeepRecordItem.RecordBy,
               Dm_UpkeepRecordItem.RecordDate,
               Dm_UpkeepRecordItem.Custom1,
               Dm_UpkeepRecordItem.Custom2,
               Dm_UpkeepRecordItem.Custom3,
               Dm_UpkeepRecordItem.Custom4,
               Dm_UpkeepRecordItem.Custom5,
               Dm_UpkeepRecordItem.Custom6,
               Dm_UpkeepRecordItem.Custom7,
               Dm_UpkeepRecordItem.Custom8,
               Dm_UpkeepRecordItem.Tenantid,
               Dm_UpkeepRecordItem.Revision,
               Dm_Device.DevCode,
               Dm_Device.DevName,
               Dm_Device.DevSpec,
               Dm_Device.DevUnit,
               Dm_UpkeepRecord.RefNo,
               Dm_UpkeepRecord.BillDate,
               Dm_UpkeepRecord.BillType,
               Dm_UpkeepRecord.BillTitle
        from Dm_UpkeepRecordItem
                 Left Join Dm_UpkeepRecord on Dm_UpkeepRecordItem.Pid = Dm_UpkeepRecord.id
                 join Dm_Device on Dm_UpkeepRecordItem.Deviceid = Dm_Device.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmUpkeeprecorditemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Dm_UpkeepRecord.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Dm_UpkeepRecord.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Dm_UpkeepRecord.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Dm_UpkeepRecord.billtype like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Dm_UpkeepRecord.billtitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.planid != null">
            and Dm_UpkeepRecord.planid like concat('%',
                #{SearchPojo.planid}, '%')
        </if>
        <if test="SearchPojo.itemlevel != null">
            and Dm_UpkeepRecord.itemlevel like concat('%',
                #{SearchPojo.itemlevel}, '%')
        </if>
        <if test="SearchPojo.itemname != null">
            and Dm_UpkeepRecord.itemname like concat('%',
                #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Dm_UpkeepRecord.operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.solution != null">
            and Dm_UpkeepRecord.solution like concat('%',
                #{SearchPojo.solution}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Dm_UpkeepRecord.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Dm_UpkeepRecord.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Dm_UpkeepRecord.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Dm_UpkeepRecord.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Dm_UpkeepRecord.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Dm_UpkeepRecord.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Dm_UpkeepRecord.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Dm_UpkeepRecord.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Dm_UpkeepRecord.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Dm_UpkeepRecord.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Dm_UpkeepRecord.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Dm_UpkeepRecord.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Dm_UpkeepRecord.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Dm_UpkeepRecord.assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Dm_UpkeepRecord.assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Dm_UpkeepRecord.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Dm_UpkeepRecord.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Dm_UpkeepRecord.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.planid != null">
                or Dm_UpkeepRecord.Planid like concat('%', #{SearchPojo.planid}, '%')
            </if>
            <if test="SearchPojo.itemlevel != null">
                or Dm_UpkeepRecord.ItemLevel like concat('%', #{SearchPojo.itemlevel}, '%')
            </if>
            <if test="SearchPojo.itemname != null">
                or Dm_UpkeepRecord.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Dm_UpkeepRecord.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.solution != null">
                or Dm_UpkeepRecord.Solution like concat('%', #{SearchPojo.solution}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Dm_UpkeepRecord.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Dm_UpkeepRecord.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Dm_UpkeepRecord.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Dm_UpkeepRecord.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Dm_UpkeepRecord.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Dm_UpkeepRecord.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Dm_UpkeepRecord.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Dm_UpkeepRecord.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Dm_UpkeepRecord.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Dm_UpkeepRecord.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Dm_UpkeepRecord.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Dm_UpkeepRecord.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Dm_UpkeepRecord.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Dm_UpkeepRecord.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Dm_UpkeepRecord.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmUpkeeprecordPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Dm_UpkeepRecord.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Dm_UpkeepRecord.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Dm_UpkeepRecord.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Dm_UpkeepRecord.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Dm_UpkeepRecord.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.planid != null">
            and Dm_UpkeepRecord.Planid like concat('%',
                #{SearchPojo.planid}, '%')
        </if>
        <if test="SearchPojo.itemlevel != null">
            and Dm_UpkeepRecord.ItemLevel like concat('%',
                #{SearchPojo.itemlevel}, '%')
        </if>
        <if test="SearchPojo.itemname != null">
            and Dm_UpkeepRecord.ItemName like concat('%',
                #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Dm_UpkeepRecord.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.solution != null">
            and Dm_UpkeepRecord.Solution like concat('%',
                #{SearchPojo.solution}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Dm_UpkeepRecord.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Dm_UpkeepRecord.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Dm_UpkeepRecord.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Dm_UpkeepRecord.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Dm_UpkeepRecord.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Dm_UpkeepRecord.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Dm_UpkeepRecord.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Dm_UpkeepRecord.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Dm_UpkeepRecord.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Dm_UpkeepRecord.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Dm_UpkeepRecord.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Dm_UpkeepRecord.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Dm_UpkeepRecord.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Dm_UpkeepRecord.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Dm_UpkeepRecord.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Dm_UpkeepRecord.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Dm_UpkeepRecord.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Dm_UpkeepRecord.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.planid != null">
                or Dm_UpkeepRecord.Planid like concat('%', #{SearchPojo.planid}, '%')
            </if>
            <if test="SearchPojo.itemlevel != null">
                or Dm_UpkeepRecord.ItemLevel like concat('%', #{SearchPojo.itemlevel}, '%')
            </if>
            <if test="SearchPojo.itemname != null">
                or Dm_UpkeepRecord.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Dm_UpkeepRecord.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.solution != null">
                or Dm_UpkeepRecord.Solution like concat('%', #{SearchPojo.solution}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Dm_UpkeepRecord.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Dm_UpkeepRecord.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Dm_UpkeepRecord.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Dm_UpkeepRecord.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Dm_UpkeepRecord.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Dm_UpkeepRecord.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Dm_UpkeepRecord.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Dm_UpkeepRecord.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Dm_UpkeepRecord.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Dm_UpkeepRecord.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Dm_UpkeepRecord.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Dm_UpkeepRecord.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Dm_UpkeepRecord.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Dm_UpkeepRecord.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Dm_UpkeepRecord.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Dm_UpkeepRecord(id, RefNo, BillDate, BillType, BillTitle, Planid, PlanDate, ItemLevel, ItemName,
                                    WorkTime, Operator, Solution, ItemCount, FinishCount, Summary, Confirmer, Custom1,
                                    Custom2,
                                    Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Listerid, Lister, CreateBy,
                                    CreateByid, CreateDate, ModifyDate, Assessorid, Assessor, AssessDate, Tenantid,
                                    Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{planid}, #{plandate}, #{itemlevel},
                #{itemname}, #{worktime}, #{operator}, #{solution}, #{itemcount}, #{finishcount}, #{summary},
                #{confirmer},
                #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
                #{listerid}, #{lister}, #{createby}, #{createbyid}, #{createdate}, #{modifydate}, #{assessorid},
                #{assessor}, #{assessdate}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_UpkeepRecord
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="planid != null">
                Planid =#{planid},
            </if>
            <if test="plandate != null">
                PlanDate =#{plandate},
            </if>
            <if test="itemlevel != null">
                ItemLevel =#{itemlevel},
            </if>
            <if test="itemname != null">
                ItemName =#{itemname},
            </if>
            <if test="worktime != null">
                WorkTime =#{worktime},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="solution != null">
                Solution =#{solution},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="confirmer != null">
                Confirmer =#{confirmer},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Dm_UpkeepRecord
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Dm_UpkeepRecord
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.eam.domain.pojo.DmUpkeeprecordPojo">
        select id
        from Dm_UpkeepRecordItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getDelSpareIds" resultType="java.lang.String"
            parameterType="inks.service.std.eam.domain.pojo.DmUpkeeprecordPojo">
        select id
        from Dm_UpkeepRecordSpare
        where Pid = #{id}
        <if test="spare != null and spare.size() > 0">
            and id not in
            <foreach collection="spare" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>


    <!--    获取指定计划id和计划日期下完成的实施finishCount数-->
    <select id="getSumFinishCount" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(FinishCount), 0)
        FROM Dm_UpkeepRecord
        WHERE Planid = #{planid}
          AND DATE(PlanDate) = DATE(#{plandate})
          AND Tenantid = #{tid}
    </select>

    <select id="getAllDevId" resultType="java.lang.String">
        SELECT DISTINCT Dm_UpkeepRecordItem.Deviceid
        FROM Dm_UpkeepRecord
                 Right join Dm_UpkeepRecordItem on Dm_UpkeepRecord.id = Dm_UpkeepRecordItem.Pid
        WHERE Dm_UpkeepRecord.Planid = #{planid}
          AND DATE(Dm_UpkeepRecord.PlanDate) = DATE(#{plandate})
          AND Dm_UpkeepRecord.Tenantid = #{tid}
    </select>

    <select id="getAllFinishDevId" resultType="java.lang.String">
        SELECT DISTINCT Dm_UpkeepRecordItem.Deviceid
        FROM Dm_UpkeepRecord
                 Right join Dm_UpkeepRecordItem on Dm_UpkeepRecord.id = Dm_UpkeepRecordItem.Pid
        WHERE Dm_UpkeepRecord.Planid = #{planid}
          AND DATE(Dm_UpkeepRecord.PlanDate) = DATE(#{plandate})
          AND Dm_UpkeepRecordItem.FinishMark = 1
          AND Dm_UpkeepRecord.Tenantid = #{tid}
    </select>

    <select id="getListByPlanidAndDate" resultType="inks.service.std.eam.domain.pojo.DmUpkeeprecordPojo">
        select *
        from Dm_UpkeepRecord
        where Planid = #{planid}
          and DATE(PlanDate) = DATE(#{plandate})
          and Tenantid = #{tid}
    </select>
</mapper>

