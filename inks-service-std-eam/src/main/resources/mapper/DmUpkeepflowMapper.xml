<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmUpkeepflowMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmUpkeepflowPojo">
        select
          id, RefNo, BillDate, BillType, BillTitle, Operator, Summary, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Assessorid, Assessor, AssessDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Tenantid, Revision
        from Dm_UpkeepFlow
        where Dm_UpkeepFlow.id = #{key} and Dm_UpkeepFlow.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
          id, RefNo, BillDate, BillType, BillTitle, Operator, Summary, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Assessorid, Assessor, AssessDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Tenantid, Revision        from Dm_UpkeepFlow
    </sql>
    <sql id="selectdetailVo">
         select
          id, RefNo, BillDate, BillType, BillTitle, Operator, Summary, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Assessorid, Assessor, AssessDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Tenantid, Revision        from Dm_UpkeepFlow
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmUpkeepflowitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 and Dm_UpkeepFlow.Tenantid =#{tenantid}
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_UpkeepFlow.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Dm_UpkeepFlow.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Dm_UpkeepFlow.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Dm_UpkeepFlow.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Dm_UpkeepFlow.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Dm_UpkeepFlow.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_UpkeepFlow.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_UpkeepFlow.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_UpkeepFlow.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_UpkeepFlow.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_UpkeepFlow.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_UpkeepFlow.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_UpkeepFlow.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_UpkeepFlow.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_UpkeepFlow.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_UpkeepFlow.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_UpkeepFlow.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_UpkeepFlow.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Dm_UpkeepFlow.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Dm_UpkeepFlow.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Dm_UpkeepFlow.deletelister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Dm_UpkeepFlow.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Dm_UpkeepFlow.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Dm_UpkeepFlow.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Dm_UpkeepFlow.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Dm_UpkeepFlow.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_UpkeepFlow.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_UpkeepFlow.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_UpkeepFlow.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_UpkeepFlow.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_UpkeepFlow.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_UpkeepFlow.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_UpkeepFlow.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_UpkeepFlow.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_UpkeepFlow.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_UpkeepFlow.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_UpkeepFlow.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_UpkeepFlow.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Dm_UpkeepFlow.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Dm_UpkeepFlow.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Dm_UpkeepFlow.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmUpkeepflowPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 and Dm_UpkeepFlow.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_UpkeepFlow.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.refno != null ">
   and Dm_UpkeepFlow.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Dm_UpkeepFlow.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Dm_UpkeepFlow.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Dm_UpkeepFlow.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Dm_UpkeepFlow.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_UpkeepFlow.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_UpkeepFlow.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_UpkeepFlow.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_UpkeepFlow.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_UpkeepFlow.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_UpkeepFlow.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_UpkeepFlow.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_UpkeepFlow.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_UpkeepFlow.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_UpkeepFlow.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_UpkeepFlow.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_UpkeepFlow.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Dm_UpkeepFlow.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Dm_UpkeepFlow.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Dm_UpkeepFlow.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Dm_UpkeepFlow.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Dm_UpkeepFlow.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Dm_UpkeepFlow.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Dm_UpkeepFlow.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Dm_UpkeepFlow.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_UpkeepFlow.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_UpkeepFlow.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_UpkeepFlow.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_UpkeepFlow.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_UpkeepFlow.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_UpkeepFlow.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_UpkeepFlow.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_UpkeepFlow.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_UpkeepFlow.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_UpkeepFlow.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_UpkeepFlow.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_UpkeepFlow.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Dm_UpkeepFlow.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Dm_UpkeepFlow.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Dm_UpkeepFlow.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_UpkeepFlow(id, RefNo, BillDate, BillType, BillTitle, Operator, Summary, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Assessorid, Assessor, AssessDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Tenantid, Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{operator}, #{summary}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{lister}, #{listerid}, #{createdate}, #{createby}, #{createbyid}, #{modifydate}, #{assessorid}, #{assessor}, #{assessdate}, #{enabledmark}, #{deletemark}, #{deletelister}, #{deletedate}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_UpkeepFlow
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_UpkeepFlow where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                                <!--通过主键审核数据-->
    <update id="approval">
        update Dm_UpkeepFlow SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
                                    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.std.eam.domain.pojo.DmUpkeepflowPojo">
        select
          id
        from Dm_UpkeepFlowItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

</mapper>

