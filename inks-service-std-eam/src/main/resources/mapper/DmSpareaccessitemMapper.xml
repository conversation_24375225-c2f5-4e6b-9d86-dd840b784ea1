<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmSpareaccessitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmSpareaccessitemPojo">
        select id,
               Pid,
               Spareid,
               Quantity,
               Price,
               Amount,
               Remark,
               RowNum,
               Location,
               BatchNo,
               ExpiryDate,
               TaxPrice,
               TaxAmount,
               ItemTaxrate,
               CiteUid,
               CiteItemid,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Tenantid,
               Revision
        from Dm_SpareAccessItem
        where Dm_SpareAccessItem.id = #{key}
          and Dm_SpareAccessItem.Tenantid = #{tid}
    </select>
    <sql id="selectDmSpareaccessitemVo">
        select id,
               Pid,
               Spareid,
               Quantity,
               Price,
               Amount,
               Remark,
               RowNum,
               Location,
               BatchNo,
               ExpiryDate,
               TaxPrice,
               TaxAmount,
               ItemTaxrate,
               CiteUid,
               CiteItemid,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Tenantid,
               Revision
        from Dm_SpareAccessItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmSpareaccessitemPojo">
        <include refid="selectDmSpareaccessitemVo"/>
        where 1 = 1 and Dm_SpareAccessItem.Tenantid =#{Tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_SpareAccessItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Dm_SpareAccessItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.spareid != null and SearchPojo.spareid != ''">
            and Dm_SpareAccessItem.spareid like concat('%', #{SearchPojo.spareid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Dm_SpareAccessItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Dm_SpareAccessItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Dm_SpareAccessItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Dm_SpareAccessItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Dm_SpareAccessItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Dm_SpareAccessItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Dm_SpareAccessItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Dm_SpareAccessItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Dm_SpareAccessItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Dm_SpareAccessItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Dm_SpareAccessItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Dm_SpareAccessItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.spareid != null and SearchPojo.spareid != ''">
                or Dm_SpareAccessItem.Spareid like concat('%', #{SearchPojo.spareid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Dm_SpareAccessItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Dm_SpareAccessItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Dm_SpareAccessItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Dm_SpareAccessItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Dm_SpareAccessItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Dm_SpareAccessItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Dm_SpareAccessItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Dm_SpareAccessItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Dm_SpareAccessItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Dm_SpareAccessItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Dm_SpareAccessItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmSpareaccessitemPojo">
        select Dm_SpareAccessItem.id,
               Dm_SpareAccessItem.Pid,
               Dm_SpareAccessItem.Spareid,
               Dm_SpareAccessItem.Quantity,
               Dm_SpareAccessItem.Price,
               Dm_SpareAccessItem.Amount,
               Dm_SpareAccessItem.Remark,
               Dm_SpareAccessItem.RowNum,
               Dm_SpareAccessItem.Location,
               Dm_SpareAccessItem.BatchNo,
               Dm_SpareAccessItem.ExpiryDate,
               Dm_SpareAccessItem.TaxPrice,
               Dm_SpareAccessItem.TaxAmount,
               Dm_SpareAccessItem.ItemTaxrate,
               Dm_SpareAccessItem.CiteUid,
               Dm_SpareAccessItem.CiteItemid,
               Dm_SpareAccessItem.Custom1,
               Dm_SpareAccessItem.Custom2,
               Dm_SpareAccessItem.Custom3,
               Dm_SpareAccessItem.Custom4,
               Dm_SpareAccessItem.Custom5,
               Dm_SpareAccessItem.Custom6,
               Dm_SpareAccessItem.Custom7,
               Dm_SpareAccessItem.Custom8,
               Dm_SpareAccessItem.Tenantid,
               Dm_SpareAccessItem.Revision,
               Dm_Spare.SpareUnit,
               Dm_Spare.SpareName,
               Dm_Spare.SpareCode,
               Dm_Spare.SpareSpec
        from Dm_SpareAccessItem
                 join Dm_Spare on Dm_SpareAccessItem.Spareid = Dm_Spare.id
        where Dm_SpareAccessItem.Pid = #{Pid}
          and Dm_SpareAccessItem.Tenantid = #{tid}
        order by Dm_SpareAccessItem.RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Dm_SpareAccessItem(id, Pid, Spareid, Quantity, Price, Amount, Remark, RowNum, Location, BatchNo,
                                       ExpiryDate, TaxPrice, TaxAmount, ItemTaxrate, CiteUid, CiteItemid, Custom1,
                                       Custom2, Custom3, Custom4,
                                       Custom5, Custom6, Custom7, Custom8, Tenantid, Revision)
        values (#{id}, #{pid}, #{spareid}, #{quantity}, #{price}, #{amount}, #{remark}, #{rownum}, #{location},
                #{batchno}, #{expirydate}, #{taxprice}, #{taxamount}, #{itemtaxrate}, #{citeuid}, #{citeitemid},
                #{custom1}, #{custom2},
                #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_SpareAccessItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="spareid != null ">
                Spareid = #{spareid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="expirydate != null">
                ExpiryDate = #{expirydate},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="citeuid != null">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null">
                CiteItemid = #{citeitemid},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Dm_SpareAccessItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

