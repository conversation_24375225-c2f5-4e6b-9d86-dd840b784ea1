<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmUpkeepplanMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmUpkeepplanPojo">
        select Dm_UpkeepPlan.id,
               Dm_UpkeepPlan.RefNo,
               Dm_UpkeepPlan.BillDate,
               Dm_UpkeepPlan.BillType,
               Dm_UpkeepPlan.BillTitle,
               Dm_UpkeepPlan.StartDate,
               Dm_UpkeepPlan.EndDate,
               Dm_UpkeepPlan.CycleType,
               Dm_UpkeepPlan.CycleValue,
               Dm_UpkeepPlan.CycleNotice,
               Dm_UpkeepPlan.NoticeStart,
               Dm_UpkeepPlan.NoticeEnd,
               Dm_UpkeepPlan.Flowid,
               Dm_UpkeepFlow.BillTitle as FlowTitle,
               Dm_UpkeepPlan.Operator,
               Dm_UpkeepPlan.ItemCount,
               Dm_UpkeepPlan.FinishCount,
               Dm_UpkeepPlan.Summary,
               Dm_UpkeepPlan.Custom1,
               Dm_UpkeepPlan.Custom2,
               Dm_UpkeepPlan.Custom3,
               Dm_UpkeepPlan.Custom4,
               Dm_UpkeepPlan.Custom5,
               Dm_UpkeepPlan.Custom6,
               Dm_UpkeepPlan.Custom7,
               Dm_UpkeepPlan.Custom8,
               Dm_UpkeepPlan.Lister,
               Dm_UpkeepPlan.Listerid,
               Dm_UpkeepPlan.CreateDate,
               Dm_UpkeepPlan.CreateByid,
               Dm_UpkeepPlan.CreateBy,
               Dm_UpkeepPlan.ModifyDate,
               Dm_UpkeepPlan.Assessorid,
               Dm_UpkeepPlan.Assessor,
               Dm_UpkeepPlan.AssessDate,
               Dm_UpkeepPlan.EnabledMark,
               Dm_UpkeepPlan.DeleteMark,
               Dm_UpkeepPlan.DeleteLister,
               Dm_UpkeepPlan.DeleteDate,
               Dm_UpkeepPlan.Tenantid,
               Dm_UpkeepPlan.Revision
        from Dm_UpkeepPlan
        Left join Dm_UpkeepFlow on Dm_UpkeepPlan.Flowid = Dm_UpkeepFlow.id
        where Dm_UpkeepPlan.id = #{key}
          and Dm_UpkeepPlan.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Dm_UpkeepPlan.id,
               Dm_UpkeepPlan.RefNo,
               Dm_UpkeepPlan.BillDate,
               Dm_UpkeepPlan.BillType,
               Dm_UpkeepPlan.BillTitle,
               Dm_UpkeepPlan.StartDate,
               Dm_UpkeepPlan.EndDate,
               Dm_UpkeepPlan.CycleType,
               Dm_UpkeepPlan.CycleValue,
               Dm_UpkeepPlan.CycleNotice,
               Dm_UpkeepPlan.NoticeStart,
               Dm_UpkeepPlan.NoticeEnd,
               Dm_UpkeepPlan.Flowid,
               Dm_UpkeepPlan.Operator,
               Dm_UpkeepPlan.ItemCount,
               Dm_UpkeepPlan.FinishCount,
               Dm_UpkeepPlan.Summary,
               Dm_UpkeepPlan.Custom1,
               Dm_UpkeepPlan.Custom2,
               Dm_UpkeepPlan.Custom3,
               Dm_UpkeepPlan.Custom4,
               Dm_UpkeepPlan.Custom5,
               Dm_UpkeepPlan.Custom6,
               Dm_UpkeepPlan.Custom7,
               Dm_UpkeepPlan.Custom8,
               Dm_UpkeepPlan.Lister,
               Dm_UpkeepPlan.Listerid,
               Dm_UpkeepPlan.CreateDate,
               Dm_UpkeepPlan.CreateByid,
               Dm_UpkeepPlan.CreateBy,
               Dm_UpkeepPlan.ModifyDate,
               Dm_UpkeepPlan.Assessorid,
               Dm_UpkeepPlan.Assessor,
               Dm_UpkeepPlan.AssessDate,
               Dm_UpkeepPlan.EnabledMark,
               Dm_UpkeepPlan.DeleteMark,
               Dm_UpkeepPlan.DeleteLister,
               Dm_UpkeepPlan.DeleteDate,
               Dm_UpkeepPlan.Tenantid,
               Dm_UpkeepPlan.Revision,
               Dm_UpkeepFlow.BillTitle as FlowTitle
        from Dm_UpkeepPlan
        LEFT JOIN Dm_UpkeepFlow on Dm_UpkeepPlan.Flowid = Dm_UpkeepFlow.id
    </sql>
    <sql id="selectdetailVo">
        select Dm_UpkeepPlanItem.id,
               Dm_UpkeepPlanItem.Pid,
               Dm_UpkeepPlanItem.Deviceid,
               Dm_UpkeepPlanItem.Remark,
               Dm_UpkeepPlanItem.RowNum,
               Dm_UpkeepPlanItem.Custom1,
               Dm_UpkeepPlanItem.Custom2,
               Dm_UpkeepPlanItem.Custom3,
               Dm_UpkeepPlanItem.Custom4,
               Dm_UpkeepPlanItem.Custom5,
               Dm_UpkeepPlanItem.Custom6,
               Dm_UpkeepPlanItem.Custom7,
               Dm_UpkeepPlanItem.Custom8,
               Dm_UpkeepPlanItem.Tenantid,
               Dm_UpkeepPlanItem.Revision,
               Dm_UpkeepPlan.RefNo,
               Dm_UpkeepPlan.BillDate,
               Dm_UpkeepPlan.BillType,
               Dm_UpkeepPlan.BillTitle,
               Dm_Device.DevName,
               Dm_Device.DevCode,
               Dm_Device.DevSpec,
               Dm_Device.DevUnit
        from Dm_UpkeepPlan
                 Right join Dm_UpkeepPlanItem on Dm_UpkeepPlan.id = Dm_UpkeepPlanItem.Pid
                 Left Join Dm_Device on Dm_UpkeepPlanItem.Deviceid = Dm_Device.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmUpkeepplanitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Dm_UpkeepPlan.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_UpkeepPlan.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Dm_UpkeepPlan.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Dm_UpkeepPlan.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Dm_UpkeepPlan.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.cycletype != null ">
            and Dm_UpkeepPlan.cycletype like concat('%', #{SearchPojo.cycletype}, '%')
        </if>
        <if test="SearchPojo.cyclevalue != null ">
            and Dm_UpkeepPlan.cyclevalue like concat('%', #{SearchPojo.cyclevalue}, '%')
        </if>
        <if test="SearchPojo.flowid != null ">
            and Dm_UpkeepPlan.flowid like concat('%', #{SearchPojo.flowid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Dm_UpkeepPlan.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Dm_UpkeepPlan.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Dm_UpkeepPlan.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Dm_UpkeepPlan.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Dm_UpkeepPlan.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Dm_UpkeepPlan.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Dm_UpkeepPlan.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Dm_UpkeepPlan.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Dm_UpkeepPlan.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Dm_UpkeepPlan.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Dm_UpkeepPlan.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Dm_UpkeepPlan.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Dm_UpkeepPlan.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Dm_UpkeepPlan.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Dm_UpkeepPlan.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Dm_UpkeepPlan.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.deletelister != null ">
            and Dm_UpkeepPlan.deletelister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Dm_UpkeepPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Dm_UpkeepPlan.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Dm_UpkeepPlan.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.cycletype != null ">
                or Dm_UpkeepPlan.CycleType like concat('%', #{SearchPojo.cycletype}, '%')
            </if>
            <if test="SearchPojo.cyclevalue != null ">
                or Dm_UpkeepPlan.CycleValue like concat('%', #{SearchPojo.cyclevalue}, '%')
            </if>
            <if test="SearchPojo.flowid != null ">
                or Dm_UpkeepPlan.Flowid like concat('%', #{SearchPojo.flowid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Dm_UpkeepPlan.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Dm_UpkeepPlan.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Dm_UpkeepPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Dm_UpkeepPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Dm_UpkeepPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Dm_UpkeepPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Dm_UpkeepPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Dm_UpkeepPlan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Dm_UpkeepPlan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Dm_UpkeepPlan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Dm_UpkeepPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Dm_UpkeepPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Dm_UpkeepPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Dm_UpkeepPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Dm_UpkeepPlan.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Dm_UpkeepPlan.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.deletelister != null ">
                or Dm_UpkeepPlan.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmUpkeepplanPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Dm_UpkeepPlan.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_UpkeepPlan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Dm_UpkeepPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Dm_UpkeepPlan.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Dm_UpkeepPlan.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.cycletype != null ">
            and Dm_UpkeepPlan.CycleType like concat('%', #{SearchPojo.cycletype}, '%')
        </if>
        <if test="SearchPojo.cyclevalue != null ">
            and Dm_UpkeepPlan.CycleValue like concat('%', #{SearchPojo.cyclevalue}, '%')
        </if>
        <if test="SearchPojo.flowid != null ">
            and Dm_UpkeepPlan.Flowid like concat('%', #{SearchPojo.flowid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Dm_UpkeepPlan.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Dm_UpkeepPlan.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Dm_UpkeepPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Dm_UpkeepPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Dm_UpkeepPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Dm_UpkeepPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Dm_UpkeepPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Dm_UpkeepPlan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Dm_UpkeepPlan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Dm_UpkeepPlan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Dm_UpkeepPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Dm_UpkeepPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Dm_UpkeepPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Dm_UpkeepPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Dm_UpkeepPlan.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Dm_UpkeepPlan.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.deletelister != null ">
            and Dm_UpkeepPlan.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Dm_UpkeepPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Dm_UpkeepPlan.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Dm_UpkeepPlan.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.cycletype != null ">
                or Dm_UpkeepPlan.CycleType like concat('%', #{SearchPojo.cycletype}, '%')
            </if>
            <if test="SearchPojo.cyclevalue != null ">
                or Dm_UpkeepPlan.CycleValue like concat('%', #{SearchPojo.cyclevalue}, '%')
            </if>
            <if test="SearchPojo.flowid != null ">
                or Dm_UpkeepPlan.Flowid like concat('%', #{SearchPojo.flowid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Dm_UpkeepPlan.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Dm_UpkeepPlan.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Dm_UpkeepPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Dm_UpkeepPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Dm_UpkeepPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Dm_UpkeepPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Dm_UpkeepPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Dm_UpkeepPlan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Dm_UpkeepPlan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Dm_UpkeepPlan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Dm_UpkeepPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Dm_UpkeepPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Dm_UpkeepPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Dm_UpkeepPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Dm_UpkeepPlan.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Dm_UpkeepPlan.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.deletelister != null ">
                or Dm_UpkeepPlan.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Dm_UpkeepPlan(id, RefNo, BillDate, BillType, BillTitle, StartDate, EndDate, CycleType, CycleValue,
                                  CycleNotice, NoticeStart, NoticeEnd, Flowid, Operator, ItemCount, FinishCount,
                                  Summary, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                  Lister, Listerid, CreateDate, CreateByid, CreateBy, ModifyDate, Assessorid, Assessor,
                                  AssessDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Tenantid, Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{startdate}, #{enddate}, #{cycletype},
                #{cyclevalue}, #{cyclenotice}, #{noticestart}, #{noticeend}, #{flowid}, #{operator}, #{itemcount},
                #{finishcount}, #{summary}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{lister}, #{listerid}, #{createdate}, #{createbyid}, #{createby},
                #{modifydate}, #{assessorid}, #{assessor}, #{assessdate}, #{enabledmark}, #{deletemark},
                #{deletelister}, #{deletedate}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_UpkeepPlan
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="cycletype != null ">
                CycleType =#{cycletype},
            </if>
            <if test="cyclevalue != null ">
                CycleValue =#{cyclevalue},
            </if>
            <if test="cyclenotice != null">
                CycleNotice =#{cyclenotice},
            </if>
            <if test="noticestart != null">
                NoticeStart =#{noticestart},
            </if>
            <if test="noticeend != null">
                NoticeEnd =#{noticeend},
            </if>
            <if test="flowid != null ">
                Flowid =#{flowid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Dm_UpkeepPlan
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Dm_UpkeepPlan
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.eam.domain.pojo.DmUpkeepplanPojo">
        select
        id
        from Dm_UpkeepPlanItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <select id="getAllList" resultType="inks.service.std.eam.domain.pojo.DmUpkeepplanPojo">
        select *
        from Dm_UpkeepPlan
        where Tenantid = #{tid}
    </select>
    <select id="getAllDevId" resultType="java.lang.String">
        select Dm_UpkeepPlanItem.Deviceid
        from Dm_UpkeepPlan
        Right join  Dm_UpkeepPlanItem on Dm_UpkeepPlan.id = Dm_UpkeepPlanItem.Pid
        where Dm_UpkeepPlan.Tenantid = #{tid}
        and Dm_UpkeepPlan.id = #{planid}
    </select>

    <select id="getPlanName" resultType="java.lang.String">
        select BillTitle
        from Dm_UpkeepPlan
        where Tenantid = #{tid}
        and id = #{planid}
    </select>
</mapper>

