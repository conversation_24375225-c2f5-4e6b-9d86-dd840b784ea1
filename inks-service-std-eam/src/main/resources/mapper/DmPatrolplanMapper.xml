<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmPatrolplanMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmPatrolplanPojo">
        select
          id, RefNo, BillType, BillDate, BillTitle, PlanGroupid, PlanCode, PlanName, Pathid, PathCode, PathName, PlanStart, PlanEnd, Receiver, RowNum, EnabledMark, FinishMark, Closed, Operator, Remark, StateCode, StateDate, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Assessor, Assessorid, AssessDate, DeleteMark, DeleteLister, DeleteDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolPlan
        where Dm_PatrolPlan.id = #{key} and Dm_PatrolPlan.Tenantid=#{tid}
    </select>
    <sql id="selectDmPatrolplanVo">
         select
          id, RefNo, BillType, BillDate, BillTitle, PlanGroupid, PlanCode, PlanName, Pathid, PathCode, PathName, PlanStart, PlanEnd, Receiver, RowNum, EnabledMark, FinishMark, Closed, Operator, Remark, StateCode, StateDate, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Assessor, Assessorid, AssessDate, DeleteMark, DeleteLister, DeleteDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolPlan
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmPatrolplanPojo">
        <include refid="selectDmPatrolplanVo"/>
         where 1 = 1 and Dm_PatrolPlan.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_PatrolPlan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Dm_PatrolPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Dm_PatrolPlan.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Dm_PatrolPlan.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.plangroupid != null ">
   and Dm_PatrolPlan.PlanGroupid like concat('%', #{SearchPojo.plangroupid}, '%')
</if>
<if test="SearchPojo.plancode != null ">
   and Dm_PatrolPlan.PlanCode like concat('%', #{SearchPojo.plancode}, '%')
</if>
<if test="SearchPojo.planname != null ">
   and Dm_PatrolPlan.PlanName like concat('%', #{SearchPojo.planname}, '%')
</if>
<if test="SearchPojo.pathid != null ">
   and Dm_PatrolPlan.Pathid like concat('%', #{SearchPojo.pathid}, '%')
</if>
<if test="SearchPojo.pathcode != null ">
   and Dm_PatrolPlan.PathCode like concat('%', #{SearchPojo.pathcode}, '%')
</if>
<if test="SearchPojo.pathname != null ">
   and Dm_PatrolPlan.PathName like concat('%', #{SearchPojo.pathname}, '%')
</if>
<if test="SearchPojo.receiver != null ">
   and Dm_PatrolPlan.Receiver like concat('%', #{SearchPojo.receiver}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Dm_PatrolPlan.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Dm_PatrolPlan.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Dm_PatrolPlan.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_PatrolPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_PatrolPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_PatrolPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_PatrolPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Dm_PatrolPlan.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Dm_PatrolPlan.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Dm_PatrolPlan.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_PatrolPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_PatrolPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_PatrolPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_PatrolPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_PatrolPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_PatrolPlan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_PatrolPlan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_PatrolPlan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Dm_PatrolPlan.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Dm_PatrolPlan.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Dm_PatrolPlan.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Dm_PatrolPlan.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Dm_PatrolPlan.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.plangroupid != null ">
   or Dm_PatrolPlan.PlanGroupid like concat('%', #{SearchPojo.plangroupid}, '%')
</if>
<if test="SearchPojo.plancode != null ">
   or Dm_PatrolPlan.PlanCode like concat('%', #{SearchPojo.plancode}, '%')
</if>
<if test="SearchPojo.planname != null ">
   or Dm_PatrolPlan.PlanName like concat('%', #{SearchPojo.planname}, '%')
</if>
<if test="SearchPojo.pathid != null ">
   or Dm_PatrolPlan.Pathid like concat('%', #{SearchPojo.pathid}, '%')
</if>
<if test="SearchPojo.pathcode != null ">
   or Dm_PatrolPlan.PathCode like concat('%', #{SearchPojo.pathcode}, '%')
</if>
<if test="SearchPojo.pathname != null ">
   or Dm_PatrolPlan.PathName like concat('%', #{SearchPojo.pathname}, '%')
</if>
<if test="SearchPojo.receiver != null ">
   or Dm_PatrolPlan.Receiver like concat('%', #{SearchPojo.receiver}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Dm_PatrolPlan.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Dm_PatrolPlan.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Dm_PatrolPlan.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_PatrolPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_PatrolPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_PatrolPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_PatrolPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Dm_PatrolPlan.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Dm_PatrolPlan.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Dm_PatrolPlan.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_PatrolPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_PatrolPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_PatrolPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_PatrolPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_PatrolPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_PatrolPlan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_PatrolPlan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_PatrolPlan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Dm_PatrolPlan.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Dm_PatrolPlan.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_PatrolPlan(id, RefNo, BillType, BillDate, BillTitle, PlanGroupid, PlanCode, PlanName, Pathid, PathCode, PathName, PlanStart, PlanEnd, Receiver, RowNum, EnabledMark, FinishMark, Closed, Operator, Remark, StateCode, StateDate, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Assessor, Assessorid, AssessDate, DeleteMark, DeleteLister, DeleteDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{plangroupid}, #{plancode}, #{planname}, #{pathid}, #{pathcode}, #{pathname}, #{planstart}, #{planend}, #{receiver}, #{rownum}, #{enabledmark}, #{finishmark}, #{closed}, #{operator}, #{remark}, #{statecode}, #{statedate}, #{lister}, #{listerid}, #{createdate}, #{createby}, #{createbyid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{deletemark}, #{deletelister}, #{deletedate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_PatrolPlan
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="plangroupid != null ">
                PlanGroupid =#{plangroupid},
            </if>
            <if test="plancode != null ">
                PlanCode =#{plancode},
            </if>
            <if test="planname != null ">
                PlanName =#{planname},
            </if>
            <if test="pathid != null ">
                Pathid =#{pathid},
            </if>
            <if test="pathcode != null ">
                PathCode =#{pathcode},
            </if>
            <if test="pathname != null ">
                PathName =#{pathname},
            </if>
            <if test="planstart != null">
                PlanStart =#{planstart},
            </if>
            <if test="planend != null">
                PlanEnd =#{planend},
            </if>
            <if test="receiver != null ">
                Receiver =#{receiver},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="finishmark != null">
                FinishMark =#{finishmark},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_PatrolPlan where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                                                        <!--通过主键审核数据-->
    <update id="approval">
        update Dm_PatrolPlan SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
                                                                        </mapper>

