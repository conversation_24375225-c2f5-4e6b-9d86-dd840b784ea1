<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.IotAttributekvMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.IotAttributekvPojo">
        <include refid="selectIotAttributekvVo"/>
        where Iot_AttributeKv.id = #{key} and Iot_AttributeKv.Tenantid=#{tid}
    </select>
    <sql id="selectIotAttributekvVo">
         select
id, Entityid, AttributeType, AttributeKey, AttributeV, BoolV, StrV, LongV, <PERSON>blV, <PERSON>son<PERSON>, LastUpdateTs, Version, Remark, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Iot_AttributeKv
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.IotAttributekvPojo">
        <include refid="selectIotAttributekvVo"/>
         where 1 = 1 and Iot_AttributeKv.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Iot_AttributeKv.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.entityid != null ">
   and Iot_AttributeKv.Entityid like concat('%', #{SearchPojo.entityid}, '%')
</if>
<if test="SearchPojo.attributev != null ">
   and Iot_AttributeKv.AttributeV like concat('%', #{SearchPojo.attributev}, '%')
</if>
<if test="SearchPojo.strv != null ">
   and Iot_AttributeKv.StrV like concat('%', #{SearchPojo.strv}, '%')
</if>
<if test="SearchPojo.jsonv != null ">
   and Iot_AttributeKv.JsonV like concat('%', #{SearchPojo.jsonv}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Iot_AttributeKv.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Iot_AttributeKv.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Iot_AttributeKv.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Iot_AttributeKv.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Iot_AttributeKv.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Iot_AttributeKv.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Iot_AttributeKv.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Iot_AttributeKv.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Iot_AttributeKv.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Iot_AttributeKv.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Iot_AttributeKv.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Iot_AttributeKv.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Iot_AttributeKv.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Iot_AttributeKv.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Iot_AttributeKv.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Iot_AttributeKv.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.entityid != null ">
   or Iot_AttributeKv.Entityid like concat('%', #{SearchPojo.entityid}, '%')
</if>
<if test="SearchPojo.attributev != null ">
   or Iot_AttributeKv.AttributeV like concat('%', #{SearchPojo.attributev}, '%')
</if>
<if test="SearchPojo.strv != null ">
   or Iot_AttributeKv.StrV like concat('%', #{SearchPojo.strv}, '%')
</if>
<if test="SearchPojo.jsonv != null ">
   or Iot_AttributeKv.JsonV like concat('%', #{SearchPojo.jsonv}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Iot_AttributeKv.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Iot_AttributeKv.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Iot_AttributeKv.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Iot_AttributeKv.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Iot_AttributeKv.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Iot_AttributeKv.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Iot_AttributeKv.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Iot_AttributeKv.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Iot_AttributeKv.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Iot_AttributeKv.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Iot_AttributeKv.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Iot_AttributeKv.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Iot_AttributeKv.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Iot_AttributeKv.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Iot_AttributeKv.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Iot_AttributeKv.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Iot_AttributeKv(id, Entityid, AttributeType, AttributeKey, AttributeV, BoolV, StrV, LongV, DblV, JsonV, LastUpdateTs, Version, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{entityid}, #{attributetype}, #{attributekey}, #{attributev}, #{boolv}, #{strv}, #{longv}, #{dblv}, #{jsonv}, #{lastupdatets}, #{version}, #{remark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Iot_AttributeKv
        <set>
            <if test="entityid != null ">
                Entityid =#{entityid},
            </if>
            <if test="attributetype != null">
                AttributeType =#{attributetype},
            </if>
            <if test="attributekey != null">
                AttributeKey =#{attributekey},
            </if>
            <if test="attributev != null ">
                AttributeV =#{attributev},
            </if>
            <if test="boolv != null">
                BoolV =#{boolv},
            </if>
            <if test="strv != null ">
                StrV =#{strv},
            </if>
            <if test="longv != null">
                LongV =#{longv},
            </if>
            <if test="dblv != null">
                DblV =#{dblv},
            </if>
            <if test="jsonv != null ">
                JsonV =#{jsonv},
            </if>
            <if test="lastupdatets != null">
                LastUpdateTs =#{lastupdatets},
            </if>
            <if test="version != null">
                Version =#{version},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Iot_AttributeKv where id = #{key} and Tenantid=#{tid}
    </delete>

    <insert id="batchLatest" parameterType="java.util.List">
        INSERT INTO Iot_AttributeKv
        (id, Entityid, AttributeType, AttributeKey, AttributeV, BoolV, StrV, LongV, DblV, JsonV, LastUpdateTs,
         Version, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2,
         Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
             #{item.entityid},
             #{item.attributetype},
             #{item.attributekey},
             #{item.attributev},
             #{item.boolv},
             #{item.strv},
             #{item.longv},
             #{item.dblv},
             #{item.jsonv},
             #{item.lastupdatets},
             #{item.version},
             #{item.remark},
             #{item.rownum},
             #{item.createby},
             #{item.createbyid},
             #{item.createdate},
             #{item.lister},
             #{item.listerid},
             #{item.modifydate},
             #{item.custom1},
             #{item.custom2},
             #{item.custom3},
             #{item.custom4},
             #{item.custom5},
             #{item.custom6},
             #{item.custom7},
             #{item.custom8},
             #{item.custom9},
             #{item.custom10},
             #{item.tenantid},
             #{item.tenantname},
             #{item.revision})
        </foreach>
        ON DUPLICATE KEY UPDATE LastUpdateTs = VALUES(LastUpdateTs),
                                BoolV        = VALUES(BoolV),
                                StrV         = VALUES(StrV),
                                LongV        = VALUES(LongV),
                                DblV         = VALUES(DblV),
                                JsonV        = VALUES(JsonV),
                                CreateDate   = VALUES(CreateDate)
    </insert>

</mapper>

