<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.IotAssetMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.IotAssetPojo">
        <include refid="selectIotAssetVo"/>
        where Iot_Asset.id = #{key} and Iot_Asset.Tenantid=#{tid}
    </select>
    <sql id="selectIotAssetVo">
        select Iot_Asset.id,
               Iot_Asset.AdditionalInfo,
               Iot_Asset.Customerid,
               Iot_Asset.AssetProfileid,
               Iot_Asset.AssetName,
               Iot_Asset.AssetLabel,
               Iot_Asset.AssetType,
               Iot_Asset.Externalid,
               Iot_Asset.Version,
               Iot_Asset.Remark,
               Iot_Asset.RowNum,
               Iot_Asset.CreateBy,
               Iot_Asset.CreateByid,
               Iot_Asset.CreateDate,
               Iot_Asset.Lister,
               Iot_Asset.Listerid,
               Iot_Asset.ModifyDate,
               Iot_Asset.Custom1,
               Iot_Asset.Custom2,
               Iot_Asset.Custom3,
               Iot_Asset.Custom4,
               Iot_Asset.Custom5,
               Iot_Asset.Custom6,
               Iot_Asset.Custom7,
               Iot_Asset.Custom8,
               Iot_Asset.Custom9,
               Iot_Asset.Custom10,
               Iot_Asset.Tenantid,
               Iot_Asset.TenantName,
               Iot_Asset.Revision,
               Iot_AssetProfile.ProfName
        from Iot_Asset
        Left join Iot_AssetProfile on Iot_Asset.AssetProfileid = Iot_AssetProfile.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.IotAssetPojo">
        <include refid="selectIotAssetVo"/>
         where 1 = 1 and Iot_Asset.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Iot_Asset.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.additionalinfo != null ">
   and Iot_Asset.AdditionalInfo like concat('%', #{SearchPojo.additionalinfo}, '%')
</if>
<if test="SearchPojo.customerid != null ">
   and Iot_Asset.Customerid like concat('%', #{SearchPojo.customerid}, '%')
</if>
<if test="SearchPojo.assetprofileid != null ">
   and Iot_Asset.AssetProfileid like concat('%', #{SearchPojo.assetprofileid}, '%')
</if>
<if test="SearchPojo.assetname != null ">
   and Iot_Asset.AssetName like concat('%', #{SearchPojo.assetname}, '%')
</if>
<if test="SearchPojo.assetlabel != null ">
   and Iot_Asset.AssetLabel like concat('%', #{SearchPojo.assetlabel}, '%')
</if>
<if test="SearchPojo.assettype != null ">
   and Iot_Asset.AssetType like concat('%', #{SearchPojo.assettype}, '%')
</if>
<if test="SearchPojo.externalid != null ">
   and Iot_Asset.Externalid like concat('%', #{SearchPojo.externalid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Iot_Asset.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Iot_Asset.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Iot_Asset.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Iot_Asset.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Iot_Asset.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Iot_Asset.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Iot_Asset.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Iot_Asset.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Iot_Asset.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Iot_Asset.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Iot_Asset.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Iot_Asset.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Iot_Asset.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Iot_Asset.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Iot_Asset.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Iot_Asset.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.additionalinfo != null ">
   or Iot_Asset.AdditionalInfo like concat('%', #{SearchPojo.additionalinfo}, '%')
</if>
<if test="SearchPojo.customerid != null ">
   or Iot_Asset.Customerid like concat('%', #{SearchPojo.customerid}, '%')
</if>
<if test="SearchPojo.assetprofileid != null ">
   or Iot_Asset.AssetProfileid like concat('%', #{SearchPojo.assetprofileid}, '%')
</if>
<if test="SearchPojo.assetname != null ">
   or Iot_Asset.AssetName like concat('%', #{SearchPojo.assetname}, '%')
</if>
<if test="SearchPojo.assetlabel != null ">
   or Iot_Asset.AssetLabel like concat('%', #{SearchPojo.assetlabel}, '%')
</if>
<if test="SearchPojo.assettype != null ">
   or Iot_Asset.AssetType like concat('%', #{SearchPojo.assettype}, '%')
</if>
<if test="SearchPojo.externalid != null ">
   or Iot_Asset.Externalid like concat('%', #{SearchPojo.externalid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Iot_Asset.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Iot_Asset.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Iot_Asset.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Iot_Asset.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Iot_Asset.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Iot_Asset.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Iot_Asset.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Iot_Asset.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Iot_Asset.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Iot_Asset.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Iot_Asset.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Iot_Asset.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Iot_Asset.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Iot_Asset.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Iot_Asset.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Iot_Asset.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Iot_Asset(id, AdditionalInfo, Customerid, AssetProfileid, AssetName, AssetLabel, AssetType, Externalid, Version, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{additionalinfo}, #{customerid}, #{assetprofileid}, #{assetname}, #{assetlabel}, #{assettype}, #{externalid}, #{version}, #{remark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Iot_Asset
        <set>
            <if test="additionalinfo != null ">
                AdditionalInfo =#{additionalinfo},
            </if>
            <if test="customerid != null ">
                Customerid =#{customerid},
            </if>
            <if test="assetprofileid != null ">
                AssetProfileid =#{assetprofileid},
            </if>
            <if test="assetname != null ">
                AssetName =#{assetname},
            </if>
            <if test="assetlabel != null ">
                AssetLabel =#{assetlabel},
            </if>
            <if test="assettype != null ">
                AssetType =#{assettype},
            </if>
            <if test="externalid != null ">
                Externalid =#{externalid},
            </if>
            <if test="version != null">
                Version =#{version},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Iot_Asset where id = #{key} and Tenantid=#{tid}
    </delete>
</mapper>

