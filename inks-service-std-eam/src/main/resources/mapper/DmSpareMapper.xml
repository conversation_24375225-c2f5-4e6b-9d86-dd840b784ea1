<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmSpareMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmSparePojo">
        select
          id, SpareCode, SpareName, SpareSpec, SpareUnit, IvQuantity, SafetyStock, Suppid, BuyPrice, Deviceid, UsefulLife, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateBy, CreateByid, CreateDate, ModifyDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Tenantid, Revision        from Dm_Spare
        where Dm_Spare.id = #{key} and Dm_Spare.Tenantid=#{tid}
    </select>
    <sql id="selectDmSpareVo">
         select
          id, SpareCode, SpareName, SpareSpec, SpareUnit, IvQuantity, SafetyStock, Suppid, BuyPrice, Deviceid, UsefulLife, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateBy, CreateByid, CreateDate, ModifyDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Tenantid, Revision        from Dm_Spare
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmSparePojo">
        <include refid="selectDmSpareVo"/>
         where 1 = 1 and Dm_Spare.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_Spare.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.sparecode != null ">
   and Dm_Spare.SpareCode like concat('%', #{SearchPojo.sparecode}, '%')
</if>
<if test="SearchPojo.sparename != null ">
   and Dm_Spare.SpareName like concat('%', #{SearchPojo.sparename}, '%')
</if>
<if test="SearchPojo.sparespec != null ">
   and Dm_Spare.SpareSpec like concat('%', #{SearchPojo.sparespec}, '%')
</if>
<if test="SearchPojo.spareunit != null ">
   and Dm_Spare.SpareUnit like concat('%', #{SearchPojo.spareunit}, '%')
</if>
<if test="SearchPojo.suppid != null ">
   and Dm_Spare.Suppid like concat('%', #{SearchPojo.suppid}, '%')
</if>
<if test="SearchPojo.deviceid != null ">
   and Dm_Spare.Deviceid like concat('%', #{SearchPojo.deviceid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Dm_Spare.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_Spare.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_Spare.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_Spare.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_Spare.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_Spare.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_Spare.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_Spare.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_Spare.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_Spare.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_Spare.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_Spare.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_Spare.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Dm_Spare.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.sparecode != null ">
   or Dm_Spare.SpareCode like concat('%', #{SearchPojo.sparecode}, '%')
</if>
<if test="SearchPojo.sparename != null ">
   or Dm_Spare.SpareName like concat('%', #{SearchPojo.sparename}, '%')
</if>
<if test="SearchPojo.sparespec != null ">
   or Dm_Spare.SpareSpec like concat('%', #{SearchPojo.sparespec}, '%')
</if>
<if test="SearchPojo.spareunit != null ">
   or Dm_Spare.SpareUnit like concat('%', #{SearchPojo.spareunit}, '%')
</if>
<if test="SearchPojo.suppid != null ">
   or Dm_Spare.Suppid like concat('%', #{SearchPojo.suppid}, '%')
</if>
<if test="SearchPojo.deviceid != null ">
   or Dm_Spare.Deviceid like concat('%', #{SearchPojo.deviceid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Dm_Spare.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_Spare.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_Spare.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_Spare.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_Spare.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_Spare.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_Spare.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_Spare.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_Spare.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_Spare.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_Spare.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_Spare.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_Spare.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Dm_Spare.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_Spare(id, SpareCode, SpareName, SpareSpec, SpareUnit, IvQuantity, SafetyStock, Suppid, BuyPrice, Deviceid, UsefulLife, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateBy, CreateByid, CreateDate, ModifyDate, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Tenantid, Revision)
        values (#{id}, #{sparecode}, #{sparename}, #{sparespec}, #{spareunit}, #{ivquantity}, #{safetystock}, #{suppid}, #{buyprice}, #{deviceid}, #{usefullife}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{lister}, #{listerid}, #{createby}, #{createbyid}, #{createdate}, #{modifydate}, #{enabledmark}, #{deletemark}, #{deletelister}, #{deletedate}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_Spare
        <set>
            <if test="sparecode != null ">
                SpareCode =#{sparecode},
            </if>
            <if test="sparename != null ">
                SpareName =#{sparename},
            </if>
            <if test="sparespec != null ">
                SpareSpec =#{sparespec},
            </if>
            <if test="spareunit != null ">
                SpareUnit =#{spareunit},
            </if>
            <if test="ivquantity != null">
                IvQuantity =#{ivquantity},
            </if>
            <if test="safetystock != null">
                SafetyStock =#{safetystock},
            </if>
            <if test="suppid != null ">
                Suppid =#{suppid},
            </if>
            <if test="buyprice != null">
                BuyPrice =#{buyprice},
            </if>
            <if test="deviceid != null ">
                Deviceid =#{deviceid},
            </if>
            <if test="usefullife != null">
                UsefulLife =#{usefullife},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_Spare where id = #{key} and Tenantid=#{tid}
    </delete>

    <update id="updateIvQuantity">
        update Dm_Spare
        set IvQuantity = #{quantity}
        where id = #{spareid} and Tenantid =#{tid}
    </update>
</mapper>

