<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmRepairserviceMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmRepairservicePojo">
        select id,
               RefNo,
               BillDate,
               BillType,
               BillTitle,
               Deviceid,
               Operator,
               RepairReason,
               BillResult,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Lister,
               Listerid,
               CreateBy,
               CreateDate,
               CreateByid,
               ModifyDate,
               Assessorid,
               Assessor,
               AssessDate,
               EnabledMark,
               DeleteMark,
               DeleteLister,
               DeleteDate,
               Tenantid,
               Revision
        from Dm_RepairService
        where Dm_RepairService.id = #{key}
          and Dm_RepairService.Tenantid = #{tid}
    </select>
    <sql id="selectDmRepairserviceVo">
        select Dm_RepairService.id,
               Dm_RepairService.RefNo,
               Dm_RepairService.BillDate,
               Dm_RepairService.BillType,
               Dm_RepairService.BillTitle,
               Dm_RepairService.Deviceid,
               Dm_RepairService.Operator,
               Dm_RepairService.RepairReason,
               Dm_RepairService.BillResult,
               Dm_RepairService.Remark,
               Dm_RepairService.Custom1,
               Dm_RepairService.Custom2,
               Dm_RepairService.Custom3,
               Dm_RepairService.Custom4,
               Dm_RepairService.Custom5,
               Dm_RepairService.Custom6,
               Dm_RepairService.Custom7,
               Dm_RepairService.Custom8,
               Dm_RepairService.Lister,
               Dm_RepairService.Listerid,
               Dm_RepairService.CreateBy,
               Dm_RepairService.CreateDate,
               Dm_RepairService.CreateByid,
               Dm_RepairService.ModifyDate,
               Dm_RepairService.Assessorid,
               Dm_RepairService.Assessor,
               Dm_RepairService.AssessDate,
               Dm_RepairService.EnabledMark,
               Dm_RepairService.DeleteMark,
               Dm_RepairService.DeleteLister,
               Dm_RepairService.DeleteDate,
               Dm_RepairService.Tenantid,
               Dm_RepairService.Revision,
               Dm_Device.DevName,
               Dm_Device.DevCode,
               Dm_Device.Devspec,
               Dm_Device.Devunit
        from Dm_RepairService
                left join Dm_Device on Dm_RepairService.Deviceid = Dm_Device.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmRepairservicePojo">
        <include refid="selectDmRepairserviceVo"/>
        where 1 = 1 and Dm_RepairService.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_RepairService.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Dm_RepairService.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Dm_RepairService.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Dm_RepairService.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.deviceid != null ">
            and Dm_RepairService.Deviceid like concat('%', #{SearchPojo.deviceid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Dm_RepairService.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.repairreason != null ">
            and Dm_RepairService.RepairReason like concat('%', #{SearchPojo.repairreason}, '%')
        </if>
        <if test="SearchPojo.billresult != null ">
            and Dm_RepairService.BillResult like concat('%', #{SearchPojo.billresult}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and Dm_RepairService.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Dm_RepairService.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Dm_RepairService.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Dm_RepairService.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Dm_RepairService.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Dm_RepairService.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Dm_RepairService.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Dm_RepairService.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Dm_RepairService.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Dm_RepairService.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Dm_RepairService.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Dm_RepairService.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Dm_RepairService.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Dm_RepairService.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Dm_RepairService.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.deletelister != null ">
            and Dm_RepairService.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Dm_RepairService.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Dm_RepairService.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Dm_RepairService.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.deviceid != null ">
                or Dm_RepairService.Deviceid like concat('%', #{SearchPojo.deviceid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Dm_RepairService.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.repairreason != null ">
                or Dm_RepairService.RepairReason like concat('%', #{SearchPojo.repairreason}, '%')
            </if>
            <if test="SearchPojo.billresult != null ">
                or Dm_RepairService.BillResult like concat('%', #{SearchPojo.billresult}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or Dm_RepairService.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Dm_RepairService.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Dm_RepairService.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Dm_RepairService.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Dm_RepairService.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Dm_RepairService.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Dm_RepairService.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Dm_RepairService.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Dm_RepairService.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Dm_RepairService.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Dm_RepairService.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Dm_RepairService.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Dm_RepairService.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Dm_RepairService.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Dm_RepairService.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.deletelister != null ">
                or Dm_RepairService.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Dm_RepairService(id, RefNo, BillDate, BillType, BillTitle, Deviceid, Operator, RepairReason,
                                     BillResult, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7,
                                     Custom8, Lister, Listerid, CreateBy, CreateDate, CreateByid, ModifyDate,
                                     Assessorid, Assessor, AssessDate, EnabledMark, DeleteMark, DeleteLister,
                                     DeleteDate, Tenantid, Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{deviceid}, #{operator}, #{repairreason},
                #{billresult}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{lister}, #{listerid}, #{createby}, #{createdate}, #{createbyid},
                #{modifydate}, #{assessorid}, #{assessor}, #{assessdate}, #{enabledmark}, #{deletemark},
                #{deletelister}, #{deletedate}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_RepairService
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="deviceid != null ">
                Deviceid =#{deviceid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="repairreason != null ">
                RepairReason =#{repairreason},
            </if>
            <if test="billresult != null ">
                BillResult =#{billresult},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Dm_RepairService
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Dm_RepairService
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <update id="updateDeviceState">
           update Dm_Device
            SET UseState = #{usestate}
            where id = #{deviceid}
            and Tenantid = #{tid}
    </update>

    <select id="checkDevice" resultType="int">
        select count(*) from Dm_RepairService
        where Deviceid = #{deviceid}
        and Tenantid = #{tenantid}
        and BillResult <![CDATA[<>]]> '完成'
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
</mapper>

