<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.IotRelationMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.IotRelationPojo">
        <include refid="selectIotRelationVo"/>
        where Iot_Relation.id = #{key} and Iot_Relation.Tenantid=#{tid}
    </select>
    <sql id="selectIotRelationVo">
        select id,
               Fromid,
               FromType,
               Toid,
               ToType,
               RelationTypeGroup,
               RelationType,
               AdditionalInfo,
               Version,
               Remark,
               RowNum,
               CreateBy,
               <PERSON>reate<PERSON>yid,
               <PERSON>reate<PERSON>ate,
               <PERSON>er,
               <PERSON>erid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Iot_Relation
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.IotRelationPojo">
        <include refid="selectIotRelationVo"/>
         where 1 = 1 and Iot_Relation.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Iot_Relation.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.fromid != null ">
   and Iot_Relation.Fromid like concat('%', #{SearchPojo.fromid}, '%')
</if>
<if test="SearchPojo.fromtype != null ">
   and Iot_Relation.FromType like concat('%', #{SearchPojo.fromtype}, '%')
</if>
<if test="SearchPojo.toid != null ">
   and Iot_Relation.Toid like concat('%', #{SearchPojo.toid}, '%')
</if>
<if test="SearchPojo.totype != null ">
   and Iot_Relation.ToType like concat('%', #{SearchPojo.totype}, '%')
</if>
<if test="SearchPojo.relationtypegroup != null ">
   and Iot_Relation.RelationTypeGroup like concat('%', #{SearchPojo.relationtypegroup}, '%')
</if>
<if test="SearchPojo.relationtype != null ">
   and Iot_Relation.RelationType like concat('%', #{SearchPojo.relationtype}, '%')
</if>
<if test="SearchPojo.additionalinfo != null ">
   and Iot_Relation.AdditionalInfo like concat('%', #{SearchPojo.additionalinfo}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Iot_Relation.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Iot_Relation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Iot_Relation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Iot_Relation.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Iot_Relation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Iot_Relation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Iot_Relation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Iot_Relation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Iot_Relation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Iot_Relation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Iot_Relation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Iot_Relation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Iot_Relation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Iot_Relation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Iot_Relation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Iot_Relation.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.fromid != null ">
   or Iot_Relation.Fromid like concat('%', #{SearchPojo.fromid}, '%')
</if>
<if test="SearchPojo.fromtype != null ">
   or Iot_Relation.FromType like concat('%', #{SearchPojo.fromtype}, '%')
</if>
<if test="SearchPojo.toid != null ">
   or Iot_Relation.Toid like concat('%', #{SearchPojo.toid}, '%')
</if>
<if test="SearchPojo.totype != null ">
   or Iot_Relation.ToType like concat('%', #{SearchPojo.totype}, '%')
</if>
<if test="SearchPojo.relationtypegroup != null ">
   or Iot_Relation.RelationTypeGroup like concat('%', #{SearchPojo.relationtypegroup}, '%')
</if>
<if test="SearchPojo.relationtype != null ">
   or Iot_Relation.RelationType like concat('%', #{SearchPojo.relationtype}, '%')
</if>
<if test="SearchPojo.additionalinfo != null ">
   or Iot_Relation.AdditionalInfo like concat('%', #{SearchPojo.additionalinfo}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Iot_Relation.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Iot_Relation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Iot_Relation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Iot_Relation.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Iot_Relation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Iot_Relation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Iot_Relation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Iot_Relation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Iot_Relation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Iot_Relation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Iot_Relation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Iot_Relation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Iot_Relation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Iot_Relation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Iot_Relation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Iot_Relation.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Iot_Relation(id, Fromid, FromType, Toid, ToType, RelationTypeGroup, RelationType, AdditionalInfo, Version, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{fromid}, #{fromtype}, #{toid}, #{totype}, #{relationtypegroup}, #{relationtype}, #{additionalinfo}, #{version}, #{remark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Iot_Relation
        <set>
            <if test="fromid != null ">
                Fromid =#{fromid},
            </if>
            <if test="fromtype != null ">
                FromType =#{fromtype},
            </if>
            <if test="toid != null ">
                Toid =#{toid},
            </if>
            <if test="totype != null ">
                ToType =#{totype},
            </if>
            <if test="relationtypegroup != null ">
                RelationTypeGroup =#{relationtypegroup},
            </if>
            <if test="relationtype != null ">
                RelationType =#{relationtype},
            </if>
            <if test="additionalinfo != null ">
                AdditionalInfo =#{additionalinfo},
            </if>
            <if test="version != null">
                Version =#{version},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Iot_Relation where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getList" resultType="inks.service.std.eam.domain.pojo.IotRelationPojo">
        <include refid="selectIotRelationVo"/>
        where 1 = 1 and Iot_Relation.Tenantid =#{tenantid}
        <if test="fromid != null and fromid != ''">
            and Iot_Relation.Fromid = #{fromid}
        </if>
        <if test="fromtype != null and fromtype != ''">
            and Iot_Relation.FromType = #{fromtype}
        </if>
        <if test="toid != null and toid != ''">
            and Iot_Relation.Toid = #{toid}
        </if>
        <if test="totype != null and totype != ''">
            and Iot_Relation.ToType = #{totype}
        </if>
    </select>

    <select id="getDeviceNamesByIds" resultType="map">
        select id, DevName as devName from Iot_Device
        where Tenantid = #{tenantid}
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getAssetNamesByIds" resultType="map">
        select id, AssetName as assetName from Iot_Asset
        where Tenantid = #{tenantid}
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>


</mapper>

