<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmSpareaccessMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmSpareaccessPojo">
        select Dm_SpareAccess.id,
               Dm_SpareAccess.RefNo,
               Dm_SpareAccess.BillDate,
               Dm_SpareAccess.BillType,
               Dm_SpareAccess.BillTitle,
               Dm_SpareAccess.Direction,
               Dm_SpareAccess.Groupid,
               Dm_SpareAccess.Storeid,
               Dm_SpareAccess.Operator,
               Dm_SpareAccess.Summary,
               Dm_SpareAccess.ReturnUid,
               Dm_SpareAccess.OrgUid,
               Dm_SpareAccess.PlusInfo,
               Dm_SpareAccess.Custom1,
               Dm_SpareAccess.Custom2,
               Dm_SpareAccess.Custom3,
               Dm_SpareAccess.Custom4,
               Dm_SpareAccess.Custom5,
               Dm_SpareAccess.Custom6,
               Dm_SpareAccess.Custom7,
               Dm_SpareAccess.Custom8,
               Dm_SpareAccess.Lister,
               Dm_SpareAccess.Listerid,
               Dm_SpareAccess.CreateBy,
               Dm_SpareAccess.CreateByid,
               Dm_SpareAccess.CreateDate,
               Dm_SpareAccess.ModifyDate,
               Dm_SpareAccess.Assessorid,
               Dm_SpareAccess.Assessor,
               Dm_SpareAccess.AssessDate,
               Dm_SpareAccess.Tenantid,
               Dm_SpareAccess.Revision,
               App_Workgroup.GroupName,
               Mat_Storage.StoreName
        from Dm_SpareAccess
                 join App_Workgroup on Dm_SpareAccess.Groupid = App_Workgroup.id
                 join Mat_Storage on Dm_SpareAccess.Storeid = Mat_Storage.id
        where Dm_SpareAccess.id = #{key}
          and Dm_SpareAccess.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Dm_SpareAccess.id,
               Dm_SpareAccess.RefNo,
               Dm_SpareAccess.BillDate,
               Dm_SpareAccess.BillType,
               Dm_SpareAccess.BillTitle,
               Dm_SpareAccess.Direction,
               Dm_SpareAccess.Groupid,
               Dm_SpareAccess.Storeid,
               Dm_SpareAccess.Operator,
               Dm_SpareAccess.Summary,
               Dm_SpareAccess.ReturnUid,
               Dm_SpareAccess.OrgUid,
               Dm_SpareAccess.PlusInfo,
               Dm_SpareAccess.Custom1,
               Dm_SpareAccess.Custom2,
               Dm_SpareAccess.Custom3,
               Dm_SpareAccess.Custom4,
               Dm_SpareAccess.Custom5,
               Dm_SpareAccess.Custom6,
               Dm_SpareAccess.Custom7,
               Dm_SpareAccess.Custom8,
               Dm_SpareAccess.Lister,
               Dm_SpareAccess.Listerid,
               Dm_SpareAccess.CreateBy,
               Dm_SpareAccess.CreateByid,
               Dm_SpareAccess.CreateDate,
               Dm_SpareAccess.ModifyDate,
               Dm_SpareAccess.Assessorid,
               Dm_SpareAccess.Assessor,
               Dm_SpareAccess.AssessDate,
               Dm_SpareAccess.Tenantid,
               Dm_SpareAccess.Revision,
               Mat_Storage.StoreName
        from Dm_SpareAccess Left join Mat_Storage on Dm_SpareAccess.Storeid = Mat_Storage.id
    </sql>
    <sql id="selectdetailVo">
        select Dm_SpareAccessItem.id,
               Dm_SpareAccessItem.Pid,
               Dm_SpareAccessItem.Spareid,
               Dm_SpareAccessItem.Quantity,
               Dm_SpareAccessItem.Price,
               Dm_SpareAccessItem.Amount,
               Dm_SpareAccessItem.Remark,
               Dm_SpareAccessItem.RowNum,
               Dm_SpareAccessItem.Location,
               Dm_SpareAccessItem.BatchNo,
               Dm_SpareAccessItem.ExpiryDate,
               Dm_SpareAccessItem.TaxPrice,
               Dm_SpareAccessItem.TaxAmount,
               Dm_SpareAccessItem.ItemTaxrate,
               Dm_SpareAccessItem.CiteUid,
               Dm_SpareAccessItem.CiteItemid,
               Dm_SpareAccessItem.Custom1,
               Dm_SpareAccessItem.Custom2,
               Dm_SpareAccessItem.Custom3,
               Dm_SpareAccessItem.Custom4,
               Dm_SpareAccessItem.Custom5,
               Dm_SpareAccessItem.Custom6,
               Dm_SpareAccessItem.Custom7,
               Dm_SpareAccessItem.Custom8,
               Dm_SpareAccessItem.Tenantid,
               Dm_SpareAccessItem.Revision,
               Dm_Spare.SpareUnit,
               Dm_Spare.SpareName,
               Dm_Spare.SpareCode,
               Dm_Spare.SpareSpec,
               Dm_SpareAccess.RefNo,
               Dm_SpareAccess.BillDate,
               Dm_SpareAccess.BillType,
               Dm_SpareAccess.BillTitle
        from Dm_SpareAccessItem
                 Left join Dm_Spare on Dm_SpareAccessItem.Spareid = Dm_Spare.id
                 left join Dm_SpareAccess on Dm_SpareAccessItem.Pid = Dm_SpareAccess.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmSpareaccessitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Dm_SpareAccess.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_SpareAccess.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Dm_SpareAccess.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Dm_SpareAccess.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Dm_SpareAccess.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.direction != null ">
            and Dm_SpareAccess.direction like concat('%', #{SearchPojo.direction}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Dm_SpareAccess.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.storeid != null ">
            and Dm_SpareAccess.storeid like concat('%', #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Dm_SpareAccess.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Dm_SpareAccess.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.returnuid != null ">
            and Dm_SpareAccess.returnuid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null ">
            and Dm_SpareAccess.orguid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.plusinfo != null ">
            and Dm_SpareAccess.plusinfo like concat('%', #{SearchPojo.plusinfo}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Dm_SpareAccess.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Dm_SpareAccess.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Dm_SpareAccess.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Dm_SpareAccess.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Dm_SpareAccess.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Dm_SpareAccess.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Dm_SpareAccess.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Dm_SpareAccess.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Dm_SpareAccess.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Dm_SpareAccess.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Dm_SpareAccess.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Dm_SpareAccess.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Dm_SpareAccess.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Dm_SpareAccess.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Dm_SpareAccess.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Dm_SpareAccess.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Dm_SpareAccess.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.direction != null ">
                or Dm_SpareAccess.Direction like concat('%', #{SearchPojo.direction}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Dm_SpareAccess.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.storeid != null ">
                or Dm_SpareAccess.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Dm_SpareAccess.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Dm_SpareAccess.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.returnuid != null ">
                or Dm_SpareAccess.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
            </if>
            <if test="SearchPojo.orguid != null ">
                or Dm_SpareAccess.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
            </if>
            <if test="SearchPojo.plusinfo != null ">
                or Dm_SpareAccess.PlusInfo like concat('%', #{SearchPojo.plusinfo}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Dm_SpareAccess.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Dm_SpareAccess.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Dm_SpareAccess.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Dm_SpareAccess.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Dm_SpareAccess.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Dm_SpareAccess.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Dm_SpareAccess.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Dm_SpareAccess.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Dm_SpareAccess.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Dm_SpareAccess.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Dm_SpareAccess.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Dm_SpareAccess.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Dm_SpareAccess.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Dm_SpareAccess.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmSpareaccessPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Dm_SpareAccess.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_SpareAccess.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Dm_SpareAccess.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Dm_SpareAccess.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Dm_SpareAccess.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.direction != null ">
            and Dm_SpareAccess.Direction like concat('%', #{SearchPojo.direction}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Dm_SpareAccess.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.storeid != null ">
            and Dm_SpareAccess.Storeid like concat('%', #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Dm_SpareAccess.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Dm_SpareAccess.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.returnuid != null ">
            and Dm_SpareAccess.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null ">
            and Dm_SpareAccess.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.plusinfo != null ">
            and Dm_SpareAccess.PlusInfo like concat('%', #{SearchPojo.plusinfo}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Dm_SpareAccess.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Dm_SpareAccess.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Dm_SpareAccess.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Dm_SpareAccess.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Dm_SpareAccess.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Dm_SpareAccess.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Dm_SpareAccess.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Dm_SpareAccess.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Dm_SpareAccess.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Dm_SpareAccess.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Dm_SpareAccess.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Dm_SpareAccess.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Dm_SpareAccess.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Dm_SpareAccess.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Dm_SpareAccess.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Dm_SpareAccess.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Dm_SpareAccess.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.direction != null ">
                or Dm_SpareAccess.Direction like concat('%', #{SearchPojo.direction}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Dm_SpareAccess.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.storeid != null ">
                or Dm_SpareAccess.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Dm_SpareAccess.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Dm_SpareAccess.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.returnuid != null ">
                or Dm_SpareAccess.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
            </if>
            <if test="SearchPojo.orguid != null ">
                or Dm_SpareAccess.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
            </if>
            <if test="SearchPojo.plusinfo != null ">
                or Dm_SpareAccess.PlusInfo like concat('%', #{SearchPojo.plusinfo}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Dm_SpareAccess.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Dm_SpareAccess.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Dm_SpareAccess.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Dm_SpareAccess.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Dm_SpareAccess.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Dm_SpareAccess.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Dm_SpareAccess.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Dm_SpareAccess.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Dm_SpareAccess.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Dm_SpareAccess.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Dm_SpareAccess.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Dm_SpareAccess.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Dm_SpareAccess.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Dm_SpareAccess.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Dm_SpareAccess(id, RefNo, BillDate, BillType, BillTitle, Direction, Groupid, Storeid, Operator,
                                   Summary, ReturnUid, OrgUid, PlusInfo, Custom1, Custom2, Custom3, Custom4, Custom5,
                                   Custom6, Custom7, Custom8, Lister, Listerid, CreateBy, CreateByid, CreateDate,
                                   ModifyDate, Assessorid, Assessor, AssessDate, Tenantid, Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{direction}, #{groupid}, #{storeid},
                #{operator}, #{summary}, #{returnuid}, #{orguid}, #{plusinfo}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{lister}, #{listerid}, #{createby},
                #{createbyid}, #{createdate}, #{modifydate}, #{assessorid}, #{assessor}, #{assessdate}, #{tenantid},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_SpareAccess
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="direction != null ">
                Direction =#{direction},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="storeid != null ">
                Storeid =#{storeid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="returnuid != null ">
                ReturnUid =#{returnuid},
            </if>
            <if test="orguid != null ">
                OrgUid =#{orguid},
            </if>
            <if test="plusinfo != null ">
                PlusInfo =#{plusinfo},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Dm_SpareAccess
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Dm_SpareAccess
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.std.eam.domain.pojo.DmSpareaccessPojo">
        select
        id
        from Dm_SpareAccessItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

</mapper>

