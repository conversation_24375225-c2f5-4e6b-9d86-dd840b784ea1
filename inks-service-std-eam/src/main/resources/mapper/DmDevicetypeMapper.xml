<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmDevicetypeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmDevicetypePojo">
        select
          id, TypeCode, TypeName, Parentid, Ancestors, EnabledMark, RowNum, Remark, Prefix, Suffix, SnCode, AllowItem, StateCode, GroupSvg, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Tenantid, Revision, ChildCount        from Dm_DeviceType
        where Dm_DeviceType.id = #{key} and Dm_DeviceType.Tenantid=#{tid}
    </select>
    <sql id="selectDmDevicetypeVo">
         select
          id, TypeCode, TypeName, Parentid, Ancestors, EnabledMark, RowNum, Remark, Prefix, Suffix, SnCode, AllowItem, StateCode, GroupSvg, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Tenantid, Revision, ChildCount        from Dm_DeviceType
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmDevicetypePojo">
        <include refid="selectDmDevicetypeVo"/>
         where 1 = 1 and Dm_DeviceType.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_DeviceType.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.typecode != null ">
   and Dm_DeviceType.TypeCode like concat('%', #{SearchPojo.typecode}, '%')
</if>
<if test="SearchPojo.typename != null ">
   and Dm_DeviceType.TypeName like concat('%', #{SearchPojo.typename}, '%')
</if>
<if test="SearchPojo.parentid != null ">
   and Dm_DeviceType.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.ancestors != null ">
   and Dm_DeviceType.Ancestors like concat('%', #{SearchPojo.ancestors}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Dm_DeviceType.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.prefix != null ">
   and Dm_DeviceType.Prefix like concat('%', #{SearchPojo.prefix}, '%')
</if>
<if test="SearchPojo.suffix != null ">
   and Dm_DeviceType.Suffix like concat('%', #{SearchPojo.suffix}, '%')
</if>
<if test="SearchPojo.sncode != null ">
   and Dm_DeviceType.SnCode like concat('%', #{SearchPojo.sncode}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Dm_DeviceType.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.groupsvg != null ">
   and Dm_DeviceType.GroupSvg like concat('%', #{SearchPojo.groupsvg}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_DeviceType.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_DeviceType.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_DeviceType.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_DeviceType.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_DeviceType.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_DeviceType.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_DeviceType.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_DeviceType.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_DeviceType.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_DeviceType.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_DeviceType.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_DeviceType.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.typecode != null ">
   or Dm_DeviceType.TypeCode like concat('%', #{SearchPojo.typecode}, '%')
</if>
<if test="SearchPojo.typename != null ">
   or Dm_DeviceType.TypeName like concat('%', #{SearchPojo.typename}, '%')
</if>
<if test="SearchPojo.parentid != null ">
   or Dm_DeviceType.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.ancestors != null ">
   or Dm_DeviceType.Ancestors like concat('%', #{SearchPojo.ancestors}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Dm_DeviceType.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.prefix != null ">
   or Dm_DeviceType.Prefix like concat('%', #{SearchPojo.prefix}, '%')
</if>
<if test="SearchPojo.suffix != null ">
   or Dm_DeviceType.Suffix like concat('%', #{SearchPojo.suffix}, '%')
</if>
<if test="SearchPojo.sncode != null ">
   or Dm_DeviceType.SnCode like concat('%', #{SearchPojo.sncode}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Dm_DeviceType.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.groupsvg != null ">
   or Dm_DeviceType.GroupSvg like concat('%', #{SearchPojo.groupsvg}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_DeviceType.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_DeviceType.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_DeviceType.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_DeviceType.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_DeviceType.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_DeviceType.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_DeviceType.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_DeviceType.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_DeviceType.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_DeviceType.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_DeviceType.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_DeviceType.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_DeviceType(id, TypeCode, TypeName, Parentid, Ancestors, EnabledMark, RowNum, Remark, Prefix, Suffix, SnCode, AllowItem, StateCode, GroupSvg, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Tenantid, Revision, ChildCount)
        values (#{id}, #{typecode}, #{typename}, #{parentid}, #{ancestors}, #{enabledmark}, #{rownum}, #{remark}, #{prefix}, #{suffix}, #{sncode}, #{allowitem}, #{statecode}, #{groupsvg}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{lister}, #{listerid}, #{createdate}, #{createby}, #{createbyid}, #{modifydate}, #{tenantid}, #{revision}, #{childcount})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_DeviceType
        <set>
            <if test="typecode != null ">
                TypeCode =#{typecode},
            </if>
            <if test="typename != null ">
                TypeName =#{typename},
            </if>
            <if test="parentid != null ">
                Parentid =#{parentid},
            </if>
            <if test="ancestors != null ">
                Ancestors =#{ancestors},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="prefix != null ">
                Prefix =#{prefix},
            </if>
            <if test="suffix != null ">
                Suffix =#{suffix},
            </if>
            <if test="sncode != null ">
                SnCode =#{sncode},
            </if>
            <if test="allowitem != null">
                AllowItem =#{allowitem},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="groupsvg != null ">
                GroupSvg =#{groupsvg},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
                Revision=Revision+1
            <if test="childcount != null">
                ChildCount =#{childcount},
            </if>
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_DeviceType where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                                                            </mapper>

