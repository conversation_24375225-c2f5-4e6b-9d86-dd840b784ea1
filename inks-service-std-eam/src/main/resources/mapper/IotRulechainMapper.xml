<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.IotRulechainMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.IotRulechainPojo">
        <include refid="selectIotRulechainVo"/>
        where Iot_RuleChain.id = #{key} and Iot_RuleChain.Tenantid=#{tid}
    </select>
    <sql id="selectIotRulechainVo">
         select
id, AdditionalInfo, Configuration, RuleChainName, RuleChainType, FirstRuleNodeid, Root, DebugMode, Externalid, Version, Remark, RowNum, CreateBy, Create<PERSON>yi<PERSON>, <PERSON>reate<PERSON>ate, <PERSON>er, <PERSON>eri<PERSON>, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Tenant<PERSON><PERSON>, Revision        from Iot_RuleChain
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.IotRulechainPojo">
        <include refid="selectIotRulechainVo"/>
         where 1 = 1 and Iot_RuleChain.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Iot_RuleChain.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.additionalinfo != null ">
   and Iot_RuleChain.AdditionalInfo like concat('%', #{SearchPojo.additionalinfo}, '%')
</if>
<if test="SearchPojo.configuration != null ">
   and Iot_RuleChain.Configuration like concat('%', #{SearchPojo.configuration}, '%')
</if>
<if test="SearchPojo.rulechainname != null ">
   and Iot_RuleChain.RuleChainName like concat('%', #{SearchPojo.rulechainname}, '%')
</if>
<if test="SearchPojo.rulechaintype != null ">
   and Iot_RuleChain.RuleChainType like concat('%', #{SearchPojo.rulechaintype}, '%')
</if>
<if test="SearchPojo.firstrulenodeid != null ">
   and Iot_RuleChain.FirstRuleNodeid like concat('%', #{SearchPojo.firstrulenodeid}, '%')
</if>
<if test="SearchPojo.externalid != null ">
   and Iot_RuleChain.Externalid like concat('%', #{SearchPojo.externalid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Iot_RuleChain.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Iot_RuleChain.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Iot_RuleChain.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Iot_RuleChain.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Iot_RuleChain.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Iot_RuleChain.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Iot_RuleChain.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Iot_RuleChain.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Iot_RuleChain.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Iot_RuleChain.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Iot_RuleChain.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Iot_RuleChain.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Iot_RuleChain.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Iot_RuleChain.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Iot_RuleChain.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Iot_RuleChain.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.additionalinfo != null ">
   or Iot_RuleChain.AdditionalInfo like concat('%', #{SearchPojo.additionalinfo}, '%')
</if>
<if test="SearchPojo.configuration != null ">
   or Iot_RuleChain.Configuration like concat('%', #{SearchPojo.configuration}, '%')
</if>
<if test="SearchPojo.rulechainname != null ">
   or Iot_RuleChain.RuleChainName like concat('%', #{SearchPojo.rulechainname}, '%')
</if>
<if test="SearchPojo.rulechaintype != null ">
   or Iot_RuleChain.RuleChainType like concat('%', #{SearchPojo.rulechaintype}, '%')
</if>
<if test="SearchPojo.firstrulenodeid != null ">
   or Iot_RuleChain.FirstRuleNodeid like concat('%', #{SearchPojo.firstrulenodeid}, '%')
</if>
<if test="SearchPojo.externalid != null ">
   or Iot_RuleChain.Externalid like concat('%', #{SearchPojo.externalid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Iot_RuleChain.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Iot_RuleChain.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Iot_RuleChain.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Iot_RuleChain.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Iot_RuleChain.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Iot_RuleChain.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Iot_RuleChain.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Iot_RuleChain.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Iot_RuleChain.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Iot_RuleChain.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Iot_RuleChain.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Iot_RuleChain.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Iot_RuleChain.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Iot_RuleChain.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Iot_RuleChain.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Iot_RuleChain.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Iot_RuleChain(id, AdditionalInfo, Configuration, RuleChainName, RuleChainType, FirstRuleNodeid, Root, DebugMode, Externalid, Version, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{additionalinfo}, #{configuration}, #{rulechainname}, #{rulechaintype}, #{firstrulenodeid}, #{root}, #{debugmode}, #{externalid}, #{version}, #{remark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Iot_RuleChain
        <set>
            <if test="additionalinfo != null ">
                AdditionalInfo =#{additionalinfo},
            </if>
            <if test="configuration != null ">
                Configuration =#{configuration},
            </if>
            <if test="rulechainname != null ">
                RuleChainName =#{rulechainname},
            </if>
            <if test="rulechaintype != null ">
                RuleChainType =#{rulechaintype},
            </if>
            <if test="firstrulenodeid != null ">
                FirstRuleNodeid =#{firstrulenodeid},
            </if>
            <if test="root != null">
                Root =#{root},
            </if>
            <if test="debugmode != null">
                DebugMode =#{debugmode},
            </if>
            <if test="externalid != null ">
                Externalid =#{externalid},
            </if>
            <if test="version != null">
                Version =#{version},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Iot_RuleChain where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getEntityByDeviceProfile" resultType="inks.service.std.eam.domain.pojo.IotRulechainPojo">
        <include refid="selectIotRulechainVo"/>
        where Iot_RuleChain.id=(select DefaultRuleChainid from Iot_DeviceProfile where id= #{deviceprofileid})
    </select>

    <update id="setRoot">
        update Iot_RuleChain
        set Root = IF(id = #{key}, 1, 0)
        where Tenantid = #{tid}
    </update>

</mapper>

