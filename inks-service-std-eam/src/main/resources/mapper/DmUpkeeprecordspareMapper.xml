<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmUpkeeprecordspareMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmUpkeeprecordsparePojo">
        select id,
               Pid,
               Spareid,
               PlanQty,
               Quantity,
               Price,
               Amount,
               Remark,
               RowNum,
               Storeid,
               Location,
               TaxPrice,
               TaxAmount,
               ItemTaxrate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Tenantid,
               Revision
        from Dm_UpkeepRecordSpare
        where Dm_UpkeepRecordSpare.id = #{key}
          and Dm_UpkeepRecordSpare.Tenantid = #{tid}
    </select>
    <sql id="selectDmUpkeeprecordspareVo">
        select id,
               Pid,
               Spareid,
               PlanQty,
               Quantity,
               Price,
               Amount,
               Remark,
               RowNum,
               Storeid,
               Location,
               TaxPrice,
               TaxAmount,
               ItemTaxrate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Tenantid,
               Revision
        from Dm_UpkeepRecordSpare
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmUpkeeprecordsparePojo">
        <include refid="selectDmUpkeeprecordspareVo"/>
        where 1 = 1 and Dm_UpkeepRecordSpare.Tenantid =#{Tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_UpkeepRecordSpare.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Dm_UpkeepRecordSpare.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.spareid != null and SearchPojo.spareid != ''">
            and Dm_UpkeepRecordSpare.spareid like concat('%', #{SearchPojo.spareid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Dm_UpkeepRecordSpare.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
            and Dm_UpkeepRecordSpare.storeid like concat('%', #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Dm_UpkeepRecordSpare.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Dm_UpkeepRecordSpare.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Dm_UpkeepRecordSpare.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Dm_UpkeepRecordSpare.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Dm_UpkeepRecordSpare.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Dm_UpkeepRecordSpare.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Dm_UpkeepRecordSpare.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Dm_UpkeepRecordSpare.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Dm_UpkeepRecordSpare.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Dm_UpkeepRecordSpare.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.spareid != null and SearchPojo.spareid != ''">
                or Dm_UpkeepRecordSpare.Spareid like concat('%', #{SearchPojo.spareid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Dm_UpkeepRecordSpare.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
                or Dm_UpkeepRecordSpare.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Dm_UpkeepRecordSpare.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Dm_UpkeepRecordSpare.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Dm_UpkeepRecordSpare.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Dm_UpkeepRecordSpare.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Dm_UpkeepRecordSpare.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Dm_UpkeepRecordSpare.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Dm_UpkeepRecordSpare.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Dm_UpkeepRecordSpare.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Dm_UpkeepRecordSpare.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmUpkeeprecordsparePojo">
        select Dm_UpkeepRecordSpare.id,
               Dm_UpkeepRecordSpare.Pid,
               Dm_UpkeepRecordSpare.Spareid,
               Dm_UpkeepRecordSpare.PlanQty,
               Dm_UpkeepRecordSpare.Quantity,
               Dm_UpkeepRecordSpare.Price,
               Dm_UpkeepRecordSpare.Amount,
               Dm_UpkeepRecordSpare.Remark,
               Dm_UpkeepRecordSpare.RowNum,
               Dm_UpkeepRecordSpare.Storeid,
               Dm_UpkeepRecordSpare.Location,
               Dm_UpkeepRecordSpare.TaxPrice,
               Dm_UpkeepRecordSpare.TaxAmount,
               Dm_UpkeepRecordSpare.ItemTaxrate,
               Dm_UpkeepRecordSpare.Custom1,
               Dm_UpkeepRecordSpare.Custom2,
               Dm_UpkeepRecordSpare.Custom3,
               Dm_UpkeepRecordSpare.Custom4,
               Dm_UpkeepRecordSpare.Custom5,
               Dm_UpkeepRecordSpare.Custom6,
               Dm_UpkeepRecordSpare.Custom7,
               Dm_UpkeepRecordSpare.Custom8,
               Dm_UpkeepRecordSpare.Tenantid,
               Dm_UpkeepRecordSpare.Revision,
               Dm_Spare.SpareCode,
               Dm_Spare.SpareName,
               Dm_Spare.SpareSpec,
               Dm_Spare.SpareUnit
        from Dm_UpkeepRecordSpare
                 join Dm_Spare on Dm_UpkeepRecordSpare.Spareid = Dm_Spare.id
        where Dm_UpkeepRecordSpare.Pid = #{Pid}
          and Dm_UpkeepRecordSpare.Tenantid = #{tid}
        order by Dm_UpkeepRecordSpare.RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Dm_UpkeepRecordSpare(id, Pid, Spareid, PlanQty, Quantity, Price, Amount, Remark, RowNum, Storeid,
                                         Location, TaxPrice, TaxAmount, ItemTaxrate, Custom1, Custom2, Custom3, Custom4,
                                         Custom5, Custom6, Custom7, Custom8, Tenantid, Revision)
        values (#{id}, #{pid}, #{spareid}, #{planqty}, #{quantity}, #{price}, #{amount}, #{remark}, #{rownum},
                #{storeid}, #{location}, #{taxprice}, #{taxamount}, #{itemtaxrate}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_UpkeepRecordSpare
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="spareid != null ">
                Spareid = #{spareid},
            </if>
            <if test="planqty != null">
                PlanQty = #{planqty},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="storeid != null ">
                Storeid = #{storeid},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Dm_UpkeepRecordSpare
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

