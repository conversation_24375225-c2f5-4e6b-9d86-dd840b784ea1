<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmToolinfoMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmToolinfoPojo">
        <include refid="selectDmToolinfoVo"/>
        where Dm_ToolInfo.id = #{key} and Dm_ToolInfo.Tenantid=#{tid}
    </select>
    <sql id="selectDmToolinfoVo">
        select Dm_ToolInfo.id,
               Dm_ToolInfo.ToolCode,
               Dm_ToolInfo.ToolName,
               Dm_ToolInfo.Categoryid,
               Dm_ToolInfo.ActivateDate,
               Dm_ToolInfo.DiscardDate,
               Dm_ToolInfo.StoreLocation,
               Dm_ToolInfo.Status,
               Dm_ToolInfo.LastMaintDate,
               Dm_ToolInfo.MaintUsedQty,
               Dm_ToolInfo.MaintWarnQty,
               Dm_ToolInfo.MaintLimitQty,
               Dm_ToolInfo.MaintCount,
               Dm_ToolInfo.ScrapUsedQty,
               Dm_ToolInfo.ScrapWarnQty,
               Dm_ToolInfo.ScrapLimitQty,
               Dm_ToolInfo.InspStatus,
               Dm_ToolInfo.LastInspDate,
               Dm_ToolInfo.InspType,
               Dm_ToolInfo.EnabledMark,
               Dm_ToolInfo.Picture,
               Dm_ToolInfo.Spec,
               Dm_ToolInfo.Brand,
               Dm_ToolInfo.Groupid,
               Dm_ToolInfo.Price,
               Dm_ToolInfo.RowNum,
               Dm_ToolInfo.Remark,
               Dm_ToolInfo.CreateBy,
               Dm_ToolInfo.CreateByid,
               Dm_ToolInfo.CreateDate,
               Dm_ToolInfo.Lister,
               Dm_ToolInfo.Listerid,
               Dm_ToolInfo.ModifyDate,
               Dm_ToolInfo.Custom1,
               Dm_ToolInfo.Custom2,
               Dm_ToolInfo.Custom3,
               Dm_ToolInfo.Custom4,
               Dm_ToolInfo.Custom5,
               Dm_ToolInfo.Custom6,
               Dm_ToolInfo.Custom7,
               Dm_ToolInfo.Custom8,
               Dm_ToolInfo.Custom9,
               Dm_ToolInfo.Custom10,
               Dm_ToolInfo.Deptid,
               Dm_ToolInfo.Tenantid,
               Dm_ToolInfo.TenantName,
               Dm_ToolInfo.Revision,
               Dm_ToolCategory.CateName,
               Dm_ToolCategory.MaintDays,
               App_Workgroup.GroupName,
               -- 新增字段：计算下一次维护时间
               DATE_ADD(Dm_ToolInfo.LastMaintDate, INTERVAL Dm_ToolCategory.MaintDays DAY) AS nextmaintdate
        from Dm_ToolInfo
                 left join Dm_ToolCategory on Dm_ToolInfo.Categoryid = Dm_ToolCategory.id
                 left join App_Workgroup on Dm_ToolInfo.Groupid = App_Workgroup.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmToolinfoPojo">
        <include refid="selectDmToolinfoVo"/>
         where 1 = 1 and Dm_ToolInfo.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_ToolInfo.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.toolcode != null ">
   and Dm_ToolInfo.ToolCode like concat('%', #{SearchPojo.toolcode}, '%')
</if>
<if test="SearchPojo.toolname != null ">
   and Dm_ToolInfo.ToolName like concat('%', #{SearchPojo.toolname}, '%')
</if>
<if test="SearchPojo.categoryid != null ">
   and Dm_ToolInfo.Categoryid like concat('%', #{SearchPojo.categoryid}, '%')
</if>
<if test="SearchPojo.storelocation != null ">
   and Dm_ToolInfo.StoreLocation like concat('%', #{SearchPojo.storelocation}, '%')
</if>
<if test="SearchPojo.picture != null ">
   and Dm_ToolInfo.Picture like concat('%', #{SearchPojo.picture}, '%')
</if>
<if test="SearchPojo.spec != null ">
   and Dm_ToolInfo.Spec like concat('%', #{SearchPojo.spec}, '%')
</if>
<if test="SearchPojo.brand != null ">
   and Dm_ToolInfo.Brand like concat('%', #{SearchPojo.brand}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Dm_ToolInfo.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Dm_ToolInfo.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_ToolInfo.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_ToolInfo.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_ToolInfo.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_ToolInfo.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_ToolInfo.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_ToolInfo.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_ToolInfo.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_ToolInfo.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_ToolInfo.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_ToolInfo.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_ToolInfo.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_ToolInfo.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Dm_ToolInfo.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Dm_ToolInfo.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Dm_ToolInfo.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Dm_ToolInfo.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.toolcode != null ">
   or Dm_ToolInfo.ToolCode like concat('%', #{SearchPojo.toolcode}, '%')
</if>
<if test="SearchPojo.toolname != null ">
   or Dm_ToolInfo.ToolName like concat('%', #{SearchPojo.toolname}, '%')
</if>
<if test="SearchPojo.categoryid != null ">
   or Dm_ToolInfo.Categoryid like concat('%', #{SearchPojo.categoryid}, '%')
</if>
<if test="SearchPojo.storelocation != null ">
   or Dm_ToolInfo.StoreLocation like concat('%', #{SearchPojo.storelocation}, '%')
</if>
<if test="SearchPojo.picture != null ">
   or Dm_ToolInfo.Picture like concat('%', #{SearchPojo.picture}, '%')
</if>
<if test="SearchPojo.spec != null ">
   or Dm_ToolInfo.Spec like concat('%', #{SearchPojo.spec}, '%')
</if>
<if test="SearchPojo.brand != null ">
   or Dm_ToolInfo.Brand like concat('%', #{SearchPojo.brand}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Dm_ToolInfo.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Dm_ToolInfo.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_ToolInfo.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_ToolInfo.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_ToolInfo.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_ToolInfo.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_ToolInfo.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_ToolInfo.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_ToolInfo.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_ToolInfo.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_ToolInfo.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_ToolInfo.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_ToolInfo.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_ToolInfo.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Dm_ToolInfo.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Dm_ToolInfo.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Dm_ToolInfo.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Dm_ToolInfo.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_ToolInfo(id, ToolCode, ToolName, Categoryid, ActivateDate, DiscardDate, StoreLocation, Status, LastMaintDate, MaintUsedQty, MaintWarnQty, MaintLimitQty, MaintCount, ScrapUsedQty, ScrapWarnQty, ScrapLimitQty, LastInspDate, InspStatus, InspType, EnabledMark, Picture, Spec, Brand, Groupid, Price, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{toolcode}, #{toolname}, #{categoryid}, #{activatedate}, #{discarddate}, #{storelocation}, #{status}, #{lastmaintdate}, #{maintusedqty}, #{maintwarnqty}, #{maintlimitqty}, #{maintcount}, #{scrapusedqty}, #{scrapwarnqty}, #{scraplimitqty}, #{lastinspdate}, #{inspstatus}, #{insptype}, #{enabledmark}, #{picture}, #{spec}, #{brand}, #{groupid}, #{price}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_ToolInfo
        <set>
            <if test="toolcode != null ">
                ToolCode =#{toolcode},
            </if>
            <if test="toolname != null ">
                ToolName =#{toolname},
            </if>
            <if test="categoryid != null ">
                Categoryid =#{categoryid},
            </if>
            <if test="activatedate != null">
                ActivateDate =#{activatedate},
            </if>
            <if test="discarddate != null">
                DiscardDate =#{discarddate},
            </if>
            <if test="storelocation != null ">
                StoreLocation =#{storelocation},
            </if>
            <if test="status != null">
                Status =#{status},
            </if>
            <if test="lastmaintdate != null">
                LastMaintDate =#{lastmaintdate},
            </if>
            <if test="maintusedqty != null">
                MaintUsedQty =#{maintusedqty},
            </if>
            <if test="maintwarnqty != null">
                MaintWarnQty =#{maintwarnqty},
            </if>
            <if test="maintlimitqty != null">
                MaintLimitQty =#{maintlimitqty},
            </if>
            <if test="maintcount != null">
                MaintCount =#{maintcount},
            </if>
            <if test="scrapusedqty != null">
                ScrapUsedQty =#{scrapusedqty},
            </if>
            <if test="scrapwarnqty != null">
                ScrapWarnQty =#{scrapwarnqty},
            </if>
            <if test="scraplimitqty != null">
                ScrapLimitQty =#{scraplimitqty},
            </if>
            <if test="lastinspdate != null">
                LastInspDate =#{lastinspdate},
            </if>
            <if test="inspstatus != null">
                InspStatus =#{inspstatus},
            </if>
            <if test="insptype != null">
                InspType =#{insptype},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="picture != null ">
                Picture =#{picture},
            </if>
            <if test="spec != null ">
                Spec =#{spec},
            </if>
            <if test="brand != null ">
                Brand =#{brand},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="price != null">
                Price =#{price},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_ToolInfo where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getEntityByToolUsageItemid" resultType="inks.service.std.eam.domain.pojo.DmToolinfoPojo">
        <include refid="selectDmToolinfoVo"/>
        where Dm_ToolInfo.id = (select Toolid from Dm_ToolUsageItem where id = #{toolusageitemid})
        and Dm_ToolInfo.Tenantid=#{tid}
    </select>
</mapper>

