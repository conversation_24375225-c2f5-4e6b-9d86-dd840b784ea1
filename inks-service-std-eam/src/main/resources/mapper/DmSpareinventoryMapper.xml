<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmSpareinventoryMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmSpareinventoryPojo">
        select id,
               Storeid,
               Spareid,
               Quantity,
               Amount,
               Location,
               BatchNo,
               ExpiryDate,
               EndUid,
               EndInUid,
               EndInDate,
               EndOutUid,
               EndOutDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Lister,
               Listerid,
               CreateBy,
               CreateByid,
               CreateDate,
               ModifyDate,
               Tenantid,
               Revision
        from Dm_SpareInventory
        where Dm_SpareInventory.id = #{key}
          and Dm_SpareInventory.Tenantid = #{tid}
    </select>
    <sql id="selectDmSpareinventoryVo">
        select Dm_SpareInventory.id,
               Dm_SpareInventory.Storeid,
               Dm_SpareInventory.Spareid,
               Dm_SpareInventory.Quantity,
               Dm_SpareInventory.Amount,
               Dm_SpareInventory.Location,
               Dm_SpareInventory.BatchNo,
               Dm_SpareInventory.ExpiryDate,
               Dm_SpareInventory.EndUid,
               Dm_SpareInventory.EndInUid,
               Dm_SpareInventory.EndInDate,
               Dm_SpareInventory.EndOutUid,
               Dm_SpareInventory.EndOutDate,
               Dm_SpareInventory.Custom1,
               Dm_SpareInventory.Custom2,
               Dm_SpareInventory.Custom3,
               Dm_SpareInventory.Custom4,
               Dm_SpareInventory.Custom5,
               Dm_SpareInventory.Custom6,
               Dm_SpareInventory.Custom7,
               Dm_SpareInventory.Custom8,
               Dm_SpareInventory.Lister,
               Dm_SpareInventory.Listerid,
               Dm_SpareInventory.CreateBy,
               Dm_SpareInventory.CreateByid,
               Dm_SpareInventory.CreateDate,
               Dm_SpareInventory.ModifyDate,
               Dm_SpareInventory.Tenantid,
               Dm_SpareInventory.Revision,
               Dm_Spare.SpareName,
               Dm_Spare.SpareCode,
               Dm_Spare.SpareSpec,
               Dm_Spare.SpareUnit
        from Dm_SpareInventory
                 left join Dm_Spare on Dm_SpareInventory.Spareid = Dm_Spare.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmSpareinventoryPojo">
        <include refid="selectDmSpareinventoryVo"/>
        where 1 = 1 and Dm_SpareInventory.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_SpareInventory.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.storeid != null ">
            and Dm_SpareInventory.Storeid like concat('%', #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.spareid != null ">
            and Dm_SpareInventory.Spareid like concat('%', #{SearchPojo.spareid}, '%')
        </if>
        <if test="SearchPojo.location != null ">
            and Dm_SpareInventory.Location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null ">
            and Dm_SpareInventory.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.enduid != null ">
            and Dm_SpareInventory.EndUid like concat('%', #{SearchPojo.enduid}, '%')
        </if>
        <if test="SearchPojo.endinuid != null ">
            and Dm_SpareInventory.EndInUid like concat('%', #{SearchPojo.endinuid}, '%')
        </if>
        <if test="SearchPojo.endoutuid != null ">
            and Dm_SpareInventory.EndOutUid like concat('%', #{SearchPojo.endoutuid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Dm_SpareInventory.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Dm_SpareInventory.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Dm_SpareInventory.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Dm_SpareInventory.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Dm_SpareInventory.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Dm_SpareInventory.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Dm_SpareInventory.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Dm_SpareInventory.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Dm_SpareInventory.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Dm_SpareInventory.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Dm_SpareInventory.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Dm_SpareInventory.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.storeid != null ">
                or Dm_SpareInventory.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.spareid != null ">
                or Dm_SpareInventory.Spareid like concat('%', #{SearchPojo.spareid}, '%')
            </if>
            <if test="SearchPojo.location != null ">
                or Dm_SpareInventory.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null ">
                or Dm_SpareInventory.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.enduid != null ">
                or Dm_SpareInventory.EndUid like concat('%', #{SearchPojo.enduid}, '%')
            </if>
            <if test="SearchPojo.endinuid != null ">
                or Dm_SpareInventory.EndInUid like concat('%', #{SearchPojo.endinuid}, '%')
            </if>
            <if test="SearchPojo.endoutuid != null ">
                or Dm_SpareInventory.EndOutUid like concat('%', #{SearchPojo.endoutuid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Dm_SpareInventory.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Dm_SpareInventory.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Dm_SpareInventory.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Dm_SpareInventory.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Dm_SpareInventory.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Dm_SpareInventory.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Dm_SpareInventory.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Dm_SpareInventory.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Dm_SpareInventory.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Dm_SpareInventory.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Dm_SpareInventory.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Dm_SpareInventory.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Dm_SpareInventory(id, Storeid, Spareid, Quantity, Amount, Location, BatchNo, ExpiryDate, EndUid,
                                      EndInUid, EndInDate, EndOutUid, EndOutDate, Custom1, Custom2, Custom3, Custom4,
                                      Custom5, Custom6, Custom7, Custom8, Lister, Listerid, CreateBy, CreateByid,
                                      CreateDate, ModifyDate, Tenantid, Revision)
        values (#{id}, #{storeid}, #{spareid}, #{quantity}, #{amount}, #{location}, #{batchno}, #{expirydate},
                #{enduid}, #{endinuid}, #{endindate}, #{endoutuid}, #{endoutdate}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{lister}, #{listerid}, #{createby},
                #{createbyid}, #{createdate}, #{modifydate}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_SpareInventory
        <set>
            <if test="storeid != null ">
                Storeid =#{storeid},
            </if>
            <if test="spareid != null ">
                Spareid =#{spareid},
            </if>
            <if test="quantity != null">
                Quantity =#{quantity},
            </if>
            <if test="amount != null">
                Amount =#{amount},
            </if>
            <if test="location != null ">
                Location =#{location},
            </if>
            <if test="batchno != null ">
                BatchNo =#{batchno},
            </if>
            <if test="expirydate != null">
                ExpiryDate =#{expirydate},
            </if>
            <if test="enduid != null ">
                EndUid =#{enduid},
            </if>
            <if test="endinuid != null ">
                EndInUid =#{endinuid},
            </if>
            <if test="endindate != null">
                EndInDate =#{endindate},
            </if>
            <if test="endoutuid != null ">
                EndOutUid =#{endoutuid},
            </if>
            <if test="endoutdate != null">
                EndOutDate =#{endoutdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Dm_SpareInventory
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <select id="getEntityBySpareid" resultType="inks.service.std.eam.domain.pojo.DmSpareinventoryPojo">
        select *
        from Dm_SpareInventory
        WHERE Tenantid = #{tid}
          and Spareid = #{spareid}
          and batchno = #{batchno}
          and location = #{location}
        ORDER BY Quantity desc limit 0,1
    </select>
</mapper>

