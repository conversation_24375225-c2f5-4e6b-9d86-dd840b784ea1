<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.IotAlarmMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.IotAlarmPojo">
        <include refid="selectIotAlarmVo"/>
        where Iot_Alarm.id = #{key} and Iot_Alarm.Tenantid=#{tid}
    </select>
    <sql id="selectIotAlarmVo">
         select
id, AckTs, ClearTs, AdditionalInfo, EndTs, Originatorid, OriginatorType, Propagate, Severity, StartTs, AssignTs, Assigneeid, Customerid, PropagateRelationTypes, Type, PropagateToOwner, PropagateToTenant, Acknowledged, Cleared, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Iot_Alarm
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.IotAlarmPojo">
        <include refid="selectIotAlarmVo"/>
         where 1 = 1 and Iot_Alarm.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Iot_Alarm.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.additionalinfo != null ">
   and Iot_Alarm.AdditionalInfo like concat('%', #{SearchPojo.additionalinfo}, '%')
</if>
<if test="SearchPojo.originatorid != null ">
   and Iot_Alarm.Originatorid like concat('%', #{SearchPojo.originatorid}, '%')
</if>
<if test="SearchPojo.severity != null ">
   and Iot_Alarm.Severity like concat('%', #{SearchPojo.severity}, '%')
</if>
<if test="SearchPojo.assigneeid != null ">
   and Iot_Alarm.Assigneeid like concat('%', #{SearchPojo.assigneeid}, '%')
</if>
<if test="SearchPojo.customerid != null ">
   and Iot_Alarm.Customerid like concat('%', #{SearchPojo.customerid}, '%')
</if>
<if test="SearchPojo.propagaterelationtypes != null ">
   and Iot_Alarm.PropagateRelationTypes like concat('%', #{SearchPojo.propagaterelationtypes}, '%')
</if>
<if test="SearchPojo.type != null ">
   and Iot_Alarm.Type like concat('%', #{SearchPojo.type}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Iot_Alarm.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Iot_Alarm.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Iot_Alarm.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Iot_Alarm.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Iot_Alarm.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Iot_Alarm.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Iot_Alarm.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Iot_Alarm.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Iot_Alarm.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Iot_Alarm.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Iot_Alarm.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Iot_Alarm.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Iot_Alarm.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Iot_Alarm.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Iot_Alarm.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Iot_Alarm.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.additionalinfo != null ">
   or Iot_Alarm.AdditionalInfo like concat('%', #{SearchPojo.additionalinfo}, '%')
</if>
<if test="SearchPojo.originatorid != null ">
   or Iot_Alarm.Originatorid like concat('%', #{SearchPojo.originatorid}, '%')
</if>
<if test="SearchPojo.severity != null ">
   or Iot_Alarm.Severity like concat('%', #{SearchPojo.severity}, '%')
</if>
<if test="SearchPojo.assigneeid != null ">
   or Iot_Alarm.Assigneeid like concat('%', #{SearchPojo.assigneeid}, '%')
</if>
<if test="SearchPojo.customerid != null ">
   or Iot_Alarm.Customerid like concat('%', #{SearchPojo.customerid}, '%')
</if>
<if test="SearchPojo.propagaterelationtypes != null ">
   or Iot_Alarm.PropagateRelationTypes like concat('%', #{SearchPojo.propagaterelationtypes}, '%')
</if>
<if test="SearchPojo.type != null ">
   or Iot_Alarm.Type like concat('%', #{SearchPojo.type}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Iot_Alarm.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Iot_Alarm.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Iot_Alarm.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Iot_Alarm.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Iot_Alarm.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Iot_Alarm.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Iot_Alarm.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Iot_Alarm.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Iot_Alarm.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Iot_Alarm.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Iot_Alarm.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Iot_Alarm.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Iot_Alarm.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Iot_Alarm.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Iot_Alarm.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Iot_Alarm.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Iot_Alarm(id, AckTs, ClearTs, AdditionalInfo, EndTs, Originatorid, OriginatorType, Propagate, Severity, StartTs, AssignTs, Assigneeid, Customerid, PropagateRelationTypes, Type, PropagateToOwner, PropagateToTenant, Acknowledged, Cleared, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{ackts}, #{clearts}, #{additionalinfo}, #{endts}, #{originatorid}, #{originatortype}, #{propagate}, #{severity}, #{startts}, #{assignts}, #{assigneeid}, #{customerid}, #{propagaterelationtypes}, #{type}, #{propagatetoowner}, #{propagatetotenant}, #{acknowledged}, #{cleared}, #{remark}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Iot_Alarm
        <set>
            <if test="ackts != null">
                AckTs =#{ackts},
            </if>
            <if test="clearts != null">
                ClearTs =#{clearts},
            </if>
            <if test="additionalinfo != null ">
                AdditionalInfo =#{additionalinfo},
            </if>
            <if test="endts != null">
                EndTs =#{endts},
            </if>
            <if test="originatorid != null ">
                Originatorid =#{originatorid},
            </if>
            <if test="originatortype != null">
                OriginatorType =#{originatortype},
            </if>
            <if test="propagate != null">
                Propagate =#{propagate},
            </if>
            <if test="severity != null ">
                Severity =#{severity},
            </if>
            <if test="startts != null">
                StartTs =#{startts},
            </if>
            <if test="assignts != null">
                AssignTs =#{assignts},
            </if>
            <if test="assigneeid != null ">
                Assigneeid =#{assigneeid},
            </if>
            <if test="customerid != null ">
                Customerid =#{customerid},
            </if>
            <if test="propagaterelationtypes != null ">
                PropagateRelationTypes =#{propagaterelationtypes},
            </if>
            <if test="type != null ">
                Type =#{type},
            </if>
            <if test="propagatetoowner != null">
                PropagateToOwner =#{propagatetoowner},
            </if>
            <if test="propagatetotenant != null">
                PropagateToTenant =#{propagatetotenant},
            </if>
            <if test="acknowledged != null">
                Acknowledged =#{acknowledged},
            </if>
            <if test="cleared != null">
                Cleared =#{cleared},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Iot_Alarm where id = #{key} and Tenantid=#{tid}
    </delete>
</mapper>

