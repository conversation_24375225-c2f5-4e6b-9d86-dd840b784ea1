<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmUpkeepflowitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmUpkeepflowitemPojo">
        select
          id, Pid, Level, Field, FieldType, Required, StartDate, EndDate, Duty, Tracker, Remark, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Tenantid, Revision        from Dm_UpkeepFlowItem
        where Dm_UpkeepFlowItem.id = #{key} and Dm_UpkeepFlowItem.Tenantid=#{tid}
    </select>
    <sql id="selectDmUpkeepflowitemVo">
         select
          id, Pid, Level, Field, FieldType, Required, StartDate, EndDate, Duty, Tracker, Remark, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Tenantid, Revision        from Dm_UpkeepFlowItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmUpkeepflowitemPojo">
        <include refid="selectDmUpkeepflowitemVo"/>
         where 1 = 1 and Dm_UpkeepFlowItem.Tenantid =#{Tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_UpkeepFlowItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Dm_UpkeepFlowItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.level != null and SearchPojo.level != ''">
   and Dm_UpkeepFlowItem.level like concat('%', #{SearchPojo.level}, '%')
</if>
<if test="SearchPojo.field != null and SearchPojo.field != ''">
   and Dm_UpkeepFlowItem.field like concat('%', #{SearchPojo.field}, '%')
</if>
<if test="SearchPojo.fieldtype != null and SearchPojo.fieldtype != ''">
   and Dm_UpkeepFlowItem.fieldtype like concat('%', #{SearchPojo.fieldtype}, '%')
</if>
<if test="SearchPojo.duty != null and SearchPojo.duty != ''">
   and Dm_UpkeepFlowItem.duty like concat('%', #{SearchPojo.duty}, '%')
</if>
<if test="SearchPojo.tracker != null and SearchPojo.tracker != ''">
   and Dm_UpkeepFlowItem.tracker like concat('%', #{SearchPojo.tracker}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Dm_UpkeepFlowItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Dm_UpkeepFlowItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Dm_UpkeepFlowItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Dm_UpkeepFlowItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Dm_UpkeepFlowItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Dm_UpkeepFlowItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Dm_UpkeepFlowItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Dm_UpkeepFlowItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Dm_UpkeepFlowItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Dm_UpkeepFlowItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.level != null and SearchPojo.level != ''">
   or Dm_UpkeepFlowItem.Level like concat('%', #{SearchPojo.level}, '%')
</if>
<if test="SearchPojo.field != null and SearchPojo.field != ''">
   or Dm_UpkeepFlowItem.Field like concat('%', #{SearchPojo.field}, '%')
</if>
<if test="SearchPojo.fieldtype != null and SearchPojo.fieldtype != ''">
   or Dm_UpkeepFlowItem.FieldType like concat('%', #{SearchPojo.fieldtype}, '%')
</if>
<if test="SearchPojo.duty != null and SearchPojo.duty != ''">
   or Dm_UpkeepFlowItem.Duty like concat('%', #{SearchPojo.duty}, '%')
</if>
<if test="SearchPojo.tracker != null and SearchPojo.tracker != ''">
   or Dm_UpkeepFlowItem.Tracker like concat('%', #{SearchPojo.tracker}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Dm_UpkeepFlowItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Dm_UpkeepFlowItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Dm_UpkeepFlowItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Dm_UpkeepFlowItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Dm_UpkeepFlowItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Dm_UpkeepFlowItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Dm_UpkeepFlowItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Dm_UpkeepFlowItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Dm_UpkeepFlowItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmUpkeepflowitemPojo">
        select
          id, Pid, Level, Field, FieldType, Required, StartDate, EndDate, Duty, Tracker, Remark, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Tenantid, Revision        from Dm_UpkeepFlowItem
        where Dm_UpkeepFlowItem.Pid = #{Pid} and Dm_UpkeepFlowItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_UpkeepFlowItem(id, Pid, Level, Field, FieldType, Required, StartDate, EndDate, Duty, Tracker, Remark, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Tenantid, Revision)
        values (#{id}, #{pid}, #{level}, #{field}, #{fieldtype}, #{required}, #{startdate}, #{enddate}, #{duty}, #{tracker}, #{remark}, #{rownum}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_UpkeepFlowItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="level != null ">
                Level = #{level},
            </if>
            <if test="field != null ">
                Field = #{field},
            </if>
            <if test="fieldtype != null ">
                FieldType = #{fieldtype},
            </if>
            <if test="required != null">
                Required = #{required},
            </if>
            <if test="startdate != null">
                StartDate = #{startdate},
            </if>
            <if test="enddate != null">
                EndDate = #{enddate},
            </if>
            <if test="duty != null ">
                Duty = #{duty},
            </if>
            <if test="tracker != null ">
                Tracker = #{tracker},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_UpkeepFlowItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

