<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmPatrolpathitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmPatrolpathitemPojo">
        select
          id, Pid, Pointid, PointCode, PointName, Stdid, StdCode, StdName, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolPathItem
        where Dm_PatrolPathItem.id = #{key} and Dm_PatrolPathItem.Tenantid=#{tid}
    </select>
    <sql id="selectDmPatrolpathitemVo">
         select
          id, Pid, Pointid, PointCode, PointName, Stdid, StdCode, StdName, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolPathItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmPatrolpathitemPojo">
        <include refid="selectDmPatrolpathitemVo"/>
         where 1 = 1 and Dm_PatrolPathItem.Tenantid =#{Tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_PatrolPathItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Dm_PatrolPathItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.pointid != null and SearchPojo.pointid != ''">
   and Dm_PatrolPathItem.pointid like concat('%', #{SearchPojo.pointid}, '%')
</if>
<if test="SearchPojo.pointcode != null and SearchPojo.pointcode != ''">
   and Dm_PatrolPathItem.pointcode like concat('%', #{SearchPojo.pointcode}, '%')
</if>
<if test="SearchPojo.pointname != null and SearchPojo.pointname != ''">
   and Dm_PatrolPathItem.pointname like concat('%', #{SearchPojo.pointname}, '%')
</if>
<if test="SearchPojo.stdid != null and SearchPojo.stdid != ''">
   and Dm_PatrolPathItem.stdid like concat('%', #{SearchPojo.stdid}, '%')
</if>
<if test="SearchPojo.stdcode != null and SearchPojo.stdcode != ''">
   and Dm_PatrolPathItem.stdcode like concat('%', #{SearchPojo.stdcode}, '%')
</if>
<if test="SearchPojo.stdname != null and SearchPojo.stdname != ''">
   and Dm_PatrolPathItem.stdname like concat('%', #{SearchPojo.stdname}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Dm_PatrolPathItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Dm_PatrolPathItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Dm_PatrolPathItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Dm_PatrolPathItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Dm_PatrolPathItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Dm_PatrolPathItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Dm_PatrolPathItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Dm_PatrolPathItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Dm_PatrolPathItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Dm_PatrolPathItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Dm_PatrolPathItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Dm_PatrolPathItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.pointid != null and SearchPojo.pointid != ''">
   or Dm_PatrolPathItem.Pointid like concat('%', #{SearchPojo.pointid}, '%')
</if>
<if test="SearchPojo.pointcode != null and SearchPojo.pointcode != ''">
   or Dm_PatrolPathItem.PointCode like concat('%', #{SearchPojo.pointcode}, '%')
</if>
<if test="SearchPojo.pointname != null and SearchPojo.pointname != ''">
   or Dm_PatrolPathItem.PointName like concat('%', #{SearchPojo.pointname}, '%')
</if>
<if test="SearchPojo.stdid != null and SearchPojo.stdid != ''">
   or Dm_PatrolPathItem.Stdid like concat('%', #{SearchPojo.stdid}, '%')
</if>
<if test="SearchPojo.stdcode != null and SearchPojo.stdcode != ''">
   or Dm_PatrolPathItem.StdCode like concat('%', #{SearchPojo.stdcode}, '%')
</if>
<if test="SearchPojo.stdname != null and SearchPojo.stdname != ''">
   or Dm_PatrolPathItem.StdName like concat('%', #{SearchPojo.stdname}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Dm_PatrolPathItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Dm_PatrolPathItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Dm_PatrolPathItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Dm_PatrolPathItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Dm_PatrolPathItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Dm_PatrolPathItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Dm_PatrolPathItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Dm_PatrolPathItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Dm_PatrolPathItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Dm_PatrolPathItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Dm_PatrolPathItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmPatrolpathitemPojo">
        select
          id, Pid, Pointid, PointCode, PointName, Stdid, StdCode, StdName, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolPathItem
        where Dm_PatrolPathItem.Pid = #{Pid} and Dm_PatrolPathItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_PatrolPathItem(id, Pid, Pointid, PointCode, PointName, Stdid, StdCode, StdName, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{pointid}, #{pointcode}, #{pointname}, #{stdid}, #{stdcode}, #{stdname}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_PatrolPathItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="pointid != null ">
                Pointid = #{pointid},
            </if>
            <if test="pointcode != null ">
                PointCode = #{pointcode},
            </if>
            <if test="pointname != null ">
                PointName = #{pointname},
            </if>
            <if test="stdid != null ">
                Stdid = #{stdid},
            </if>
            <if test="stdcode != null ">
                StdCode = #{stdcode},
            </if>
            <if test="stdname != null ">
                StdName = #{stdname},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_PatrolPathItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

