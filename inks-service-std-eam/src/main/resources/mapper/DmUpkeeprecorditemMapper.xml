<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmUpkeeprecorditemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmUpkeeprecorditemPojo">
        select id,
               Pid,
               Deviceid,
               Remark,
               RowNum,
               ExpiryDate,
               FinishMark,
               Recordid,
               RecordBy,
               RecordDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Tenantid,
               Revision
        from Dm_UpkeepRecordItem
        where Dm_UpkeepRecordItem.id = #{key}
          and Dm_UpkeepRecordItem.Tenantid = #{tid}
    </select>
    <sql id="selectDmUpkeeprecorditemVo">
        select id,
               Pid,
               Deviceid,
               Remark,
               <PERSON>Num,
               ExpiryDate,
               FinishMark,
               Recordid,
               RecordBy,
               RecordDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Tenantid,
               Revision
        from Dm_UpkeepRecordItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.std.eam.domain.pojo.DmUpkeeprecorditemPojo">
        <include refid="selectDmUpkeeprecorditemVo"/>
        where 1 = 1 and Dm_UpkeepRecordItem.Tenantid =#{Tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_UpkeepRecordItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Dm_UpkeepRecordItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.deviceid != null and SearchPojo.deviceid != ''">
            and Dm_UpkeepRecordItem.deviceid like concat('%', #{SearchPojo.deviceid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Dm_UpkeepRecordItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.recordid != null and SearchPojo.recordid != ''">
            and Dm_UpkeepRecordItem.recordid like concat('%', #{SearchPojo.recordid}, '%')
        </if>
        <if test="SearchPojo.recordby != null and SearchPojo.recordby != ''">
            and Dm_UpkeepRecordItem.recordby like concat('%', #{SearchPojo.recordby}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Dm_UpkeepRecordItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Dm_UpkeepRecordItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Dm_UpkeepRecordItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Dm_UpkeepRecordItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Dm_UpkeepRecordItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Dm_UpkeepRecordItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Dm_UpkeepRecordItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Dm_UpkeepRecordItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Dm_UpkeepRecordItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.deviceid != null and SearchPojo.deviceid != ''">
                or Dm_UpkeepRecordItem.Deviceid like concat('%', #{SearchPojo.deviceid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Dm_UpkeepRecordItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.recordid != null and SearchPojo.recordid != ''">
                or Dm_UpkeepRecordItem.Recordid like concat('%', #{SearchPojo.recordid}, '%')
            </if>
            <if test="SearchPojo.recordby != null and SearchPojo.recordby != ''">
                or Dm_UpkeepRecordItem.RecordBy like concat('%', #{SearchPojo.recordby}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Dm_UpkeepRecordItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Dm_UpkeepRecordItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Dm_UpkeepRecordItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Dm_UpkeepRecordItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Dm_UpkeepRecordItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Dm_UpkeepRecordItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Dm_UpkeepRecordItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Dm_UpkeepRecordItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmUpkeeprecorditemPojo">
        select Dm_UpkeepRecordItem.id,
               Dm_UpkeepRecordItem.Pid,
               Dm_UpkeepRecordItem.Deviceid,
               Dm_UpkeepRecordItem.Remark,
               Dm_UpkeepRecordItem.RowNum,
               Dm_UpkeepRecordItem.ExpiryDate,
               Dm_UpkeepRecordItem.FinishMark,
               Dm_UpkeepRecordItem.Recordid,
               Dm_UpkeepRecordItem.RecordBy,
               Dm_UpkeepRecordItem.RecordDate,
               Dm_UpkeepRecordItem.Custom1,
               Dm_UpkeepRecordItem.Custom2,
               Dm_UpkeepRecordItem.Custom3,
               Dm_UpkeepRecordItem.Custom4,
               Dm_UpkeepRecordItem.Custom5,
               Dm_UpkeepRecordItem.Custom6,
               Dm_UpkeepRecordItem.Custom7,
               Dm_UpkeepRecordItem.Custom8,
               Dm_UpkeepRecordItem.Tenantid,
               Dm_UpkeepRecordItem.Revision,
               Dm_Device.DevCode,
               Dm_Device.DevName,
               Dm_Device.DevSpec,
               Dm_Device.DevUnit
        from Dm_UpkeepRecordItem
                 join Dm_Device on Dm_UpkeepRecordItem.Deviceid = Dm_Device.id
        where Dm_UpkeepRecordItem.Pid = #{Pid}
          and Dm_UpkeepRecordItem.Tenantid = #{tid}
        order by Dm_UpkeepRecordItem.RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Dm_UpkeepRecordItem(id, Pid, Deviceid, Remark, RowNum, ExpiryDate, FinishMark, Recordid, RecordBy,
                                        RecordDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7,
                                        Custom8, Tenantid, Revision)
        values (#{id}, #{pid}, #{deviceid}, #{remark}, #{rownum}, #{expirydate}, #{finishmark}, #{recordid},
                #{recordby}, #{recorddate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
                #{custom7}, #{custom8}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_UpkeepRecordItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="deviceid != null ">
                Deviceid = #{deviceid},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="expirydate != null">
                ExpiryDate = #{expirydate},
            </if>
            <if test="finishmark != null">
                FinishMark = #{finishmark},
            </if>
            <if test="recordid != null ">
                Recordid = #{recordid},
            </if>
            <if test="recordby != null ">
                RecordBy = #{recordby},
            </if>
            <if test="recorddate != null">
                RecordDate = #{recorddate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Dm_UpkeepRecordItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

