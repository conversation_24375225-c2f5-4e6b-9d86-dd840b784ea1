<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmMoldMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmMoldPojo">
        <include refid="selectDmMoldVo"/>
        where Dm_Mold.id = #{key} and Dm_Mold.Tenantid=#{tid}
    </select>
    <sql id="selectDmMoldVo">
         select
id, MoldCode, MoldName, Description, Groupid, DrawingNumber, Quantity, AbnormalQty, LastUsedDate, MaxProcessing, CurrentProcessing, StatusCode, RowNum, Remark, CreateBy, CreateByid, <PERSON>reateDate, Lister, <PERSON><PERSON>d, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Dm_Mold
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmMoldPojo">
        <include refid="selectDmMoldVo"/>
         where 1 = 1 and Dm_Mold.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_Mold.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.moldcode != null ">
   and Dm_Mold.MoldCode like concat('%', #{SearchPojo.moldcode}, '%')
</if>
<if test="SearchPojo.moldname != null ">
   and Dm_Mold.MoldName like concat('%', #{SearchPojo.moldname}, '%')
</if>
<if test="SearchPojo.description != null ">
   and Dm_Mold.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Dm_Mold.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.drawingnumber != null ">
   and Dm_Mold.DrawingNumber like concat('%', #{SearchPojo.drawingnumber}, '%')
</if>
<if test="SearchPojo.statuscode != null ">
   and Dm_Mold.StatusCode like concat('%', #{SearchPojo.statuscode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Dm_Mold.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_Mold.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_Mold.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_Mold.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_Mold.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_Mold.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_Mold.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_Mold.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_Mold.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_Mold.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_Mold.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_Mold.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_Mold.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Dm_Mold.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Dm_Mold.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Dm_Mold.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.moldcode != null ">
   or Dm_Mold.MoldCode like concat('%', #{SearchPojo.moldcode}, '%')
</if>
<if test="SearchPojo.moldname != null ">
   or Dm_Mold.MoldName like concat('%', #{SearchPojo.moldname}, '%')
</if>
<if test="SearchPojo.description != null ">
   or Dm_Mold.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Dm_Mold.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.drawingnumber != null ">
   or Dm_Mold.DrawingNumber like concat('%', #{SearchPojo.drawingnumber}, '%')
</if>
<if test="SearchPojo.statuscode != null ">
   or Dm_Mold.StatusCode like concat('%', #{SearchPojo.statuscode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Dm_Mold.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_Mold.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_Mold.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_Mold.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_Mold.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_Mold.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_Mold.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_Mold.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_Mold.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_Mold.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_Mold.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_Mold.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_Mold.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Dm_Mold.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Dm_Mold.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Dm_Mold.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_Mold(id, MoldCode, MoldName, Description, Groupid, DrawingNumber, Quantity, AbnormalQty, LastUsedDate, MaxProcessing, CurrentProcessing, StatusCode, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{moldcode}, #{moldname}, #{description}, #{groupid}, #{drawingnumber}, #{quantity}, #{abnormalqty}, #{lastuseddate}, #{maxprocessing}, #{currentprocessing}, #{statuscode}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_Mold
        <set>
            <if test="moldcode != null ">
                MoldCode =#{moldcode},
            </if>
            <if test="moldname != null ">
                MoldName =#{moldname},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="drawingnumber != null ">
                DrawingNumber =#{drawingnumber},
            </if>
            <if test="quantity != null">
                Quantity =#{quantity},
            </if>
            <if test="abnormalqty != null">
                AbnormalQty =#{abnormalqty},
            </if>
            <if test="lastuseddate != null">
                LastUsedDate =#{lastuseddate},
            </if>
            <if test="maxprocessing != null">
                MaxProcessing =#{maxprocessing},
            </if>
            <if test="currentprocessing != null">
                CurrentProcessing =#{currentprocessing},
            </if>
            <if test="statuscode != null ">
                StatusCode =#{statuscode},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_Mold where id = #{key} and Tenantid=#{tid}
    </delete>
</mapper>

