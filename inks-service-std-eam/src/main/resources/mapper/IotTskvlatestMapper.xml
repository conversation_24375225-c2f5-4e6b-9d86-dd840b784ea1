<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.IotTskvlatestMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.IotTskvlatestPojo">
        <include refid="selectIotTskvlatestVo"/>
        where Iot_TsKvLatest.id = #{key} and Iot_TsKvLatest.Tenantid=#{tid}
    </select>
    <sql id="selectIotTskvlatestVo">
         select
id, Entityid, Keyid, KeyV, Ts, BoolV, StrV, LongV, DblV, JsonV, CreateDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName        from Iot_TsKvLatest
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.IotTskvlatestPojo">
        <include refid="selectIotTskvlatestVo"/>
         where 1 = 1 and Iot_TsKvLatest.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Iot_TsKvLatest.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.entityid != null ">
   and Iot_TsKvLatest.Entityid like concat('%', #{SearchPojo.entityid}, '%')
</if>
<if test="SearchPojo.strv != null ">
   and Iot_TsKvLatest.StrV like concat('%', #{SearchPojo.strv}, '%')
</if>
<if test="SearchPojo.jsonv != null ">
   and Iot_TsKvLatest.JsonV like concat('%', #{SearchPojo.jsonv}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Iot_TsKvLatest.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Iot_TsKvLatest.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Iot_TsKvLatest.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Iot_TsKvLatest.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Iot_TsKvLatest.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Iot_TsKvLatest.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Iot_TsKvLatest.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Iot_TsKvLatest.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Iot_TsKvLatest.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Iot_TsKvLatest.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Iot_TsKvLatest.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.entityid != null ">
   or Iot_TsKvLatest.Entityid like concat('%', #{SearchPojo.entityid}, '%')
</if>
<if test="SearchPojo.strv != null ">
   or Iot_TsKvLatest.StrV like concat('%', #{SearchPojo.strv}, '%')
</if>
<if test="SearchPojo.jsonv != null ">
   or Iot_TsKvLatest.JsonV like concat('%', #{SearchPojo.jsonv}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Iot_TsKvLatest.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Iot_TsKvLatest.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Iot_TsKvLatest.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Iot_TsKvLatest.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Iot_TsKvLatest.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Iot_TsKvLatest.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Iot_TsKvLatest.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Iot_TsKvLatest.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Iot_TsKvLatest.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Iot_TsKvLatest.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Iot_TsKvLatest.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Iot_TsKvLatest(id, Entityid, Keyid, KeyV, Ts, BoolV, StrV, LongV, DblV, JsonV, CreateDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName)
        values (#{id}, #{entityid}, #{keyid}, #{keyv}, #{ts}, #{boolv}, #{strv}, #{longv}, #{dblv}, #{jsonv}, #{createdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Iot_TsKvLatest
        <set>
            <if test="entityid != null ">
                Entityid =#{entityid},
            </if>
            <if test="keyid != null">
                Keyid =#{keyid},
            </if>
            <if test="keyv != null ">
                KeyV =#{keyv},
            </if>
            <if test="ts != null">
                Ts =#{ts},
            </if>
            <if test="boolv != null">
                BoolV =#{boolv},
            </if>
            <if test="strv != null ">
                StrV =#{strv},
            </if>
            <if test="longv != null">
                LongV =#{longv},
            </if>
            <if test="dblv != null">
                DblV =#{dblv},
            </if>
            <if test="jsonv != null ">
                JsonV =#{jsonv},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Iot_TsKvLatest where id = #{key} and Tenantid=#{tid}
    </delete>

    <insert id="batchLatest" parameterType="java.util.List">
        INSERT INTO Iot_TsKvLatest
        (id, Entityid, Keyid, KeyV, Ts, BoolV, StrV, LongV, DblV, JsonV, CreateDate, Tenantid)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
             #{item.entityid},
             #{item.keyid},
             #{item.keyv},
             #{item.ts},
             #{item.boolv},
             #{item.strv},
             #{item.longv},
             #{item.dblv},
             #{item.jsonv},
             #{item.createdate},
             #{item.tenantid})
        </foreach>
        ON DUPLICATE KEY UPDATE Ts         = VALUES(Ts),
                                BoolV      = VALUES(BoolV),
                                StrV       = VALUES(StrV),
                                LongV      = VALUES(LongV),
                                DblV       = VALUES(DblV),
                                JsonV      = VALUES(JsonV),
                                CreateDate = VALUES(CreateDate)
    </insert>

    <select id="getList" resultType="inks.service.std.eam.domain.pojo.IotTskvlatestPojo">
        <include refid="selectIotTskvlatestVo"/>
        where Iot_TsKvLatest.Tenantid =#{tid}
         and Iot_TsKvLatest.Entityid =#{entityid}
    </select>
</mapper>

