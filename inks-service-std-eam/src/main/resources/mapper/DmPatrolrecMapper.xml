<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmPatrolrecMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmPatrolrecPojo">
        select
          id, RefNo, BillDate, BillType, Planid, PlanCode, PlanName, Pathid, PathCode, PathName, Operator, Summary, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Assessor, Assessorid, AssessDate, EnabledMark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision
        from Dm_PatrolRec
        where Dm_PatrolRec.id = #{key} and Dm_PatrolRec.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
          id, RefNo, BillDate, BillType, Planid, PlanCode, PlanName, Pathid, PathCode, PathName, Operator, Summary, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Assessor, Assessorid, AssessDate, EnabledMark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolRec
    </sql>
    <sql id="selectdetailVo">
         select
          id, RefNo, BillDate, BillType, Planid, PlanCode, PlanName, Pathid, PathCode, PathName, Operator, Summary, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Assessor, Assessorid, AssessDate, EnabledMark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolRec
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmPatrolrecitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 and Dm_PatrolRec.Tenantid =#{tenantid}
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_PatrolRec.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Dm_PatrolRec.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Dm_PatrolRec.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.planid != null ">
   and Dm_PatrolRec.planid like concat('%', #{SearchPojo.planid}, '%')
</if>
<if test="SearchPojo.plancode != null ">
   and Dm_PatrolRec.plancode like concat('%', #{SearchPojo.plancode}, '%')
</if>
<if test="SearchPojo.planname != null ">
   and Dm_PatrolRec.planname like concat('%', #{SearchPojo.planname}, '%')
</if>
<if test="SearchPojo.pathid != null ">
   and Dm_PatrolRec.pathid like concat('%', #{SearchPojo.pathid}, '%')
</if>
<if test="SearchPojo.pathcode != null ">
   and Dm_PatrolRec.pathcode like concat('%', #{SearchPojo.pathcode}, '%')
</if>
<if test="SearchPojo.pathname != null ">
   and Dm_PatrolRec.pathname like concat('%', #{SearchPojo.pathname}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Dm_PatrolRec.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Dm_PatrolRec.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_PatrolRec.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_PatrolRec.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_PatrolRec.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_PatrolRec.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Dm_PatrolRec.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Dm_PatrolRec.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_PatrolRec.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_PatrolRec.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_PatrolRec.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_PatrolRec.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_PatrolRec.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_PatrolRec.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_PatrolRec.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_PatrolRec.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Dm_PatrolRec.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Dm_PatrolRec.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Dm_PatrolRec.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Dm_PatrolRec.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.planid != null ">
   or Dm_PatrolRec.Planid like concat('%', #{SearchPojo.planid}, '%')
</if>
<if test="SearchPojo.plancode != null ">
   or Dm_PatrolRec.PlanCode like concat('%', #{SearchPojo.plancode}, '%')
</if>
<if test="SearchPojo.planname != null ">
   or Dm_PatrolRec.PlanName like concat('%', #{SearchPojo.planname}, '%')
</if>
<if test="SearchPojo.pathid != null ">
   or Dm_PatrolRec.Pathid like concat('%', #{SearchPojo.pathid}, '%')
</if>
<if test="SearchPojo.pathcode != null ">
   or Dm_PatrolRec.PathCode like concat('%', #{SearchPojo.pathcode}, '%')
</if>
<if test="SearchPojo.pathname != null ">
   or Dm_PatrolRec.PathName like concat('%', #{SearchPojo.pathname}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Dm_PatrolRec.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Dm_PatrolRec.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_PatrolRec.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_PatrolRec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_PatrolRec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_PatrolRec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Dm_PatrolRec.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Dm_PatrolRec.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_PatrolRec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_PatrolRec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_PatrolRec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_PatrolRec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_PatrolRec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_PatrolRec.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_PatrolRec.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_PatrolRec.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Dm_PatrolRec.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Dm_PatrolRec.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmPatrolrecPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 and Dm_PatrolRec.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_PatrolRec.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.refno != null ">
   and Dm_PatrolRec.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Dm_PatrolRec.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.planid != null ">
   and Dm_PatrolRec.Planid like concat('%', #{SearchPojo.planid}, '%')
</if>
<if test="SearchPojo.plancode != null ">
   and Dm_PatrolRec.PlanCode like concat('%', #{SearchPojo.plancode}, '%')
</if>
<if test="SearchPojo.planname != null ">
   and Dm_PatrolRec.PlanName like concat('%', #{SearchPojo.planname}, '%')
</if>
<if test="SearchPojo.pathid != null ">
   and Dm_PatrolRec.Pathid like concat('%', #{SearchPojo.pathid}, '%')
</if>
<if test="SearchPojo.pathcode != null ">
   and Dm_PatrolRec.PathCode like concat('%', #{SearchPojo.pathcode}, '%')
</if>
<if test="SearchPojo.pathname != null ">
   and Dm_PatrolRec.PathName like concat('%', #{SearchPojo.pathname}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Dm_PatrolRec.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Dm_PatrolRec.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_PatrolRec.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_PatrolRec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_PatrolRec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_PatrolRec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Dm_PatrolRec.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Dm_PatrolRec.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_PatrolRec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_PatrolRec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_PatrolRec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_PatrolRec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_PatrolRec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_PatrolRec.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_PatrolRec.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_PatrolRec.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Dm_PatrolRec.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Dm_PatrolRec.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Dm_PatrolRec.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Dm_PatrolRec.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.planid != null ">
   or Dm_PatrolRec.Planid like concat('%', #{SearchPojo.planid}, '%')
</if>
<if test="SearchPojo.plancode != null ">
   or Dm_PatrolRec.PlanCode like concat('%', #{SearchPojo.plancode}, '%')
</if>
<if test="SearchPojo.planname != null ">
   or Dm_PatrolRec.PlanName like concat('%', #{SearchPojo.planname}, '%')
</if>
<if test="SearchPojo.pathid != null ">
   or Dm_PatrolRec.Pathid like concat('%', #{SearchPojo.pathid}, '%')
</if>
<if test="SearchPojo.pathcode != null ">
   or Dm_PatrolRec.PathCode like concat('%', #{SearchPojo.pathcode}, '%')
</if>
<if test="SearchPojo.pathname != null ">
   or Dm_PatrolRec.PathName like concat('%', #{SearchPojo.pathname}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Dm_PatrolRec.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Dm_PatrolRec.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_PatrolRec.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_PatrolRec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_PatrolRec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_PatrolRec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Dm_PatrolRec.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Dm_PatrolRec.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_PatrolRec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_PatrolRec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_PatrolRec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_PatrolRec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_PatrolRec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_PatrolRec.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_PatrolRec.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_PatrolRec.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Dm_PatrolRec.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Dm_PatrolRec.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_PatrolRec(id, RefNo, BillDate, BillType, Planid, PlanCode, PlanName, Pathid, PathCode, PathName, Operator, Summary, Lister, Listerid, CreateDate, CreateBy, CreateByid, ModifyDate, Assessor, Assessorid, AssessDate, EnabledMark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{planid}, #{plancode}, #{planname}, #{pathid}, #{pathcode}, #{pathname}, #{operator}, #{summary}, #{lister}, #{listerid}, #{createdate}, #{createby}, #{createbyid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{enabledmark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_PatrolRec
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="planid != null ">
                Planid =#{planid},
            </if>
            <if test="plancode != null ">
                PlanCode =#{plancode},
            </if>
            <if test="planname != null ">
                PlanName =#{planname},
            </if>
            <if test="pathid != null ">
                Pathid =#{pathid},
            </if>
            <if test="pathcode != null ">
                PathCode =#{pathcode},
            </if>
            <if test="pathname != null ">
                PathName =#{pathname},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_PatrolRec where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                <!--通过主键审核数据-->
    <update id="approval">
        update Dm_PatrolRec SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
                                                                    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.std.eam.domain.pojo.DmPatrolrecPojo">
        select
          id
        from Dm_PatrolRecItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

</mapper>

