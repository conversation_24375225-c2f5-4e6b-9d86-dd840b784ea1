<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmPatrolpointMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmPatrolpointPojo">
        select
          id, PointGroupid, PointCode, PointName, Stdid, StdCode, StdName, RowNum, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Remark, StateCode, StateDate, Lister, Listerid, CreateBy, CreateByid, CreateDate, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolPoint
        where Dm_PatrolPoint.id = #{key} and Dm_PatrolPoint.Tenantid=#{tid}
    </select>
    <sql id="selectDmPatrolpointVo">
         select
          id, PointGroupid, PointCode, PointName, Stdid, StdCode, StdName, RowNum, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Remark, StateCode, StateDate, Lister, Listerid, CreateBy, CreateByid, CreateDate, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolPoint
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmPatrolpointPojo">
        <include refid="selectDmPatrolpointVo"/>
         where 1 = 1 and Dm_PatrolPoint.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_PatrolPoint.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pointgroupid != null ">
   and Dm_PatrolPoint.PointGroupid like concat('%', #{SearchPojo.pointgroupid}, '%')
</if>
<if test="SearchPojo.pointcode != null ">
   and Dm_PatrolPoint.PointCode like concat('%', #{SearchPojo.pointcode}, '%')
</if>
<if test="SearchPojo.pointname != null ">
   and Dm_PatrolPoint.PointName like concat('%', #{SearchPojo.pointname}, '%')
</if>
<if test="SearchPojo.stdid != null ">
   and Dm_PatrolPoint.Stdid like concat('%', #{SearchPojo.stdid}, '%')
</if>
<if test="SearchPojo.stdcode != null ">
   and Dm_PatrolPoint.StdCode like concat('%', #{SearchPojo.stdcode}, '%')
</if>
<if test="SearchPojo.stdname != null ">
   and Dm_PatrolPoint.StdName like concat('%', #{SearchPojo.stdname}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Dm_PatrolPoint.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Dm_PatrolPoint.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Dm_PatrolPoint.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Dm_PatrolPoint.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Dm_PatrolPoint.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Dm_PatrolPoint.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Dm_PatrolPoint.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Dm_PatrolPoint.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Dm_PatrolPoint.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Dm_PatrolPoint.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Dm_PatrolPoint.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Dm_PatrolPoint.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Dm_PatrolPoint.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Dm_PatrolPoint.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Dm_PatrolPoint.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Dm_PatrolPoint.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Dm_PatrolPoint.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pointgroupid != null ">
   or Dm_PatrolPoint.PointGroupid like concat('%', #{SearchPojo.pointgroupid}, '%')
</if>
<if test="SearchPojo.pointcode != null ">
   or Dm_PatrolPoint.PointCode like concat('%', #{SearchPojo.pointcode}, '%')
</if>
<if test="SearchPojo.pointname != null ">
   or Dm_PatrolPoint.PointName like concat('%', #{SearchPojo.pointname}, '%')
</if>
<if test="SearchPojo.stdid != null ">
   or Dm_PatrolPoint.Stdid like concat('%', #{SearchPojo.stdid}, '%')
</if>
<if test="SearchPojo.stdcode != null ">
   or Dm_PatrolPoint.StdCode like concat('%', #{SearchPojo.stdcode}, '%')
</if>
<if test="SearchPojo.stdname != null ">
   or Dm_PatrolPoint.StdName like concat('%', #{SearchPojo.stdname}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Dm_PatrolPoint.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Dm_PatrolPoint.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Dm_PatrolPoint.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Dm_PatrolPoint.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Dm_PatrolPoint.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Dm_PatrolPoint.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Dm_PatrolPoint.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Dm_PatrolPoint.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Dm_PatrolPoint.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Dm_PatrolPoint.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Dm_PatrolPoint.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Dm_PatrolPoint.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Dm_PatrolPoint.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Dm_PatrolPoint.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Dm_PatrolPoint.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Dm_PatrolPoint.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Dm_PatrolPoint.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_PatrolPoint(id, PointGroupid, PointCode, PointName, Stdid, StdCode, StdName, RowNum, EnabledMark, DeleteMark, DeleteLister, DeleteDate, Remark, StateCode, StateDate, Lister, Listerid, CreateBy, CreateByid, CreateDate, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pointgroupid}, #{pointcode}, #{pointname}, #{stdid}, #{stdcode}, #{stdname}, #{rownum}, #{enabledmark}, #{deletemark}, #{deletelister}, #{deletedate}, #{remark}, #{statecode}, #{statedate}, #{lister}, #{listerid}, #{createby}, #{createbyid}, #{createdate}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_PatrolPoint
        <set>
            <if test="pointgroupid != null ">
                PointGroupid =#{pointgroupid},
            </if>
            <if test="pointcode != null ">
                PointCode =#{pointcode},
            </if>
            <if test="pointname != null ">
                PointName =#{pointname},
            </if>
            <if test="stdid != null ">
                Stdid =#{stdid},
            </if>
            <if test="stdcode != null ">
                StdCode =#{stdcode},
            </if>
            <if test="stdname != null ">
                StdName =#{stdname},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_PatrolPoint where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                                                                    </mapper>

