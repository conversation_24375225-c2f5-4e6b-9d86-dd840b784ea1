<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmToolinspectitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmToolinspectitemPojo">
        <include refid="selectDmToolinspectitemVo"/>
        where Dm_ToolInspectItem.id = #{key} and Dm_ToolInspectItem.Tenantid=#{tid}
    </select>
    <sql id="selectDmToolinspectitemVo">
         select
id, Pid, Toolid, Result, MeasValue, CompDate, EquipUsed, ProcRef, NcHandling, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Dm_ToolInspectItem
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmToolinspectitemPojo">
        <include refid="selectDmToolinspectitemVo"/>
        where Dm_ToolInspectItem.Pid = #{Pid} and Dm_ToolInspectItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmToolinspectitemPojo">
        <include refid="selectDmToolinspectitemVo"/>
         where 1 = 1 and Dm_ToolInspectItem.Tenantid =#{tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_ToolInspectItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Dm_ToolInspectItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.toolid != null and SearchPojo.toolid != ''">
   and Dm_ToolInspectItem.toolid like concat('%', #{SearchPojo.toolid}, '%')
</if>
<if test="SearchPojo.result != null and SearchPojo.result != ''">
   and Dm_ToolInspectItem.result like concat('%', #{SearchPojo.result}, '%')
</if>
<if test="SearchPojo.equipused != null and SearchPojo.equipused != ''">
   and Dm_ToolInspectItem.equipused like concat('%', #{SearchPojo.equipused}, '%')
</if>
<if test="SearchPojo.procref != null and SearchPojo.procref != ''">
   and Dm_ToolInspectItem.procref like concat('%', #{SearchPojo.procref}, '%')
</if>
<if test="SearchPojo.nchandling != null and SearchPojo.nchandling != ''">
   and Dm_ToolInspectItem.nchandling like concat('%', #{SearchPojo.nchandling}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Dm_ToolInspectItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Dm_ToolInspectItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Dm_ToolInspectItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Dm_ToolInspectItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Dm_ToolInspectItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Dm_ToolInspectItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Dm_ToolInspectItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Dm_ToolInspectItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Dm_ToolInspectItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Dm_ToolInspectItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Dm_ToolInspectItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   and Dm_ToolInspectItem.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Dm_ToolInspectItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.toolid != null and SearchPojo.toolid != ''">
   or Dm_ToolInspectItem.Toolid like concat('%', #{SearchPojo.toolid}, '%')
</if>
<if test="SearchPojo.result != null and SearchPojo.result != ''">
   or Dm_ToolInspectItem.Result like concat('%', #{SearchPojo.result}, '%')
</if>
<if test="SearchPojo.equipused != null and SearchPojo.equipused != ''">
   or Dm_ToolInspectItem.EquipUsed like concat('%', #{SearchPojo.equipused}, '%')
</if>
<if test="SearchPojo.procref != null and SearchPojo.procref != ''">
   or Dm_ToolInspectItem.ProcRef like concat('%', #{SearchPojo.procref}, '%')
</if>
<if test="SearchPojo.nchandling != null and SearchPojo.nchandling != ''">
   or Dm_ToolInspectItem.NcHandling like concat('%', #{SearchPojo.nchandling}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Dm_ToolInspectItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Dm_ToolInspectItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Dm_ToolInspectItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Dm_ToolInspectItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Dm_ToolInspectItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Dm_ToolInspectItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Dm_ToolInspectItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Dm_ToolInspectItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Dm_ToolInspectItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Dm_ToolInspectItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Dm_ToolInspectItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   or Dm_ToolInspectItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_ToolInspectItem(id, Pid, Toolid, Result, MeasValue, CompDate, EquipUsed, ProcRef, NcHandling, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{toolid}, #{result}, #{measvalue}, #{compdate}, #{equipused}, #{procref}, #{nchandling}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_ToolInspectItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="toolid != null ">
                Toolid = #{toolid},
            </if>
            <if test="result != null ">
                Result = #{result},
            </if>
            <if test="measvalue != null">
                MeasValue = #{measvalue},
            </if>
            <if test="compdate != null">
                CompDate = #{compdate},
            </if>
            <if test="equipused != null ">
                EquipUsed = #{equipused},
            </if>
            <if test="procref != null ">
                ProcRef = #{procref},
            </if>
            <if test="nchandling != null ">
                NcHandling = #{nchandling},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_ToolInspectItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

