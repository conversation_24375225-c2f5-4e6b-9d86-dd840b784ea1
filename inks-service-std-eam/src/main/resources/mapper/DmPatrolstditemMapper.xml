<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmPatrolstditemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmPatrolstditemPojo">
        select
          id, Pid, ClauseGroupid, ClauseType, Clause, Severity, Requirement, AcceptPhoto, RejectPhoto, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolStdItem
        where Dm_PatrolStdItem.id = #{key} and Dm_PatrolStdItem.Tenantid=#{tid}
    </select>
    <sql id="selectDmPatrolstditemVo">
         select
          id, Pid, <PERSON>e<PERSON><PERSON><PERSON>, <PERSON>e<PERSON><PERSON>, <PERSON>e, <PERSON>verity, Requirement, AcceptPhoto, <PERSON>jectPhoto, <PERSON>N<PERSON>, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolStdItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmPatrolstditemPojo">
        <include refid="selectDmPatrolstditemVo"/>
         where 1 = 1 and Dm_PatrolStdItem.Tenantid =#{Tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_PatrolStdItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Dm_PatrolStdItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.clausegroupid != null and SearchPojo.clausegroupid != ''">
   and Dm_PatrolStdItem.clausegroupid like concat('%', #{SearchPojo.clausegroupid}, '%')
</if>
<if test="SearchPojo.clausetype != null and SearchPojo.clausetype != ''">
   and Dm_PatrolStdItem.clausetype like concat('%', #{SearchPojo.clausetype}, '%')
</if>
<if test="SearchPojo.clause != null and SearchPojo.clause != ''">
   and Dm_PatrolStdItem.clause like concat('%', #{SearchPojo.clause}, '%')
</if>
<if test="SearchPojo.severity != null and SearchPojo.severity != ''">
   and Dm_PatrolStdItem.severity like concat('%', #{SearchPojo.severity}, '%')
</if>
<if test="SearchPojo.requirement != null and SearchPojo.requirement != ''">
   and Dm_PatrolStdItem.requirement like concat('%', #{SearchPojo.requirement}, '%')
</if>
<if test="SearchPojo.acceptphoto != null and SearchPojo.acceptphoto != ''">
   and Dm_PatrolStdItem.acceptphoto like concat('%', #{SearchPojo.acceptphoto}, '%')
</if>
<if test="SearchPojo.rejectphoto != null and SearchPojo.rejectphoto != ''">
   and Dm_PatrolStdItem.rejectphoto like concat('%', #{SearchPojo.rejectphoto}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Dm_PatrolStdItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Dm_PatrolStdItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Dm_PatrolStdItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Dm_PatrolStdItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Dm_PatrolStdItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Dm_PatrolStdItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Dm_PatrolStdItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Dm_PatrolStdItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Dm_PatrolStdItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Dm_PatrolStdItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Dm_PatrolStdItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Dm_PatrolStdItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.clausegroupid != null and SearchPojo.clausegroupid != ''">
   or Dm_PatrolStdItem.ClauseGroupid like concat('%', #{SearchPojo.clausegroupid}, '%')
</if>
<if test="SearchPojo.clausetype != null and SearchPojo.clausetype != ''">
   or Dm_PatrolStdItem.ClauseType like concat('%', #{SearchPojo.clausetype}, '%')
</if>
<if test="SearchPojo.clause != null and SearchPojo.clause != ''">
   or Dm_PatrolStdItem.Clause like concat('%', #{SearchPojo.clause}, '%')
</if>
<if test="SearchPojo.severity != null and SearchPojo.severity != ''">
   or Dm_PatrolStdItem.Severity like concat('%', #{SearchPojo.severity}, '%')
</if>
<if test="SearchPojo.requirement != null and SearchPojo.requirement != ''">
   or Dm_PatrolStdItem.Requirement like concat('%', #{SearchPojo.requirement}, '%')
</if>
<if test="SearchPojo.acceptphoto != null and SearchPojo.acceptphoto != ''">
   or Dm_PatrolStdItem.AcceptPhoto like concat('%', #{SearchPojo.acceptphoto}, '%')
</if>
<if test="SearchPojo.rejectphoto != null and SearchPojo.rejectphoto != ''">
   or Dm_PatrolStdItem.RejectPhoto like concat('%', #{SearchPojo.rejectphoto}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Dm_PatrolStdItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Dm_PatrolStdItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Dm_PatrolStdItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Dm_PatrolStdItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Dm_PatrolStdItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Dm_PatrolStdItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Dm_PatrolStdItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Dm_PatrolStdItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Dm_PatrolStdItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Dm_PatrolStdItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Dm_PatrolStdItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmPatrolstditemPojo">
        select
          id, Pid, ClauseGroupid, ClauseType, Clause, Severity, Requirement, AcceptPhoto, RejectPhoto, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Dm_PatrolStdItem
        where Dm_PatrolStdItem.Pid = #{Pid} and Dm_PatrolStdItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_PatrolStdItem(id, Pid, ClauseGroupid, ClauseType, Clause, Severity, Requirement, AcceptPhoto, RejectPhoto, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{clausegroupid}, #{clausetype}, #{clause}, #{severity}, #{requirement}, #{acceptphoto}, #{rejectphoto}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_PatrolStdItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="clausegroupid != null ">
                ClauseGroupid = #{clausegroupid},
            </if>
            <if test="clausetype != null ">
                ClauseType = #{clausetype},
            </if>
            <if test="clause != null ">
                Clause = #{clause},
            </if>
            <if test="severity != null ">
                Severity = #{severity},
            </if>
            <if test="requirement != null ">
                Requirement = #{requirement},
            </if>
            <if test="acceptphoto != null ">
                AcceptPhoto = #{acceptphoto},
            </if>
            <if test="rejectphoto != null ">
                RejectPhoto = #{rejectphoto},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_PatrolStdItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

