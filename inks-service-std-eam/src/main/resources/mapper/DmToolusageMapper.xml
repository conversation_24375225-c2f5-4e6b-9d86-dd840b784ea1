<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmToolusageMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmToolusagePojo">
        <include refid="selectbillVo"/>
        where Dm_ToolUsage.id = #{key} and Dm_ToolUsage.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select
id, RefNo, BillDate, BillType, BillTitle, Operatorid, Operator, ItemCount, FinishCount, Summary, RowNum, CreateBy, CreateByid, <PERSON>reate<PERSON>ate, Lister, <PERSON>erid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision        from Dm_ToolUsage
    </sql>
    <sql id="selectdetailVo">
        select Dm_ToolUsage.RefNo,
               Dm_ToolUsage.BillDate,
               Dm_ToolUsage.BillType,
               Dm_ToolUsage.BillTitle,
               Dm_ToolUsage.Operator,
               Dm_ToolUsage.CreateBy,
               Dm_ToolUsage.Lister,
               Dm_ToolUsageItem.*,
               Dm_ToolInfo.ToolName,
               Dm_ToolInfo.ToolCode
        from Dm_ToolUsageItem
                 left join Dm_ToolUsage on Dm_ToolUsage.id = Dm_ToolUsageItem.Pid
                 left join Dm_ToolInfo on Dm_ToolInfo.id = Dm_ToolUsageItem.Toolid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmToolusageitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Dm_ToolUsage.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_ToolUsage.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Dm_ToolUsage.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Dm_ToolUsage.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Dm_ToolUsage.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Dm_ToolUsage.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Dm_ToolUsage.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Dm_ToolUsage.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Dm_ToolUsage.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Dm_ToolUsage.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Dm_ToolUsage.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Dm_ToolUsage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Dm_ToolUsage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Dm_ToolUsage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Dm_ToolUsage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Dm_ToolUsage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Dm_ToolUsage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Dm_ToolUsage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Dm_ToolUsage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Dm_ToolUsage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Dm_ToolUsage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Dm_ToolUsage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Dm_ToolUsage.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Dm_ToolUsage.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Dm_ToolUsage.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Dm_ToolUsage.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Dm_ToolUsage.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Dm_ToolUsage.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Dm_ToolUsage.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Dm_ToolUsage.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Dm_ToolUsage.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Dm_ToolUsage.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Dm_ToolUsage.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Dm_ToolUsage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Dm_ToolUsage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Dm_ToolUsage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Dm_ToolUsage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Dm_ToolUsage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Dm_ToolUsage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Dm_ToolUsage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Dm_ToolUsage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Dm_ToolUsage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Dm_ToolUsage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Dm_ToolUsage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Dm_ToolUsage.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Dm_ToolUsage.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmToolusagePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Dm_ToolUsage.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Dm_ToolUsage.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Dm_ToolUsage.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Dm_ToolUsage.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Dm_ToolUsage.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Dm_ToolUsage.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Dm_ToolUsage.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Dm_ToolUsage.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Dm_ToolUsage.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Dm_ToolUsage.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Dm_ToolUsage.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Dm_ToolUsage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Dm_ToolUsage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Dm_ToolUsage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Dm_ToolUsage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Dm_ToolUsage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Dm_ToolUsage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Dm_ToolUsage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Dm_ToolUsage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Dm_ToolUsage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Dm_ToolUsage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Dm_ToolUsage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Dm_ToolUsage.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Dm_ToolUsage.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Dm_ToolUsage.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Dm_ToolUsage.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Dm_ToolUsage.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Dm_ToolUsage.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Dm_ToolUsage.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Dm_ToolUsage.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Dm_ToolUsage.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Dm_ToolUsage.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Dm_ToolUsage.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Dm_ToolUsage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Dm_ToolUsage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Dm_ToolUsage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Dm_ToolUsage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Dm_ToolUsage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Dm_ToolUsage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Dm_ToolUsage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Dm_ToolUsage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Dm_ToolUsage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Dm_ToolUsage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Dm_ToolUsage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Dm_ToolUsage.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Dm_ToolUsage.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_ToolUsage(id, RefNo, BillDate, BillType, BillTitle, Operatorid, Operator, ItemCount, FinishCount, Summary, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{operatorid}, #{operator}, #{itemcount}, #{finishcount}, #{summary}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_ToolUsage
        <set>
            <if test="refno != null ">
            RefNo =#{refno},
        </if>
            <if test="billdate != null">
            BillDate =#{billdate},
        </if>
            <if test="billtype != null ">
            BillType =#{billtype},
        </if>
            <if test="billtitle != null ">
            BillTitle =#{billtitle},
        </if>
            <if test="operatorid != null ">
            Operatorid =#{operatorid},
        </if>
            <if test="operator != null ">
            Operator =#{operator},
        </if>
            <if test="itemcount != null">
            ItemCount =#{itemcount},
        </if>
            <if test="finishcount != null">
            FinishCount =#{finishcount},
        </if>
            <if test="summary != null ">
            Summary =#{summary},
        </if>
            <if test="rownum != null">
            RowNum =#{rownum},
        </if>
            <if test="createby != null ">
            CreateBy =#{createby},
        </if>
            <if test="createbyid != null ">
            CreateByid =#{createbyid},
        </if>
            <if test="createdate != null">
            CreateDate =#{createdate},
        </if>
            <if test="lister != null ">
            Lister =#{lister},
        </if>
            <if test="listerid != null ">
            Listerid =#{listerid},
        </if>
            <if test="modifydate != null">
            ModifyDate =#{modifydate},
        </if>
            <if test="custom1 != null ">
            Custom1 =#{custom1},
        </if>
            <if test="custom2 != null ">
            Custom2 =#{custom2},
        </if>
            <if test="custom3 != null ">
            Custom3 =#{custom3},
        </if>
            <if test="custom4 != null ">
            Custom4 =#{custom4},
        </if>
            <if test="custom5 != null ">
            Custom5 =#{custom5},
        </if>
            <if test="custom6 != null ">
            Custom6 =#{custom6},
        </if>
            <if test="custom7 != null ">
            Custom7 =#{custom7},
        </if>
            <if test="custom8 != null ">
            Custom8 =#{custom8},
        </if>
            <if test="custom9 != null ">
            Custom9 =#{custom9},
        </if>
            <if test="custom10 != null ">
            Custom10 =#{custom10},
        </if>
            <if test="deptid != null ">
            Deptid =#{deptid},
        </if>
            <if test="tenantname != null ">
            TenantName =#{tenantname},
        </if>
        Revision=Revision+1
    </set>
    where id = #{id} and Tenantid =#{tenantid}
</update>

        <!--通过主键删除-->
<delete id="delete">
delete from Dm_ToolUsage where id = #{key} and Tenantid=#{tid}
</delete>
        <!--查询DelListIds-->
<select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.std.eam.domain.pojo.DmToolusagePojo">
select
id
from Dm_ToolUsageItem
where Pid = #{id}
<if test="item !=null and item.size()>0">
    and id not in
    <foreach collection="item" open="(" close=")" separator="," item="item">
        <if test="item.id != null">
            #{item.id}
        </if>
        <if test="item.id == null">
            ''
        </if>
    </foreach>
</if>
</select>

</mapper>

