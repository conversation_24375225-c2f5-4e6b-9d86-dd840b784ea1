<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.std.eam.mapper.DmToolmaintenanceitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.std.eam.domain.pojo.DmToolmaintenanceitemPojo">
        <include refid="selectDmToolmaintenanceitemVo"/>
        where Dm_ToolMaintenanceItem.id = #{key} and Dm_ToolMaintenanceItem.Tenantid=#{tid}
    </select>
    <sql id="selectDmToolmaintenanceitemVo">
        select Dm_ToolMaintenanceItem.id,
               Dm_ToolMaintenanceItem.Pid,
               Dm_ToolMaintenanceItem.Toolid,
               Dm_ToolMaintenanceItem.MaintDate,
               Dm_ToolMaintenanceItem.RowNum,
               Dm_ToolMaintenanceItem.Remark,
               Dm_ToolMaintenanceItem.Custom1,
               Dm_ToolMaintenanceItem.Custom2,
               Dm_ToolMaintenanceItem.Custom3,
               Dm_ToolMaintenanceItem.Custom4,
               Dm_ToolMaintenanceItem.Custom5,
               Dm_ToolMaintenanceItem.Custom6,
               Dm_ToolMaintenanceItem.Custom7,
               Dm_ToolMaintenanceItem.Custom8,
               Dm_ToolMaintenanceItem.Custom9,
               Dm_ToolMaintenanceItem.Custom10,
               Dm_ToolMaintenanceItem.Tenantid,
               Dm_ToolMaintenanceItem.TenantName,
               Dm_ToolMaintenanceItem.Revision,
               Dm_ToolInfo.ToolName,
               Dm_ToolInfo.ToolCode
        from Dm_ToolMaintenanceItem
                 left join Dm_ToolInfo on Dm_ToolMaintenanceItem.Toolid = Dm_ToolInfo.id
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.std.eam.domain.pojo.DmToolmaintenanceitemPojo">
        <include refid="selectDmToolmaintenanceitemVo"/>
        where Dm_ToolMaintenanceItem.Pid = #{Pid} and Dm_ToolMaintenanceItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.std.eam.domain.pojo.DmToolmaintenanceitemPojo">
        <include refid="selectDmToolmaintenanceitemVo"/>
         where 1 = 1 and Dm_ToolMaintenanceItem.Tenantid =#{tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Dm_ToolMaintenanceItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Dm_ToolMaintenanceItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.toolid != null and SearchPojo.toolid != ''">
   and Dm_ToolMaintenanceItem.toolid like concat('%', #{SearchPojo.toolid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Dm_ToolMaintenanceItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Dm_ToolMaintenanceItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Dm_ToolMaintenanceItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Dm_ToolMaintenanceItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Dm_ToolMaintenanceItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Dm_ToolMaintenanceItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Dm_ToolMaintenanceItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Dm_ToolMaintenanceItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Dm_ToolMaintenanceItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Dm_ToolMaintenanceItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Dm_ToolMaintenanceItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   and Dm_ToolMaintenanceItem.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Dm_ToolMaintenanceItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.toolid != null and SearchPojo.toolid != ''">
   or Dm_ToolMaintenanceItem.Toolid like concat('%', #{SearchPojo.toolid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Dm_ToolMaintenanceItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Dm_ToolMaintenanceItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Dm_ToolMaintenanceItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Dm_ToolMaintenanceItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Dm_ToolMaintenanceItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Dm_ToolMaintenanceItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Dm_ToolMaintenanceItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Dm_ToolMaintenanceItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Dm_ToolMaintenanceItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Dm_ToolMaintenanceItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Dm_ToolMaintenanceItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   or Dm_ToolMaintenanceItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Dm_ToolMaintenanceItem(id, Pid, Toolid, MaintDate, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{toolid}, #{maintdate}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Dm_ToolMaintenanceItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="toolid != null ">
                Toolid = #{toolid},
            </if>
            <if test="maintdate != null">
                MaintDate = #{maintdate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Dm_ToolMaintenanceItem where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getLeastEntity" resultType="inks.service.std.eam.domain.pojo.DmToolmaintenanceitemPojo">
        select Dm_ToolMaintenanceItem.*,
               Dm_ToolInfo.ToolName,
               Dm_ToolInfo.ToolCode,
               Dm_ToolMaintenance.RefNo
        from Dm_ToolMaintenanceItem
                 left join Dm_ToolInfo on Dm_ToolMaintenanceItem.Toolid = Dm_ToolInfo.id
                 left join Dm_ToolMaintenance on Dm_ToolMaintenance.id = Dm_ToolMaintenanceItem.Pid
        where Dm_ToolMaintenanceItem.Toolid = #{toolid}
        order by Dm_ToolMaintenance.CreateDate desc
        limit 1
    </select>
</mapper>

