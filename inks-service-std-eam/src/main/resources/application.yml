server:
  port: 10180 #服务端口
  address: 0.0.0.0 #服务地址
spring:
  application:
    name: eam
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848



logging:
  level:
    org:
      springframework:
        security: info

pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true