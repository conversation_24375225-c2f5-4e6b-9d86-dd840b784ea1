<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IoT设备管理与MQTT监控平台</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome CSS for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .container-fluid {
            max-width: 1800px;
        }
        .card-header-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .status-online {
            color: #28a745;
            font-weight: bold;
        }
        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }
        .mqtt-stats {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .device-card {
            transition: transform 0.2s;
            border-left: 4px solid #667eea;
        }
        .device-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .nav-tabs .nav-link {
            font-weight: 500;
            border-radius: 10px 10px 0 0;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }
        .table-hover tbody tr:hover {
            background-color: #e3f2fd;
        }
        .connection-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .connection-online {
            background-color: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }
        .connection-offline {
            background-color: #dc3545;
        }
        .refresh-btn {
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>

<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary">
            <i class="fas fa-microchip me-2"></i>IoT设备管理与MQTT监控平台
        </h2>
        <div>
            <button class="btn btn-outline-primary me-2" onclick="refreshAllData()">
                <i class="fas fa-sync-alt" id="refreshIcon"></i> 刷新数据
            </button>
            <span class="badge bg-success fs-6">系统运行中</span>
        </div>
    </div>

    <!-- MQTT Statistics Dashboard -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="totalDevices">0</div>
                <div class="stats-label">总设备数</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number status-online" id="onlineDevices">0</div>
                <div class="stats-label">在线设备</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number status-offline" id="offlineDevices">0</div>
                <div class="stats-label">离线设备</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="activeConnections">0</div>
                <div class="stats-label">活跃连接</div>
            </div>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs" id="managementTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="device-list-tab" data-bs-toggle="tab" data-bs-target="#device-list-pane" type="button" role="tab">
                <i class="fas fa-list me-1"></i> 设备列表
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="mqtt-monitor-tab" data-bs-toggle="tab" data-bs-target="#mqtt-monitor-pane" type="button" role="tab">
                <i class="fas fa-wifi me-1"></i> MQTT监控
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="token-management-tab" data-bs-toggle="tab" data-bs-target="#token-management-pane" type="button" role="tab">
                <i class="fas fa-key me-1"></i> Token管理
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="system-tools-tab" data-bs-toggle="tab" data-bs-target="#system-tools-pane" type="button" role="tab">
                <i class="fas fa-tools me-1"></i> 系统工具
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="managementTabContent">
        
        <!-- Device List Pane -->
        <div class="tab-pane fade show active" id="device-list-pane" role="tabpanel">
            <div class="card shadow-sm mt-3">
                <div class="card-header card-header-custom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-microchip me-2"></i>设备管理</h5>
                        <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#deviceModal">
                            <i class="fas fa-plus me-1"></i> 新增设备
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="searchDevice" placeholder="搜索设备名称、SN码或Token...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterDeviceType">
                                <option value="">所有设备类型</option>
                                <option value="sensor">传感器</option>
                                <option value="gateway">网关</option>
                                <option value="controller">控制器</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterConnectionStatus">
                                <option value="">所有连接状态</option>
                                <option value="online">在线</option>
                                <option value="offline">离线</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="searchDevices()">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>

                    <!-- Device Table -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th>连接状态</th>
                                    <th>设备ID</th>
                                    <th>设备名称</th>
                                    <th>设备类型</th>
                                    <th>SN码</th>
                                    <th>Token</th>
                                    <th>最后更新</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="deviceTableBody">
                                <!-- 动态加载设备数据 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="设备分页">
                        <ul class="pagination justify-content-center" id="devicePagination">
                            <!-- 动态生成分页 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- MQTT Monitor Pane -->
        <div class="tab-pane fade" id="mqtt-monitor-pane" role="tabpanel">
            <div class="card shadow-sm mt-3">
                <div class="card-header card-header-custom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-wifi me-2"></i>MQTT连接监控</h5>
                        <div>
                            <button class="btn btn-light btn-sm me-2" onclick="refreshMqttStatus()">
                                <i class="fas fa-sync-alt"></i> 刷新状态
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="cleanupConnections()">
                                <i class="fas fa-broom"></i> 清理无效连接
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Real-time Connection Status -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="mqtt-stats">
                                <div class="row text-center">
                                    <div class="col-md-4">
                                        <h3 id="mqttTotalConnections">0</h3>
                                        <p>总连接数</p>
                                    </div>
                                    <div class="col-md-4">
                                        <h3 id="mqttActiveTokens">0</h3>
                                        <p>活跃Token</p>
                                    </div>
                                    <div class="col-md-4">
                                        <h3 id="mqttLastUpdate">--:--:--</h3>
                                        <p>最后更新</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Connected Devices List -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>连接状态</th>
                                    <th>设备Token</th>
                                    <th>设备ID</th>
                                    <th>设备名称</th>
                                    <th>连接时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="mqttConnectionsTableBody">
                                <!-- 动态加载连接数据 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Token Management Pane -->
        <div class="tab-pane fade" id="token-management-pane" role="tabpanel">
            <div class="card shadow-sm mt-3">
                <div class="card-header card-header-custom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-key me-2"></i>Token管理</h5>
                        <button class="btn btn-light btn-sm" onclick="generateNewToken()">
                            <i class="fas fa-plus me-1"></i> 生成Token
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Token Generation Form -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <label class="form-label">设备ID</label>
                            <input type="text" class="form-control" id="tokenDeviceId" placeholder="输入设备ID">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">设备类型</label>
                            <select class="form-select" id="tokenDeviceType">
                                <option value="sensor">传感器</option>
                                <option value="gateway">网关</option>
                                <option value="controller">控制器</option>
                                <option value="iphone">iPhone</option>
                                <option value="android">Android</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button class="btn btn-primary w-100" onclick="generateToken()">
                                <i class="fas fa-key"></i> 生成Token
                            </button>
                        </div>
                    </div>

                    <!-- Token List -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Token</th>
                                    <th>设备ID</th>
                                    <th>状态</th>
                                    <th>在线状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="tokenTableBody">
                                <!-- 动态加载Token数据 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Tools Pane -->
        <div class="tab-pane fade" id="system-tools-pane" role="tabpanel">
            <div class="card shadow-sm mt-3">
                <div class="card-header card-header-custom">
                    <h5 class="mb-0"><i class="fas fa-tools me-2"></i>系统工具</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-database me-2"></i>Redis管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">管理Redis缓存和数据同步</p>
                                    <button class="btn btn-primary btn-sm me-2" onclick="refreshRedisCache()">
                                        <i class="fas fa-sync"></i> 刷新缓存
                                    </button>
                                    <button class="btn btn-info btn-sm me-2" onclick="checkRedisHealth()">
                                        <i class="fas fa-heartbeat"></i> 健康检查
                                    </button>
                                    <button class="btn btn-warning btn-sm" onclick="checkDataConsistency()">
                                        <i class="fas fa-check-double"></i> 一致性检查
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-network-wired me-2"></i>连接管理</h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">管理MQTT连接和清理操作</p>
                                    <button class="btn btn-success btn-sm me-2" onclick="getAllConnections()">
                                        <i class="fas fa-list"></i> 查看连接
                                    </button>
                                    <button class="btn btn-warning btn-sm me-2" onclick="cleanupConnections()">
                                        <i class="fas fa-broom"></i> 清理连接
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="disconnectAllDevices()">
                                        <i class="fas fa-power-off"></i> 断开所有
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Status -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>系统状态</h6>
                                </div>
                                <div class="card-body">
                                    <div id="systemStatus">
                                        <div class="row text-center">
                                            <div class="col-md-3">
                                                <div class="border rounded p-3">
                                                    <h5 class="text-primary" id="redisStatus">检查中...</h5>
                                                    <small class="text-muted">Redis状态</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="border rounded p-3">
                                                    <h5 class="text-success" id="dbStatus">正常</h5>
                                                    <small class="text-muted">数据库状态</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="border rounded p-3">
                                                    <h5 class="text-info" id="mqttStatus">运行中</h5>
                                                    <small class="text-muted">MQTT服务</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="border rounded p-3">
                                                    <h5 class="text-warning" id="syncStatus">同步中</h5>
                                                    <small class="text-muted">数据同步</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Device Modal -->
<div class="modal fade" id="deviceModal" tabindex="-1" aria-labelledby="deviceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header card-header-custom">
                <h5 class="modal-title" id="deviceModalLabel">
                    <i class="fas fa-microchip me-2"></i>设备信息
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="deviceForm">
                    <input type="hidden" id="deviceId" name="id">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="devname" class="form-label">设备名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="devname" name="devname" required>
                        </div>
                        <div class="col-md-6">
                            <label for="devsn" class="form-label">设备SN码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="devsn" name="devsn" required>
                        </div>
                        <div class="col-md-6">
                            <label for="devtype" class="form-label">设备类型</label>
                            <select class="form-select" id="devtype" name="devtype">
                                <option value="sensor">传感器</option>
                                <option value="gateway">网关</option>
                                <option value="controller">控制器</option>
                                <option value="actuator">执行器</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="devlabel" class="form-label">设备标签</label>
                            <input type="text" class="form-control" id="devlabel" name="devlabel">
                        </div>
                        <div class="col-md-6">
                            <label for="token" class="form-label">MQTT Token</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="token" name="token">
                                <button class="btn btn-outline-secondary" type="button" onclick="generateTokenForDevice()">
                                    <i class="fas fa-key"></i> 生成
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="customerid" class="form-label">客户ID</label>
                            <input type="text" class="form-control" id="customerid" name="customerid">
                        </div>
                        <div class="col-md-12">
                            <label for="devicedata" class="form-label">设备数据 (JSON)</label>
                            <textarea class="form-control" id="devicedata" name="devicedata" rows="3" placeholder='{"key": "value"}'></textarea>
                        </div>
                        <div class="col-md-12">
                            <label for="remark" class="form-label">备注</label>
                            <textarea class="form-control" id="remark" name="remark" rows="2"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" onclick="saveDevice()">
                    <i class="fas fa-save me-1"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Disconnect Device Modal -->
<div class="modal fade" id="disconnectModal" tabindex="-1" aria-labelledby="disconnectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="disconnectModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>断开设备连接
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要断开以下设备的MQTT连接吗？</p>
                <div class="alert alert-info">
                    <strong>设备信息：</strong><br>
                    <span id="disconnectDeviceInfo"></span>
                </div>
                <div class="mb-3">
                    <label for="disconnectReason" class="form-label">断开原因</label>
                    <input type="text" class="form-control" id="disconnectReason" value="管理员主动断开" placeholder="请输入断开原因">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="confirmDisconnect()">
                    <i class="fas fa-power-off me-1"></i> 断开连接
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 5 JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
// 全局变量
let currentPage = 1;
let pageSize = 10;
let currentDeviceForDisconnect = null;
let refreshInterval = null;

// API基础URL
const API_BASE_URL = '';
const API_BASE = `${API_BASE_URL}/api/device-tokens`;
const DEVICE_API_BASE = `${API_BASE_URL}/D09M30B1`; // 使用D09M30B1Controller

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    startAutoRefresh();
});

// 初始化页面
function initializePage() {
    loadDeviceList();
    loadMqttStatus();
    loadTokenList();
    checkSystemHealth();
}

// 开始自动刷新
function startAutoRefresh() {
    refreshInterval = setInterval(() => {
        loadMqttStatus();
        updateConnectionStatus();
    }, 30000); // 30秒刷新一次
}

// 停止自动刷新
function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
}

// 刷新所有数据
function refreshAllData() {
    const refreshIcon = document.getElementById('refreshIcon');
    refreshIcon.classList.add('refresh-btn');

    Promise.all([
        loadDeviceList(),
        loadMqttStatus(),
        loadTokenList(),
        checkSystemHealth()
    ]).finally(() => {
        refreshIcon.classList.remove('refresh-btn');
        showToast('数据刷新完成', 'success');
    });
}

// 加载设备列表
async function loadDeviceList(searchTerm = '', deviceType = '', connectionStatus = '') {
    try {
        // 构建QueryParam对象，符合后端API格式
        const queryParam = {
            pageNum: currentPage,
            pageSize: pageSize,
            searchPojo: null,
            searchType: 0,
            filterstr: "",
            dateRange: null
        };

        // 如果有搜索条件，添加到filterstr中
        if (searchTerm) {
            queryParam.filterstr += ` and (Iot_Device.DevName like '%${searchTerm}%' or Iot_Device.DevSN like '%${searchTerm}%' or Iot_Device.Id like '%${searchTerm}%')`;
        }

        if (deviceType) {
            queryParam.filterstr += ` and Iot_Device.DevType = '${deviceType}'`;
        }

        const response = await fetch(`${DEVICE_API_BASE}/getPageList`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'b8'
            },
            body: JSON.stringify(queryParam)
        });

        if (response.ok) {
            const result = await response.json();
            if (result.code === 200) {
                displayDeviceList(result.data.list || []);
                updateDeviceStats(result.data.list || []);
                generatePagination(result.data);
            } else {
                console.error('加载设备列表失败:', result.msg);
                showToast('加载设备列表失败: ' + result.msg, 'error');
                // 使用模拟数据
                loadMockDeviceData();
            }
        } else {
            console.error('API请求失败:', response.status);
            showToast('API请求失败', 'error');
            loadMockDeviceData();
        }
    } catch (error) {
        console.error('加载设备列表出错:', error);
        showToast('加载设备列表出错', 'error');
        loadMockDeviceData();
    }
}

// 加载模拟设备数据（用于演示）
function loadMockDeviceData() {
    const mockDevices = [
        {
            id: 'DEV001',
            devname: '温度传感器01',
            devtype: 'sensor',
            devsn: 'SN001234567',
            token: 'sensor_DEV001_abc12345',
            modifydate: '2025-01-11 10:30:00',
            remark: '车间A区温度监控'
        },
        {
            id: 'DEV002',
            devname: '智能网关02',
            devtype: 'gateway',
            devsn: 'SN002345678',
            token: 'gateway_DEV002_def67890',
            modifydate: '2025-01-11 09:15:00',
            remark: '主控制网关'
        },
        {
            id: 'DEV003',
            devname: '压力控制器03',
            devtype: 'controller',
            devsn: 'SN003456789',
            token: 'controller_DEV003_ghi11111',
            modifydate: '2025-01-11 08:45:00',
            remark: '液压系统控制'
        }
    ];

    displayDeviceList(mockDevices);
    updateDeviceStats(mockDevices);
}

// 显示设备列表
function displayDeviceList(devices) {
    const tbody = document.getElementById('deviceTableBody');
    tbody.innerHTML = '';

    devices.forEach(device => {
        const row = document.createElement('tr');
        row.className = 'device-row';

        // 检查设备在线状态（这里需要调用MQTT API）
        checkDeviceOnlineStatus(device.token).then(isOnline => {
            row.innerHTML = `
                <td>
                    <span class="connection-indicator ${isOnline ? 'connection-online' : 'connection-offline'}"></span>
                    <span class="${isOnline ? 'status-online' : 'status-offline'}">
                        ${isOnline ? '在线' : '离线'}
                    </span>
                </td>
                <td>${device.id || ''}</td>
                <td>${device.devname || ''}</td>
                <td>
                    <span class="badge bg-secondary">${getDeviceTypeLabel(device.devtype)}</span>
                </td>
                <td><code>${device.devsn || ''}</code></td>
                <td>
                    <small class="text-muted">${device.token ? device.token.substring(0, 20) + '...' : '未设置'}</small>
                </td>
                <td>${formatDateTime(device.modifydate)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editDevice('${device.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewDeviceDetails('${device.id}')" title="详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${device.token && isOnline ? `
                        <button class="btn btn-outline-warning" onclick="showDisconnectModal('${device.id}', '${device.devname}', '${device.token}')" title="断开连接">
                            <i class="fas fa-power-off"></i>
                        </button>
                        ` : ''}
                        <button class="btn btn-outline-danger" onclick="deleteDevice('${device.id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
        });

        tbody.appendChild(row);
    });
}

// 检查设备在线状态
async function checkDeviceOnlineStatus(token) {
    if (!token) return false;

    try {
        const response = await fetch(`${API_BASE}/token-online?token=${encodeURIComponent(token)}`, {
            headers: {
                'authorization': 'b8'
            }
        });
        if (response.ok) {
            const result = await response.json();
            return result.online || false;
        }
    } catch (error) {
        console.error('检查设备在线状态失败:', error);
    }
    return false;
}

// 更新设备统计
function updateDeviceStats(devices) {
    document.getElementById('totalDevices').textContent = devices.length;

    // 这里需要异步检查每个设备的在线状态
    let onlineCount = 0;
    const promises = devices.map(device => {
        if (device.token) {
            return checkDeviceOnlineStatus(device.token).then(isOnline => {
                if (isOnline) onlineCount++;
            });
        }
        return Promise.resolve();
    });

    Promise.all(promises).then(() => {
        document.getElementById('onlineDevices').textContent = onlineCount;
        document.getElementById('offlineDevices').textContent = devices.length - onlineCount;
    });
}

// 加载MQTT状态
async function loadMqttStatus() {
    try {
        const response = await fetch(`${API_BASE}/connection-status`, {
            headers: {
                'authorization': 'b8'
            }
        });
        if (response.ok) {
            const result = await response.json();
            updateMqttStats(result);
            loadConnectedDevices(result.connectedTokens);
        }
    } catch (error) {
        console.error('加载MQTT状态失败:', error);
    }
}

// 更新MQTT统计
function updateMqttStats(data) {
    document.getElementById('activeConnections').textContent = data.connectionCount || 0;
    document.getElementById('mqttTotalConnections').textContent = data.connectionCount || 0;
    document.getElementById('mqttActiveTokens').textContent = data.connectedTokens ? data.connectedTokens.length : 0;
    document.getElementById('mqttLastUpdate').textContent = new Date().toLocaleTimeString();
}

// 加载已连接设备
async function loadConnectedDevices(connectedTokens) {
    const tbody = document.getElementById('mqttConnectionsTableBody');
    tbody.innerHTML = '';

    if (!connectedTokens || connectedTokens.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无设备连接</td></tr>';
        return;
    }

    for (const token of connectedTokens) {
        try {
            // 这里需要根据token获取设备信息
            const deviceInfo = await getDeviceInfoByToken(token);
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <span class="connection-indicator connection-online"></span>
                    <span class="status-online">在线</span>
                </td>
                <td><code>${token.substring(0, 20)}...</code></td>
                <td>${deviceInfo ? deviceInfo.id : '未知'}</td>
                <td>${deviceInfo ? deviceInfo.devname : '未知设备'}</td>
                <td>${new Date().toLocaleString()}</td>
                <td>
                    <button class="btn btn-sm btn-outline-warning" onclick="disconnectByToken('${token}')">
                        <i class="fas fa-power-off"></i> 断开
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        } catch (error) {
            console.error('获取设备信息失败:', error);
        }
    }
}

// 根据token获取设备信息
async function getDeviceInfoByToken(token) {
    // 这里需要实现根据token查询设备信息的API
    // 暂时返回模拟数据
    return {
        id: 'DEV' + token.substring(token.length - 3),
        devname: '设备' + token.substring(token.length - 3)
    };
}

// 设备类型标签
function getDeviceTypeLabel(type) {
    const labels = {
        'sensor': '传感器',
        'gateway': '网关',
        'controller': '控制器',
        'actuator': '执行器'
    };
    return labels[type] || type || '未知';
}

// 格式化日期时间
function formatDateTime(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN');
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建简单的提示框
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed top-0 end-0 m-3`;
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    // 3秒后自动消失
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}

// 搜索设备
function searchDevices() {
    const searchTerm = document.getElementById('searchDevice').value;
    const deviceType = document.getElementById('filterDeviceType').value;
    const connectionStatus = document.getElementById('filterConnectionStatus').value;

    // 重置页码
    currentPage = 1;

    // 重新加载设备列表（带搜索条件）
    loadDeviceList(searchTerm, deviceType, connectionStatus);
}

// 编辑设备
function editDevice(deviceId) {
    // 获取设备详情并填充表单
    fetch(`${DEVICE_API_BASE}/getEntity?key=${deviceId}`, {
        headers: {
            'authorization': 'b8'
        }
    })
        .then(response => response.json())
        .then(result => {
            if (result.code === 200) {
                fillDeviceForm(result.data);
                const modal = new bootstrap.Modal(document.getElementById('deviceModal'));
                modal.show();
            } else {
                showToast('获取设备信息失败: ' + result.msg, 'error');
            }
        })
        .catch(error => {
            console.error('获取设备信息失败:', error);
            showToast('获取设备信息失败', 'error');
        });
}

// 填充设备表单
function fillDeviceForm(device) {
    document.getElementById('deviceId').value = device.id || '';
    document.getElementById('devname').value = device.devname || '';
    document.getElementById('devsn').value = device.devsn || '';
    document.getElementById('devtype').value = device.devtype || 'sensor';
    document.getElementById('devlabel').value = device.devlabel || '';
    document.getElementById('token').value = device.token || '';
    document.getElementById('customerid').value = device.customerid || '';
    document.getElementById('devicedata').value = device.devicedata || '';
    document.getElementById('remark').value = device.remark || '';
}

// 清空设备表单
function clearDeviceForm() {
    document.getElementById('deviceForm').reset();
    document.getElementById('deviceId').value = '';
}

// 保存设备
function saveDevice() {
    const form = document.getElementById('deviceForm');
    const formData = new FormData(form);
    const deviceData = Object.fromEntries(formData.entries());

    // 验证必填字段
    if (!deviceData.devname || !deviceData.devsn) {
        showToast('请填写设备名称和SN码', 'warning');
        return;
    }

    const isEdit = !!deviceData.id;
    const url = isEdit ? `${DEVICE_API_BASE}/update` : `${DEVICE_API_BASE}/create`;

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'b8'
        },
        body: JSON.stringify(deviceData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            showToast(isEdit ? '设备更新成功' : '设备创建成功', 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('deviceModal'));
            modal.hide();
            loadDeviceList(); // 重新加载列表
        } else {
            showToast('保存失败: ' + result.msg, 'error');
        }
    })
    .catch(error => {
        console.error('保存设备失败:', error);
        showToast('保存设备失败', 'error');
    });
}

// 删除设备
function deleteDevice(deviceId) {
    if (confirm('确定要删除这个设备吗？此操作不可恢复。')) {
        fetch(`${DEVICE_API_BASE}/delete?key=${deviceId}`, {
            method: 'GET',
            headers: {
                'authorization': 'b8'
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.code === 200) {
                showToast('设备删除成功', 'success');
                loadDeviceList(); // 重新加载列表
            } else {
                showToast('删除失败: ' + result.msg, 'error');
            }
        })
        .catch(error => {
            console.error('删除设备失败:', error);
            showToast('删除设备失败', 'error');
        });
    }
}

// 查看设备详情
function viewDeviceDetails(deviceId) {
    // 可以实现一个详情查看模态框
    editDevice(deviceId); // 暂时复用编辑功能
}

// 为设备生成Token
function generateTokenForDevice() {
    const deviceId = document.getElementById('deviceId').value;
    const deviceType = document.getElementById('devtype').value;

    if (!deviceId) {
        showToast('请先保存设备后再生成Token', 'warning');
        return;
    }

    fetch(`${API_BASE}/generate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'authorization': 'b8'
        },
        body: `deviceId=${encodeURIComponent(deviceId)}&deviceType=${encodeURIComponent(deviceType)}`
    })
    .then(response => response.json())
    .then(result => {
        document.getElementById('token').value = result.token;
        showToast('Token生成成功', 'success');
    })
    .catch(error => {
        console.error('生成Token失败:', error);
        showToast('生成Token失败', 'error');
    });
}

// 显示断开连接模态框
function showDisconnectModal(deviceId, deviceName, token) {
    currentDeviceForDisconnect = { deviceId, deviceName, token };
    document.getElementById('disconnectDeviceInfo').innerHTML = `
        <strong>设备ID:</strong> ${deviceId}<br>
        <strong>设备名称:</strong> ${deviceName}<br>
        <strong>Token:</strong> ${token.substring(0, 20)}...
    `;

    const modal = new bootstrap.Modal(document.getElementById('disconnectModal'));
    modal.show();
}

// 确认断开连接
function confirmDisconnect() {
    if (!currentDeviceForDisconnect) return;

    const reason = document.getElementById('disconnectReason').value || '管理员主动断开';

    fetch(`${API_BASE}/disconnect-by-token`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'authorization': 'b8'
        },
        body: `token=${encodeURIComponent(currentDeviceForDisconnect.token)}&reason=${encodeURIComponent(reason)}`
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showToast('设备连接已断开', 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('disconnectModal'));
            modal.hide();
            loadDeviceList(); // 刷新设备列表
            loadMqttStatus(); // 刷新MQTT状态
        } else {
            showToast('断开连接失败: ' + result.message, 'error');
        }
    })
    .catch(error => {
        console.error('断开连接失败:', error);
        showToast('断开连接失败', 'error');
    });
}

// 通过Token断开连接
function disconnectByToken(token) {
    if (confirm('确定要断开此设备的连接吗？')) {
        fetch(`${API_BASE}/disconnect-by-token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'authorization': 'b8'
            },
            body: `token=${encodeURIComponent(token)}&reason=管理员主动断开`
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast('设备连接已断开', 'success');
                loadMqttStatus(); // 刷新MQTT状态
            } else {
                showToast('断开连接失败: ' + result.message, 'error');
            }
        })
        .catch(error => {
            console.error('断开连接失败:', error);
            showToast('断开连接失败', 'error');
        });
    }
}

// 刷新MQTT状态
function refreshMqttStatus() {
    loadMqttStatus();
    showToast('MQTT状态已刷新', 'info');
}

// 更新连接状态
function updateConnectionStatus() {
    // 更新设备列表中的连接状态
    const deviceRows = document.querySelectorAll('.device-row');
    deviceRows.forEach(row => {
        // 这里可以实现更精细的状态更新逻辑
    });
}

// ===== Token管理功能 =====

// 加载Token列表
async function loadTokenList() {
    try {
        const response = await fetch(`${API_BASE}`);
        if (response.ok) {
            const tokens = await response.json();
            displayTokenList(tokens);
        }
    } catch (error) {
        console.error('加载Token列表失败:', error);
    }
}

// 显示Token列表
async function displayTokenList(tokens) {
    const tbody = document.getElementById('tokenTableBody');
    tbody.innerHTML = '';

    for (const [token, deviceId] of Object.entries(tokens)) {
        const isValid = await validateToken(token);
        const isOnline = await checkDeviceOnlineStatus(token);

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <code class="text-break">${token.substring(0, 30)}...</code>
                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('${token}')">
                    <i class="fas fa-copy"></i>
                </button>
            </td>
            <td>${deviceId}</td>
            <td>
                <span class="badge ${isValid ? 'bg-success' : 'bg-danger'}">
                    ${isValid ? '有效' : '无效'}
                </span>
            </td>
            <td>
                <span class="connection-indicator ${isOnline ? 'connection-online' : 'connection-offline'}"></span>
                <span class="${isOnline ? 'status-online' : 'status-offline'}">
                    ${isOnline ? '在线' : '离线'}
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-info" onclick="validateTokenManually('${token}')" title="验证">
                        <i class="fas fa-check"></i>
                    </button>
                    ${isOnline ? `
                    <button class="btn btn-outline-warning" onclick="disconnectByToken('${token}')" title="断开">
                        <i class="fas fa-power-off"></i>
                    </button>
                    ` : ''}
                    <button class="btn btn-outline-danger" onclick="removeToken('${token}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    }
}

// 生成新Token
function generateNewToken() {
    const deviceId = document.getElementById('tokenDeviceId').value;
    const deviceType = document.getElementById('tokenDeviceType').value;

    if (!deviceId) {
        showToast('请输入设备ID', 'warning');
        return;
    }

    generateToken();
}

// 生成Token
function generateToken() {
    const deviceId = document.getElementById('tokenDeviceId').value;
    const deviceType = document.getElementById('tokenDeviceType').value;

    fetch(`${API_BASE}/generate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'authorization': 'b8'
        },
        body: `deviceId=${encodeURIComponent(deviceId)}&deviceType=${encodeURIComponent(deviceType)}`
    })
    .then(response => response.json())
    .then(result => {
        showToast(`Token生成成功: ${result.token}`, 'success');
        loadTokenList(); // 刷新Token列表
        // 清空输入框
        document.getElementById('tokenDeviceId').value = '';
    })
    .catch(error => {
        console.error('生成Token失败:', error);
        showToast('生成Token失败', 'error');
    });
}

// 验证Token
async function validateToken(token) {
    try {
        const response = await fetch(`${API_BASE}/validate?token=${encodeURIComponent(token)}`, {
            headers: {
                'authorization': 'b8'
            }
        });
        if (response.ok) {
            const result = await response.json();
            return result.valid;
        }
    } catch (error) {
        console.error('验证Token失败:', error);
    }
    return false;
}

// 手动验证Token
function validateTokenManually(token) {
    validateToken(token).then(isValid => {
        showToast(`Token ${isValid ? '有效' : '无效'}`, isValid ? 'success' : 'warning');
    });
}

// 删除Token
function removeToken(token) {
    if (confirm('确定要删除这个Token吗？')) {
        fetch(`${API_BASE}?token=${encodeURIComponent(token)}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast('Token删除成功', 'success');
                loadTokenList(); // 刷新Token列表
            } else {
                showToast('Token删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('删除Token失败:', error);
            showToast('删除Token失败', 'error');
        });
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('已复制到剪贴板', 'success');
    }).catch(err => {
        console.error('复制失败:', err);
        showToast('复制失败', 'error');
    });
}

// ===== 系统工具功能 =====

// 刷新Redis缓存
function refreshRedisCache() {
    fetch(`${API_BASE}/refresh-redis`, {
        method: 'POST',
        headers: {
            'authorization': 'b8'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showToast(`Redis缓存刷新成功，同步了${result.syncCount}个Token`, 'success');
            document.getElementById('syncStatus').textContent = '已同步';
            document.getElementById('syncStatus').className = 'text-success';
        } else {
            showToast('Redis缓存刷新失败', 'error');
        }
    })
    .catch(error => {
        console.error('刷新Redis缓存失败:', error);
        showToast('刷新Redis缓存失败', 'error');
    });
}

// 检查Redis健康状态
function checkRedisHealth() {
    fetch(`${API_BASE}/redis-health`, {
        headers: {
            'authorization': 'b8'
        }
    })
    .then(response => response.json())
    .then(result => {
        const status = result.healthy ? '正常' : '异常';
        const className = result.healthy ? 'text-success' : 'text-danger';

        document.getElementById('redisStatus').textContent = status;
        document.getElementById('redisStatus').className = className;

        showToast(`Redis状态: ${result.message}`, result.healthy ? 'success' : 'error');
    })
    .catch(error => {
        console.error('检查Redis健康状态失败:', error);
        document.getElementById('redisStatus').textContent = '检查失败';
        document.getElementById('redisStatus').className = 'text-danger';
    });
}

// 检查数据一致性
function checkDataConsistency() {
    fetch(`${API_BASE}/consistency-check`, {
        headers: {
            'authorization': 'b8'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const data = result.data;
            showToast(`一致性检查完成 - 数据库:${data.databaseCount}, Redis:${data.redisCount}, 不一致:${data.inconsistentCount}`, 'info');

            if (data.inconsistentCount > 0) {
                if (confirm(`发现${data.inconsistentCount}个不一致项，是否立即修复？`)) {
                    repairDataInconsistency();
                }
            }
        }
    })
    .catch(error => {
        console.error('检查数据一致性失败:', error);
        showToast('检查数据一致性失败', 'error');
    });
}

// 修复数据不一致
function repairDataInconsistency() {
    fetch(`${API_BASE}/repair-inconsistency`, {
        method: 'POST',
        headers: {
            'authorization': 'b8'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showToast(`数据不一致修复完成，修复了${result.repairCount}个项目`, 'success');
        } else {
            showToast('数据不一致修复失败', 'error');
        }
    })
    .catch(error => {
        console.error('修复数据不一致失败:', error);
        showToast('修复数据不一致失败', 'error');
    });
}

// 清理无效连接
function cleanupConnections() {
    fetch(`${API_BASE}/cleanup-connections`, {
        method: 'POST',
        headers: {
            'authorization': 'b8'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showToast('无效连接清理完成', 'success');
            loadMqttStatus(); // 刷新MQTT状态
        } else {
            showToast('清理无效连接失败', 'error');
        }
    })
    .catch(error => {
        console.error('清理无效连接失败:', error);
        showToast('清理无效连接失败', 'error');
    });
}

// 获取所有连接
function getAllConnections() {
    loadMqttStatus();
    // 切换到MQTT监控标签页
    const tab = new bootstrap.Tab(document.getElementById('mqtt-monitor-tab'));
    tab.show();
}

// 断开所有设备连接
function disconnectAllDevices() {
    if (confirm('确定要断开所有设备的MQTT连接吗？此操作将影响所有在线设备。')) {
        // 这里需要实现批量断开功能
        fetch(`${API_BASE}/connection-status`, {
            headers: {
                'authorization': 'b8'
            }
        })
        .then(response => response.json())
        .then(result => {
            const tokens = result.connectedTokens || [];
            const promises = tokens.map(token =>
                fetch(`${API_BASE}/disconnect-by-token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'authorization': 'b8'
                    },
                    body: `token=${encodeURIComponent(token)}&reason=管理员批量断开`
                })
            );

            Promise.all(promises).then(() => {
                showToast(`已断开${tokens.length}个设备连接`, 'success');
                loadMqttStatus(); // 刷新状态
            });
        })
        .catch(error => {
            console.error('断开所有设备连接失败:', error);
            showToast('断开所有设备连接失败', 'error');
        });
    }
}

// 检查系统健康状态
function checkSystemHealth() {
    checkRedisHealth();
    // 可以添加更多系统健康检查
}

// 生成分页
function generatePagination(pageInfo) {
    const pagination = document.getElementById('devicePagination');
    pagination.innerHTML = '';

    if (!pageInfo || pageInfo.pages <= 1) return;

    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${pageInfo.hasPreviousPage ? '' : 'disabled'}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${pageInfo.prePage})">上一页</a>`;
    pagination.appendChild(prevLi);

    // 页码
    for (let i = 1; i <= pageInfo.pages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === pageInfo.pageNum ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${pageInfo.hasNextPage ? '' : 'disabled'}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${pageInfo.nextPage})">下一页</a>`;
    pagination.appendChild(nextLi);
}

// 切换页面
function changePage(page) {
    if (page && page > 0) {
        currentPage = page;
        loadDeviceList();
    }
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
});

// 模态框事件监听
document.getElementById('deviceModal').addEventListener('show.bs.modal', function (event) {
    const button = event.relatedTarget;
    if (!button || !button.onclick) {
        // 新增设备
        clearDeviceForm();
        document.getElementById('deviceModalLabel').innerHTML = '<i class="fas fa-plus me-2"></i>新增设备';
    }
});
</script>

</body>
</html>
