<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工装具全功能管理 (最新版)</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome CSS for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .container-fluid {
            max-width: 1600px;
        }
        .card-header-custom {
            background-color: #0d6efd;
            color: white;
        }
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        fieldset {
            border: 1px solid #dee2e6;
            border-radius: .375rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        legend {
            float: none;
            width: auto;
            padding: 0 10px;
            font-size: 1.25rem;
            font-weight: 500;
        }
        .table-hover tbody tr:hover {
            background-color: #e9ecef;
        }
        .nav-tabs .nav-link {
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            background-color: #f8f9fa;
            border-bottom-color: #f8f9fa;
        }
        .form-label {
            font-weight: 500;
        }
    </style>
</head>
<body>

<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="text-primary"><i class="fas fa-tools me-2"></i>工装具全生命周期管理</h2>
    </div>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs" id="managementTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="tool-info-tab" data-bs-toggle="tab" data-bs-target="#tool-info-pane" type="button" role="tab" aria-controls="tool-info-pane" aria-selected="true">
                <i class="fas fa-list-alt me-1"></i> 工装具信息管理
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="tool-category-tab" data-bs-toggle="tab" data-bs-target="#tool-category-pane" type="button" role="tab" aria-controls="tool-category-pane" aria-selected="false">
                <i class="fas fa-sitemap me-1"></i> 工装具类别管理
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="managementTabContent">

        <!-- Tool Information Management Pane -->
        <div class="tab-pane fade show active" id="tool-info-pane" role="tabpanel" aria-labelledby="tool-info-tab" tabindex="0">
            <div class="card shadow-sm mt-3">
                <div class="card-body">
                    <div class="d-flex justify-content-end mb-3">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#toolInfoModal">
                            <i class="fas fa-plus me-1"></i> 新增工装
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                            <tr>
                                <th>工装编码</th><th>工装名称</th><th>类别</th><th>规格型号</th><th>状态</th><th>存放位置</th><th>上次保养日期</th><th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>T-HPM-001</td><td>左前门内板高精密模具</td><td>高精密模具</td><td>SKD11-HPM-L</td><td><span class="badge bg-success">在库</span></td><td>A-01-03</td><td>2025-01-15 10:30</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#toolInfoModal"><i class="fas fa-edit"></i> 编辑</button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i> 删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>C-STD-055</td><td>标准检具-卡规</td><td>检具</td><td>55mm-Level2</td><td><span class="badge bg-warning text-dark">使用中</span></td><td>B-02-11</td><td>2024-09-20 09:00</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#toolInfoModal"><i class="fas fa-edit"></i> 编辑</button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i> 删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>T-MNT-003</td><td>三号热流道模具</td><td>注塑模具</td><td>HR-T3-V2</td><td><span class="badge bg-info text-dark">保养中</span></td><td>维修车间</td><td>-</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#toolInfoModal"><i class="fas fa-edit"></i> 编辑</button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i> 删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>T-OLD-001</td><td>旧一号冲压模具</td><td>高精密模具</td><td>OLD-PUNCH-V1</td><td><span class="badge bg-danger">已报废</span></td><td>报废区</td><td>-</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#toolInfoModal"><i class="fas fa-edit"></i> 编辑</button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i> 删除</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tool Category Management Pane -->
        <div class="tab-pane fade" id="tool-category-pane" role="tabpanel" aria-labelledby="tool-category-tab" tabindex="0">
            <div class="card shadow-sm mt-3">
                <div class="card-body">
                    <div class="d-flex justify-content-end mb-3">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
                            <i class="fas fa-plus me-1"></i> 新增类别
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                            <tr>
                                <th>类别编码</th><th>类别名称</th><th>保养类型</th><th>保养周期/次数</th><th>租户</th><th>有效性</th><th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>CAT-001</td><td>高精密模具</td><td>使用次数</td><td>50000 次</td><td>默认租户</td><td><span class="badge bg-success">有效</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#categoryModal"><i class="fas fa-edit"></i> 编辑</button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i> 删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>CAT-002</td><td>标准检具</td><td>时间周期</td><td>180 天</td><td>默认租户</td><td><span class="badge bg-success">有效</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#categoryModal"><i class="fas fa-edit"></i> 编辑</button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i> 删除</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tool Info Modal -->
<div class="modal fade" id="toolInfoModal" tabindex="-1" aria-labelledby="toolInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header card-header-custom">
                <h5 class="modal-title" id="toolInfoModalLabel"><i class="fas fa-edit me-2"></i>工装信息</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <fieldset>
                        <legend>基本信息</legend>
                        <div class="row g-3">
                            <div class="col-md-4"><label for="toolCode" class="form-label">工装编码 <span class="text-danger">*</span></label><input type="text" class="form-control" id="toolCode" required></div>
                            <div class="col-md-4"><label for="toolName" class="form-label">工装名称 <span class="text-danger">*</span></label><input type="text" class="form-control" id="toolName" required></div>
                            <div class="col-md-4"><label for="categoryId" class="form-label">工装类别 <span class="text-danger">*</span></label><select id="categoryId" class="form-select"><option>高精密模具</option><option>检具</option></select></div>
                            <div class="col-md-4"><label for="spec" class="form-label">规格型号</label><input type="text" class="form-control" id="spec"></div>
                            <div class="col-md-4"><label for="brand" class="form-label">品牌</label><input type="text" class="form-control" id="brand"></div>
                            <div class="col-md-4"><label for="groupid" class="form-label">供应商</label><input type="text" class="form-control" id="groupid"></div>
                            <div class="col-md-4"><label for="storeLocation" class="form-label">存放位置</label><input type="text" class="form-control" id="storeLocation"></div>
                            <div class="col-md-4"><label for="deptId" class="form-label">所属部门</label><input type="text" class="form-control" id="deptId"></div>
                            <div class="col-md-4"><label for="tenantNameInfo" class="form-label">租户名称 <span class="text-danger">*</span></label><input type="text" class="form-control" id="tenantNameInfo" required></div>
                            <div class="col-md-12"><label class="form-label">当前状态</label><div><div class="form-check form-check-inline"><input class="form-check-input" type="radio" name="status" id="status1" value="1" checked><label class="form-check-label" for="status1">在库</label></div><div class="form-check form-check-inline"><input class="form-check-input" type="radio" name="status" id="status2" value="2"><label class="form-check-label" for="status2">使用中</label></div><div class="form-check form-check-inline"><input class="form-check-input" type="radio" name="status" id="status3" value="3"><label class="form-check-label" for="status3">保养中</label></div><div class="form-check form-check-inline"><input class="form-check-input" type="radio" name="status" id="status4" value="4"><label class="form-check-label" for="status4">已报废</label></div></div></div>
                            <div class="col-md-4"><label for="purchaseDate" class="form-label">购买日期</label><input type="datetime-local" class="form-control" id="purchaseDate"></div>
                            <div class="col-md-4"><label for="price" class="form-label">购买价格(元)</label><input type="number" class="form-control" id="price" step="0.0001"></div>
                            <div class="col-md-4"><label for="lifeMonths" class="form-label">预期寿命(月)</label><input type="number" class="form-control" id="lifeMonths"></div>
                        </div>
                    </fieldset>
                    <fieldset>
                        <legend>保养管理</legend>
                        <div class="row g-3">
                            <div class="col-md-4"><label for="lastMaintDate" class="form-label">上次保养日期</label><input type="datetime-local" class="form-control" id="lastMaintDate"></div>
                            <div class="col-md-2"><label for="maintUsedQty" class="form-label">已用量(保养)</label><input type="number" class="form-control" id="maintUsedQty"></div>
                            <div class="col-md-3"><label for="maintWarnQty" class="form-label">预警量(保养)</label><input type="number" class="form-control" id="maintWarnQty"></div>
                            <div class="col-md-3"><label for="maintLimitQty" class="form-label">限制量(保养)</label><input type="number" class="form-control" id="maintLimitQty"></div>
                        </div>
                    </fieldset>
                    <fieldset>
                        <legend>报废管理</legend>
                        <div class="row g-3">
                            <div class="col-md-4"><label for="scrapUsedQty" class="form-label">已用量(累计)</label><input type="number" class="form-control" id="scrapUsedQty"></div>
                            <div class="col-md-4"><label for="scrapWarnQty" class="form-label">预警量(报废)</label><input type="number" class="form-control" id="scrapWarnQty"></div>
                            <div class="col-md-4"><label for="scrapLimitQty" class="form-label">限制量(报废)</label><input type="number" class="form-control" id="scrapLimitQty"></div>
                        </div>
                    </fieldset>
                    <div class="row g-3">
                        <div class="col-md-10"><label for="remark" class="form-label">备注</label><textarea class="form-control" id="remark" rows="2"></textarea></div>
                        <div class="col-md-2 d-flex align-items-end"><div class="form-check form-switch"><input class="form-check-input" type="checkbox" role="switch" id="enabledMark" checked><label class="form-check-label" for="enabledMark">是否有效</label></div></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="fas fa-times me-1"></i> 关闭</button><button type="button" class="btn btn-primary"><i class="fas fa-save me-1"></i> 保存</button></div>
        </div>
    </div>
</div>

<!-- Tool Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header card-header-custom">
                <h5 class="modal-title" id="categoryModalLabel"><i class="fas fa-sitemap me-2"></i>类别信息</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row g-3">
                        <div class="col-md-6"><label for="cateCode" class="form-label">类别编码 <span class="text-danger">*</span></label><input type="text" class="form-control" id="cateCode" required></div>
                        <div class="col-md-6"><label for="cateName" class="form-label">类别名称 <span class="text-danger">*</span></label><input type="text" class="form-control" id="cateName" required></div>
                        <div class="col-md-12"><label for="tenantNameCategory" class="form-label">租户名称 <span class="text-danger">*</span></label><input type="text" class="form-control" id="tenantNameCategory" required></div>
                        <div class="col-md-6"><label for="parentId" class="form-label">父类别</label><select id="parentId" class="form-select"><option value="0" selected>无 (根类别)</option><option value="1">模具</option></select></div>
                        <div class="col-md-6"><label for="maintType" class="form-label">保养类型</label><select id="maintType" class="form-select"><option value="0">无</option><option value="1">时间周期</option><option value="2">使用次数</option></select></div>
                        <div class="col-md-6" id="maintCycleDaysWrapper"><label for="maintCycleDays" class="form-label">保养周期(天)</label><input type="number" class="form-control" id="maintCycleDays"></div>
                        <div class="col-md-6" id="maintUsageCountWrapper"><label for="maintUsageCount" class="form-label">保养使用次数</label><input type="number" class="form-control" id="maintUsageCount"></div>
                        <div class="col-12"><label for="cateRemark" class="form-label">备注</label><textarea class="form-control" id="cateRemark" rows="3"></textarea></div>
                        <div class="col-12"><div class="form-check form-switch"><input class="form-check-input" type="checkbox" role="switch" id="cateEnabledMark" checked><label class="form-check-label" for="cateEnabledMark">是否有效</label></div></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="fas fa-times me-1"></i> 关闭</button><button type="button" class="btn btn-primary"><i class="fas fa-save me-1"></i> 保存</button></div>
        </div>
    </div>
</div>


<!-- Bootstrap 5 JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // JS for dynamic form fields in Category Modal
    const maintTypeSelect = document.getElementById('maintType');
    const cycleDaysWrapper = document.getElementById('maintCycleDaysWrapper');
    const usageCountWrapper = document.getElementById('maintUsageCountWrapper');

    function toggleMaintFields() {
        const selectedType = maintTypeSelect.value;
        if (selectedType === '1') { // Time-based
            cycleDaysWrapper.style.display = 'block';
            usageCountWrapper.style.display = 'none';
        } else if (selectedType === '2') { // Usage-based
            cycleDaysWrapper.style.display = 'none';
            usageCountWrapper.style.display = 'block';
        } else { // None
            cycleDaysWrapper.style.display = 'none';
            usageCountWrapper.style.display = 'none';
        }
    }

    maintTypeSelect.addEventListener('change', toggleMaintFields);

    // Initial call to set the correct state when the modal loads
    document.getElementById('categoryModal').addEventListener('show.bs.modal', toggleMaintFields);
</script>
</body>
</html>
