<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时监控 - MQTT管理界面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/common.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-server"></i> MQTT管理</h3>
            <div class="subtitle">服务端控制台</div>
        </div>

        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="clients.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>客户端管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="publish.html" class="nav-link">
                    <i class="fas fa-paper-plane"></i>
                    <span>消息发布</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="subscriptions.html" class="nav-link">
                    <i class="fas fa-rss"></i>
                    <span>订阅管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="messages.html" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    <span>消息管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-messages.html" class="nav-link">
                    <i class="fas fa-bolt"></i>
                    <span>实时消息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-monitor.html" class="nav-link active">
                    <i class="fas fa-eye"></i>
                    <span>实时监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="stats.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计信息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
                <div class="content-header">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2"><i class="fas fa-eye"></i> 实时监控</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleAutoRefresh()">
                                    <i class="fas fa-sync-alt"></i> <span id="autoRefreshText">自动刷新</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-body">
                    <!-- 连接状态 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <div class="status-indicator" id="connectionStatus">
                                                <i class="fas fa-circle text-danger"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">WebSocket连接状态</h6>
                                            <small class="text-muted" id="connectionText">未连接</small>
                                        </div>
                                        <div class="ms-auto">
                                            <button class="btn btn-sm btn-primary" onclick="connectWebSocket()" id="connectBtn">
                                                <i class="fas fa-plug"></i> 连接
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <div class="display-6 text-primary" id="currentConnections">0</div>
                                    <h6 class="card-title">当前连接</h6>
                                    <small class="text-muted">实时更新</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <div class="display-6 text-success" id="totalMessages">0</div>
                                    <h6 class="card-title">总消息数</h6>
                                    <small class="text-muted">累计统计</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <div class="display-6 text-warning" id="totalSubscriptions">0</div>
                                    <h6 class="card-title">订阅数</h6>
                                    <small class="text-muted">活跃订阅</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <div class="display-6 text-info" id="uniqueTopics">0</div>
                                    <h6 class="card-title">主题数</h6>
                                    <small class="text-muted">唯一主题</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时图表 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> 连接数趋势</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="connectionChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-area"></i> 消息速率</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="messageChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 活动日志 -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list"></i> 活动日志</h5>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary" onclick="clearActivityLog()">
                                    <i class="fas fa-trash"></i> 清空
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="activity-log" id="activityLog">
                                <div class="text-center py-4 text-muted">
                                    <i class="fas fa-info-circle"></i> 连接WebSocket以查看实时活动
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 通用JS -->
    <script src="js/config.js"></script>
    <script src="js/common.js"></script>



    <script>
        let websocket = null;
        let autoRefresh = true;
        let connectionChart = null;
        let messageChart = null;
        let chartData = {
            connections: [],
            messages: [],
            labels: []
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化UI
            mqttUI.initializeTheme();

            // 初始化图表
            initializeCharts();
            connectWebSocket();
        });

        // 连接WebSocket
        function connectWebSocket() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                return;
            }

            const wsUrl = CONFIG.WEBSOCKET.URL;

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    updateConnectionStatus(true);
                    addActivityLog('WebSocket连接已建立', 'connect');
                };

                websocket.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    handleWebSocketMessage(message);
                };

                websocket.onclose = function(event) {
                    updateConnectionStatus(false);
                    addActivityLog('WebSocket连接已关闭', 'disconnect');
                };

                websocket.onerror = function(error) {
                    updateConnectionStatus(false);
                    addActivityLog('WebSocket连接错误', 'disconnect');
                };

            } catch (error) {
                updateConnectionStatus(false);
                addActivityLog('WebSocket连接失败: ' + error.message, 'disconnect');
            }
        }

        // 初始化图表
        function initializeCharts() {
            // 连接数趋势图
            const connectionCtx = document.getElementById('connectionChart').getContext('2d');
            connectionChart = new Chart(connectionCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '连接数',
                        data: [],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 消息速率图
            const messageCtx = document.getElementById('messageChart').getContext('2d');
            messageChart = new Chart(messageCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '消息数',
                        data: [],
                        backgroundColor: '#28a745'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            switch (message.type) {
                case 'connection':
                    addActivityLog('连接成功，会话ID: ' + message.sessionId, 'connect');
                    break;
                case 'mqtt_message':
                    addActivityLog(`收到消息 - 主题: ${message.topic}, 客户端: ${message.clientId}`, 'message');
                    break;
                case 'stats_update':
                    updateStats(message.data);
                    updateCharts(message.data);
                    break;
            }
        }

        // 更新统计数据
        function updateStats(stats) {
            document.getElementById('currentConnections').textContent = stats.connections || 0;
            document.getElementById('totalMessages').textContent = stats.messages || 0;
            document.getElementById('totalSubscriptions').textContent = stats.subscriptions || 0;
            document.getElementById('uniqueTopics').textContent = stats.topics || 0;
        }

        // 更新图表
        function updateCharts(stats) {
            const now = new Date().toLocaleTimeString();

            chartData.labels.push(now);
            chartData.connections.push(stats.connections || 0);
            chartData.messages.push(stats.messages || 0);

            const maxDataPoints = 20;
            if (chartData.labels.length > maxDataPoints) {
                chartData.labels.shift();
                chartData.connections.shift();
                chartData.messages.shift();
            }

            connectionChart.data.labels = [...chartData.labels];
            connectionChart.data.datasets[0].data = [...chartData.connections];
            connectionChart.update('none');

            messageChart.data.labels = [...chartData.labels];
            messageChart.data.datasets[0].data = [...chartData.messages];
            messageChart.update('none');
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            const textElement = document.getElementById('connectionText');
            const connectBtn = document.getElementById('connectBtn');

            if (connected) {
                statusElement.innerHTML = '<i class="fas fa-circle text-success"></i>';
                textElement.textContent = '已连接';
                connectBtn.innerHTML = '<i class="fas fa-unlink"></i> 断开';
                connectBtn.onclick = disconnectWebSocket;
            } else {
                statusElement.innerHTML = '<i class="fas fa-circle text-danger"></i>';
                textElement.textContent = '未连接';
                connectBtn.innerHTML = '<i class="fas fa-plug"></i> 连接';
                connectBtn.onclick = connectWebSocket;
            }
        }

        // 断开WebSocket
        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }

        // 添加活动日志
        function addActivityLog(text, type = 'info') {
            const container = document.getElementById('activityLog');
            const activityElement = document.createElement('div');
            activityElement.className = `activity-item activity-type-${type}`;

            activityElement.innerHTML = `
                ${text}
                <span class="activity-time">${new Date().toLocaleString()}</span>
            `;

            container.appendChild(activityElement);
            container.scrollTop = container.scrollHeight;

            const maxLogs = 100;
            while (container.children.length > maxLogs) {
                container.removeChild(container.firstChild);
            }
        }

        // 清空活动日志
        function clearActivityLog() {
            document.getElementById('activityLog').innerHTML = '';
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            document.getElementById('autoRefreshText').textContent = autoRefresh ? '自动刷新' : '手动刷新';
        }
    </script>
</body>
</html>
