// MQTT管理界面配置文件
const CONFIG = {
    // API基础配置
    API: {
        BASE_URL: 'http://dev.inksyun.com:31080/eam/api/v1',
        AUTH: {
            username: 'mica',
            password: 'mica'
        },
        TIMEOUT: 10000
    },
    
    // WebSocket配置
    WEBSOCKET: {
        URL: 'ws://192.168.99.96:10180/ws/mqtt-management',
        RECONNECT_INTERVAL: 5000,
        MAX_RECONNECT_ATTEMPTS: 10
    },
    
    // MQTT服务器配置
    MQTT: {
        SERVER_PORT: 1883,
        WS_PORT: 8083,
        HTTP_PORT: 10180,
        SSL_PORT: 8883,
        WSS_PORT: 8084
    },
    
    // 界面配置
    UI: {
        REFRESH_INTERVAL: 10000, // 数据刷新间隔（毫秒）- 从5秒调整为10秒
        CHART_REFRESH_INTERVAL: 15000, // 图表刷新间隔 - 从2秒调整为15秒，避免频繁调用
        PAGE_SIZE: 20, // 默认分页大小
        MAX_MESSAGE_DISPLAY: 100, // 最大消息显示数量
        TOAST_DURATION: 3000, // 提示消息持续时间
        DEBOUNCE_DELAY: 500, // 防抖延迟（毫秒）
        MAX_CONCURRENT_REQUESTS: 3 // 最大并发请求数
    },
    
    // 主题配置
    THEME: {
        PRIMARY_COLOR: '#007bff',
        SUCCESS_COLOR: '#28a745',
        DANGER_COLOR: '#dc3545',
        WARNING_COLOR: '#ffc107',
        INFO_COLOR: '#17a2b8'
    },
    
    // 图表配置
    CHART: {
        COLORS: [
            '#007bff', '#28a745', '#dc3545', '#ffc107', 
            '#17a2b8', '#6f42c1', '#fd7e14', '#20c997'
        ],
        ANIMATION_DURATION: 1000
    },
    
    // 消息类型配置
    MESSAGE_TYPES: {
        CONNECT: 'CONNECT',
        DISCONNECT: 'DISCONNECT',
        PUBLISH: 'PUBLISH',
        SUBSCRIBE: 'SUBSCRIBE',
        UNSUBSCRIBE: 'UNSUBSCRIBE'
    },
    
    // QoS级别
    QOS_LEVELS: [
        { value: 0, label: 'QoS 0 - 最多一次' },
        { value: 1, label: 'QoS 1 - 至少一次' },
        { value: 2, label: 'QoS 2 - 恰好一次' }
    ],
    
    // 编码类型
    ENCODING_TYPES: [
        { value: 'plain', label: '纯文本' },
        { value: 'hex', label: 'HEX编码' },
        { value: 'base64', label: 'Base64编码' }
    ],
    
    // 状态映射
    STATUS: {
        ONLINE: 'online',
        OFFLINE: 'offline',
        CONNECTING: 'connecting',
        DISCONNECTING: 'disconnecting'
    },
    
    // 错误代码映射
    ERROR_CODES: {
        1: '成功',
        101: '关键请求参数缺失',
        102: '请求参数错误',
        103: '用户名或密码错误',
        104: '请求方法错误',
        105: '未知错误'
    },
    
    // 导航菜单配置
    NAVIGATION: [
        {
            id: 'dashboard',
            title: '仪表板',
            icon: 'fas fa-tachometer-alt',
            url: 'dashboard.html',
            description: '系统概览和实时监控'
        },
        {
            id: 'clients',
            title: '客户端管理',
            icon: 'fas fa-users',
            url: 'clients.html',
            description: '管理MQTT客户端连接'
        },
        {
            id: 'publish',
            title: '消息发布',
            icon: 'fas fa-paper-plane',
            url: 'publish.html',
            description: '发布MQTT消息'
        },
        {
            id: 'subscriptions',
            title: '订阅管理',
            icon: 'fas fa-rss',
            url: 'subscriptions.html',
            description: '管理主题订阅'
        },
        {
            id: 'messages',
            title: '消息管理',
            icon: 'fas fa-envelope',
            url: 'messages.html',
            description: '消息管理和查看'
        },
        {
            id: 'realtime-messages',
            title: '实时消息',
            icon: 'fas fa-bolt',
            url: 'realtime-messages.html',
            description: '实时消息监控'
        },
        {
            id: 'realtime-monitor',
            title: '实时监控',
            icon: 'fas fa-eye',
            url: 'realtime-monitor.html',
            description: '实时系统监控'
        },
        {
            id: 'stats',
            title: '统计信息',
            icon: 'fas fa-chart-bar',
            url: 'stats.html',
            description: '详细统计数据'
        },
        {
            id: 'settings',
            title: '系统设置',
            icon: 'fas fa-cog',
            url: 'settings.html',
            description: '系统配置管理'
        }
    ],
    
    // 统计指标配置
    METRICS: {
        CONNECTIONS: {
            title: '连接数',
            icon: 'fas fa-link',
            color: '#007bff'
        },
        MESSAGES: {
            title: '消息数',
            icon: 'fas fa-envelope',
            color: '#28a745'
        },
        SUBSCRIPTIONS: {
            title: '订阅数',
            icon: 'fas fa-rss',
            color: '#17a2b8'
        },
        TOPICS: {
            title: '主题数',
            icon: 'fas fa-tags',
            color: '#ffc107'
        }
    },
    
    // 本地存储键名
    STORAGE_KEYS: {
        THEME: 'mqtt_ui_theme',
        SETTINGS: 'mqtt_ui_settings',
        LAST_ACTIVE_PAGE: 'mqtt_ui_last_page'
    }
};

// 导出配置（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
