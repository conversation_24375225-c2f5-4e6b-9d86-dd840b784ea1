// MQTT管理界面通用JavaScript功能
class MqttUI {
    constructor() {
        this.init();
    }

    init() {
        this.loadingCount = 0;
        this.activeRequests = new Map(); // 跟踪活跃请求
        this.requestQueue = []; // 请求队列
        this.setupAxios();
        this.setupEventListeners();
        this.initializeTheme();
        this.updateActiveNavigation();
    }

    // 配置Axios
    setupAxios() {
        // 设置默认配置
        axios.defaults.baseURL = CONFIG.API.BASE_URL;
        axios.defaults.timeout = CONFIG.API.TIMEOUT;

        // 设置CORS相关头部
        axios.defaults.headers.common['Content-Type'] = 'application/json';
        axios.defaults.headers.common['Accept'] = 'application/json';

        // 请求拦截器
        axios.interceptors.request.use(
            config => {
                this.showLoading();
                return config;
            },
            error => {
                this.hideLoading();
                return Promise.reject(error);
            }
        );

        // 响应拦截器
        axios.interceptors.response.use(
            response => {
                this.hideLoading();
                return response;
            },
            error => {
                this.hideLoading();
                this.handleApiError(error);
                return Promise.reject(error);
            }
        );
    }

    // 设置事件监听器
    setupEventListeners() {
        // 侧边栏切换
        document.addEventListener('click', (e) => {
            if (e.target.matches('.sidebar-toggle')) {
                this.toggleSidebar();
            }
        });

        // 响应式处理
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // 监听localStorage变化，实现跨页面主题同步
        window.addEventListener('storage', (e) => {
            if (e.key === CONFIG.STORAGE_KEYS.THEME && e.newValue) {
                this.applyTheme(e.newValue);
                this.updateThemeButtons(e.newValue);
            }
        });

        // 监听自定义主题变化事件，用于同页面内的组件同步
        window.addEventListener('themeChanged', (e) => {
            // 这里可以添加额外的主题变化处理逻辑
            // 例如更新图表颜色、重新渲染某些组件等
        });
    }

    // 初始化主题
    initializeTheme() {
        const savedTheme = localStorage.getItem(CONFIG.STORAGE_KEYS.THEME) || 'theme-light';
        this.applyTheme(savedTheme);
        this.updateThemeButtons(savedTheme);
    }

    // 应用主题
    applyTheme(theme) {
        // 移除所有主题类
        document.body.classList.remove('theme-light', 'theme-dark');
        // 添加新主题类
        document.body.classList.add(theme);
    }

    // 切换主题
    toggleTheme(theme) {
        this.applyTheme(theme);
        localStorage.setItem(CONFIG.STORAGE_KEYS.THEME, theme);
        this.updateThemeButtons(theme);
        this.showToast(`已切换到${theme === 'theme-light' ? '浅色' : '深色'}主题`, 'success');

        // 触发自定义主题变化事件，用于同页面内的组件同步
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: theme }
        }));
    }

    // 更新主题按钮状态
    updateThemeButtons(currentTheme) {
        const lightBtn = document.getElementById('lightThemeBtn');
        const darkBtn = document.getElementById('darkThemeBtn');

        if (lightBtn && darkBtn) {
            lightBtn.classList.remove('active');
            darkBtn.classList.remove('active');

            if (currentTheme === 'theme-light') {
                lightBtn.classList.add('active');
            } else {
                darkBtn.classList.add('active');
            }
        }
    }

    // 更新活动导航
    updateActiveNavigation() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === currentPage) {
                link.classList.add('active');
            }
        });
    }

    // API调用方法 - 增加请求控制和防抖
    async apiCall(method, endpoint, data = null, params = null) {
        const requestKey = `${method}:${endpoint}:${JSON.stringify(data || {})}:${JSON.stringify(params || {})}`;

        // 检查是否有相同的请求正在进行
        if (this.activeRequests.has(requestKey)) {
            console.log('请求已在进行中，跳过重复请求:', requestKey);
            return this.activeRequests.get(requestKey);
        }

        // 检查并发请求数量限制
        if (this.activeRequests.size >= (CONFIG.UI.MAX_CONCURRENT_REQUESTS || 3)) {
            console.warn('达到最大并发请求数，请求被延迟');
            await this.waitForRequestSlot();
        }

        try {
            const config = {
                method: method.toLowerCase(),
                url: endpoint,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };

            // 处理GET请求的查询参数
            if (method.toUpperCase() === 'GET' && data) {
                config.params = data;
            } else if (method.toUpperCase() === 'GET' && params) {
                config.params = params;
            }

            // 处理POST/PUT/DELETE请求的数据
            if (method.toUpperCase() !== 'GET' && data) {
                config.data = data;
            }

            // 处理POST请求的查询参数（如删除客户端时的clientId参数）
            if (params) {
                config.params = params;
            }

            // 创建请求Promise并加入活跃请求跟踪
            const requestPromise = axios(config).then(response => {
                this.activeRequests.delete(requestKey);
                return response.data;
            }).catch(error => {
                this.activeRequests.delete(requestKey);
                throw error;
            });

            this.activeRequests.set(requestKey, requestPromise);
            return await requestPromise;
        } catch (error) {
            console.error('API调用失败:', error);
            throw error;
        }
    }

    // 处理API错误
    handleApiError(error) {
        let message = '请求失败';
        
        if (error.response) {
            const status = error.response.status;
            const data = error.response.data;
            
            switch (status) {
                case 404:
                    message = '请求的资源不存在';
                    break;
                case 500:
                    message = '服务器内部错误';
                    break;
                default:
                    message = data?.message || `请求失败 (${status})`;
            }
        } else if (error.request) {
            message = '网络连接失败，请检查服务器状态';
        }

        this.showToast(message, 'error');
    }

    // 显示加载状态
    showLoading() {
        const existingLoader = document.querySelector('.global-loader');
        if (!existingLoader) {
            const loader = document.createElement('div');
            loader.className = 'global-loader';
            loader.innerHTML = '<div class="loading"></div>';
            loader.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            `;
            document.body.appendChild(loader);
        }
    }

    // 隐藏加载状态
    hideLoading() {
        const loader = document.querySelector('.global-loader');
        if (loader) {
            loader.remove();
        }
    }

    // 显示提示消息
    showToast(message, type = 'info', duration = CONFIG.UI.TOAST_DURATION) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${this.getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;
        
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--card-bg);
            color: var(--text-light);
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            border-left: 4px solid var(--${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'}-color);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    // 获取提示图标
    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // 切换侧边栏
    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.toggle('show');
        }
    }

    // 处理窗口大小变化
    handleResize() {
        const sidebar = document.querySelector('.sidebar');
        if (window.innerWidth > 768 && sidebar) {
            sidebar.classList.remove('show');
        }
    }

    // 等待请求槽位可用
    async waitForRequestSlot() {
        return new Promise(resolve => {
            const checkSlot = () => {
                if (this.activeRequests.size < (CONFIG.UI.MAX_CONCURRENT_REQUESTS || 3)) {
                    resolve();
                } else {
                    setTimeout(checkSlot, 100);
                }
            };
            checkSlot();
        });
    }

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    // 格式化时间
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleString('zh-CN');
    }

    // 格式化字节大小
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 格式化数字
    formatNumber(num) {
        return new Intl.NumberFormat('zh-CN').format(num);
    }

    // 生成随机ID
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    }

    // 验证主题格式
    validateTopic(topic, allowWildcards = false) {
        if (!topic || typeof topic !== 'string') {
            return false;
        }

        if (allowWildcards) {
            // 订阅主题可以包含通配符
            return topic.length > 0 && !topic.includes(' ');
        } else {
            // 发布主题不能包含通配符
            const invalidChars = /[+#]/g;
            return !invalidChars.test(topic) && topic.length > 0;
        }
    }

    // 验证JSON格式
    validateJSON(str) {
        try {
            JSON.parse(str);
            return true;
        } catch (e) {
            return false;
        }
    }

    // 深拷贝对象
    deepClone(obj) {
        return JSON.parse(JSON.stringify(obj));
    }

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 保存设置到本地存储
    saveSettings(settings) {
        localStorage.setItem(CONFIG.STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
    }

    // 从本地存储加载设置
    loadSettings() {
        const settings = localStorage.getItem(CONFIG.STORAGE_KEYS.SETTINGS);
        return settings ? JSON.parse(settings) : {};
    }
}

// 全局实例
const mqttUI = new MqttUI();

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// 全局变量
window.mqttUI = new MqttUI();

// 全局主题切换函数
window.toggleTheme = function(theme) {
    window.mqttUI.toggleTheme(theme);
};
