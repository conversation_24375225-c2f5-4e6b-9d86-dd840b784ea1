<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - MQTT管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义样式 -->
    <link href="css/common.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-server"></i> MQTT管理</h3>
            <div class="subtitle">服务端控制台</div>
        </div>
        
        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="clients.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>客户端管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="publish.html" class="nav-link">
                    <i class="fas fa-paper-plane"></i>
                    <span>消息发布</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="subscriptions.html" class="nav-link">
                    <i class="fas fa-rss"></i>
                    <span>订阅管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="messages.html" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    <span>消息管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-messages.html" class="nav-link">
                    <i class="fas fa-bolt"></i>
                    <span>实时消息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-monitor.html" class="nav-link">
                    <i class="fas fa-eye"></i>
                    <span>实时监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="stats.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计信息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <div class="content-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-tachometer-alt"></i> 仪表板</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html">首页</a></li>
                            <li class="breadcrumb-item active">仪表板</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-outline-light me-2" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <div class="form-check form-switch d-inline-block align-middle me-2">
                        <input class="form-check-input" type="checkbox" role="switch" id="autoRefreshSwitch">
                        <label class="form-check-label text-white" for="autoRefreshSwitch">自动刷新</label>
                    </div>
                    <button class="btn btn-outline-light d-md-none sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="content-body">
            <!-- 实时统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-primary">
                            <i class="fas fa-link"></i>
                        </div>
                        <div class="stat-value text-primary" id="currentConnections">0</div>
                        <div class="stat-label">当前连接</div>
                        <div class="stat-change" id="connectionsChange">
                            <i class="fas fa-arrow-up text-success"></i> +0
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-success">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-value text-success" id="messagesPerSecond">0</div>
                        <div class="stat-label">消息/秒</div>
                        <div class="stat-change" id="messagesChange">
                            <i class="fas fa-arrow-up text-success"></i> +0
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-info">
                            <i class="fas fa-rss"></i>
                        </div>
                        <div class="stat-value text-info" id="activeSubscriptions">0</div>
                        <div class="stat-label">活跃订阅</div>
                        <div class="stat-change" id="subscriptionsChange">
                            <i class="fas fa-arrow-up text-success"></i> +0
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-warning">
                            <i class="fas fa-memory"></i>
                        </div>
                        <div class="stat-value text-warning" id="memoryUsage">0%</div>
                        <div class="stat-label">内存使用</div>
                        <div class="stat-change" id="memoryChange">
                            <i class="fas fa-arrow-up text-warning"></i> +0%
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-line"></i> 实时监控</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="realtimeChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-pie"></i> 连接分布</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="connectionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近活动和热门主题 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-clock"></i> 最近活动</h5>
                        </div>
                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                            <div id="recentActivities">
                                <div class="text-center py-3">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-fire"></i> 热门主题</h5>
                        </div>
                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                            <div id="topTopics">
                                <div class="text-center py-3">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-server"></i> 系统状态</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>服务状态</h6>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>MQTT服务</span>
                                            <span class="badge bg-success">运行中</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>WebSocket服务</span>
                                            <span class="badge bg-success">运行中</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>HTTP API</span>
                                            <span class="badge bg-success">运行中</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <h6>端口信息</h6>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>MQTT端口</span>
                                            <span>1883</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>WebSocket端口</span>
                                            <span>8083</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>HTTP API端口</span>
                                            <span>10180</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <h6>运行时间</h6>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>启动时间</span>
                                            <span id="startTime">-</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>运行时长</span>
                                            <span id="uptime">-</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>最后更新</span>
                                            <span id="lastUpdate">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/common.js"></script>
    <script>
        let realtimeChart, connectionChart;
        let startTime = new Date();
        let previousStats = {};
        let autoRefreshInterval = null;
        let systemInfoInterval = null; // 用于跟踪系统信息更新的定时器
        let isLoadingData = false; // 防止重复加载数据
        let lastLoadTime = 0; // 记录上次加载时间
        const MIN_LOAD_INTERVAL = 5000; // 最小加载间隔5秒

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();

            // 初始加载数据
            setTimeout(() => {
                loadDashboardData();
            }, 500); // 延迟加载，避免页面加载时的性能问题

            updateSystemInfo();

            // 定时刷新系统信息 - 降低频率
            systemInfoInterval = setInterval(updateSystemInfo, 5000); // 从1秒改为5秒

            // 设置事件监听器
            setupEventListeners();

            // 监听主题变化事件
            window.addEventListener('themeChanged', function(event) {
                updateChartsTheme();
            });
        });

        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('autoRefreshSwitch').addEventListener('change', function() {
                if (this.checked) {
                    if (!autoRefreshInterval) {
                        // 使用防抖的加载函数
                        const debouncedLoad = mqttUI.debounce(loadDashboardData, CONFIG.UI.DEBOUNCE_DELAY || 500);
                        autoRefreshInterval = setInterval(debouncedLoad, CONFIG.UI.CHART_REFRESH_INTERVAL);
                        console.log('自动刷新已启动，间隔:', CONFIG.UI.CHART_REFRESH_INTERVAL + 'ms');
                    }
                } else {
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                        autoRefreshInterval = null;
                        console.log('自动刷新已停止');
                    }
                }
            });
        }

        // 初始化图表
        function initializeCharts() {
            // 实时监控图表
            const realtimeCtx = document.getElementById('realtimeChart').getContext('2d');
            realtimeChart = new Chart(realtimeCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '连接数',
                        data: [],
                        borderColor: CONFIG.THEME.PRIMARY_COLOR,
                        backgroundColor: CONFIG.THEME.PRIMARY_COLOR + '20',
                        tension: 0.4
                    }, {
                        label: '消息数/秒',
                        data: [],
                        borderColor: CONFIG.THEME.SUCCESS_COLOR,
                        backgroundColor: CONFIG.THEME.SUCCESS_COLOR + '20',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: document.body.classList.contains('theme-dark') ? '#d1d5db' : '#212529'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: document.body.classList.contains('theme-dark') ? '#d1d5db' : '#495057'
                            },
                            grid: {
                                color: document.body.classList.contains('theme-dark') ? '#6b7280' : '#dee2e6'
                            }
                        },
                        y: {
                            ticks: {
                                color: document.body.classList.contains('theme-dark') ? '#d1d5db' : '#495057'
                            },
                            grid: {
                                color: document.body.classList.contains('theme-dark') ? '#6b7280' : '#dee2e6'
                            }
                        }
                    }
                }
            });

            // 连接分布图表
            const connectionCtx = document.getElementById('connectionChart').getContext('2d');
            connectionChart = new Chart(connectionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['MQTT', 'WebSocket', 'SSL'],
                    datasets: [{
                        data: [0, 0, 0],
                        backgroundColor: [
                            CONFIG.THEME.PRIMARY_COLOR,
                            CONFIG.THEME.SUCCESS_COLOR,
                            CONFIG.THEME.WARNING_COLOR
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                color: document.body.classList.contains('theme-dark') ? '#d1d5db' : '#212529'
                            }
                        }
                    }
                }
            });
        }

        // 更新图表主题
        function updateChartsTheme() {
            const isDark = document.body.classList.contains('theme-dark');
            const textColor = isDark ? '#d1d5db' : '#212529';
            const tickColor = isDark ? '#d1d5db' : '#495057';
            const gridColor = isDark ? '#6b7280' : '#dee2e6';

            if (realtimeChart) {
                realtimeChart.options.plugins.legend.labels.color = textColor;
                realtimeChart.options.scales.x.ticks.color = tickColor;
                realtimeChart.options.scales.x.grid.color = gridColor;
                realtimeChart.options.scales.y.ticks.color = tickColor;
                realtimeChart.options.scales.y.grid.color = gridColor;
                realtimeChart.update();
            }

            if (connectionChart) {
                connectionChart.options.plugins.legend.labels.color = textColor;
                connectionChart.update();
            }
        }

        // 加载仪表板数据 - 增加防重复调用机制
        async function loadDashboardData() {
            const currentTime = Date.now();

            // 防止频繁调用
            if (isLoadingData) {
                console.log('数据正在加载中，跳过重复请求');
                return;
            }

            // 检查最小间隔
            if (currentTime - lastLoadTime < MIN_LOAD_INTERVAL) {
                console.log('调用过于频繁，跳过请求');
                return;
            }

            isLoadingData = true;
            lastLoadTime = currentTime;

            try {
                // 加载统计数据
                const stats = await mqttUI.apiCall('GET', '/stats');
                if (stats && stats.code === 1) {
                    updateStatsCards(stats.data);
                    updateRealtimeChart(stats.data);
                }

                // 加载客户端数据 - 减少limit避免大量数据传输
                const clients = await mqttUI.apiCall('GET', '/clients', { _limit: 50 });
                if (clients && clients.code === 1) {
                    updateConnectionChart(clients.data.list);
                    updateRecentActivities(clients.data.list);
                }

                // 更新最后更新时间
                document.getElementById('lastUpdate').textContent = new Date().toLocaleString('zh-CN');
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
            } finally {
                isLoadingData = false;
            }
        }

        // 更新统计卡片
        function updateStatsCards(data) {
            const connections = data.connections || 0;
            const messages = data.messages || 0;
            const subscriptions = data.subscriptions || 0;

            document.getElementById('currentConnections').textContent = mqttUI.formatNumber(connections);
            document.getElementById('messagesPerSecond').textContent = mqttUI.formatNumber(messages);
            document.getElementById('activeSubscriptions').textContent = mqttUI.formatNumber(subscriptions);
            document.getElementById('memoryUsage').textContent = '65%'; // 模拟数据

            // 计算变化
            updateChangeIndicator('connectionsChange', connections, previousStats.connections);
            updateChangeIndicator('messagesChange', messages, previousStats.messages);
            updateChangeIndicator('subscriptionsChange', subscriptions, previousStats.subscriptions);

            previousStats = { connections, messages, subscriptions };
        }

        // 更新变化指示器
        function updateChangeIndicator(elementId, current, previous) {
            const element = document.getElementById(elementId);
            if (previous !== undefined) {
                const change = current - previous;
                const icon = change >= 0 ? 'fa-arrow-up text-success' : 'fa-arrow-down text-danger';
                const sign = change >= 0 ? '+' : '';
                element.innerHTML = `<i class="fas ${icon}"></i> ${sign}${change}`;
            }
        }

        // 更新实时图表
        function updateRealtimeChart(data) {
            const now = new Date().toLocaleTimeString();
            const maxDataPoints = 20;

            // 添加新数据点
            realtimeChart.data.labels.push(now);
            realtimeChart.data.datasets[0].data.push(data.connections || 0);
            realtimeChart.data.datasets[1].data.push(data.messages || 0);

            // 保持数据点数量
            if (realtimeChart.data.labels.length > maxDataPoints) {
                realtimeChart.data.labels.shift();
                realtimeChart.data.datasets[0].data.shift();
                realtimeChart.data.datasets[1].data.shift();
            }

            realtimeChart.update('none');
        }

        // 更新连接分布图表
        function updateConnectionChart(clients) {
            const mqtt = clients.filter(c => c.protoName === 'MQTT').length;
            const websocket = clients.filter(c => c.protoName === 'WebSocket').length;
            const ssl = clients.filter(c => c.ssl === true).length;

            connectionChart.data.datasets[0].data = [mqtt, websocket, ssl];
            connectionChart.update();
        }

        // 更新最近活动
        function updateRecentActivities(clients) {
            const container = document.getElementById('recentActivities');
            const recentClients = clients
                .sort((a, b) => b.connectedAt - a.connectedAt)
                .slice(0, 10);

            if (recentClients.length === 0) {
                container.innerHTML = '<div class="text-muted text-center py-3">暂无活动</div>';
                return;
            }

            container.innerHTML = recentClients.map(client => `
                <div class="d-flex align-items-center mb-2 p-2 rounded" style="background: var(--bg-darker);">
                    <div class="status-indicator ${client.connected ? 'status-online' : 'status-offline'}"></div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${client.clientId}</div>
                        <small class="text-muted">${mqttUI.formatTime(client.connectedAt)}</small>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">${client.protoName}</small>
                    </div>
                </div>
            `).join('');
        }

        // 更新系统信息
        function updateSystemInfo() {
            document.getElementById('startTime').textContent = startTime.toLocaleString('zh-CN');

            const uptime = Date.now() - startTime.getTime();
            const hours = Math.floor(uptime / (1000 * 60 * 60));
            const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
            document.getElementById('uptime').textContent = `${hours}小时${minutes}分钟`;
        }

        // 刷新仪表板 - 增加防抖
        function refreshDashboard() {
            // 检查是否可以刷新
            const currentTime = Date.now();
            if (isLoadingData) {
                mqttUI.showToast('数据正在加载中，请稍候', 'info');
                return;
            }

            if (currentTime - lastLoadTime < MIN_LOAD_INTERVAL) {
                mqttUI.showToast('刷新过于频繁，请稍候再试', 'warning');
                return;
            }

            loadDashboardData();
            mqttUI.showToast('仪表板数据已刷新', 'success');
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            if (systemInfoInterval) {
                clearInterval(systemInfoInterval);
            }
        });
    </script>

    <style>
        .stat-change {
            font-size: 0.75rem;
            margin-top: 0.5rem;
        }
    </style>
</body>
</html>
