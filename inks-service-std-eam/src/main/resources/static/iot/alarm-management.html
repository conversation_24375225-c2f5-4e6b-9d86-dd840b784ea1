<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>警报管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .alarm-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .alarm-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .severity-badge {
            font-size: 0.8em;
            padding: 4px 8px;
        }

        .severity-critical {
            background-color: #dc3545;
            color: white;
        }

        .severity-major {
            background-color: #fd7e14;
            color: white;
        }

        .severity-minor {
            background-color: #ffc107;
            color: black;
        }

        .severity-warning {
            background-color: #20c997;
            color: white;
        }

        .severity-indeterminate {
            background-color: #6c757d;
            color: white;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-active {
            background-color: #dc3545;
        }

        .status-acknowledged {
            background-color: #ffc107;
        }

        .status-cleared {
            background-color: #28a745;
        }

        .alarm-header {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .comment-item {
            border-left: 4px solid #007bff;
            padding: 10px 15px;
            margin: 10px 0;
            background-color: #f8f9fa;
            border-radius: 0 6px 6px 0;
        }

        .comment-system {
            border-left-color: #28a745;
            background-color: #f0f8f0;
        }

        .comment-user {
            border-left-color: #007bff;
            background-color: #f0f4ff;
        }

        .tab-content {
            border: 1px solid #dee2e6;
            border-top: none;
            padding: 20px;
            background-color: #fff;
        }

        .nav-tabs .nav-link.active {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        /* 自动淡出提示样式 */
        .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            max-width: 500px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .toast-notification.show {
            transform: translateX(0);
        }

        .toast-notification.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .toast-notification.error {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
        }

        .toast-notification.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .toast-notification.info {
            background: linear-gradient(135deg, #17a2b8, #6f42c1);
        }

        .filter-section {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .alarm-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .alarm-timestamp {
            font-size: 0.9em;
            color: #6c757d;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="alarm-header">
        <h2><i class="bi bi-exclamation-triangle"></i> 警报管理</h2>
        <p class="mb-0">实时监控和管理IoT设备警报信息</p>
    </div>

    <!-- 过滤器区域 -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">严重程度</label>
                <select class="form-select" id="severityFilter">
                    <option value="">全部</option>
                    <option value="CRITICAL">严重</option>
                    <option value="MAJOR">重要</option>
                    <option value="MINOR">次要</option>
                    <option value="WARNING">警告</option>
                    <option value="INDETERMINATE">未确定</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">状态</label>
                <select class="form-select" id="statusFilter">
                    <option value="">全部</option>
                    <option value="unacknowledged">未确认</option>
                    <option value="acknowledged">已确认</option>
                    <option value="cleared">已清除</option>
                    <option value="active">活跃</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">警报类型</label>
                <input type="text" class="form-control" id="typeFilter" placeholder="输入警报类型">
            </div>
            <div class="col-md-3">
                <label class="form-label">操作</label>
                <div>
                    <button class="btn btn-primary" onclick="loadAlarms()">
                        <i class="bi bi-search"></i> 搜索
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 左侧：警报列表 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> 警报列表</h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="loadAlarms()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="card-body p-0" style="max-height: 600px; overflow-y: auto;">
                    <div id="alarmList">
                        <!-- 警报列表将在这里动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：警报详情 -->
        <div class="col-md-6">
            <div id="alarmDetail" style="display: none;">
                <!-- 警报详细信息 -->
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" id="currentAlarmTitle">警报详情</h5>
                        <div class="alarm-actions">
                            <button class="btn btn-warning btn-sm" onclick="acknowledgeAlarm()" id="ackBtn">
                                <i class="bi bi-check-circle"></i> 确认
                            </button>
                            <button class="btn btn-success btn-sm" onclick="clearAlarm()" id="clearBtn">
                                <i class="bi bi-check-square"></i> 清除
                            </button>
                            <button class="btn btn-info btn-sm" onclick="showAssignModal()" id="assignBtn">
                                <i class="bi bi-person-plus"></i> 分配
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="unacknowledgeAlarm()"
                                    id="unackBtn">
                                <i class="bi bi-x-circle"></i> 取消确认
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>警报ID:</strong> <span id="alarmId"></span></p>
                                <p><strong>类型:</strong> <span id="alarmType"></span></p>
                                <p><strong>严重程度:</strong> <span id="alarmSeverity"></span></p>
                                <p><strong>触发实体:</strong> <span id="originatorId"></span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>开始时间:</strong> <span id="startTime"></span></p>
                                <p><strong>结束时间:</strong> <span id="endTime"></span></p>
                                <p><strong>分配给:</strong> <span id="assigneeId"></span></p>
                                <p><strong>状态:</strong> <span id="alarmStatus"></span></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <p><strong>附加信息:</strong></p>
                                <pre id="additionalInfo" class="bg-light p-2 rounded"></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 评论区域 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-chat-dots"></i> 评论记录</h5>
                        <button class="btn btn-primary btn-sm" onclick="showAddCommentModal()">
                            <i class="bi bi-plus"></i> 添加评论
                        </button>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="commentsList">
                            <!-- 评论列表将在这里动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 默认提示 -->
            <div id="defaultTip" class="text-center text-muted mt-5">
                <i class="bi bi-arrow-left" style="font-size: 2rem;"></i>
                <h4>请选择一个警报</h4>
                <p>从左侧列表中选择一个警报来查看详细信息和评论</p>
            </div>
        </div>
    </div>
</div>

<!-- 分配警报模态框 -->
<div class="modal fade" id="assignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">分配警报</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="assignForm">
                    <div class="mb-3">
                        <label class="form-label">分配给用户ID *</label>
                        <input type="text" class="form-control" id="assigneeIdInput" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">分配给用户名 *</label>
                        <input type="text" class="form-control" id="assigneeNameInput" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="assignAlarm()">分配</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加评论模态框 -->
<div class="modal fade" id="commentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加评论</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="commentForm">
                    <div class="mb-3">
                        <label class="form-label">评论内容 *</label>
                        <textarea class="form-control" id="commentText" rows="4" required
                                  placeholder="请输入评论内容..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addComment()">添加评论</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // 全局变量
    let currentAlarmId = null;
    const API_BASE_URL = 'http://dev.inksyun.com:31080/eam';
    const AUTH_HEADER = 'b8';

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        loadAlarms();
    });

    // 加载警报列表
    async function loadAlarms() {
        try {
            showLoading('正在加载警报列表...');

            // 构建查询参数
            const queryParam = {
                pageNum: 1,
                pageSize: 100,
                orderBy: "Iot_Alarm.CreateDate",
                orderType: 1, // 降序
                searchPojo: {},
                searchType: 0, // AND逻辑
                scenedata: "[]" // 高级筛选条件
            };

            // 获取过滤条件
            const severityFilter = document.getElementById('severityFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;

            // 构建高级筛选条件数组
            const sceneConditions = [];

            // 添加严重程度过滤
            if (severityFilter) {
                sceneConditions.push({
                    field: "Iot_Alarm.Severity",
                    math: "equal",
                    value: severityFilter
                });
            }

            // 添加警报类型过滤
            if (typeFilter) {
                sceneConditions.push({
                    field: "Iot_Alarm.Type",
                    math: "like",
                    value: typeFilter
                });
            }

            // 添加状态过滤
            if (statusFilter) {
                switch (statusFilter) {
                    case 'unacknowledged':
                        // 未确认：acknowledged = 0 且 cleared = 0
                        sceneConditions.push({
                            field: "Iot_Alarm.Acknowledged",
                            math: "equal",
                            value: "0"
                        });
                        sceneConditions.push({
                            field: "Iot_Alarm.Cleared",
                            math: "equal",
                            value: "0"
                        });
                        break;
                    case 'acknowledged':
                        // 已确认：acknowledged = 1 且 cleared = 0
                        sceneConditions.push({
                            field: "Iot_Alarm.Acknowledged",
                            math: "equal",
                            value: "1"
                        });
                        sceneConditions.push({
                            field: "Iot_Alarm.Cleared",
                            math: "equal",
                            value: "0"
                        });
                        break;
                    case 'cleared':
                        // 已清除：cleared = 1
                        sceneConditions.push({
                            field: "Iot_Alarm.Cleared",
                            math: "equal",
                            value: "1"
                        });
                        break;
                    case 'active':
                        // 活跃：cleared = 0（包含已确认和未确认）
                        sceneConditions.push({
                            field: "Iot_Alarm.Cleared",
                            math: "equal",
                            value: "0"
                        });
                        break;
                }
            }

            // 将筛选条件转换为JSON字符串
            if (sceneConditions.length > 0) {
                queryParam.scenedata = JSON.stringify(sceneConditions);
            }

            const response = await fetch(`${API_BASE_URL}/D09M36B1/getPageList`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': AUTH_HEADER
                },
                body: JSON.stringify(queryParam)
            });

            const result = await response.json();
            hideLoading();

            if (result.code === 200) {
                displayAlarmList(result.data.list || []);
            } else {
                showToast('加载警报列表失败: ' + result.msg, 'error');
            }
        } catch (error) {
            hideLoading();
            showToast('网络错误: ' + error.message, 'error');
        }
    }

    // 显示警报列表
    function displayAlarmList(alarms) {
        const alarmList = document.getElementById('alarmList');

        if (alarms.length === 0) {
            alarmList.innerHTML = `
                    <div class="text-center text-muted p-4">
                        <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                        <p class="mt-2">暂无警报数据</p>
                    </div>
                `;
            return;
        }

        alarmList.innerHTML = alarms.map(alarm => {
            const status = getAlarmStatus(alarm);
            const statusClass = getStatusClass(status);
            const severityClass = getSeverityClass(alarm.severity);

            return `
                    <div class="alarm-card p-3 ${currentAlarmId === alarm.id ? 'border-primary' : ''}"
                         onclick="selectAlarm('${alarm.id}')" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="status-indicator ${statusClass}"></span>
                                    <strong>${alarm.type || '未知类型'}</strong>
                                    <span class="badge ${severityClass} ms-2">${alarm.severity || 'UNKNOWN'}</span>
                                </div>
                                <p class="mb-1 text-muted small">触发实体: ${alarm.originatorid || '未知'}</p>
                                <p class="mb-1 alarm-timestamp">
                                    开始: ${formatTimestamp(alarm.startts)}
                                </p>
                                ${alarm.endts ? `<p class="mb-0 alarm-timestamp">结束: ${formatTimestamp(alarm.endts)}</p>` : ''}
                            </div>
                            <div class="text-end">
                                <small class="text-muted">${status}</small>
                            </div>
                        </div>
                    </div>
                `;
        }).join('');
    }

    // 选择警报
    async function selectAlarm(alarmId) {
        currentAlarmId = alarmId;

        try {
            // 重新渲染列表以高亮选中项
            loadAlarms();

            // 加载警报详情
            const response = await fetch(`${API_BASE_URL}/D09M36B1/getEntity?key=${alarmId}`, {
                headers: {
                    'Authorization': AUTH_HEADER
                }
            });

            const result = await response.json();
            if (result.code === 200) {
                displayAlarmDetail(result.data);
                loadComments(alarmId);
            } else {
                showToast('加载警报详情失败: ' + result.msg, 'error');
            }
        } catch (error) {
            showToast('网络错误: ' + error.message, 'error');
        }
    }

    // 显示警报详情
    function displayAlarmDetail(alarm) {
        document.getElementById('defaultTip').style.display = 'none';
        document.getElementById('alarmDetail').style.display = 'block';

        // 填充基本信息
        document.getElementById('currentAlarmTitle').textContent = `警报: ${alarm.type || '未知类型'}`;
        document.getElementById('alarmId').textContent = alarm.id || '';
        document.getElementById('alarmType').textContent = alarm.type || '';
        document.getElementById('alarmSeverity').innerHTML = `<span class="badge ${getSeverityClass(alarm.severity)}">${alarm.severity || 'UNKNOWN'}</span>`;
        document.getElementById('originatorId').textContent = alarm.originatorid || '';
        document.getElementById('startTime').textContent = formatTimestamp(alarm.startts);
        document.getElementById('endTime').textContent = alarm.endts ? formatTimestamp(alarm.endts) : '未结束';
        document.getElementById('assigneeId').textContent = alarm.assigneeid || '未分配';
        document.getElementById('alarmStatus').innerHTML = `<span class="status-indicator ${getStatusClass(getAlarmStatus(alarm))}"></span>${getAlarmStatus(alarm)}`;

        // 显示附加信息
        let additionalInfo = '无';
        if (alarm.additionalinfo) {
            try {
                // 尝试解析为JSON
                const parsed = JSON.parse(alarm.additionalinfo);
                additionalInfo = JSON.stringify(parsed, null, 2);
            } catch (e) {
                // 如果不是JSON格式，直接显示原文本
                additionalInfo = alarm.additionalinfo;
            }
        }
        document.getElementById('additionalInfo').textContent = additionalInfo;

        // 更新按钮状态
        updateActionButtons(alarm);
    }

    // 更新操作按钮状态
    function updateActionButtons(alarm) {
        const ackBtn = document.getElementById('ackBtn');
        const clearBtn = document.getElementById('clearBtn');
        const assignBtn = document.getElementById('assignBtn');
        const unackBtn = document.getElementById('unackBtn');

        const isAcknowledged = alarm.acknowledged === 1;
        const isCleared = alarm.cleared === 1;

        // 确认按钮
        ackBtn.disabled = isAcknowledged || isCleared;
        ackBtn.style.display = isAcknowledged ? 'none' : 'inline-block';

        // 清除按钮
        clearBtn.disabled = isCleared;

        // 分配按钮
        assignBtn.disabled = isCleared;

        // 取消确认按钮
        unackBtn.disabled = !isAcknowledged || isCleared;
        unackBtn.style.display = isAcknowledged && !isCleared ? 'inline-block' : 'none';
    }

    // 确认警报
    async function acknowledgeAlarm() {
        if (!currentAlarmId) return;

        try {
            const response = await fetch(`${API_BASE_URL}/D09M36B1/acknowledge`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Authorization': AUTH_HEADER
                },
                body: `alarmId=${currentAlarmId}`
            });

            const result = await response.json();
            if (result.code === 200) {
                showToast('警报已确认', 'success');
                selectAlarm(currentAlarmId); // 刷新详情
            } else {
                showToast('确认失败: ' + result.msg, 'error');
            }
        } catch (error) {
            showToast('网络错误: ' + error.message, 'error');
        }
    }

    // 清除警报
    async function clearAlarm() {
        if (!currentAlarmId) return;

        if (!confirm('确定要清除这个警报吗？')) return;

        try {
            const response = await fetch(`${API_BASE_URL}/D09M36B1/clear`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Authorization': AUTH_HEADER
                },
                body: `alarmId=${currentAlarmId}`
            });

            const result = await response.json();
            if (result.code === 200) {
                showToast('警报已清除', 'success');
                selectAlarm(currentAlarmId); // 刷新详情
            } else {
                showToast('清除失败: ' + result.msg, 'error');
            }
        } catch (error) {
            showToast('网络错误: ' + error.message, 'error');
        }
    }

    // 取消确认警报
    async function unacknowledgeAlarm() {
        if (!currentAlarmId) return;

        try {
            const response = await fetch(`${API_BASE_URL}/D09M36B1/unacknowledge`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Authorization': AUTH_HEADER
                },
                body: `alarmId=${currentAlarmId}`
            });

            const result = await response.json();
            if (result.code === 200) {
                showToast('已取消确认', 'success');
                selectAlarm(currentAlarmId); // 刷新详情
            } else {
                showToast('取消确认失败: ' + result.msg, 'error');
            }
        } catch (error) {
            showToast('网络错误: ' + error.message, 'error');
        }
    }

    // 显示分配模态框
    function showAssignModal() {
        const modal = new bootstrap.Modal(document.getElementById('assignModal'));
        document.getElementById('assigneeIdInput').value = '';
        document.getElementById('assigneeNameInput').value = '';
        modal.show();
    }

    // 分配警报
    async function assignAlarm() {
        if (!currentAlarmId) return;

        const assigneeId = document.getElementById('assigneeIdInput').value.trim();
        const assigneeName = document.getElementById('assigneeNameInput').value.trim();

        if (!assigneeId || !assigneeName) {
            showToast('请填写完整的分配信息', 'warning');
            return;
        }

        try {
            const response = await fetch(`${API_BASE_URL}/D09M36B1/assign`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Authorization': AUTH_HEADER
                },
                body: `alarmId=${currentAlarmId}&assigneeId=${assigneeId}&assigneeName=${encodeURIComponent(assigneeName)}`
            });

            const result = await response.json();
            if (result.code === 200) {
                showToast('警报已分配', 'success');
                bootstrap.Modal.getInstance(document.getElementById('assignModal')).hide();
                selectAlarm(currentAlarmId); // 刷新详情
            } else {
                showToast('分配失败: ' + result.msg, 'error');
            }
        } catch (error) {
            showToast('网络错误: ' + error.message, 'error');
        }
    }

    // 加载评论列表
    async function loadComments(alarmId) {
        try {
            const response = await fetch(`${API_BASE_URL}/D09M36B2/getCommentsByAlarmId?alarmId=${alarmId}`, {
                headers: {
                    'Authorization': AUTH_HEADER
                }
            });

            const result = await response.json();
            if (result.code === 200) {
                displayComments(result.data || []);
            } else {
                showToast('加载评论失败: ' + result.msg, 'error');
            }
        } catch (error) {
            showToast('网络错误: ' + error.message, 'error');
        }
    }

    // 显示评论列表
    function displayComments(comments) {
        const commentsList = document.getElementById('commentsList');

        if (comments.length === 0) {
            commentsList.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="bi bi-chat"></i>
                        <p class="mt-2">暂无评论</p>
                    </div>
                `;
            return;
        }

        commentsList.innerHTML = comments.map(comment => {
            const isSystemComment = comment.type === '0';
            const commentClass = isSystemComment ? 'comment-system' : 'comment-user';
            const iconClass = isSystemComment ? 'bi-gear' : 'bi-person';

            let commentContent = '';
            try {
                const commentData = JSON.parse(comment.comment);
                commentContent = commentData.text || comment.comment;
            } catch (e) {
                // 如果不是JSON格式，直接显示原文本
                commentContent = comment.comment || '无内容';
            }

            return `
                    <div class="comment-item ${commentClass}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi ${iconClass} me-2"></i>
                                    <strong>${isSystemComment ? '系统' : (comment.createby || '未知用户')}</strong>
                                    <span class="badge ${isSystemComment ? 'bg-success' : 'bg-primary'} ms-2">
                                        ${isSystemComment ? '系统操作' : '用户评论'}
                                    </span>
                                </div>
                                <p class="mb-1">${commentContent}</p>
                            </div>
                            <small class="text-muted">${formatDate(comment.createdate)}</small>
                        </div>
                    </div>
                `;
        }).join('');
    }

    // 显示添加评论模态框
    function showAddCommentModal() {
        const modal = new bootstrap.Modal(document.getElementById('commentModal'));
        document.getElementById('commentText').value = '';
        modal.show();
    }

    // 添加评论
    async function addComment() {
        if (!currentAlarmId) return;

        const commentText = document.getElementById('commentText').value.trim();
        if (!commentText) {
            showToast('请输入评论内容', 'warning');
            return;
        }

        try {
            const response = await fetch(`${API_BASE_URL}/D09M36B2/addUserComment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Authorization': AUTH_HEADER
                },
                body: `alarmId=${currentAlarmId}&commentText=${encodeURIComponent(commentText)}`
            });

            const result = await response.json();
            if (result.code === 200) {
                showToast('评论已添加', 'success');
                bootstrap.Modal.getInstance(document.getElementById('commentModal')).hide();
                loadComments(currentAlarmId); // 刷新评论列表
            } else {
                showToast('添加评论失败: ' + result.msg, 'error');
            }
        } catch (error) {
            showToast('网络错误: ' + error.message, 'error');
        }
    }

    // 清除过滤器
    function clearFilters() {
        document.getElementById('severityFilter').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('typeFilter').value = '';
        loadAlarms();
    }

    // 工具函数：获取警报状态
    function getAlarmStatus(alarm) {
        if (alarm.cleared === 1) return '已清除';
        if (alarm.acknowledged === 1) return '已确认';
        return '未确认';
    }

    // 工具函数：获取状态样式类
    function getStatusClass(status) {
        switch (status) {
            case '已清除':
                return 'status-cleared';
            case '已确认':
                return 'status-acknowledged';
            case '未确认':
                return 'status-active';
            case '活跃':
                return 'status-active';
            default:
                return 'status-active';
        }
    }

    // 工具函数：获取严重程度样式类
    function getSeverityClass(severity) {
        switch (severity) {
            case 'CRITICAL':
                return 'severity-critical';
            case 'MAJOR':
                return 'severity-major';
            case 'MINOR':
                return 'severity-minor';
            case 'WARNING':
                return 'severity-warning';
            case 'INDETERMINATE':
                return 'severity-indeterminate';
            default:
                return 'severity-indeterminate';
        }
    }

    // 工具函数：格式化时间戳
    function formatTimestamp(timestamp) {
        if (!timestamp) return '未知';
        const date = new Date(timestamp);
        return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour12: false});
    }

    // 工具函数：格式化日期
    function formatDate(dateStr) {
        if (!dateStr) return '未知';
        const date = new Date(dateStr);
        return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour12: false});
    }

    // 显示Toast通知
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast-notification ${type}`;
        toast.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi ${getToastIcon(type)} me-2"></i>
                    <span>${message}</span>
                </div>
            `;

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => toast.classList.add('show'), 100);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => document.body.removeChild(toast), 300);
        }, 3000);
    }

    // 获取Toast图标
    function getToastIcon(type) {
        switch (type) {
            case 'success':
                return 'bi-check-circle';
            case 'error':
                return 'bi-x-circle';
            case 'warning':
                return 'bi-exclamation-triangle';
            case 'info':
                return 'bi-info-circle';
            default:
                return 'bi-info-circle';
        }
    }

    // 显示加载状态
    function showLoading(message = '加载中...') {
        // 简单的加载提示实现
        const alarmList = document.getElementById('alarmList');
        alarmList.innerHTML = `
                <div class="text-center text-muted p-4">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">${message}</p>
                </div>
            `;
    }

    // 隐藏加载状态
    function hideLoading() {
        // 加载状态会被实际数据替换，这里不需要特殊处理
    }
</script>
</body>
</html>
