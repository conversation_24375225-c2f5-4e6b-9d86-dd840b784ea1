<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则链管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .rule-node-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .rule-node-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .rule-type-badge {
            font-size: 0.8em;
            padding: 4px 8px;
        }

        .json-editor {
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .config-section {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }

        .alarm-condition-row {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
        }

        .jsonpath-rule-row {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
        }

        .tab-content {
            border: 1px solid #dee2e6;
            border-top: none;
            padding: 20px;
            background-color: #fff;
        }

        .nav-tabs .nav-link.active {
            background-color: #0d6efd;
            color: white;
            border-color: #0d6efd;
        }

        .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .rule-chain-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-active {
            background-color: #28a745;
        }

        .status-inactive {
            background-color: #6c757d;
        }

        .status-debug {
            background-color: #ffc107;
        }

        /* 自动淡出提示样式 */
        .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            max-width: 500px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .toast-notification.show {
            transform: translateX(0);
        }

        .toast-notification.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .toast-notification.error {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
        }

        .toast-notification.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .toast-notification.info {
            background: linear-gradient(135deg, #17a2b8, #6f42c1);
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="rule-chain-header">
        <h2><i class="bi bi-diagram-3"></i> 规则链管理</h2>
        <p class="mb-0">智能配置IoT设备数据处理规则链和节点</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 左侧：规则链列表 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> 规则链列表</h5>
                    <button class="btn btn-primary btn-sm" onclick="showCreateRuleChainModal()">
                        <i class="bi bi-plus"></i> 新建
                    </button>
                </div>
                <div class="card-body p-0">
                    <div id="ruleChainList" class="list-group list-group-flush">
                        <!-- 规则链列表将在这里动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：规则链详情和节点管理 -->
        <div class="col-md-8">
            <div id="ruleChainDetail" style="display: none;">
                <!-- 规则链信息 -->
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" id="currentRuleChainName">规则链详情</h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editRuleChain()">
                                <i class="bi bi-pencil"></i> 编辑
                            </button>
                            <button class="btn btn-outline-success" onclick="setAsRoot()">
                                <i class="bi bi-star"></i> 设为根链
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteRuleChain()">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>规则链ID:</strong> <span id="ruleChainId"></span></p>
                                <p><strong>类型:</strong> <span id="ruleChainType"></span></p>
                                <p><strong>是否根链:</strong> <span id="isRoot"></span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>调试模式:</strong> <span id="debugMode"></span></p>
                                <p><strong>创建时间:</strong> <span id="createDate"></span></p>
                                <p><strong>创建者:</strong> <span id="createBy"></span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 规则节点管理 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-diagram-2"></i> 规则节点</h5>
                        <button class="btn btn-success btn-sm" onclick="showCreateRuleNodeModal()">
                            <i class="bi bi-plus"></i> 添加节点
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="ruleNodesList">
                            <!-- 规则节点列表将在这里动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 默认提示 -->
            <div id="defaultTip" class="text-center text-muted mt-5">
                <i class="bi bi-arrow-left" style="font-size: 2rem;"></i>
                <h4>请选择一个规则链</h4>
                <p>从左侧列表中选择一个规则链来查看和管理其节点</p>
            </div>
        </div>
    </div>
</div>

<!-- 创建/编辑规则链模态框 -->
<div class="modal fade" id="ruleChainModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ruleChainModalTitle">创建规则链</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="ruleChainForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">规则链名称 *</label>
                                <input type="text" class="form-control" id="rulechainname" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">规则链类型</label>
                                <select class="form-select" id="rulechaintype">
                                    <option value="CORE">核心规则链</option>
                                    <option value="EDGE">边缘规则链</option>
                                    <option value="CUSTOM">自定义规则链</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="debugmode">
                                    <label class="form-check-label" for="debugmode">启用调试模式</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="root">
                                    <label class="form-check-label" for="root">设为根规则链</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" id="remark" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRuleChain()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 创建/编辑规则节点模态框 -->
<div class="modal fade" id="ruleNodeModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ruleNodeModalTitle">创建规则节点</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="ruleNodeForm">
                    <!-- 基本信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">节点名称 *</label>
                                <input type="text" class="form-control" id="rulenodename" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">节点类型 *</label>
                                <select class="form-select" id="rulenodetype" onchange="onRuleNodeTypeChange()"
                                        required>
                                    <option value="">请选择</option>
                                    <option value="JsonPath">JsonPath数据提取</option>
                                    <option value="SaveTimeSeries">保存遥测数据</option>
                                    <option value="SaveAttributes">保存属性数据</option>
                                    <option value="Alarm">告警规则</option>
                                    <option value="Notifications">通知规则</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">执行顺序</label>
                                <input type="number" class="form-control" id="rownum" min="1" value="1">
                            </div>
                        </div>
                    </div>

                    <!-- 动态配置区域 -->
                    <div id="nodeConfigArea">
                        <!-- 配置内容将根据节点类型动态生成 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRuleNode()">保存</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // 全局变量
    const API_BASE_URL = 'http://dev.inksyun.com:31080/eam';
    const AUTH_HEADER = 'b8';
    let currentRuleChainId = null;
    let currentRuleNodeId = null;
    let isEditMode = false;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        loadRuleChains();
    });

    // 显示自动淡出提示
    function showToast(message, type = 'info', duration = 3000) {
        // 移除已存在的提示
        const existingToast = document.querySelector('.toast-notification');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建新提示
        const toast = document.createElement('div');
        toast.className = `toast-notification ${type}`;
        toast.innerHTML = `
                <div style="display: flex; align-items: center;">
                    <i class="bi bi-${getIconByType(type)} me-2"></i>
                    <span>${message}</span>
                </div>
            `;

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, duration);
    }

    // 根据类型获取图标
    function getIconByType(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // API请求封装
    async function apiRequest(url, method = 'GET', data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': AUTH_HEADER
            }
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(API_BASE_URL + url, options);
            const result = await response.json();

            if (result.code === 200) {
                return result.data;
            } else {
                throw new Error(result.msg || '请求失败');
            }
        } catch (error) {
            console.error('API请求错误:', error);
            showToast('请求失败: ' + error.message, 'error');
            throw error;
        }
    }

    // 加载规则链列表
    async function loadRuleChains() {
        try {
            const queryParam = {
                pageNum: 1,
                pageSize: 100
            };

            const result = await apiRequest('/D09M32B1/getPageList', 'POST', queryParam);
            displayRuleChains(result.list || []);
        } catch (error) {
            console.error('加载规则链失败:', error);
        }
    }

    // 显示规则链列表
    function displayRuleChains(ruleChains) {
        const listContainer = document.getElementById('ruleChainList');
        listContainer.innerHTML = '';

        if (ruleChains.length === 0) {
            listContainer.innerHTML = '<div class="p-3 text-muted text-center">暂无规则链</div>';
            return;
        }

        ruleChains.forEach(chain => {
            const statusClass = chain.root === 1 ? 'status-active' :
                chain.debugmode === 1 ? 'status-debug' : 'status-inactive';
            const statusText = chain.root === 1 ? '根链' :
                chain.debugmode === 1 ? '调试' : '普通';

            const item = document.createElement('div');
            item.className = 'list-group-item list-group-item-action';
            item.style.cursor = 'pointer';
            item.onclick = () => selectRuleChain(chain.id);

            item.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${chain.rulechainname}</h6>
                            <p class="mb-1 text-muted small">${chain.rulechaintype || '未分类'}</p>
                            <small class="text-muted">
                                <span class="status-indicator ${statusClass}"></span>
                                ${statusText}
                            </small>
                        </div>
                        <small class="text-muted">${formatDate(chain.createdate)}</small>
                    </div>
                `;

            listContainer.appendChild(item);
        });
    }

    // 选择规则链
    async function selectRuleChain(chainId) {
        try {
            currentRuleChainId = chainId;

            // 高亮选中的规则链
            document.querySelectorAll('#ruleChainList .list-group-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.list-group-item').classList.add('active');

            // 加载规则链详情
            const chainDetail = await apiRequest(`/D09M32B1/getEntity?key=${chainId}`);
            displayRuleChainDetail(chainDetail);

            // 加载规则节点
            await loadRuleNodes(chainId);

            // 显示详情区域
            document.getElementById('defaultTip').style.display = 'none';
            document.getElementById('ruleChainDetail').style.display = 'block';
        } catch (error) {
            console.error('选择规则链失败:', error);
        }
    }

    // 显示规则链详情
    function displayRuleChainDetail(chain) {
        document.getElementById('currentRuleChainName').textContent = chain.rulechainname;
        document.getElementById('ruleChainId').textContent = chain.id;
        document.getElementById('ruleChainType').textContent = chain.rulechaintype || '未设置';
        document.getElementById('isRoot').innerHTML = chain.root === 1 ?
            '<span class="badge bg-success">是</span>' : '<span class="badge bg-secondary">否</span>';
        document.getElementById('debugMode').innerHTML = chain.debugmode === 1 ?
            '<span class="badge bg-warning">开启</span>' : '<span class="badge bg-secondary">关闭</span>';
        document.getElementById('createDate').textContent = formatDate(chain.createdate);
        document.getElementById('createBy').textContent = chain.createby || '未知';
    }

    // 加载规则节点
    async function loadRuleNodes(chainId) {
        try {
            // 确保参数名一致
            const params = {chainid: chainId};  // 或者直接使用 chainId 作为查询参数
            const nodes = await apiRequest(`/D09M32B2/getListByChainid?chainid=${chainId}`, 'GET');
            displayRuleNodes(nodes || []);
        } catch (error) {
            console.error('加载规则节点失败:', error);
            document.getElementById('ruleNodesList').innerHTML =
                '<div class="alert alert-warning">加载规则节点失败</div>';
        }
    }


    // 显示规则节点列表
    function displayRuleNodes(nodes) {
        const container = document.getElementById('ruleNodesList');
        container.innerHTML = '';

        if (nodes.length === 0) {
            container.innerHTML = '<div class="text-center text-muted py-4">暂无规则节点，点击"添加节点"开始配置</div>';
            return;
        }

        // 按执行顺序排序
        nodes.sort((a, b) => (a.rownum || 0) - (b.rownum || 0));

        nodes.forEach(node => {
            const nodeCard = createRuleNodeCard(node);
            container.appendChild(nodeCard);
        });
    }

    // 创建规则节点卡片
    function createRuleNodeCard(node) {
        const card = document.createElement('div');
        card.className = 'rule-node-card p-3';

        const typeColors = {
            'JsonPath': 'primary',
            'SaveTimeSeries': 'success',
            'SaveAttributes': 'info',
            'Alarm': 'warning',
            'Notifications': 'danger'
        };

        const typeColor = typeColors[node.rulenodetype] || 'secondary';

        card.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-${typeColor} rule-type-badge me-2">${node.rulenodetype}</span>
                            <h6 class="mb-0">${node.rulenodename}</h6>
                            <span class="badge bg-light text-dark ms-2">顺序: ${node.rownum || 0}</span>
                        </div>
                        <div class="text-muted small">
                            ${getNodeConfigSummary(node)}
                        </div>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editRuleNode('${node.id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteRuleNode('${node.id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;

        return card;
    }

    // 获取节点配置摘要
    function getNodeConfigSummary(node) {
        if (!node.configuration) return '无配置';

        try {
            const config = JSON.parse(node.configuration);

            switch (node.rulenodetype) {
                case 'JsonPath':
                    const rules = Array.isArray(config) ? config : [];
                    return `提取 ${rules.length} 个字段: ${rules.map(r => r.alias).join(', ')}`;

                case 'Alarm':
                    return `告警规则: ${config.name || '未命名'} (${config.severity || 'WARNING'})`;

                case 'Notifications':
                    return `通知代理: ${config.agentcode || '未设置'}`;

                case 'SaveTimeSeries':
                    return '保存遥测数据到时序数据库';

                case 'SaveAttributes':
                    return '保存属性数据';

                default:
                    return '自定义配置';
            }
        } catch (e) {
            return '配置格式错误';
        }
    }

    // 显示创建规则链模态框
    function showCreateRuleChainModal() {
        isEditMode = false;
        currentRuleChainId = null;
        document.getElementById('ruleChainModalTitle').textContent = '创建规则链';
        document.getElementById('ruleChainForm').reset();
        new bootstrap.Modal(document.getElementById('ruleChainModal')).show();
    }

    // 编辑规则链
    async function editRuleChain() {
        if (!currentRuleChainId) return;

        try {
            const chain = await apiRequest(`/D09M32B1/getEntity?key=${currentRuleChainId}`);

            isEditMode = true;
            document.getElementById('ruleChainModalTitle').textContent = '编辑规则链';

            // 填充表单
            document.getElementById('rulechainname').value = chain.rulechainname || '';
            document.getElementById('rulechaintype').value = chain.rulechaintype || '';
            document.getElementById('debugmode').checked = chain.debugmode === 1;
            document.getElementById('root').checked = chain.root === 1;
            document.getElementById('remark').value = chain.remark || '';

            new bootstrap.Modal(document.getElementById('ruleChainModal')).show();
        } catch (error) {
            console.error('加载规则链详情失败:', error);
        }
    }

    // 保存规则链
    async function saveRuleChain() {
        const form = document.getElementById('ruleChainForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const data = {
            rulechainname: document.getElementById('rulechainname').value,
            rulechaintype: document.getElementById('rulechaintype').value,
            debugmode: document.getElementById('debugmode').checked ? 1 : 0,
            root: document.getElementById('root').checked ? 1 : 0,
            remark: document.getElementById('remark').value
        };

        try {
            if (isEditMode && currentRuleChainId) {
                data.id = currentRuleChainId;
                await apiRequest('/D09M32B1/update', 'POST', data);
                showToast('规则链更新成功！', 'success');
            } else {
                await apiRequest('/D09M32B1/create', 'POST', data);
                showToast('规则链创建成功！', 'success');
            }

            bootstrap.Modal.getInstance(document.getElementById('ruleChainModal')).hide();
            await loadRuleChains();

            if (isEditMode && currentRuleChainId) {
                await selectRuleChain(currentRuleChainId);
            }
        } catch (error) {
            console.error('保存规则链失败:', error);
        }
    }

    // 设为根规则链
    async function setAsRoot() {
        if (!currentRuleChainId) return;

        if (confirm('确定要将此规则链设为根规则链吗？这将取消其他规则链的根状态。')) {
            try {
                await apiRequest(`/D09M32B1/setRoot?key=${currentRuleChainId}`);
                alert('设置成功！');
                await loadRuleChains();
                await selectRuleChain(currentRuleChainId);
            } catch (error) {
                console.error('设置根规则链失败:', error);
            }
        }
    }

    // 删除规则链
    async function deleteRuleChain() {
        if (!currentRuleChainId) return;

        if (confirm('确定要删除此规则链吗？此操作不可恢复，同时会删除所有关联的规则节点。')) {
            try {
                await apiRequest(`/D09M32B1/delete?key=${currentRuleChainId}`);
                alert('删除成功！');

                // 重置界面
                currentRuleChainId = null;
                document.getElementById('ruleChainDetail').style.display = 'none';
                document.getElementById('defaultTip').style.display = 'block';

                await loadRuleChains();
            } catch (error) {
                console.error('删除规则链失败:', error);
            }
        }
    }

    // 显示创建规则节点模态框
    function showCreateRuleNodeModal() {
        if (!currentRuleChainId) {
            // 显示友好提示而不是弹窗
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="bi bi-exclamation-circle"></i> 请先选择规则链';
            btn.className = 'btn btn-warning btn-sm';

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.className = 'btn btn-success btn-sm';
            }, 2000);
            return;
        }

        isEditMode = false;
        currentRuleNodeId = null;
        document.getElementById('ruleNodeModalTitle').textContent = '创建规则节点';
        document.getElementById('ruleNodeForm').reset();
        document.getElementById('nodeConfigArea').innerHTML = '';
        new bootstrap.Modal(document.getElementById('ruleNodeModal')).show();
    }

    // 编辑规则节点
    async function editRuleNode(nodeId) {
        try {
            const node = await apiRequest(`/D09M32B2/getEntity?key=${nodeId}`);

            isEditMode = true;
            currentRuleNodeId = nodeId;
            document.getElementById('ruleNodeModalTitle').textContent = '编辑规则节点';

            // 填充基本信息
            document.getElementById('rulenodename').value = node.rulenodename || '';
            document.getElementById('rulenodetype').value = node.rulenodetype || '';
            document.getElementById('rownum').value = node.rownum || 1;

            // 生成配置界面并填充数据
            onRuleNodeTypeChange();
            setTimeout(() => {
                fillNodeConfiguration(node.configuration, node.rulenodetype);
            }, 100);

            new bootstrap.Modal(document.getElementById('ruleNodeModal')).show();
        } catch (error) {
            console.error('加载规则节点详情失败:', error);
        }
    }

    // 删除规则节点
    async function deleteRuleNode(nodeId) {
        if (confirm('确定要删除此规则节点吗？')) {
            try {
                await apiRequest(`/D09M32B2/delete?key=${nodeId}`);
                alert('删除成功！');
                await loadRuleNodes(currentRuleChainId);
            } catch (error) {
                console.error('删除规则节点失败:', error);
            }
        }
    }

    // 规则节点类型变化处理
    function onRuleNodeTypeChange() {
        const nodeType = document.getElementById('rulenodetype').value;
        const configArea = document.getElementById('nodeConfigArea');

        switch (nodeType) {
            case 'JsonPath':
                configArea.innerHTML = createJsonPathConfig();
                // JsonPath页面加载完成后自动调用"生成示例消息"
                setTimeout(() => {
                    generateMqttExample();
                    showToast('已自动生成JsonPath示例消息', 'info', 2000);
                }, 200);
                break;
            case 'Alarm':
                configArea.innerHTML = createAlarmConfig();
                // 初始化告警示例JSON和事件监听
                setTimeout(() => {
                    updateAlarmExampleJson();
                    setupAlarmEventListeners();
                    // Alarm页面加载完成后自动调用"手动更新示例"
                    updateAlarmExampleJson();
                    showToast('已自动更新Alarm示例数据', 'info', 2000);
                }, 200);
                break;
            case 'Notifications':
                configArea.innerHTML = createNotificationsConfig();
                break;
            case 'SaveTimeSeries':
            case 'SaveAttributes':
                configArea.innerHTML = createSimpleConfig(nodeType);
                break;
            default:
                configArea.innerHTML = '';
        }
    }

    // 创建JsonPath配置界面
    function createJsonPathConfig() {
        return `
                <div class="config-section">
                    <h6><i class="bi bi-code-slash"></i> JsonPath数据提取配置</h6>
                    <p class="text-muted small">配置从MQTT消息中提取的字段，支持JSONPath表达式</p>

                    <!-- MQTT消息示例输入区域 -->
                    <div class="mb-4">
                        <h6><i class="bi bi-clipboard-data"></i> MQTT消息示例</h6>
                        <p class="text-muted small">粘贴您的MQTT消息JSON，系统将自动分析并辅助生成JSONPath表达式</p>
                        <div class="row">
                            <div class="col-md-8">
                                <textarea class="form-control json-editor" id="mqttJsonInput" rows="8"
                                          placeholder='粘贴MQTT消息JSON，例如：
{
  "sn": "ABC12138",
  "ts": 333333,
  "msg": {
    "info": {
      "content": [
        {"key": "temp", "value": 55},
        {"key": "dianya", "value": "220V"},
        {"key": "weizhi", "value": 27.21},
        {"key": "xinhao", "value": "差"}
      ]
    }
  }
}'></textarea>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-primary" onclick="analyzeJsonStructure()">
                                        <i class="bi bi-search"></i> 分析JSON结构
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="autoGenerateRules()">
                                        <i class="bi bi-magic"></i> 自动生成规则
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="clearJsonInput()">
                                        <i class="bi bi-eraser"></i> 清空
                                    </button>
                                </div>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-lightbulb"></i>
                                        <strong>提示：</strong><br>
                                        • 粘贴JSON后点击"分析结构"<br>
                                        • 在下方字段配置时会自动提示路径<br>
                                        • 支持复杂嵌套结构解析
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- JSON结构分析结果 -->
                        <div id="jsonAnalysisResult" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-tree"></i> JSON结构分析</h6>
                                <div id="jsonStructureTree"></div>
                            </div>
                        </div>
                    </div>

                    <div id="jsonPathRules">
                        <div class="jsonpath-rule-row">
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">字段别名</label>
                                    <input type="text" class="form-control" placeholder="如: temp" name="alias"
                                           onchange="suggestJsonPath(this); onJsonPathFieldChange();">
                                </div>
                                <div class="col-md-7">
                                    <label class="form-label">JSONPath表达式</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="如: msg.info.content[?(@.key=='temp')].value" name="path"
                                               onchange="onJsonPathFieldChange();">
                                        <button type="button" class="btn btn-outline-secondary" onclick="showPathSuggestions(this)" title="路径建议">
                                            <i class="bi bi-lightbulb"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeJsonPathRule(this); onJsonPathFieldChange();">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="addJsonPathRule()">
                        <i class="bi bi-plus"></i> 添加字段
                    </button>

                    <div class="mt-3">
                        <h6>常用模板</h6>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="applyJsonPathTemplate('iot')">
                                IoT设备模板
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="applyJsonPathTemplate('sensor')">
                                传感器模板
                            </button>
                        </div>
                    </div>

                    <!-- MQTT消息示例生成 -->
                    <div class="mt-4">
                        <h6><i class="bi bi-eye"></i> 提取结果预览</h6>
                        <div class="card">
                            <div class="card-body">
                                <h6 class="text-success">根据当前数据库中保存的JsonPath规则，反推的原始MQTT消息示例：</h6>
                                <pre id="jsonPathGeneratedExample" class="json-editor mb-0" style="background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 10px; font-size: 12px; max-height: 300px; overflow-y: auto;">
{
  "ts": 1640995200000,
  "msg": {
    "info": {
      "content": [
        {"key": "temp", "value": 55},
        {"key": "dianya", "value": "220V"},
        {"key": "weizhi", "value": 27.21},
        {"key": "xinhao", "value": "差"}
      ]
    }
  }
}
                                </pre>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="generateMqttExample()">
                                        <i class="bi bi-magic"></i> 生成示例消息
                                    </button>
                                    <small class="text-muted ms-3">
                                        <i class="bi bi-info-circle"></i>
                                        此示例会根据您配置的字段别名自动生成对应的MQTT消息结构
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
    }

    // 创建告警配置界面
    function createAlarmConfig() {
        return `
                <div class="config-section">
                    <h6><i class="bi bi-exclamation-triangle"></i> 告警规则配置</h6>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">告警名称</label>
                            <input type="text" class="form-control" id="alarmName" placeholder="如: 高温高压告警">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">告警类型</label>
                            <select class="form-select" id="alarmType">
                                <option value="SIMPLE">简单告警</option>
                                <option value="DURATION">持续时间告警</option>
                                <option value="REPEATING">重复告警</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">严重程度</label>
                            <select class="form-select" id="alarmSeverity">
                                <option value="WARNING">警告</option>
                                <option value="MINOR">轻微</option>
                                <option value="MAJOR">重要</option>
                                <option value="CRITICAL">严重</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">条件类型</label>
                        <select class="form-select" id="conditionType" onchange="onConditionTypeChange()">
                            <option value="SIMPLE">单一条件</option>
                            <option value="AND">AND组合条件</option>
                            <option value="OR">OR组合条件</option>
                        </select>
                    </div>

                    <div id="alarmConditions">
                        <div class="alarm-condition-row">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">字段名</label>
                                    <input type="text" class="form-control" placeholder="如: temp" name="conditionKey">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">操作符</label>
                                    <select class="form-select" name="conditionOperation">
                                        <option value="GREATER">大于 ></option>
                                        <option value="GREATER_OR_EQUAL">大于等于 >=</option>
                                        <option value="LESS">小于 <</option>
                                        <option value="LESS_OR_EQUAL">小于等于 <=</option>
                                        <option value="EQUALS">等于 =</option>
                                        <option value="NOT_EQUALS">不等于 !=</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">阈值</label>
                                    <input type="text" class="form-control" placeholder="如: 50" name="conditionValue">
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeAlarmCondition(this)">
                                        <i class="bi bi-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="addAlarmCondition()" id="addConditionBtn" style="display: none;">
                        <i class="bi bi-plus"></i> 添加条件
                    </button>

                    <div class="mt-3">
                        <h6>快速模板</h6>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="applyAlarmTemplate('temperature')">
                                温度告警
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="applyAlarmTemplate('voltage')">
                                电压告警
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="applyAlarmTemplate('combined')">
                                组合告警
                            </button>
                        </div>
                    </div>

                    <!-- 示例数据展示区域 -->
                    <div class="mt-4">
                        <h6><i class="bi bi-lightbulb"></i> 满足条件的数据示例</h6>
                        <p class="text-muted small">以下是满足当前告警条件的JSON数据示例，供设计者参考：</p>
                        <div class="card">
                            <div class="card-body">
                                <pre id="alarmExampleJson" class="json-editor mb-0" style="background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 10px; font-size: 12px; max-height: 200px; overflow-y: auto;">
{
  "ts": 1753338887932,
  "temp": 55,
  "dianya": "220V"
}
                                </pre>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="updateAlarmExampleJson()">
                                        <i class="bi bi-refresh"></i> 手动更新示例
                                    </button>
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        此示例会根据您设置的告警条件自动更新
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
    }

    // 创建通知配置界面
    function createNotificationsConfig() {
        return `
                <div class="config-section">
                    <h6><i class="bi bi-bell"></i> 通知规则配置</h6>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">代理代码</label>
                            <input type="text" class="form-control" id="agentCode" placeholder="如: ems-wxebot">
                            <div class="form-text">用于标识通知发送的代理服务</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">通知类型</label>
                            <select class="form-select" id="notificationType" onchange="onNotificationTypeChange()">
                                <option value="wxebot">企业微信机器人</option>
                                <option value="wxe">企业微信应用</option>
                                <option value="ding">钉钉应用</option>
                                <option value="dingbot">钉钉机器人</option>
                                <option value="email">邮件通知</option>
                                <option value="mqtt">MQTT推送</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">消息模板</label>
                        <textarea class="form-control" id="messageTemplate" rows="4"
                                  placeholder="支持变量替换，如: $sn, $ts, $temp, $dianya 等"></textarea>
                        <div class="form-text">
                            可用变量: $sn(设备序列号), $ts(时间戳), $temp(温度), $dianya(电压), $weizhi(位置), $xinhao(信号)
                        </div>
                    </div>

                    <div class="mt-3">
                        <h6>模板示例</h6>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="applyNotificationTemplate('wechat')">
                                微信告警模板
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="applyNotificationTemplate('simple')">
                                简单通知模板
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="applyNotificationTemplate('detailed')">
                                详细报告模板
                            </button>
                        </div>
                    </div>
                </div>
            `;
    }

    // 创建简单配置界面
    function createSimpleConfig(nodeType) {
        const descriptions = {
            'SaveTimeSeries': '将提取的数据保存到时序数据库，用于历史数据查询和分析',
            'SaveAttributes': '将提取的数据保存为设备属性，用于设备状态管理'
        };

        return `
                <div class="config-section">
                    <h6><i class="bi bi-database"></i> ${nodeType} 配置</h6>
                    <p class="text-muted">${descriptions[nodeType]}</p>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> 此节点类型无需额外配置，将自动处理前序节点提取的数据。
                    </div>
                </div>
            `;
    }

    // JsonPath规则管理函数
    function addJsonPathRule() {
        const container = document.getElementById('jsonPathRules');
        const newRule = document.createElement('div');
        newRule.className = 'jsonpath-rule-row';
        newRule.innerHTML = `
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">字段别名</label>
                        <input type="text" class="form-control" placeholder="如: temp" name="alias"
                               onchange="suggestJsonPath(this); onJsonPathFieldChange();">
                    </div>
                    <div class="col-md-7">
                        <label class="form-label">JSONPath表达式</label>
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="如: msg.info.content[?(@.key=='temp')].value" name="path"
                                   onchange="onJsonPathFieldChange();">
                            <button type="button" class="btn btn-outline-secondary" onclick="showPathSuggestions(this)" title="路径建议">
                                <i class="bi bi-lightbulb"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeJsonPathRule(this); onJsonPathFieldChange();">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        container.appendChild(newRule);
    }

    function removeJsonPathRule(button) {
        const container = document.getElementById('jsonPathRules');
        if (container.children.length > 1) {
            button.closest('.jsonpath-rule-row').remove();
        }
    }

    function applyJsonPathTemplate(type) {
        const container = document.getElementById('jsonPathRules');
        container.innerHTML = '';

        const templates = {
            'iot': [
                {alias: 'temp', path: "msg.info.content[?(@.key=='temp')].value"},
                {alias: 'dianya', path: "msg.info.content[?(@.key=='dianya')].value"},
                {alias: 'weizhi', path: "msg.info.content[?(@.key=='weizhi')].value"},
                {alias: 'xinhao', path: "msg.info.content[?(@.key=='xinhao')].value"}
            ],
            'sensor': [
                {alias: 'temperature', path: "data.temperature"},
                {alias: 'humidity', path: "data.humidity"},
                {alias: 'pressure', path: "data.pressure"}
            ]
        };

        const rules = templates[type] || [];
        rules.forEach(rule => {
            const ruleDiv = document.createElement('div');
            ruleDiv.className = 'jsonpath-rule-row';
            ruleDiv.innerHTML = `
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">字段别名</label>
                            <input type="text" class="form-control" name="alias" value="${rule.alias}">
                        </div>
                        <div class="col-md-7">
                            <label class="form-label">JSONPath表达式</label>
                            <input type="text" class="form-control" name="path" value="${rule.path}">
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeJsonPathRule(this)">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            container.appendChild(ruleDiv);
        });
    }

    // 告警条件管理函数
    function onConditionTypeChange() {
        const conditionType = document.getElementById('conditionType').value;
        const addBtn = document.getElementById('addConditionBtn');

        if (conditionType === 'AND' || conditionType === 'OR') {
            addBtn.style.display = 'block';
        } else {
            addBtn.style.display = 'none';
            // 保留第一个条件，删除其他
            const container = document.getElementById('alarmConditions');
            while (container.children.length > 1) {
                container.removeChild(container.lastChild);
            }
        }

        // 更新示例JSON
        updateAlarmExampleJson();
    }

    function addAlarmCondition() {
        const container = document.getElementById('alarmConditions');
        const newCondition = document.createElement('div');
        newCondition.className = 'alarm-condition-row';
        newCondition.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">字段名</label>
                        <input type="text" class="form-control" placeholder="如: temp" name="conditionKey">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">操作符</label>
                        <select class="form-select" name="conditionOperation">
                            <option value="GREATER">大于 ></option>
                            <option value="GREATER_OR_EQUAL">大于等于 >=</option>
                            <option value="LESS">小于 <</option>
                            <option value="LESS_OR_EQUAL">小于等于 <=</option>
                            <option value="EQUALS">等于 =</option>
                            <option value="NOT_EQUALS">不等于 !=</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">阈值</label>
                        <input type="text" class="form-control" placeholder="如: 50" name="conditionValue">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeAlarmCondition(this)">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `;
        container.appendChild(newCondition);

        // 为新添加的条件行添加事件监听
        addEventListenersToConditionRow(newCondition);
    }

    function removeAlarmCondition(button) {
        const container = document.getElementById('alarmConditions');
        if (container.children.length > 1) {
            button.closest('.alarm-condition-row').remove();
            updateAlarmExampleJson();
        }
    }

    function applyAlarmTemplate(type) {
        const templates = {
            'temperature': {
                name: '高温告警',
                severity: 'WARNING',
                conditions: [{key: 'temp', operation: 'GREATER', value: '50'}]
            },
            'voltage': {
                name: '电压异常告警',
                severity: 'MAJOR',
                conditions: [{key: 'dianya', operation: 'NOT_EQUALS', value: '220V'}]
            },
            'combined': {
                name: '高温高压告警',
                severity: 'WARNING',
                conditionType: 'AND',
                conditions: [
                    {key: 'temp', operation: 'GREATER', value: '50'},
                    {key: 'dianya', operation: 'EQUALS', value: '220V'}
                ]
            }
        };

        const template = templates[type];
        if (!template) return;

        document.getElementById('alarmName').value = template.name;
        document.getElementById('alarmSeverity').value = template.severity;

        if (template.conditionType) {
            document.getElementById('conditionType').value = template.conditionType;
            onConditionTypeChange();
        }

        // 清空现有条件
        const container = document.getElementById('alarmConditions');
        container.innerHTML = '';

        // 添加模板条件
        template.conditions.forEach(condition => {
            const conditionDiv = document.createElement('div');
            conditionDiv.className = 'alarm-condition-row';
            conditionDiv.innerHTML = `
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">字段名</label>
                            <input type="text" class="form-control" name="conditionKey" value="${condition.key}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">操作符</label>
                            <select class="form-select" name="conditionOperation">
                                <option value="GREATER" ${condition.operation === 'GREATER' ? 'selected' : ''}>大于 ></option>
                                <option value="GREATER_OR_EQUAL" ${condition.operation === 'GREATER_OR_EQUAL' ? 'selected' : ''}>大于等于 >=</option>
                                <option value="LESS" ${condition.operation === 'LESS' ? 'selected' : ''}>小于 <</option>
                                <option value="LESS_OR_EQUAL" ${condition.operation === 'LESS_OR_EQUAL' ? 'selected' : ''}>小于等于 <=</option>
                                <option value="EQUALS" ${condition.operation === 'EQUALS' ? 'selected' : ''}>等于 =</option>
                                <option value="NOT_EQUALS" ${condition.operation === 'NOT_EQUALS' ? 'selected' : ''}>不等于 !=</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">阈值</label>
                            <input type="text" class="form-control" name="conditionValue" value="${condition.value}">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeAlarmCondition(this)">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                `;
            container.appendChild(conditionDiv);
        });

        // 为新创建的条件行添加事件监听
        setTimeout(() => {
            setupAlarmEventListeners();
            updateAlarmExampleJson();
        }, 100);
    }

    // 通知模板函数
    function applyNotificationTemplate(type) {
        const templates = {
            'wechat': {
                agentCode: 'ems-wxebot',
                template: '> ⚠️ **告警提示**  \n> 设备 **[$sn]** 在 **[$ts]** 时间点：  \n> • 🌡️ 温度：**$temp℃**  \n> • 🔋 电压：**$dianya**  \n> 请注意及时处理！'
            },
            'simple': {
                agentCode: 'ems001',
                template: '设备[$sn]在[$ts]时间点温度为[$temp]℃，电压为[$dianya]，请注意！'
            },
            'detailed': {
                agentCode: 'ems-detail',
                template: '【设备告警详情】\n设备编号：$sn\n告警时间：$ts\n温度：$temp℃\n电压：$dianya\n位置：$weizhi\n信号强度：$xinhao\n请及时处理！'
            }
        };

        const template = templates[type];
        if (template) {
            document.getElementById('agentCode').value = template.agentCode;
            document.getElementById('messageTemplate').value = template.template;
        }
    }

    // 保存规则节点
    async function saveRuleNode() {
        const form = document.getElementById('ruleNodeForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const nodeType = document.getElementById('rulenodetype').value;
        const configuration = generateNodeConfiguration(nodeType);

        if (configuration === null) {
            alert('配置验证失败，请检查输入');
            return;
        }

        const data = {
            rulechainid: currentRuleChainId,
            rulenodename: document.getElementById('rulenodename').value,
            rulenodetype: nodeType,
            rownum: parseInt(document.getElementById('rownum').value) || 1,
            configuration: configuration,
            configurationversion: 0,
            singletonmode: 0
        };

        try {
            if (isEditMode && currentRuleNodeId) {
                data.id = currentRuleNodeId;
                await apiRequest('/D09M32B2/update', 'POST', data);
                alert('规则节点更新成功！');
            } else {
                await apiRequest('/D09M32B2/create', 'POST', data);
                alert('规则节点创建成功！');
            }

            bootstrap.Modal.getInstance(document.getElementById('ruleNodeModal')).hide();
            await loadRuleNodes(currentRuleChainId);
        } catch (error) {
            console.error('保存规则节点失败:', error);
        }
    }

    // 生成节点配置
    function generateNodeConfiguration(nodeType) {
        switch (nodeType) {
            case 'JsonPath':
                return generateJsonPathConfiguration();
            case 'Alarm':
                return generateAlarmConfiguration();
            case 'Notifications':
                return generateNotificationsConfiguration();
            case 'SaveTimeSeries':
            case 'SaveAttributes':
                return ''; // 这些节点类型不需要配置
            default:
                return '';
        }
    }

    // 生成JsonPath配置
    function generateJsonPathConfiguration() {
        const rules = [];
        const ruleRows = document.querySelectorAll('#jsonPathRules .jsonpath-rule-row');

        for (const row of ruleRows) {
            const alias = row.querySelector('input[name="alias"]').value.trim();
            const path = row.querySelector('input[name="path"]').value.trim();

            if (alias && path) {
                rules.push({alias, path});
            }
        }

        if (rules.length === 0) {
            alert('请至少配置一个JsonPath规则');
            return null;
        }

        return JSON.stringify(rules);
    }

    // 生成告警配置
    function generateAlarmConfiguration() {
        const name = document.getElementById('alarmName').value.trim();
        const type = document.getElementById('alarmType').value;
        const severity = document.getElementById('alarmSeverity').value;
        const conditionType = document.getElementById('conditionType').value;

        if (!name) {
            alert('请输入告警名称');
            return null;
        }

        const conditions = [];
        const conditionRows = document.querySelectorAll('#alarmConditions .alarm-condition-row');

        for (const row of conditionRows) {
            const key = row.querySelector('input[name="conditionKey"]').value.trim();
            const operation = row.querySelector('select[name="conditionOperation"]').value;
            const value = row.querySelector('input[name="conditionValue"]').value.trim();

            if (key && operation && value) {
                const conditionValue = isNaN(value) ? value : parseFloat(value);
                conditions.push({
                    type: 'SIMPLE',
                    key,
                    operation,
                    value: conditionValue
                });
            }
        }

        if (conditions.length === 0) {
            alert('请至少配置一个告警条件');
            return null;
        }

        const config = {
            name,
            type,
            severity,
            condition: conditions.length === 1 && conditionType === 'SIMPLE' ?
                conditions[0] : {
                    type: conditionType,
                    conditions
                }
        };

        return JSON.stringify(config);
    }

    // 生成通知配置
    function generateNotificationsConfiguration() {
        const agentcode = document.getElementById('agentCode').value.trim();
        const template = document.getElementById('messageTemplate').value.trim();

        if (!agentcode || !template) {
            alert('请填写代理代码和消息模板');
            return null;
        }

        return JSON.stringify({agentcode, template});
    }

    // 填充节点配置（编辑时使用）
    function fillNodeConfiguration(configStr, nodeType) {
        if (!configStr) return;

        try {
            const config = JSON.parse(configStr);

            switch (nodeType) {
                case 'JsonPath':
                    fillJsonPathConfiguration(config);
                    break;
                case 'Alarm':
                    fillAlarmConfiguration(config);
                    break;
                case 'Notifications':
                    fillNotificationsConfiguration(config);
                    break;
            }
        } catch (e) {
            console.error('解析配置失败:', e);
        }
    }

    // 填充JsonPath配置
    function fillJsonPathConfiguration(config) {
        if (!Array.isArray(config)) return;

        const container = document.getElementById('jsonPathRules');
        container.innerHTML = '';

        config.forEach(rule => {
            const ruleDiv = document.createElement('div');
            ruleDiv.className = 'jsonpath-rule-row';
            ruleDiv.innerHTML = `
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">字段别名</label>
                            <input type="text" class="form-control" name="alias" value="${rule.alias || ''}">
                        </div>
                        <div class="col-md-7">
                            <label class="form-label">JSONPath表达式</label>
                            <input type="text" class="form-control" name="path" value="${rule.path || ''}">
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeJsonPathRule(this)">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            container.appendChild(ruleDiv);
        });
    }

    // 填充告警配置
    function fillAlarmConfiguration(config) {
        document.getElementById('alarmName').value = config.name || '';
        document.getElementById('alarmType').value = config.type || 'SIMPLE';
        document.getElementById('alarmSeverity').value = config.severity || 'WARNING';

        const condition = config.condition;
        if (!condition) return;

        if (condition.type === 'AND' || condition.type === 'OR') {
            document.getElementById('conditionType').value = condition.type;
            onConditionTypeChange();

            const container = document.getElementById('alarmConditions');
            container.innerHTML = '';

            condition.conditions.forEach(cond => {
                const conditionDiv = document.createElement('div');
                conditionDiv.className = 'alarm-condition-row';
                conditionDiv.innerHTML = `
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">字段名</label>
                                <input type="text" class="form-control" name="conditionKey" value="${cond.key || ''}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">操作符</label>
                                <select class="form-select" name="conditionOperation">
                                    <option value="GREATER" ${cond.operation === 'GREATER' ? 'selected' : ''}>大于 ></option>
                                    <option value="GREATER_OR_EQUAL" ${cond.operation === 'GREATER_OR_EQUAL' ? 'selected' : ''}>大于等于 >=</option>
                                    <option value="LESS" ${cond.operation === 'LESS' ? 'selected' : ''}>小于 <</option>
                                    <option value="LESS_OR_EQUAL" ${cond.operation === 'LESS_OR_EQUAL' ? 'selected' : ''}>小于等于 <=</option>
                                    <option value="EQUALS" ${cond.operation === 'EQUALS' ? 'selected' : ''}>等于 =</option>
                                    <option value="NOT_EQUALS" ${cond.operation === 'NOT_EQUALS' ? 'selected' : ''}>不等于 !=</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">阈值</label>
                                <input type="text" class="form-control" name="conditionValue" value="${cond.value || ''}">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeAlarmCondition(this)">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    `;
                container.appendChild(conditionDiv);
            });
        } else {
            // 单一条件
            document.getElementById('conditionType').value = 'SIMPLE';
            const firstRow = document.querySelector('#alarmConditions .alarm-condition-row');
            if (firstRow) {
                firstRow.querySelector('input[name="conditionKey"]').value = condition.key || '';
                firstRow.querySelector('select[name="conditionOperation"]').value = condition.operation || '';
                firstRow.querySelector('input[name="conditionValue"]').value = condition.value || '';
            }
        }
    }

    // 填充通知配置
    function fillNotificationsConfiguration(config) {
        document.getElementById('agentCode').value = config.agentcode || '';
        document.getElementById('messageTemplate').value = config.template || '';
    }

    // 设置告警事件监听器
    function setupAlarmEventListeners() {
        console.log('设置告警事件监听器');

        // 为所有现有的条件行添加事件监听
        const conditionRows = document.querySelectorAll('#alarmConditions .alarm-condition-row');
        conditionRows.forEach((row, index) => {
            console.log(`为条件行 ${index + 1} 添加事件监听`);
            addEventListenersToConditionRow(row);
        });
    }

    // 为单个条件行添加事件监听
    function addEventListenersToConditionRow(row) {
        const keyInput = row.querySelector('input[name="conditionKey"]');
        const operationSelect = row.querySelector('select[name="conditionOperation"]');
        const valueInput = row.querySelector('input[name="conditionValue"]');

        if (keyInput) {
            keyInput.addEventListener('input', updateAlarmExampleJson);
            keyInput.addEventListener('change', updateAlarmExampleJson);
            keyInput.addEventListener('blur', updateAlarmExampleJson);
        }

        if (operationSelect) {
            operationSelect.addEventListener('change', updateAlarmExampleJson);
        }

        if (valueInput) {
            valueInput.addEventListener('input', updateAlarmExampleJson);
            valueInput.addEventListener('change', updateAlarmExampleJson);
            valueInput.addEventListener('blur', updateAlarmExampleJson);
        }
    }

    // 更新告警示例JSON
    function updateAlarmExampleJson() {
        const exampleContainer = document.getElementById('alarmExampleJson');
        if (!exampleContainer) {
            return;
        }

        // 获取当前条件
        const conditionRows = document.querySelectorAll('#alarmConditions .alarm-condition-row');

        const exampleData = {
            ts: Date.now()
        };

        let hasValidConditions = false;

        // 根据条件生成满足条件的示例数据
        conditionRows.forEach((row, index) => {
            const keyInput = row.querySelector('input[name="conditionKey"]');
            const operationSelect = row.querySelector('select[name="conditionOperation"]');
            const valueInput = row.querySelector('input[name="conditionValue"]');

            const key = keyInput?.value?.trim();
            const operation = operationSelect?.value;
            const value = valueInput?.value?.trim();

            if (key && operation && value) {
                // 根据操作符生成满足条件的示例值
                exampleData[key] = generateExampleValue(key, operation, value);
                hasValidConditions = true;
            }
        });

        // 如果没有有效条件，显示提示信息
        if (!hasValidConditions) {
            exampleContainer.textContent = '请配置告警条件以查看示例数据';
            return;
        }

        // 格式化并显示JSON
        exampleContainer.textContent = JSON.stringify(exampleData, null, 2);
    }

    // 根据条件生成示例值
    function generateExampleValue(key, operation, thresholdValue) {
        const numValue = parseFloat(thresholdValue);

        // 如果是数字类型的阈值
        if (!isNaN(numValue)) {
            switch (operation) {
                case 'GREATER':
                    return numValue + 5; // 比阈值大5
                case 'GREATER_OR_EQUAL':
                    return numValue; // 等于阈值
                case 'LESS':
                    return numValue - 5; // 比阈值小5
                case 'LESS_OR_EQUAL':
                    return numValue; // 等于阈值
                case 'EQUALS':
                    return numValue; // 等于阈值
                case 'NOT_EQUALS':
                    return numValue + 1; // 不等于阈值
                default:
                    return numValue;
            }
        } else {
            // 字符串类型的阈值
            switch (operation) {
                case 'EQUALS':
                    return thresholdValue; // 等于阈值
                case 'NOT_EQUALS':
                    return thresholdValue + "_modified"; // 不等于阈值
                default:
                    return thresholdValue;
            }
        }
    }

    // JSON结构分析相关函数
    let analyzedJsonStructure = null;

    // 分析JSON结构
    function analyzeJsonStructure() {
        const jsonInput = document.getElementById('mqttJsonInput').value.trim();
        if (!jsonInput) {
            showToast('请先输入MQTT消息JSON', 'warning');
            return false;
        }

        try {
            const jsonData = JSON.parse(jsonInput);
            analyzedJsonStructure = jsonData;

            // 分析JSON结构并显示
            const paths = extractAllPaths(jsonData);
            displayJsonStructure(paths);

            showToast('JSON结构分析完成！', 'success');
            return true;
        } catch (error) {
            showToast('JSON格式错误：' + error.message, 'error');
            return false;
        }
    }

    // 提取JSON中所有可能的路径
    function extractAllPaths(obj, currentPath = '', paths = []) {
        if (typeof obj !== 'object' || obj === null) {
            return paths;
        }

        if (Array.isArray(obj)) {
            // 处理数组
            obj.forEach((item, index) => {
                if (typeof item === 'object' && item !== null) {
                    // 如果数组元素是对象，检查是否有key-value结构
                    if (item.key && item.value !== undefined) {
                        // 特殊处理key-value结构
                        paths.push({
                            path: `${currentPath}[?(@.key=='${item.key}')].value`,
                            alias: item.key,
                            value: item.value,
                            type: typeof item.value
                        });
                    } else {
                        // 普通对象数组
                        extractAllPaths(item, `${currentPath}[${index}]`, paths);
                    }
                } else {
                    paths.push({
                        path: `${currentPath}[${index}]`,
                        alias: `item_${index}`,
                        value: item,
                        type: typeof item
                    });
                }
            });
        } else {
            // 处理对象
            Object.keys(obj).forEach(key => {
                const newPath = currentPath ? `${currentPath}.${key}` : key;
                const value = obj[key];

                if (typeof value === 'object' && value !== null) {
                    extractAllPaths(value, newPath, paths);
                } else {
                    paths.push({
                        path: newPath,
                        alias: key,
                        value: value,
                        type: typeof value
                    });
                }
            });
        }

        return paths;
    }

    // 显示JSON结构分析结果
    function displayJsonStructure(paths) {
        const resultDiv = document.getElementById('jsonAnalysisResult');
        const treeDiv = document.getElementById('jsonStructureTree');

        let html = '<div class="row">';
        html += '<div class="col-md-6"><h6>可提取字段：</h6><ul class="list-group list-group-flush">';

        paths.forEach(pathInfo => {
            const typeColor = pathInfo.type === 'number' ? 'primary' :
                pathInfo.type === 'string' ? 'success' : 'secondary';
            html += `
                    <li class="list-group-item d-flex justify-content-between align-items-center py-1">
                        <div>
                            <strong>${pathInfo.alias}</strong>
                            <small class="text-muted d-block">${pathInfo.path}</small>
                        </div>
                        <div>
                            <span class="badge bg-${typeColor}">${pathInfo.type}</span>
                            <button type="button" class="btn btn-outline-primary btn-sm ms-1"
                                    onclick="useThisPath('${pathInfo.alias}', '${pathInfo.path}'); event.stopPropagation();"
                                    title="使用此路径">
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                    </li>
                `;
        });

        html += '</ul></div>';
        html += '<div class="col-md-6"><h6>示例值：</h6><ul class="list-group list-group-flush">';

        paths.forEach(pathInfo => {
            html += `
                    <li class="list-group-item py-1">
                        <strong>${pathInfo.alias}:</strong>
                        <code>${JSON.stringify(pathInfo.value)}</code>
                    </li>
                `;
        });

        html += '</ul></div></div>';

        treeDiv.innerHTML = html;
        resultDiv.style.display = 'block';
    }

    // 使用指定的路径
    function useThisPath(alias, path) {
        try {
            // 查找空的规则行或添加新行
            const container = document.getElementById('jsonPathRules');
            if (!container) {
                showToast('配置区域未找到', 'error');
                return;
            }

            let emptyRow = null;

            // 查找空的规则行
            const rows = container.querySelectorAll('.jsonpath-rule-row');
            for (const row of rows) {
                const aliasInput = row.querySelector('input[name="alias"]');
                const pathInput = row.querySelector('input[name="path"]');
                if (aliasInput && pathInput && !aliasInput.value.trim() && !pathInput.value.trim()) {
                    emptyRow = row;
                    break;
                }
            }

            // 如果没有空行，添加新行
            if (!emptyRow) {
                addJsonPathRule();
                // 等待DOM更新
                setTimeout(() => {
                    emptyRow = container.lastElementChild;
                    if (emptyRow) {
                        fillRowData(emptyRow, alias, path);
                    }
                }, 50);
            } else {
                fillRowData(emptyRow, alias, path);
            }

        } catch (error) {
            console.error('使用路径时出错:', error);
            showToast('添加字段时出错', 'error');
        }
    }

    // 填充行数据的辅助函数
    function fillRowData(row, alias, path) {
        try {
            const aliasInput = row.querySelector('input[name="alias"]');
            const pathInput = row.querySelector('input[name="path"]');

            if (aliasInput && pathInput) {
                aliasInput.value = alias;
                pathInput.value = path;
                showToast(`已添加字段：${alias}`, 'success', 2000);
            } else {
                showToast('字段输入框未找到', 'error');
            }
        } catch (error) {
            console.error('填充数据时出错:', error);
            showToast('填充数据时出错', 'error');
        }
    }

    // 自动生成所有规则
    function autoGenerateRules() {
        // 先检查是否有JSON输入
        const jsonInput = document.getElementById('mqttJsonInput').value.trim();
        if (!jsonInput) {
            showToast('请先输入MQTT消息JSON', 'warning');
            return;
        }

        // 先自动分析JSON结构
        try {
            const jsonData = JSON.parse(jsonInput);
            analyzedJsonStructure = jsonData;

            // 分析JSON结构
            const paths = extractAllPaths(jsonData);

            // 显示分析结果
            displayJsonStructure(paths);

            // 生成规则
            const container = document.getElementById('jsonPathRules');

            // 清空现有规则
            container.innerHTML = '';

            // 为每个路径创建规则
            paths.forEach(pathInfo => {
                const ruleDiv = document.createElement('div');
                ruleDiv.className = 'jsonpath-rule-row';
                ruleDiv.innerHTML = `
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">字段别名</label>
                                <input type="text" class="form-control" name="alias" value="${pathInfo.alias}"
                                       onchange="suggestJsonPath(this); onJsonPathFieldChange();">
                            </div>
                            <div class="col-md-7">
                                <label class="form-label">JSONPath表达式</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="path" value="${pathInfo.path}"
                                           onchange="onJsonPathFieldChange();">
                                    <button type="button" class="btn btn-outline-secondary" onclick="showPathSuggestions(this)" title="路径建议">
                                        <i class="bi bi-lightbulb"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeJsonPathRule(this); onJsonPathFieldChange();">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                container.appendChild(ruleDiv);
            });

            showToast(`已分析JSON结构并自动生成 ${paths.length} 个提取规则`, 'success');

            // 自动生成示例消息
            setTimeout(() => generateMqttExample(), 500);

        } catch (error) {
            showToast('JSON格式错误：' + error.message, 'error');
            return;
        }
    }

    // 清空JSON输入
    function clearJsonInput() {
        document.getElementById('mqttJsonInput').value = '';
        document.getElementById('jsonAnalysisResult').style.display = 'none';
        analyzedJsonStructure = null;
        showToast('已清空输入', 'info', 1500);
    }

    // 根据别名建议JSONPath
    function suggestJsonPath(aliasInput) {
        const alias = aliasInput.value.trim();
        if (!alias) return;

        const pathInput = aliasInput.closest('.jsonpath-rule-row').querySelector('input[name="path"]');
        if (pathInput.value.trim()) return; // 如果已有路径，不覆盖

        // 如果没有分析过JSON结构，尝试自动分析
        if (!analyzedJsonStructure) {
            const jsonInput = document.getElementById('mqttJsonInput').value.trim();
            if (jsonInput) {
                try {
                    analyzedJsonStructure = JSON.parse(jsonInput);
                } catch (error) {
                    // JSON格式错误，静默处理
                    return;
                }
            } else {
                return;
            }
        }

        // 在分析的结构中查找匹配的路径
        const paths = extractAllPaths(analyzedJsonStructure);
        const matchedPath = paths.find(p => p.alias === alias);

        if (matchedPath) {
            pathInput.value = matchedPath.path;
            showToast(`已自动填充 ${alias} 的路径`, 'success', 2000);
        }
    }

    // 显示路径建议
    function showPathSuggestions(button) {
        if (!analyzedJsonStructure) {
            showToast('请先分析JSON结构以获取路径建议', 'warning');
            return;
        }

        const paths = extractAllPaths(analyzedJsonStructure);
        const pathInput = button.previousElementSibling;

        // 创建下拉建议列表
        let suggestions = '<div class="dropdown-menu show" style="position: absolute; top: 100%; left: 0; right: 0; z-index: 1000;">';
        paths.forEach(pathInfo => {
            suggestions += `
                    <button type="button" class="dropdown-item" onclick="selectSuggestedPath('${pathInfo.path}', this); event.stopPropagation();">
                        <div><strong>${pathInfo.alias}</strong></div>
                        <small class="text-muted">${pathInfo.path}</small>
                    </button>
                `;
        });
        suggestions += '</div>';

        // 移除已存在的建议
        const existingSuggestions = button.parentNode.querySelector('.dropdown-menu');
        if (existingSuggestions) {
            existingSuggestions.remove();
        } else {
            button.parentNode.style.position = 'relative';
            button.parentNode.insertAdjacentHTML('beforeend', suggestions);

            // 点击其他地方时隐藏建议
            setTimeout(() => {
                document.addEventListener('click', function hideSuggestions(e) {
                    if (!button.parentNode.contains(e.target)) {
                        const suggestions = button.parentNode.querySelector('.dropdown-menu');
                        if (suggestions) suggestions.remove();
                        document.removeEventListener('click', hideSuggestions);
                    }
                });
            }, 100);
        }
    }

    // 选择建议的路径
    function selectSuggestedPath(path, element) {
        const pathInput = element.closest('.input-group').querySelector('input[name="path"]');
        pathInput.value = path;
        element.closest('.dropdown-menu').remove();
        showToast('路径已选择', 'success', 1500);

        // 自动更新预览
        setTimeout(() => generateMqttExample(), 500);
    }

    // 生成MQTT消息示例
    function generateMqttExample() {
        const exampleContainer = document.getElementById('jsonPathGeneratedExample');

        // 获取所有配置的JsonPath规则
        const rules = getJsonPathRules();

        if (rules.length === 0) {
            exampleContainer.textContent = '请先配置JsonPath提取规则';
            showToast('请先配置JsonPath提取规则', 'warning');
            return;
        }

        // 生成示例MQTT消息
        const mqttExample = generateMqttMessageFromRules(rules);

        // 显示生成的示例
        exampleContainer.textContent = JSON.stringify(mqttExample, null, 2);

        showToast(`已生成包含 ${rules.length} 个字段的MQTT消息示例`, 'success');
    }

    // 根据JsonPath规则生成MQTT消息示例
    function generateMqttMessageFromRules(rules) {
        const mqttMessage = {
            ts: Date.now(),
            msg: {
                info: {
                    content: []
                }
            }
        };

        // 为每个规则生成对应的数据
        rules.forEach(rule => {
            const exampleValue = generateExampleValueForField(rule.alias);

            // 根据JSONPath路径类型生成数据
            if (rule.path.includes('msg.info.content[?(@.key==')) {
                // key-value结构
                mqttMessage.msg.info.content.push({
                    key: rule.alias,
                    value: exampleValue
                });
            } else if (rule.path.startsWith('msg.')) {
                // 直接在msg对象中设置
                setNestedValue(mqttMessage, rule.path, exampleValue);
            } else {
                // 在根级别设置
                mqttMessage[rule.alias] = exampleValue;
            }
        });

        return mqttMessage;
    }

    // 为字段生成示例值
    function generateExampleValueForField(fieldName) {
        const fieldExamples = {
            temp: 25.5,
            temperature: 28.3,
            dianya: "220V",
            voltage: "380V",
            weizhi: 27.21,
            location: 31.23,
            position: 116.46,
            xinhao: "良好",
            signal: "强",
            xinhao2: "差2",
            humidity: 65.2,
            shidu: 58.7,
            pressure: 1013.25,
            qiya: 1015.8,
            speed: 15.6,
            sudu: 12.3,
            status: "正常",
            zhuangtai: "运行中",
            power: 85.4,
            gonglv: 92.1,
            current: 2.5,
            dianliu: 3.2,
            frequency: 50.0,
            pinlv: 60.0
        };

        // 如果有预定义的示例值，使用它
        if (fieldExamples[fieldName.toLowerCase()]) {
            return fieldExamples[fieldName.toLowerCase()];
        }

        // 根据字段名推测类型
        if (fieldName.toLowerCase().includes('temp') || fieldName.toLowerCase().includes('wen')) {
            return Math.round((Math.random() * 40 + 10) * 10) / 10; // 10-50度
        } else if (fieldName.toLowerCase().includes('hum') || fieldName.toLowerCase().includes('shi')) {
            return Math.round((Math.random() * 40 + 40) * 10) / 10; // 40-80%
        } else if (fieldName.toLowerCase().includes('volt') || fieldName.toLowerCase().includes('ya')) {
            return Math.random() > 0.5 ? "220V" : "380V";
        } else if (fieldName.toLowerCase().includes('signal') || fieldName.toLowerCase().includes('xin')) {
            const signals = ["强", "中", "弱", "良好", "一般", "差"];
            return signals[Math.floor(Math.random() * signals.length)];
        } else if (fieldName.toLowerCase().includes('status') || fieldName.toLowerCase().includes('zhuang')) {
            const statuses = ["正常", "异常", "运行中", "停止", "维护中"];
            return statuses[Math.floor(Math.random() * statuses.length)];
        } else {
            // 默认返回数字
            return Math.round((Math.random() * 100) * 10) / 10;
        }
    }

    // 设置嵌套对象的值
    function setNestedValue(obj, path, value) {
        const keys = path.split('.');
        let current = obj;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }

        current[keys[keys.length - 1]] = value;
    }

    // 获取当前配置的JsonPath规则
    function getJsonPathRules() {
        const rules = [];
        const container = document.getElementById('jsonPathRules');
        if (!container) return rules;

        const rows = container.querySelectorAll('.jsonpath-rule-row');
        rows.forEach(row => {
            const aliasInput = row.querySelector('input[name="alias"]');
            const pathInput = row.querySelector('input[name="path"]');

            if (aliasInput && pathInput) {
                const alias = aliasInput.value.trim();
                const path = pathInput.value.trim();

                if (alias && path) {
                    rules.push({alias, path});
                }
            }
        });

        return rules;
    }

    // 执行JsonPath提取
    function executeJsonPathExtraction(jsonData, rules) {
        const result = {
            ts: Date.now(),
            sn: jsonData.sn || "UNKNOWN",
            entityid: jsonData.entityid || "UNKNOWN"
        };

        rules.forEach(rule => {
            try {
                const value = extractValueByPath(jsonData, rule.path);
                if (value !== undefined && value !== null) {
                    result[rule.alias] = value;
                } else {
                    result[rule.alias] = null; // 明确标记未找到的值
                }
            } catch (error) {
                console.warn(`提取字段 ${rule.alias} 失败:`, error);
                result[rule.alias] = `ERROR: ${error.message}`;
            }
        });

        return result;
    }

    // 简化的JsonPath提取器（支持常见的JsonPath表达式）
    function extractValueByPath(data, path) {
        try {
            // 处理简单的点号路径，如: msg.info.content
            if (!path.includes('[') && !path.includes('?')) {
                return getNestedValue(data, path);
            }

            // 处理数组查询路径，如: msg.info.content[?(@.key=='temp')].value
            const arrayQueryMatch = path.match(/^(.+)\[\?\(@\.(.+)==['"](.+)['"]\)\.(.+)$/);
            if (arrayQueryMatch) {
                const [, basePath, keyField, keyValue, valueField] = arrayQueryMatch;
                const arrayData = getNestedValue(data, basePath);

                if (Array.isArray(arrayData)) {
                    const foundItem = arrayData.find(item =>
                        item && typeof item === 'object' && item[keyField] === keyValue
                    );
                    return foundItem ? foundItem[valueField] : undefined;
                }
            }

            // 处理数组索引路径，如: msg.info.content[0].value
            const arrayIndexMatch = path.match(/^(.+)\[(\d+)\]\.(.+)$/);
            if (arrayIndexMatch) {
                const [, basePath, index, field] = arrayIndexMatch;
                const arrayData = getNestedValue(data, basePath);

                if (Array.isArray(arrayData) && arrayData[parseInt(index)]) {
                    return arrayData[parseInt(index)][field];
                }
            }

            // 如果都不匹配，尝试直接路径
            return getNestedValue(data, path);

        } catch (error) {
            throw new Error(`路径解析失败: ${path}`);
        }
    }

    // 获取嵌套对象的值
    function getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && typeof current === 'object' ? current[key] : undefined;
        }, obj);
    }

    // 在字段配置变化时自动更新预览
    function onJsonPathFieldChange() {
        // 延迟执行，避免频繁更新
        clearTimeout(window.jsonPathPreviewTimer);
        window.jsonPathPreviewTimer = setTimeout(() => {
            generateMqttExample();
        }, 1000);
    }

    // 工具函数：格式化日期
    function formatDate(dateStr) {
        if (!dateStr) return '未知';
        const date = new Date(dateStr);
        return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour12: false});
    }
</script>
</body>
</html>
</script>
</body>
</html>
