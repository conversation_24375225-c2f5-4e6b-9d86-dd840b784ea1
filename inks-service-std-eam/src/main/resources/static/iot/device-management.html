<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome CSS for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }

        .container-fluid {
            max-width: 1800px;
        }

        .card-header-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .status-online {
            color: #28a745;
            font-weight: bold;
        }

        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }

        .device-card {
            transition: transform 0.2s;
            border-left: 4px solid #667eea;
        }

        .device-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-tabs .nav-link {
            font-weight: 500;
            border-radius: 10px 10px 0 0;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }

        .table-hover tbody tr:hover {
            background-color: #e3f2fd;
        }

        .connection-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .connection-online {
            background-color: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }

        .connection-offline {
            background-color: #dc3545;
        }

        .refresh-btn {
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>

<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary">
            <i class="fas fa-microchip me-2"></i>设备管理
        </h2>
        <div>
            <button class="btn btn-outline-primary me-2" onclick="refreshAllData()">
                <i class="fas fa-sync-alt" id="refreshIcon"></i> 刷新数据
            </button>
            <div class="form-check form-switch d-inline-block align-middle me-2">
                <input class="form-check-input" type="checkbox" role="switch" id="autoRefreshSwitch">
                <label class="form-check-label" for="autoRefreshSwitch">自动刷新</label>
            </div>
            <span class="badge bg-success fs-6">系统运行中</span>
        </div>
    </div>

    <!-- 设备统计 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="totalDevices">0</div>
                <div class="stats-label">总设备数</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number status-online" id="onlineDevices">0</div>
                <div class="stats-label">在线设备</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number status-offline" id="offlineDevices">0</div>
                <div class="stats-label">离线设备</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="activeConnections">0</div>
                <div class="stats-label">活跃连接</div>
            </div>
        </div>
    </div>

    <!-- 设备管理标题 -->
    <div class="mb-3">
        <h4 class="text-primary">
            <i class="fas fa-list me-2"></i>设备管理
        </h4>
    </div>

    <!-- 设备管理内容 -->
    <div class="card shadow-sm mt-3">
        <div class="card-header card-header-custom">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-microchip me-2"></i>设备管理</h5>
                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#deviceModal">
                    <i class="fas fa-plus me-1"></i> 新增设备
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <input type="text" class="form-control" id="searchDevice" placeholder="搜索设备名称、SN码或Token...">
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterDeviceType">
                        <option value="">所有设备类型</option>
                        <option value="sensor">传感器</option>
                        <option value="gateway">网关</option>
                        <option value="controller">控制器</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterConnectionStatus">
                        <option value="">所有连接状态</option>
                        <option value="online">在线</option>
                        <option value="offline">离线</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-primary w-100" onclick="searchDevices()">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>

            <!-- Device Table -->
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                    <tr>
                        <th>连接状态</th>
                        <th>设备ID</th>
                        <th>设备名称</th>
                        <th>设备类型</th>
                        <th>SN码</th>
                        <th>Token</th>
                        <th>设备配置</th>
                        <th>规则链</th>
                        <th>最后更新</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody id="deviceTableBody">
                    <!-- 动态加载设备数据 -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="设备分页">
                <ul class="pagination justify-content-center" id="devicePagination">
                    <!-- 动态生成分页 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Device Modal -->
<div class="modal fade" id="deviceModal" tabindex="-1" aria-labelledby="deviceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header card-header-custom">
                <h5 class="modal-title" id="deviceModalLabel">
                    <i class="fas fa-microchip me-2"></i>设备信息
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="deviceForm">
                    <input type="hidden" id="deviceId" name="id">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="devname" class="form-label">设备名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="devname" name="devname" required>
                        </div>
                        <div class="col-md-6">
                            <label for="devsn" class="form-label">设备SN码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="devsn" name="devsn" required>
                        </div>
                        <div class="col-md-6">
                            <label for="devtype" class="form-label">设备类型</label>
                            <select class="form-select" id="devtype" name="devtype">
                                <option value="sensor">传感器</option>
                                <option value="gateway">网关</option>
                                <option value="controller">控制器</option>
                                <option value="actuator">执行器</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="devlabel" class="form-label">设备标签</label>
                            <input type="text" class="form-control" id="devlabel" name="devlabel">
                        </div>
                        <div class="col-md-6">
                            <label for="deviceprofileid" class="form-label">设备配置</label>
                            <select class="form-select" id="deviceprofileid" name="deviceprofileid">
                                <option value="">请选择设备配置</option>
                                <!-- 设备配置选项将动态加载 -->
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="defaultRuleChainid" class="form-label">默认规则链</label>
                            <select class="form-select" id="defaultRuleChainid" name="defaultRuleChainid">
                                <option value="">请选择规则链</option>
                                <!-- 规则链选项将动态加载 -->
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="token" class="form-label">MQTT Token</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="token" name="token">
                                <button class="btn btn-outline-secondary" type="button"
                                        onclick="generateTokenForDevice()">
                                    <i class="fas fa-key"></i> 生成
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="customerid" class="form-label">客户ID</label>
                            <input type="text" class="form-control" id="customerid" name="customerid">
                        </div>
                        <div class="col-md-12">
                            <label for="devicedata" class="form-label">设备数据 (JSON)</label>
                            <textarea class="form-control" id="devicedata" name="devicedata" rows="3"
                                      placeholder='{"key": "value"}'></textarea>
                        </div>
                        <div class="col-md-12">
                            <label for="remark" class="form-label">备注</label>
                            <textarea class="form-control" id="remark" name="remark" rows="2"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" onclick="saveDevice()">
                    <i class="fas fa-save me-1"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Disconnect Device Modal -->
<div class="modal fade" id="disconnectModal" tabindex="-1" aria-labelledby="disconnectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="disconnectModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>断开设备连接
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要断开以下设备的MQTT连接吗？</p>
                <div class="alert alert-info">
                    <strong>设备信息：</strong><br>
                    <span id="disconnectDeviceInfo"></span>
                </div>
                <div class="mb-3">
                    <label for="disconnectReason" class="form-label">断开原因</label>
                    <input type="text" class="form-control" id="disconnectReason" value="管理员主动断开"
                           placeholder="请输入断开原因">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="confirmDisconnect()">
                    <i class="fas fa-power-off me-1"></i> 断开连接
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Device Profile Management Modal -->
<div class="modal fade" id="deviceProfileModal" tabindex="-1" aria-labelledby="deviceProfileModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header card-header-custom">
                <h5 class="modal-title" id="deviceProfileModalLabel">
                    <i class="fas fa-cog me-2"></i>设备配置管理
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Device Profile Form -->
                <form id="deviceProfileForm">
                    <input type="hidden" id="profileId" name="id">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="profileName" class="form-label">配置名称 <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="profileName" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="profileType" class="form-label">配置类型</label>
                            <select class="form-select" id="profileType" name="type">
                                <option value="DEFAULT">默认配置</option>
                                <option value="SENSOR">传感器配置</option>
                                <option value="GATEWAY">网关配置</option>
                                <option value="CONTROLLER">控制器配置</option>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label for="profileDefaultRuleChain" class="form-label">默认规则链</label>
                            <select class="form-select" id="profileDefaultRuleChain" name="defaultrulechainid">
                                <option value="">请选择默认规则链</option>
                                <!-- 规则链选项将动态加载 -->
                            </select>
                            <div class="form-text">选择此设备配置的默认规则链，使用此配置的设备将自动关联到此规则链</div>
                        </div>
                        <div class="col-md-12">
                            <label for="profileDescription" class="form-label">配置描述</label>
                            <textarea class="form-control" id="profileDescription" name="description"
                                      rows="3"></textarea>
                        </div>
                        <div class="col-md-12">
                            <label for="profileConfiguration" class="form-label">配置参数 (JSON)</label>
                            <textarea class="form-control" id="profileConfiguration" name="configuration" rows="4"
                                      placeholder='{"key": "value"}'></textarea>
                        </div>
                    </div>
                </form>

                <!-- Device Profile List -->
                <hr class="my-4">
                <h6><i class="fas fa-list me-2"></i>现有设备配置</h6>
                <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                    <table class="table table-sm table-hover">
                        <thead class="table-light">
                        <tr>
                            <th>配置名称</th>
                            <th>类型</th>
                            <th>默认规则链</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody id="deviceProfileTableBody">
                        <!-- 动态加载设备配置列表 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> 关闭
                </button>
                <button type="button" class="btn btn-success" onclick="saveDeviceProfile()">
                    <i class="fas fa-save me-1"></i> 保存配置
                </button>
                <button type="button" class="btn btn-primary" onclick="clearDeviceProfileForm()">
                    <i class="fas fa-plus me-1"></i> 新增配置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 5 JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // 全局变量
    let currentPage = 1;
    let pageSize = 10;
    let currentDeviceForDisconnect = null;
    let refreshInterval = null;
    let deviceProfiles = []; // 存储设备配置
    let ruleChains = []; // 存储规则链

    // API基础URL
    const API_BASE_URL = 'http://dev.inksyun.com:31080/eam';
    const API_BASE = `${API_BASE_URL}/api/device-tokens`;
    const DEVICE_API_BASE = `${API_BASE_URL}/D09M30B1`; // 使用D09M30B1Controller
    const DEVICE_PROFILE_API_BASE = `${API_BASE_URL}/D09M31B1`; // 设备配置API
    const RULE_CHAIN_API_BASE = `${API_BASE_URL}/D09M32B1`; // 规则链API

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        initializePage();
        setupEventListeners();
    });

    function initializePage() {
        loadDeviceList();
        loadMqttStatus();
        loadDeviceProfiles(); // 加载设备配置
        loadRuleChains(); // 加载规则链
        setupDeviceProfileChangeHandler(); // 设置设备配置变更处理

        // 延迟创建管理按钮，确保DOM已加载
        setTimeout(() => {
            createDeviceProfileManagementButton();
        }, 500);
    }

    function setupEventListeners() {
        document.getElementById('autoRefreshSwitch').addEventListener('change', function () {
            if (this.checked) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });
    }

    // 开始自动刷新
    function startAutoRefresh() {
        if (refreshInterval) return; // 避免重复启动
        refreshInterval = setInterval(() => {
            loadMqttStatus();
            updateConnectionStatus();
        }, 30000); // 30秒刷新一次
    }

    // 停止自动刷新
    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
            console.log("自动刷新已停止");
        }
    }

    // 刷新所有数据
    function refreshAllData() {
        const refreshIcon = document.getElementById('refreshIcon');
        refreshIcon.classList.add('refresh-btn');

        Promise.all([
            loadDeviceList(),
            loadMqttStatus(),
            loadDeviceProfiles(),
            loadRuleChains()
        ]).finally(() => {
            refreshIcon.classList.remove('refresh-btn');
            showToast('数据刷新完成', 'success');
        });
    }

    // 加载设备配置
    async function loadDeviceProfiles() {
        try {
            const queryParam = {
                pageNum: 1,
                pageSize: 1000
            };

            const response = await fetch(`${DEVICE_PROFILE_API_BASE}/getPageList`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'b8'
                },
                body: JSON.stringify(queryParam)
            });

            if (response.ok) {
                const result = await response.json();
                if (result.code === 200) {
                    deviceProfiles = result.data.list || [];
                    populateDeviceProfileSelect();
                } else {
                    console.error('加载设备配置失败:', result.msg);
                    showToast('加载设备配置失败: ' + result.msg, 'error');
                }
            } else {
                console.error('API请求失败:', response.status);
                showToast('加载设备配置API请求失败', 'error');
            }
        } catch (error) {
            console.error('加载设备配置出错:', error);
            showToast('加载设备配置出错', 'error');
        }
    }

    // 填充设备配置下拉框
    function populateDeviceProfileSelect() {
        const select = document.getElementById('deviceprofileid');
        select.innerHTML = '<option value="">请选择设备配置</option>';

        deviceProfiles.forEach(profile => {
            const option = document.createElement('option');
            option.value = profile.id;
            option.textContent = profile.name || profile.id;
            select.appendChild(option);
        });
    }

    // 加载规则链
    async function loadRuleChains() {
        try {
            const queryParam = {
                pageNum: 1,
                pageSize: 1000
            };

            const response = await fetch(`${RULE_CHAIN_API_BASE}/getPageList`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'b8'
                },
                body: JSON.stringify(queryParam)
            });

            if (response.ok) {
                const result = await response.json();
                if (result.code === 200) {
                    ruleChains = result.data.list || [];
                    populateRuleChainSelect();
                } else {
                    console.error('加载规则链失败:', result.msg);
                    showToast('加载规则链失败: ' + result.msg, 'error');
                }
            } else {
                console.error('API请求失败:', response.status);
                showToast('加载规则链API请求失败', 'error');
            }
        } catch (error) {
            console.error('加载规则链出错:', error);
            showToast('加载规则链出错', 'error');
        }
    }

    // 填充规则链下拉框
    function populateRuleChainSelect() {
        const select = document.getElementById('defaultRuleChainid');
        select.innerHTML = '<option value="">请选择规则链</option>';

        ruleChains.forEach(chain => {
            const option = document.createElement('option');
            option.value = chain.id;
            option.textContent = chain.rulechainname || chain.id;
            select.appendChild(option);
        });
    }

    // 加载设备列表
    async function loadDeviceList(searchTerm = '', deviceType = '', connectionStatus = '') {
        try {
            // 构建QueryParam对象，符合后端API格式
            const queryParam = {
                pageNum: currentPage,
                pageSize: pageSize,
                searchPojo: null,
                searchType: 0,
                filterstr: "",
                dateRange: null
            };

            // 如果有搜索条件，添加到filterstr中
            if (searchTerm) {
                queryParam.filterstr += ` and (Iot_Device.DevName like '%${searchTerm}%' or Iot_Device.DevSN like '%${searchTerm}%' or Iot_Device.Id like '%${searchTerm}%')`;
            }

            if (deviceType) {
                queryParam.filterstr += ` and Iot_Device.DevType = '${deviceType}'`;
            }

            const response = await fetch(`${DEVICE_API_BASE}/getPageList`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'b8'
                },
                body: JSON.stringify(queryParam)
            });

            if (response.ok) {
                const result = await response.json();
                if (result.code === 200) {
                    displayDeviceList(result.data.list || []);
                    updateDeviceStats(result.data.list || []);
                    generatePagination(result.data);
                } else {
                    console.error('加载设备列表失败:', result.msg);
                    showToast('加载设备列表失败: ' + result.msg, 'error');
                    // 使用模拟数据
                    loadMockDeviceData();
                }
            } else {
                console.error('API请求失败:', response.status);
                showToast('API请求失败', 'error');
                loadMockDeviceData();
            }
        } catch (error) {
            console.error('加载设备列表出错:', error);
            showToast('加载设备列表出错', 'error');
            loadMockDeviceData();
        }
    }

    // 加载模拟设备数据（用于演示）
    function loadMockDeviceData() {
        const mockDevices = [
            {
                id: 'DEV001',
                devname: '温度传感器01',
                devtype: 'sensor',
                devsn: 'SN001234567',
                token: 'sensor_DEV001_abc12345',
                deviceprofileid: 'PROFILE001',
                defaultRuleChainid: 'CHAIN001',
                modifydate: '2025-01-11 10:30:00',
                remark: '车间A区温度监控'
            },
            {
                id: 'DEV002',
                devname: '智能网关02',
                devtype: 'gateway',
                devsn: 'SN002345678',
                token: 'gateway_DEV002_def67890',
                deviceprofileid: 'PROFILE002',
                defaultRuleChainid: 'CHAIN002',
                modifydate: '2025-01-11 09:15:00',
                remark: '主控制网关'
            },
            {
                id: 'DEV003',
                devname: '压力控制器03',
                devtype: 'controller',
                devsn: 'SN003456789',
                token: 'controller_DEV003_ghi11111',
                deviceprofileid: 'PROFILE003',
                defaultRuleChainid: 'CHAIN003',
                modifydate: '2025-01-11 08:45:00',
                remark: '液压系统控制'
            }
        ];

        displayDeviceList(mockDevices);
        updateDeviceStats(mockDevices);
    }

    // 显示设备列表
    function displayDeviceList(devices) {
        const tbody = document.getElementById('deviceTableBody');
        tbody.innerHTML = '';

        devices.forEach(device => {
            const row = document.createElement('tr');
            row.className = 'device-row';

            // 获取设备配置和规则链名称
            const profileName = deviceProfiles.find(p => p.id === device.deviceprofileid)?.name || '未设置';
            const ruleChainName = ruleChains.find(c => c.id === device.defaultRuleChainid)?.rulechainname || '未设置';

            // 检查设备在线状态（这里需要调用MQTT API）
            checkDeviceOnlineStatus(device.token).then(isOnline => {
                row.innerHTML = `
                <td>
                    <span class="connection-indicator ${isOnline ? 'connection-online' : 'connection-offline'}"></span>
                    <span class="${isOnline ? 'status-online' : 'status-offline'}">
                        ${isOnline ? '在线' : '离线'}
                    </span>
                </td>
                <td>${device.id || ''}</td>
                <td>${device.devname || ''}</td>
                <td>
                    <span class="badge bg-secondary">${getDeviceTypeLabel(device.devtype)}</span>
                </td>
                <td><code>${device.devsn || ''}</code></td>
                <td>
                    <small class="text-muted">${device.token ? device.token.substring(0, 20) + '...' : '未设置'}</small>
                </td>
                <td>${profileName}</td>
                <td>${ruleChainName}</td>
                <td>${formatDateTime(device.modifydate)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editDevice('${device.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewDeviceDetails('${device.id}')" title="详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${device.token && isOnline ? `
                        <button class="btn btn-outline-warning" onclick="showDisconnectModal('${device.id}', '${device.devname}', '${device.token}')" title="断开连接">
                            <i class="fas fa-power-off"></i>
                        </button>
                        ` : ''}
                        <button class="btn btn-outline-danger" onclick="deleteDevice('${device.id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            });

            tbody.appendChild(row);
        });
    }

    // 检查设备在线状态
    async function checkDeviceOnlineStatus(token) {
        if (!token) return false;

        try {
            const response = await fetch(`${API_BASE}/token-online?token=${encodeURIComponent(token)}`, {
                headers: {
                    'authorization': 'b8'
                }
            });
            if (response.ok) {
                const result = await response.json();
                return result.online || false;
            }
        } catch (error) {
            console.error('检查设备在线状态失败:', error);
        }
        return false;
    }

    // 更新设备统计
    function updateDeviceStats(devices) {
        document.getElementById('totalDevices').textContent = devices.length;

        // 这里需要异步检查每个设备的在线状态
        let onlineCount = 0;
        const promises = devices.map(device => {
            if (device.token) {
                return checkDeviceOnlineStatus(device.token).then(isOnline => {
                    if (isOnline) onlineCount++;
                });
            }
            return Promise.resolve();
        });

        Promise.all(promises).then(() => {
            document.getElementById('onlineDevices').textContent = onlineCount;
            document.getElementById('offlineDevices').textContent = devices.length - onlineCount;
        });
    }

    // 加载MQTT状态
    async function loadMqttStatus() {
        try {
            const response = await fetch(`${API_BASE}/connection-status`, {
                headers: {
                    'authorization': 'b8'
                }
            });
            if (response.ok) {
                const result = await response.json();
                updateMqttStats(result);
            }
        } catch (error) {
            console.error('加载MQTT状态失败:', error);
        }
    }

    // 更新MQTT统计
    function updateMqttStats(data) {
        document.getElementById('activeConnections').textContent = data.connectionCount || 0;
    }

    // 设备类型标签
    function getDeviceTypeLabel(type) {
        const labels = {
            'sensor': '传感器',
            'gateway': '网关',
            'controller': '控制器',
            'actuator': '执行器'
        };
        return labels[type] || type || '未知';
    }

    // 格式化日期时间
    function formatDateTime(dateStr) {
        if (!dateStr) return '-';
        const date = new Date(dateStr);
        return date.toLocaleString('zh-CN');
    }

    // 显示提示消息
    function showToast(message, type = 'info') {
        // 创建简单的提示框
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} position-fixed top-0 end-0 m-3`;
        toast.style.zIndex = '9999';
        toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
        document.body.appendChild(toast);

        // 3秒后自动消失
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 3000);
    }

    // 搜索设备
    function searchDevices() {
        const searchTerm = document.getElementById('searchDevice').value;
        const deviceType = document.getElementById('filterDeviceType').value;
        const connectionStatus = document.getElementById('filterConnectionStatus').value;

        // 重置页码
        currentPage = 1;

        // 重新加载设备列表（带搜索条件）
        loadDeviceList(searchTerm, deviceType, connectionStatus);
    }

    // 编辑设备
    function editDevice(deviceId) {
        console.log('开始编辑设备，设备ID:', deviceId);

        // 获取设备详情并填充表单
        const url = `${DEVICE_API_BASE}/getEntity?key=${deviceId}`;
        console.log('请求URL:', url);

        fetch(url, {
            headers: {
                'authorization': 'b8'
            }
        })
            .then(response => {
                console.log('getEntity响应状态:', response.status);
                return response.json();
            })
            .then(result => {
                console.log('getEntity响应数据:', result);

                if (result.code === 200) {
                    console.log('设备数据:', result.data);
                    fillDeviceForm(result.data);

                    // 更新模态框标题
                    document.getElementById('deviceModalLabel').innerHTML = '<i class="fas fa-edit me-2"></i>编辑设备';

                    const modal = new bootstrap.Modal(document.getElementById('deviceModal'));
                    modal.show();
                } else {
                    console.error('获取设备信息失败:', result);
                    showToast('获取设备信息失败: ' + (result.msg || result.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('获取设备信息失败:', error);
                showToast('获取设备信息失败: ' + error.message, 'error');
            });
    }

    // 填充设备表单
    function fillDeviceForm(device) {
        console.log('填充设备表单，设备数据:', device);

        // 检查设备数据是否存在
        if (!device) {
            console.error('设备数据为空');
            showToast('设备数据为空', 'error');
            return;
        }

        // 填充隐藏的设备ID字段
        const deviceIdField = document.getElementById('deviceId');
        if (deviceIdField) {
            deviceIdField.value = device.id || '';
            console.log(`填充字段 id: ${device.id || ''}`);
        } else {
            console.error('找不到字段元素: deviceId');
        }

        // 填充表单字段
        const fieldMapping = {
            'devname': device.devname || '',
            'devsn': device.devsn || '',
            'devtype': device.devtype || 'sensor',
            'devlabel': device.devlabel || '',
            'deviceprofileid': device.deviceprofileid || '',
            'defaultRuleChainid': device.defaultRuleChainid || '',
            'token': device.token || '',
            'customerid': device.customerid || '',
            'devicedata': device.devicedata || '',
            'remark': device.remark || ''
        };

        Object.keys(fieldMapping).forEach(fieldName => {
            const element = document.getElementById(fieldName);
            if (element) {
                element.value = fieldMapping[fieldName];
                console.log(`填充字段 ${fieldName}: ${fieldMapping[fieldName]}`);
            } else {
                console.error(`找不到字段元素: ${fieldName}`);
            }
        });

        // 如果设备有配置ID，触发规则链的自动选择
        if (device.deviceprofileid) {
            setTimeout(() => {
                updateRuleChainBasedOnProfile(device.deviceprofileid);
            }, 100);
        }

        console.log('表单填充完成');
    }

    // 清空设备表单
    function clearDeviceForm() {
        document.getElementById('deviceForm').reset();
        document.getElementById('deviceId').value = '';
    }

    // 保存设备
    function saveDevice() {
        const form = document.getElementById('deviceForm');
        const formData = new FormData(form);
        const deviceData = Object.fromEntries(formData.entries());

        console.log('保存设备数据:', deviceData);

        // 验证必填字段
        if (!deviceData.devname || !deviceData.devsn) {
            showToast('请填写设备名称和SN码', 'warning');
            return;
        }

        const isEdit = !!deviceData.id;
        const url = isEdit ? `${DEVICE_API_BASE}/update` : `${DEVICE_API_BASE}/create`;

        console.log('保存设备 - 是否编辑:', isEdit);
        console.log('保存设备 - 请求URL:', url);
        console.log('保存设备 - 请求数据:', JSON.stringify(deviceData, null, 2));

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'b8'
            },
            body: JSON.stringify(deviceData)
        })
            .then(response => {
                console.log('保存设备响应状态:', response.status);
                return response.json();
            })
            .then(result => {
                console.log('保存设备响应数据:', result);

                if (result.code === 200) {
                    showToast(isEdit ? '设备更新成功' : '设备创建成功', 'success');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('deviceModal'));
                    modal.hide();
                    loadDeviceList(); // 重新加载列表
                } else {
                    console.error('保存设备失败:', result);
                    showToast('保存失败: ' + (result.msg || result.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('保存设备失败:', error);
                showToast('保存设备失败: ' + error.message, 'error');
            });
    }

    // 删除设备
    function deleteDevice(deviceId) {
        if (confirm('确定要删除这个设备吗？此操作不可恢复。')) {
            fetch(`${DEVICE_API_BASE}/delete?key=${deviceId}`, {
                method: 'GET',
                headers: {
                    'authorization': 'b8'
                }
            })
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        showToast('设备删除成功', 'success');
                        loadDeviceList(); // 重新加载列表
                    } else {
                        showToast('删除失败: ' + result.msg, 'error');
                    }
                })
                .catch(error => {
                    console.error('删除设备失败:', error);
                    showToast('删除设备失败', 'error');
                });
        }
    }

    // 查看设备详情
    function viewDeviceDetails(deviceId) {
        // 可以实现一个详情查看模态框
        editDevice(deviceId); // 暂时复用编辑功能
    }

    // 为设备生成Token
    function generateTokenForDevice() {
        const deviceId = document.getElementById('deviceId').value;
        const deviceType = document.getElementById('devtype').value;

        if (!deviceId) {
            showToast('请先保存设备后再生成Token', 'warning');
            return;
        }

        generateToken();
    }

    // 生成Token
    function generateToken() {
        const deviceId = document.getElementById('tokenDeviceId').value;
        const deviceType = document.getElementById('tokenDeviceType').value;

        if (!deviceId) {
            showToast('请输入设备ID', 'warning');
            return;
        }

        fetch(`${API_BASE}/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'authorization': 'b8'
            },
            body: `deviceId=${encodeURIComponent(deviceId)}&deviceType=${encodeURIComponent(deviceType)}`
        })
            .then(response => response.json())
            .then(result => {
                showToast(`Token生成成功: ${result.token}`, 'success');
                // 清空输入框
                document.getElementById('tokenDeviceId').value = '';
            })
            .catch(error => {
                console.error('生成Token失败:', error);
                showToast('生成Token失败', 'error');
            });
    }

    // 显示断开连接模态框
    function showDisconnectModal(deviceId, deviceName, token) {
        currentDeviceForDisconnect = {deviceId, deviceName, token};
        document.getElementById('disconnectDeviceInfo').innerHTML = `
        <strong>设备ID:</strong> ${deviceId}<br>
        <strong>设备名称:</strong> ${deviceName}<br>
        <strong>Token:</strong> ${token.substring(0, 20)}...
    `;

        const modal = new bootstrap.Modal(document.getElementById('disconnectModal'));
        modal.show();
    }

    // 确认断开连接
    function confirmDisconnect() {
        if (!currentDeviceForDisconnect) return;

        const reason = document.getElementById('disconnectReason').value || '管理员主动断开';

        fetch(`${API_BASE}/disconnect-by-token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'authorization': 'b8'
            },
            body: `token=${encodeURIComponent(currentDeviceForDisconnect.token)}&reason=${encodeURIComponent(reason)}`
        })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showToast('设备连接已断开', 'success');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('disconnectModal'));
                    modal.hide();
                    loadDeviceList(); // 刷新设备列表
                    loadMqttStatus(); // 刷新MQTT状态
                } else {
                    showToast('断开连接失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                console.error('断开连接失败:', error);
                showToast('断开连接失败', 'error');
            });
    }

    // 更新连接状态
    function updateConnectionStatus() {
        // 更新设备列表中的连接状态
        const deviceRows = document.querySelectorAll('.device-row');
        deviceRows.forEach(row => {
            // 这里可以实现更精细的状态更新逻辑
        });
    }

    // 设置设备配置变更处理
    function setupDeviceProfileChangeHandler() {
        const deviceProfileSelect = document.getElementById('deviceprofileid');
        if (deviceProfileSelect) {
            deviceProfileSelect.addEventListener('change', function () {
                const selectedProfileId = this.value;
                updateRuleChainBasedOnProfile(selectedProfileId);
            });
        }
    }

    // 根据设备配置更新规则链选择
    function updateRuleChainBasedOnProfile(profileId) {
        const ruleChainSelect = document.getElementById('defaultRuleChainid');

        if (!profileId) {
            // 如果没有选择设备配置，显示所有规则链
            populateRuleChainSelect(ruleChains);
            return;
        }

        // 查找选中的设备配置
        const selectedProfile = deviceProfiles.find(profile => profile.id === profileId);

        if (selectedProfile && selectedProfile.defaultrulechainid) {
            // 如果设备配置有默认规则链，自动选择它
            ruleChainSelect.value = selectedProfile.defaultrulechainid;

            // 高亮显示推荐的规则链
            highlightRecommendedRuleChain(selectedProfile.defaultrulechainid);

            showToast(`已自动选择设备配置推荐的规则链`, 'info', 2000);
        } else {
            // 清除选择
            ruleChainSelect.value = '';
        }
    }

    // 高亮显示推荐的规则链
    function highlightRecommendedRuleChain(ruleChainId) {
        const ruleChainSelect = document.getElementById('defaultRuleChainid');
        const options = ruleChainSelect.querySelectorAll('option');

        options.forEach(option => {
            if (option.value === ruleChainId) {
                option.style.backgroundColor = '#e3f2fd';
                option.style.fontWeight = 'bold';
                option.textContent = option.textContent.includes('(推荐)') ?
                    option.textContent : option.textContent + ' (推荐)';
            } else {
                option.style.backgroundColor = '';
                option.style.fontWeight = '';
                option.textContent = option.textContent.replace(' (推荐)', '');
            }
        });
    }

    // 创建设备配置管理按钮
    function createDeviceProfileManagementButton() {
        const deviceProfileSelect = document.getElementById('deviceprofileid');
        const parentDiv = deviceProfileSelect.parentElement;

        // 创建管理按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'mt-2';
        buttonContainer.innerHTML = `
        <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="openDeviceProfileModal()">
            <i class="fas fa-cog"></i> 管理设备配置
        </button>
        <button type="button" class="btn btn-outline-success btn-sm" onclick="refreshDeviceProfiles()">
            <i class="fas fa-sync"></i> 刷新
        </button>
    `;

        parentDiv.appendChild(buttonContainer);
    }

    // 刷新设备配置列表
    async function refreshDeviceProfiles() {
        try {
            await loadDeviceProfiles();
            showToast('设备配置列表已刷新', 'success', 1500);
        } catch (error) {
            showToast('刷新设备配置失败', 'error');
        }
    }

    // 打开设备配置管理模态框
    function openDeviceProfileModal() {
        // 加载规则链选项到设备配置表单中
        populateRuleChainSelectForProfile();

        // 加载设备配置列表
        loadDeviceProfilesForModal();

        // 清空表单
        clearDeviceProfileForm();

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('deviceProfileModal'));
        modal.show();
    }

    // 为设备配置表单填充规则链选项
    function populateRuleChainSelectForProfile() {
        const select = document.getElementById('profileDefaultRuleChain');
        select.innerHTML = '<option value="">请选择默认规则链</option>';

        ruleChains.forEach(ruleChain => {
            const option = document.createElement('option');
            option.value = ruleChain.id;
            option.textContent = ruleChain.name || ruleChain.id;
            select.appendChild(option);
        });
    }

    // 加载设备配置列表到模态框中
    async function loadDeviceProfilesForModal() {
        try {
            const tbody = document.getElementById('deviceProfileTableBody');
            tbody.innerHTML = '<tr><td colspan="4" class="text-center">加载中...</td></tr>';

            // 确保设备配置数据是最新的
            await loadDeviceProfiles();

            tbody.innerHTML = '';

            if (deviceProfiles.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">暂无设备配置</td></tr>';
                return;
            }

            deviceProfiles.forEach(profile => {
                const ruleChainName = getRuleChainNameById(profile.defaultrulechainid);
                const row = document.createElement('tr');
                row.innerHTML = `
                <td>${profile.name || profile.id}</td>
                <td><span class="badge bg-secondary">${profile.type || 'DEFAULT'}</span></td>
                <td>${ruleChainName || '<span class="text-muted">未设置</span>'}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editDeviceProfile('${profile.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteDeviceProfile('${profile.id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
                tbody.appendChild(row);
            });
        } catch (error) {
            console.error('加载设备配置列表失败:', error);
            const tbody = document.getElementById('deviceProfileTableBody');
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">加载失败</td></tr>';
        }
    }

    // 根据规则链ID获取规则链名称
    function getRuleChainNameById(ruleChainId) {
        if (!ruleChainId) return null;
        const ruleChain = ruleChains.find(rc => rc.id === ruleChainId);
        return ruleChain ? (ruleChain.name || ruleChain.id) : null;
    }

    // 清空设备配置表单
    function clearDeviceProfileForm() {
        document.getElementById('deviceProfileForm').reset();
        document.getElementById('profileId').value = '';
        document.getElementById('deviceProfileModalLabel').innerHTML = '<i class="fas fa-plus me-2"></i>新增设备配置';
    }

    // 编辑设备配置
    async function editDeviceProfile(profileId) {
        try {
            const response = await fetch(`${DEVICE_PROFILE_API_BASE}/getEntity?key=${profileId}`, {
                headers: {
                    'Authorization': 'b8'
                }
            });

            const result = await response.json();
            if (result.code === 200) {
                fillDeviceProfileForm(result.data);
                document.getElementById('deviceProfileModalLabel').innerHTML = '<i class="fas fa-edit me-2"></i>编辑设备配置';
            } else {
                showToast('获取设备配置信息失败: ' + result.msg, 'error');
            }
        } catch (error) {
            console.error('获取设备配置信息失败:', error);
            showToast('获取设备配置信息失败', 'error');
        }
    }

    // 填充设备配置表单
    function fillDeviceProfileForm(profile) {
        document.getElementById('profileId').value = profile.id || '';
        document.getElementById('profileName').value = profile.name || '';
        document.getElementById('profileType').value = profile.type || 'DEFAULT';
        document.getElementById('profileDefaultRuleChain').value = profile.defaultrulechainid || '';
        document.getElementById('profileDescription').value = profile.description || '';
        document.getElementById('profileConfiguration').value = profile.configuration || '';
    }

    // 保存设备配置
    async function saveDeviceProfile() {
        const form = document.getElementById('deviceProfileForm');
        const formData = new FormData(form);
        const profileData = Object.fromEntries(formData.entries());

        // 验证必填字段
        if (!profileData.name) {
            showToast('请填写配置名称', 'warning');
            return;
        }

        const isEdit = !!profileData.id;
        const url = isEdit ? `${DEVICE_PROFILE_API_BASE}/update` : `${DEVICE_PROFILE_API_BASE}/create`;

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'b8'
                },
                body: JSON.stringify(profileData)
            });

            const result = await response.json();
            if (result.code === 200) {
                showToast(isEdit ? '设备配置更新成功' : '设备配置创建成功', 'success');

                // 刷新设备配置列表
                await loadDeviceProfiles();
                await loadDeviceProfilesForModal();

                // 刷新设备表单中的设备配置选项
                populateDeviceProfileSelect(deviceProfiles);

                // 清空表单
                clearDeviceProfileForm();
            } else {
                showToast('保存失败: ' + (result.msg || result.message || '未知错误'), 'error');
            }
        } catch (error) {
            console.error('保存设备配置失败:', error);
            showToast('保存设备配置失败', 'error');
        }
    }

    // 删除设备配置
    async function deleteDeviceProfile(profileId) {
        if (!confirm('确定要删除这个设备配置吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await fetch(`${DEVICE_PROFILE_API_BASE}/delete?key=${profileId}`, {
                method: 'GET',
                headers: {
                    'Authorization': 'b8'
                }
            });

            const result = await response.json();
            if (result.code === 200) {
                showToast('设备配置删除成功', 'success');

                // 刷新设备配置列表
                await loadDeviceProfiles();
                await loadDeviceProfilesForModal();

                // 刷新设备表单中的设备配置选项
                populateDeviceProfileSelect(deviceProfiles);
            } else {
                showToast('删除失败: ' + result.msg, 'error');
            }
        } catch (error) {
            console.error('删除设备配置失败:', error);
            showToast('删除设备配置失败', 'error');
        }
    }

    // 页面卸载时清理定时器
    window.addEventListener('beforeunload', function () {
        stopAutoRefresh();
    });

    // 模态框事件监听
    document.getElementById('deviceModal').addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        if (!button || !button.onclick) {
            // 新增设备
            clearDeviceForm();
            document.getElementById('deviceModalLabel').innerHTML = '<i class="fas fa-plus me-2"></i>新增设备';
        }
    });

    // 生成分页
    function generatePagination(pageInfo) {
        const pagination = document.getElementById('devicePagination');
        pagination.innerHTML = '';

        if (!pageInfo || pageInfo.pages <= 1) return;

        // 上一页
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${pageInfo.hasPreviousPage ? '' : 'disabled'}`;
        prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${pageInfo.prePage})">上一页</a>`;
        pagination.appendChild(prevLi);

        // 页码
        for (let i = 1; i <= pageInfo.pages; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === pageInfo.pageNum ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
            pagination.appendChild(li);
        }

        // 下一页
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${pageInfo.hasNextPage ? '' : 'disabled'}`;
        nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${pageInfo.nextPage})">下一页</a>`;
        pagination.appendChild(nextLi);
    }

    // 切换页面
    function changePage(page) {
        if (page && page > 0) {
            currentPage = page;
            loadDeviceList();
        }
    }
</script>

</body>
</html>