<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅管理 - MQTT管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/common.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-server"></i> MQTT管理</h3>
            <div class="subtitle">服务端控制台</div>
        </div>
        
        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="clients.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>客户端管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="publish.html" class="nav-link">
                    <i class="fas fa-paper-plane"></i>
                    <span>消息发布</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="subscriptions.html" class="nav-link active">
                    <i class="fas fa-rss"></i>
                    <span>订阅管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="messages.html" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    <span>消息管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-messages.html" class="nav-link">
                    <i class="fas fa-bolt"></i>
                    <span>实时消息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-monitor.html" class="nav-link">
                    <i class="fas fa-eye"></i>
                    <span>实时监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="stats.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计信息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <div class="content-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-rss"></i> 订阅管理</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html">首页</a></li>
                            <li class="breadcrumb-item active">订阅管理</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-outline-light me-2" onclick="loadAllSubscriptions()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <div class="form-check form-switch d-inline-block align-middle me-2">
                        <input class="form-check-input" type="checkbox" role="switch" id="autoRefreshSwitch">
                        <label class="form-check-label text-white" for="autoRefreshSwitch">自动刷新</label>
                    </div>
                    <button class="btn btn-outline-light d-md-none sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="content-body">
            <!-- 操作面板 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-plus"></i> 添加订阅</h5>
                        </div>
                        <div class="card-body">
                            <form id="subscribeForm">
                                <div class="mb-3">
                                    <label for="subscribeTopic" class="form-label">主题 *</label>
                                    <input type="text" class="form-control" id="subscribeTopic" 
                                           placeholder="例如: sensor/+/temperature" required>
                                    <div class="form-text">支持通配符: + (单级) 和 # (多级)</div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="subscribeClientId" class="form-label">客户端ID *</label>
                                            <input type="text" class="form-control" id="subscribeClientId" 
                                                   value="httpApi" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="subscribeQos" class="form-label">QoS等级</label>
                                            <select class="form-select" id="subscribeQos">
                                                <option value="0">QoS 0 - 最多一次</option>
                                                <option value="1">QoS 1 - 至少一次</option>
                                                <option value="2">QoS 2 - 恰好一次</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetSubscribeForm()">
                                        <i class="fas fa-undo"></i> 重置
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> 添加订阅
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-layer-group"></i> 批量操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="batchTopics" class="form-label">批量主题</label>
                                <textarea class="form-control" id="batchTopics" rows="3" 
                                          placeholder="每行一个主题，例如:&#10;sensor/+/temperature&#10;device/+/status&#10;alert/#"></textarea>
                                <div class="form-text">每行输入一个主题</div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="batchClientId" class="form-label">客户端ID</label>
                                        <input type="text" class="form-control" id="batchClientId" value="httpApi">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="batchQos" class="form-label">QoS等级</label>
                                        <select class="form-select" id="batchQos">
                                            <option value="0">QoS 0</option>
                                            <option value="1">QoS 1</option>
                                            <option value="2">QoS 2</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-warning" onclick="batchUnsubscribe()">
                                    <i class="fas fa-minus"></i> 批量取消
                                </button>
                                <button type="button" class="btn btn-success" onclick="batchSubscribe()">
                                    <i class="fas fa-plus"></i> 批量订阅
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订阅统计 -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-primary">
                            <i class="fas fa-rss"></i>
                        </div>
                        <div class="stat-value text-primary" id="totalSubscriptions">0</div>
                        <div class="stat-label">总订阅数</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-success">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-value text-success" id="uniqueTopics">0</div>
                        <div class="stat-label">唯一主题</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-info">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-value text-info" id="subscribedClients">0</div>
                        <div class="stat-label">订阅客户端</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-warning">
                            <i class="fas fa-hashtag"></i>
                        </div>
                        <div class="stat-value text-warning" id="wildcardSubscriptions">0</div>
                        <div class="stat-label">通配符订阅</div>
                    </div>
                </div>
            </div>

            <!-- 搜索和过滤 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="searchTopic" class="form-label">搜索主题</label>
                                <input type="text" class="form-control" id="searchTopic" placeholder="输入主题或客户端ID">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="qosFilter" class="form-label">QoS过滤</label>
                                <select class="form-select" id="qosFilter">
                                    <option value="">全部</option>
                                    <option value="0">QoS 0</option>
                                    <option value="1">QoS 1</option>
                                    <option value="2">QoS 2</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="topicTypeFilter" class="form-label">主题类型</label>
                                <select class="form-select" id="topicTypeFilter">
                                    <option value="">全部</option>
                                    <option value="exact">精确主题</option>
                                    <option value="wildcard">通配符主题</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button class="btn btn-primary w-100" onclick="applyFilters()">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订阅列表 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> 订阅列表</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-danger" onclick="unsubscribeSelected()" id="unsubscribeBtn" disabled>
                            <i class="fas fa-minus"></i> 取消选中
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>主题</th>
                                    <th>客户端ID</th>
                                    <th>QoS等级</th>
                                    <th>类型</th>
                                    <th>订阅时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="subscriptionsTableBody">
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 主题统计图表 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> 主题订阅统计</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <canvas id="topicChart" height="100"></canvas>
                        </div>
                        <div class="col-md-4">
                            <div id="topicStats">
                                <!-- 动态生成主题统计 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/config.js"></script>
    <script src="js/common.js"></script>
    <script>
        let allSubscriptions = [];
        let filteredSubscriptions = [];
        let topicChart;
        let autoRefreshInterval = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            loadAllSubscriptions();
            initializeChart();

            // 监听主题变化事件
            window.addEventListener('themeChanged', function(event) {
                updateChartTheme();
            });
        });

        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('subscribeForm').addEventListener('submit', handleSubscribe);
            document.getElementById('searchTopic').addEventListener('input',
                mqttUI.debounce(applyFilters, 300));

            document.getElementById('autoRefreshSwitch').addEventListener('change', function() {
                if (this.checked) {
                    if (!autoRefreshInterval) {
                        autoRefreshInterval = setInterval(loadAllSubscriptions, CONFIG.UI.REFRESH_INTERVAL);
                    }
                } else {
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                        autoRefreshInterval = null;
                    }
                }
            });
        }

        // 初始化图表
        function initializeChart() {
            const ctx = document.getElementById('topicChart').getContext('2d');
            topicChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: CONFIG.CHART.COLORS
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: document.body.classList.contains('theme-dark') ? '#d1d5db' : '#212529'
                            }
                        }
                    }
                }
            });
        }

        // 更新图表主题
        function updateChartTheme() {
            if (topicChart) {
                const isDark = document.body.classList.contains('theme-dark');
                topicChart.options.plugins.legend.labels.color = isDark ? '#d1d5db' : '#212529';
                topicChart.update();
            }
        }

        // 加载所有订阅信息
        async function loadAllSubscriptions() {
            try {
                // 使用新的聚合API接口获取所有客户端及其订阅信息
                const response = await mqttUI.apiCall('GET', '/clients/subscriptions');
                if (!response || response.code !== 1) {
                    console.error('获取客户端订阅信息失败:', response);
                    return;
                }

                const clientsData = response.data || [];
                allSubscriptions = [];

                // 处理每个客户端的订阅信息
                for (const clientData of clientsData) {
                    const { clientId, username, subscriptions, connectedAt } = clientData;
                    
                    // 将订阅信息转换为原有格式
                    for (const topicFilter of subscriptions) {
                        allSubscriptions.push({
                            clientId: clientId,
                            username: username,
                            topicFilter: topicFilter,
                            mqttQoS: 0, // 默认QoS，实际值可能需要从详细接口获取
                            subscribeTime: connectedAt,
                            isWildcard: topicFilter.includes('+') || topicFilter.includes('#')
                        });
                    }
                }

                console.log(`成功加载 ${clientsData.length} 个客户端的 ${allSubscriptions.length} 个订阅信息`);
                
                applyFilters();
                updateStats();
                updateChart();
            } catch (error) {
                console.error('加载订阅信息失败:', error);
                
                // 降级到原有方式（备用方案）
                console.log('尝试使用原有方式加载订阅信息...');
                await loadAllSubscriptionsLegacy();
            }
        }

        // 原有的加载方式作为备用方案
        async function loadAllSubscriptionsLegacy() {
            try {
                // 获取所有客户端
                const clientsResponse = await mqttUI.apiCall('GET', '/clients', { _limit: 1000 });
                if (!clientsResponse || clientsResponse.code !== 1) return;

                const clients = clientsResponse.data.list || [];
                allSubscriptions = [];

                // 为每个客户端获取订阅信息
                for (const client of clients) {
                    try {
                        const subResponse = await mqttUI.apiCall('GET', '/client/subscriptions', {
                            clientId: client.clientId
                        });

                        if (subResponse && subResponse.code === 1) {
                            const subscriptions = subResponse.data.map(sub => ({
                                ...sub,
                                subscribeTime: client.connectedAt,
                                isWildcard: sub.topicFilter.includes('+') || sub.topicFilter.includes('#')
                            }));
                            allSubscriptions.push(...subscriptions);
                        }
                    } catch (error) {
                        console.error(`获取客户端 ${client.clientId} 订阅失败:`, error);
                    }
                }

                applyFilters();
                updateStats();
                updateChart();
            } catch (error) {
                console.error('备用方式加载订阅信息也失败:', error);
            }
        }

        // 应用过滤器
        function applyFilters() {
            const searchTerm = document.getElementById('searchTopic').value.toLowerCase();
            const qosFilter = document.getElementById('qosFilter').value;
            const typeFilter = document.getElementById('topicTypeFilter').value;

            filteredSubscriptions = allSubscriptions.filter(sub => {
                const matchesSearch = !searchTerm ||
                    sub.topicFilter.toLowerCase().includes(searchTerm) ||
                    sub.clientId.toLowerCase().includes(searchTerm);

                const matchesQos = !qosFilter || sub.mqttQoS.toString() === qosFilter;

                const matchesType = !typeFilter ||
                    (typeFilter === 'wildcard' && sub.isWildcard) ||
                    (typeFilter === 'exact' && !sub.isWildcard);

                return matchesSearch && matchesQos && matchesType;
            });

            renderSubscriptionsTable();
        }

        // 渲染订阅表格
        function renderSubscriptionsTable() {
            const tbody = document.getElementById('subscriptionsTableBody');

            if (filteredSubscriptions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted py-4">暂无订阅数据</td></tr>';
                return;
            }

            tbody.innerHTML = filteredSubscriptions.map((sub, index) => `
                <tr>
                    <td>
                        <input type="checkbox" class="subscription-checkbox"
                               value="${sub.clientId}|${sub.topicFilter}"
                               onchange="updateUnsubscribeButton()">
                    </td>
                    <td>
                        <code>${sub.topicFilter}</code>
                        ${sub.isWildcard ? '<span class="badge bg-warning ms-1">通配符</span>' : ''}
                    </td>
                    <td>
                        <a href="clients.html" class="text-decoration-none">${sub.clientId}</a>
                    </td>
                    <td>
                        <span class="badge bg-info">QoS ${sub.mqttQoS}</span>
                    </td>
                    <td>
                        <span class="badge ${sub.isWildcard ? 'bg-warning' : 'bg-success'}">
                            ${sub.isWildcard ? '通配符' : '精确'}
                        </span>
                    </td>
                    <td>${mqttUI.formatTime(sub.subscribeTime)}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-danger"
                                onclick="unsubscribeSingle('${sub.clientId}', '${sub.topicFilter}')">
                            <i class="fas fa-minus"></i> 取消
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 更新统计信息
        function updateStats() {
            const uniqueTopics = new Set(allSubscriptions.map(sub => sub.topicFilter)).size;
            const subscribedClients = new Set(allSubscriptions.map(sub => sub.clientId)).size;
            const wildcardSubs = allSubscriptions.filter(sub => sub.isWildcard).length;

            document.getElementById('totalSubscriptions').textContent = mqttUI.formatNumber(allSubscriptions.length);
            document.getElementById('uniqueTopics').textContent = mqttUI.formatNumber(uniqueTopics);
            document.getElementById('subscribedClients').textContent = mqttUI.formatNumber(subscribedClients);
            document.getElementById('wildcardSubscriptions').textContent = mqttUI.formatNumber(wildcardSubs);
        }

        // 更新图表
        function updateChart() {
            const topicCounts = {};
            allSubscriptions.forEach(sub => {
                const topic = sub.topicFilter;
                topicCounts[topic] = (topicCounts[topic] || 0) + 1;
            });

            const sortedTopics = Object.entries(topicCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10);

            topicChart.data.labels = sortedTopics.map(([topic]) => topic);
            topicChart.data.datasets[0].data = sortedTopics.map(([,count]) => count);
            topicChart.update();

            // 更新统计列表
            const statsContainer = document.getElementById('topicStats');
            statsContainer.innerHTML = `
                <h6>热门主题 TOP 10</h6>
                ${sortedTopics.map(([topic, count], index) => `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 rounded"
                         style="background: var(--bg-darker);">
                        <div>
                            <span class="badge bg-primary me-2">${index + 1}</span>
                            <code style="font-size: 0.8rem;">${topic.length > 20 ? topic.substring(0, 20) + '...' : topic}</code>
                        </div>
                        <span class="badge bg-success">${count}</span>
                    </div>
                `).join('')}
            `;
        }

        // 处理订阅
        async function handleSubscribe(event) {
            event.preventDefault();

            const subscribeData = {
                topic: document.getElementById('subscribeTopic').value,
                clientId: document.getElementById('subscribeClientId').value,
                qos: parseInt(document.getElementById('subscribeQos').value)
            };

            if (!mqttUI.validateTopic(subscribeData.topic, true)) {
                mqttUI.showToast('主题格式不正确', 'error');
                return;
            }

            try {
                const response = await mqttUI.apiCall('POST', '/mqtt/subscribe', subscribeData);
                if (response && response.code === 1) {
                    mqttUI.showToast('订阅成功', 'success');
                    resetSubscribeForm();
                    loadAllSubscriptions();
                }
            } catch (error) {
                console.error('订阅失败:', error);
                mqttUI.showToast('订阅失败', 'error');
            }
        }

        // 批量订阅
        async function batchSubscribe() {
            const topics = document.getElementById('batchTopics').value
                .split('\n')
                .map(t => t.trim())
                .filter(t => t.length > 0);

            const clientId = document.getElementById('batchClientId').value;
            const qos = parseInt(document.getElementById('batchQos').value);

            if (topics.length === 0) {
                mqttUI.showToast('请输入要订阅的主题', 'warning');
                return;
            }

            const subscriptions = topics.map(topic => ({
                topic,
                clientId,
                qos
            }));

            // 验证主题格式
            for (const sub of subscriptions) {
                if (!mqttUI.validateTopic(sub.topic)) {
                    mqttUI.showToast(`主题 "${sub.topic}" 格式不正确`, 'error');
                    return;
                }
            }

            try {
                const response = await mqttUI.apiCall('POST', '/mqtt/subscribe/batch', subscriptions);
                if (response && response.code === 1) {
                    mqttUI.showToast(`成功订阅 ${topics.length} 个主题`, 'success');
                    document.getElementById('batchTopics').value = '';
                    loadAllSubscriptions();
                }
            } catch (error) {
                console.error('批量订阅失败:', error);
                mqttUI.showToast('批量订阅失败', 'error');
            }
        }

        // 批量取消订阅
        async function batchUnsubscribe() {
            const topics = document.getElementById('batchTopics').value
                .split('\n')
                .map(t => t.trim())
                .filter(t => t.length > 0);

            const clientId = document.getElementById('batchClientId').value;

            if (topics.length === 0) {
                mqttUI.showToast('请输入要取消订阅的主题', 'warning');
                return;
            }

            const unsubscriptions = topics.map(topic => ({
                topic,
                clientId
            }));

            try {
                const response = await mqttUI.apiCall('POST', '/mqtt/unsubscribe/batch', unsubscriptions);
                if (response && response.code === 1) {
                    mqttUI.showToast(`成功取消订阅 ${topics.length} 个主题`, 'success');
                    document.getElementById('batchTopics').value = '';
                    loadAllSubscriptions();
                }
            } catch (error) {
                console.error('批量取消订阅失败:', error);
                mqttUI.showToast('批量取消订阅失败', 'error');
            }
        }

        // 单个取消订阅
        async function unsubscribeSingle(clientId, topic) {
            if (!confirm(`确定要取消客户端 ${clientId} 对主题 "${topic}" 的订阅吗？`)) {
                return;
            }

            try {
                const response = await mqttUI.apiCall('POST', '/mqtt/unsubscribe', {
                    topic,
                    clientId
                });

                if (response && response.code === 1) {
                    mqttUI.showToast('取消订阅成功', 'success');
                    loadAllSubscriptions();
                }
            } catch (error) {
                console.error('取消订阅失败:', error);
                mqttUI.showToast('取消订阅失败', 'error');
            }
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.subscription-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateUnsubscribeButton();
        }

        // 更新取消订阅按钮状态
        function updateUnsubscribeButton() {
            const checkedBoxes = document.querySelectorAll('.subscription-checkbox:checked');
            const unsubscribeBtn = document.getElementById('unsubscribeBtn');

            unsubscribeBtn.disabled = checkedBoxes.length === 0;
        }

        // 取消选中的订阅
        async function unsubscribeSelected() {
            const checkedBoxes = document.querySelectorAll('.subscription-checkbox:checked');
            const subscriptions = Array.from(checkedBoxes).map(cb => {
                const [clientId, topic] = cb.value.split('|');
                return { clientId, topic };
            });

            if (subscriptions.length === 0) return;

            if (!confirm(`确定要取消选中的 ${subscriptions.length} 个订阅吗？`)) {
                return;
            }

            let successCount = 0;
            for (const sub of subscriptions) {
                try {
                    const response = await mqttUI.apiCall('POST', '/mqtt/unsubscribe', sub);
                    if (response && response.code === 1) {
                        successCount++;
                    }
                } catch (error) {
                    console.error(`取消订阅失败:`, error);
                }
            }

            mqttUI.showToast(`成功取消 ${successCount} 个订阅`, 'success');
            loadAllSubscriptions();

            // 重置选择
            document.getElementById('selectAll').checked = false;
            updateUnsubscribeButton();
        }

        // 重置订阅表单
        function resetSubscribeForm() {
            document.getElementById('subscribeForm').reset();
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
