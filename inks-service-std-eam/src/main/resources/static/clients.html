<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户端管理 - MQTT管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/common.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-server"></i> MQTT管理</h3>
            <div class="subtitle">服务端控制台</div>
        </div>
        
        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="clients.html" class="nav-link active">
                    <i class="fas fa-users"></i>
                    <span>客户端管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="publish.html" class="nav-link">
                    <i class="fas fa-paper-plane"></i>
                    <span>消息发布</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="subscriptions.html" class="nav-link">
                    <i class="fas fa-rss"></i>
                    <span>订阅管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="messages.html" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    <span>消息管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-messages.html" class="nav-link">
                    <i class="fas fa-bolt"></i>
                    <span>实时消息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-monitor.html" class="nav-link">
                    <i class="fas fa-eye"></i>
                    <span>实时监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="stats.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计信息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <div class="content-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-users"></i> 客户端管理</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html">首页</a></li>
                            <li class="breadcrumb-item active">客户端管理</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-outline-light me-2" onclick="refreshClients()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <div class="form-check form-switch d-inline-block align-middle me-2">
                        <input class="form-check-input" type="checkbox" role="switch" id="autoRefreshSwitch">
                        <label class="form-check-label text-white" for="autoRefreshSwitch">自动刷新</label>
                    </div>
                    <button class="btn btn-outline-light d-md-none sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="content-body">
            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-success">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-value text-success" id="onlineClients">0</div>
                        <div class="stat-label">在线客户端</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-danger">
                            <i class="fas fa-user-times"></i>
                        </div>
                        <div class="stat-value text-danger" id="offlineClients">0</div>
                        <div class="stat-label">离线客户端</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-value text-primary" id="totalClients">0</div>
                        <div class="stat-label">总客户端数</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-info">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <div class="stat-value text-info" id="activeConnections">0</div>
                        <div class="stat-label">活跃连接</div>
                    </div>
                </div>
            </div>

            <!-- 搜索和过滤 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="searchClient" class="form-label">搜索客户端</label>
                                <input type="text" class="form-control" id="searchClient" placeholder="输入客户端ID或用户名">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="statusFilter" class="form-label">连接状态</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">全部</option>
                                    <option value="online">在线</option>
                                    <option value="offline">离线</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="protocolFilter" class="form-label">协议类型</label>
                                <select class="form-select" id="protocolFilter">
                                    <option value="">全部</option>
                                    <option value="MQTT">MQTT</option>
                                    <option value="WebSocket">WebSocket</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button class="btn btn-primary w-100" onclick="applyFilters()">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 客户端列表 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> 客户端列表</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-danger" onclick="disconnectSelected()" id="disconnectBtn" disabled>
                            <i class="fas fa-times"></i> 断开选中
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>状态</th>
                                    <th>客户端ID</th>
                                    <th>用户名</th>
                                    <th>协议</th>
                                    <th>IP地址</th>
                                    <th>连接时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="clientsTableBody">
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <nav aria-label="客户端分页">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- 动态生成分页 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </main>

    <!-- 客户端详情模态框 -->
    <div class="modal fade" id="clientDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-info-circle"></i> 客户端详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="clientDetailBody">
                    <!-- 动态内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-danger" onclick="disconnectClient(currentClientId)">
                        <i class="fas fa-times"></i> 断开连接
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/common.js"></script>
    <script>
        let currentPage = 1;
        let totalPages = 1;
        let allClients = [];
        let filteredClients = [];
        let currentClientId = null;
        let autoRefreshInterval = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadClients();
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('searchClient').addEventListener('input',
                mqttUI.debounce(applyFilters, 300));

            document.getElementById('autoRefreshSwitch').addEventListener('change', function() {
                if (this.checked) {
                    if (!autoRefreshInterval) {
                        autoRefreshInterval = setInterval(loadClients, CONFIG.UI.REFRESH_INTERVAL);
                    }
                } else {
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                        autoRefreshInterval = null;
                    }
                }
            });
        }

        // 加载客户端列表
        async function loadClients() {
            try {
                const response = await mqttUI.apiCall('GET', '/clients', {
                    _page: currentPage,
                    _limit: CONFIG.UI.PAGE_SIZE
                });

                if (response && response.code === 1) {
                    allClients = response.data.list || [];
                    totalPages = Math.ceil(response.data.totalRow / CONFIG.UI.PAGE_SIZE);

                    applyFilters();
                    updateStats();
                }
            } catch (error) {
                console.error('加载客户端列表失败:', error);
                document.getElementById('clientsTableBody').innerHTML =
                    '<tr><td colspan="8" class="text-center text-danger py-4">加载失败</td></tr>';
            }
        }

        // 应用过滤器
        function applyFilters() {
            const searchTerm = document.getElementById('searchClient').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const protocolFilter = document.getElementById('protocolFilter').value;

            filteredClients = allClients.filter(client => {
                const matchesSearch = !searchTerm ||
                    client.clientId.toLowerCase().includes(searchTerm) ||
                    (client.username && client.username.toLowerCase().includes(searchTerm));

                const matchesStatus = !statusFilter ||
                    (statusFilter === 'online' && client.connected) ||
                    (statusFilter === 'offline' && !client.connected);

                const matchesProtocol = !protocolFilter ||
                    client.protoName === protocolFilter;

                return matchesSearch && matchesStatus && matchesProtocol;
            });

            renderClientsTable();
            renderPagination();
        }

        // 渲染客户端表格
        function renderClientsTable() {
            const tbody = document.getElementById('clientsTableBody');

            if (filteredClients.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted py-4">暂无数据</td></tr>';
                return;
            }

            tbody.innerHTML = filteredClients.map(client => `
                <tr>
                    <td>
                        <input type="checkbox" class="client-checkbox" value="${client.clientId}"
                               onchange="updateDisconnectButton()">
                    </td>
                    <td>
                        <span class="status-indicator ${client.connected ? 'status-online' : 'status-offline'}"></span>
                        ${client.connected ? '在线' : '离线'}
                    </td>
                    <td>
                        <a href="#" onclick="showClientDetail('${client.clientId}')" class="text-decoration-none">
                            ${client.clientId}
                        </a>
                    </td>
                    <td>${client.username || '-'}</td>
                    <td>
                        <span class="badge bg-info">${client.protoName} v${client.protoVer}</span>
                    </td>
                    <td>${client.ipAddress}:${client.port}</td>
                    <td>${mqttUI.formatTime(client.connectedAt)}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-info me-1"
                                onclick="showClientDetail('${client.clientId}')">
                            <i class="fas fa-info"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-primary me-1"
                                onclick="showSubscriptions('${client.clientId}')">
                            <i class="fas fa-rss"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger"
                                onclick="disconnectClient('${client.clientId}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染分页
        function renderPagination() {
            const pagination = document.getElementById('pagination');
            let paginationHTML = '';

            // 上一页
            paginationHTML += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
                </li>
            `;

            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                paginationHTML += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            }

            // 下一页
            paginationHTML += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
                </li>
            `;

            pagination.innerHTML = paginationHTML;
        }

        // 更新统计信息
        function updateStats() {
            const online = allClients.filter(c => c.connected).length;
            const offline = allClients.filter(c => !c.connected).length;
            const total = allClients.length;

            document.getElementById('onlineClients').textContent = mqttUI.formatNumber(online);
            document.getElementById('offlineClients').textContent = mqttUI.formatNumber(offline);
            document.getElementById('totalClients').textContent = mqttUI.formatNumber(total);
            document.getElementById('activeConnections').textContent = mqttUI.formatNumber(online);
        }

        // 切换页面
        function changePage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadClients();
            }
        }

        // 显示客户端详情
        async function showClientDetail(clientId) {
            try {
                const response = await mqttUI.apiCall('GET', '/clients/info', { clientId });
                if (response && response.code === 1) {
                    const client = response.data;
                    currentClientId = clientId;

                    document.getElementById('clientDetailBody').innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>基本信息</h6>
                                <table class="table table-borderless table-sm">
                                    <tr><td><strong>客户端ID:</strong></td><td>${client.clientId}</td></tr>
                                    <tr><td><strong>用户名:</strong></td><td>${client.username || '-'}</td></tr>
                                    <tr><td><strong>连接状态:</strong></td><td>
                                        <span class="status-indicator ${client.connected ? 'status-online' : 'status-offline'}"></span>
                                        ${client.connected ? '在线' : '离线'}
                                    </td></tr>
                                    <tr><td><strong>协议:</strong></td><td>${client.protoName} v${client.protoVer}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>连接信息</h6>
                                <table class="table table-borderless table-sm">
                                    <tr><td><strong>IP地址:</strong></td><td>${client.ipAddress}</td></tr>
                                    <tr><td><strong>端口:</strong></td><td>${client.port}</td></tr>
                                    <tr><td><strong>连接时间:</strong></td><td>${mqttUI.formatTime(client.connectedAt)}</td></tr>
                                    <tr><td><strong>创建时间:</strong></td><td>${mqttUI.formatTime(client.createdAt)}</td></tr>
                                </table>
                            </div>
                        </div>
                    `;

                    new bootstrap.Modal(document.getElementById('clientDetailModal')).show();
                }
            } catch (error) {
                console.error('获取客户端详情失败:', error);
                mqttUI.showToast('获取客户端详情失败', 'error');
            }
        }

        // 显示订阅信息
        async function showSubscriptions(clientId) {
            try {
                const response = await mqttUI.apiCall('GET', '/client/subscriptions', { clientId });
                if (response && response.code === 1) {
                    const subscriptions = response.data;

                    let content = '<h6>订阅列表</h6>';
                    if (subscriptions.length === 0) {
                        content += '<p class="text-muted">该客户端暂无订阅</p>';
                    } else {
                        content += '<div class="table-responsive"><table class="table table-sm">';
                        content += '<thead><tr><th>主题</th><th>QoS</th></tr></thead><tbody>';
                        subscriptions.forEach(sub => {
                            content += `<tr><td>${sub.topicFilter}</td><td><span class="badge bg-info">QoS ${sub.mqttQoS}</span></td></tr>`;
                        });
                        content += '</tbody></table></div>';
                    }

                    document.getElementById('clientDetailBody').innerHTML = content;
                    new bootstrap.Modal(document.getElementById('clientDetailModal')).show();
                }
            } catch (error) {
                console.error('获取订阅信息失败:', error);
                mqttUI.showToast('获取订阅信息失败', 'error');
            }
        }

        // 断开客户端连接
        async function disconnectClient(clientId) {
            if (!confirm(`确定要断开客户端 ${clientId} 的连接吗？`)) {
                return;
            }

            try {
                const response = await mqttUI.apiCall('POST', '/clients/delete', null, { clientId });
                if (response && response.code === 1) {
                    mqttUI.showToast('客户端连接已断开', 'success');
                    loadClients();

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('clientDetailModal'));
                    if (modal) modal.hide();
                }
            } catch (error) {
                console.error('断开连接失败:', error);
                mqttUI.showToast('断开连接失败', 'error');
            }
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.client-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateDisconnectButton();
        }

        // 更新断开按钮状态
        function updateDisconnectButton() {
            const checkedBoxes = document.querySelectorAll('.client-checkbox:checked');
            const disconnectBtn = document.getElementById('disconnectBtn');

            disconnectBtn.disabled = checkedBoxes.length === 0;
        }

        // 断开选中的客户端
        async function disconnectSelected() {
            const checkedBoxes = document.querySelectorAll('.client-checkbox:checked');
            const clientIds = Array.from(checkedBoxes).map(cb => cb.value);

            if (clientIds.length === 0) return;

            if (!confirm(`确定要断开选中的 ${clientIds.length} 个客户端连接吗？`)) {
                return;
            }

            let successCount = 0;
            for (const clientId of clientIds) {
                try {
                    const response = await mqttUI.apiCall('POST', '/clients/delete', null, { clientId });
                    if (response && response.code === 1) {
                        successCount++;
                    }
                } catch (error) {
                    console.error(`断开客户端 ${clientId} 失败:`, error);
                }
            }

            mqttUI.showToast(`成功断开 ${successCount} 个客户端连接`, 'success');
            loadClients();

            // 重置选择
            document.getElementById('selectAll').checked = false;
            updateDisconnectButton();
        }

        // 刷新客户端列表
        function refreshClients() {
            loadClients();
            mqttUI.showToast('客户端列表已刷新', 'success');
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
