<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - MQTT管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/common.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-server"></i> MQTT管理</h3>
            <div class="subtitle">服务端控制台</div>
        </div>

        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="clients.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>客户端管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="publish.html" class="nav-link">
                    <i class="fas fa-paper-plane"></i>
                    <span>消息发布</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="subscriptions.html" class="nav-link">
                    <i class="fas fa-rss"></i>
                    <span>订阅管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="messages.html" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    <span>消息管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-messages.html" class="nav-link">
                    <i class="fas fa-bolt"></i>
                    <span>实时消息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-monitor.html" class="nav-link">
                    <i class="fas fa-eye"></i>
                    <span>实时监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="stats.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计信息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="settings.html" class="nav-link active">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <div class="content-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-cog"></i> 系统设置</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html">首页</a></li>
                            <li class="breadcrumb-item active">系统设置</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-outline-light me-2" onclick="resetAllSettings()">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                    <button class="btn btn-outline-light d-md-none sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="content-body">
            <!-- 设置导航 -->
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">设置分类</h6>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="#general" class="list-group-item list-group-item-action active" onclick="showSettingsPanel('general')">
                                <i class="fas fa-cog me-2"></i>常规设置
                            </a>
                            <a href="#server" class="list-group-item list-group-item-action" onclick="showSettingsPanel('server')">
                                <i class="fas fa-server me-2"></i>服务器配置
                            </a>
                            <a href="#security" class="list-group-item list-group-item-action" onclick="showSettingsPanel('security')">
                                <i class="fas fa-shield-alt me-2"></i>安全设置
                            </a>
                            <a href="#monitoring" class="list-group-item list-group-item-action" onclick="showSettingsPanel('monitoring')">
                                <i class="fas fa-chart-line me-2"></i>监控设置
                            </a>
                            <a href="#notifications" class="list-group-item list-group-item-action" onclick="showSettingsPanel('notifications')">
                                <i class="fas fa-bell me-2"></i>通知设置
                            </a>
                            <a href="#backup" class="list-group-item list-group-item-action" onclick="showSettingsPanel('backup')">
                                <i class="fas fa-database me-2"></i>备份恢复
                            </a>
                            <a href="#about" class="list-group-item list-group-item-action" onclick="showSettingsPanel('about')">
                                <i class="fas fa-info-circle me-2"></i>关于系统
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-9">
                    <!-- 常规设置 -->
                    <div id="generalPanel" class="settings-panel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-cog"></i> 常规设置</h5>
                            </div>
                            <div class="card-body">
                                <form id="generalForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="systemName" class="form-label">系统名称</label>
                                                <input type="text" class="form-control" id="systemName" value="MQTT管理系统">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="language" class="form-label">语言</label>
                                                <select class="form-select" id="language">
                                                    <option value="zh-CN" selected>简体中文</option>
                                                    <option value="en-US">English</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">主题</label>
                                                <div class="btn-group w-100" role="group">
                                                    <button type="button" class="btn btn-outline-primary active" id="lightThemeBtn" onclick="toggleTheme('theme-light')">
                                                        <i class="fas fa-sun"></i> 浅色
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary" id="darkThemeBtn" onclick="toggleTheme('theme-dark')">
                                                        <i class="fas fa-moon"></i> 深色
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="timezone" class="form-label">时区</label>
                                                <select class="form-select" id="timezone">
                                                    <option value="Asia/Shanghai" selected>Asia/Shanghai (UTC+8)</option>
                                                    <option value="UTC">UTC (UTC+0)</option>
                                                    <option value="America/New_York">America/New_York (UTC-5)</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="refreshInterval" class="form-label">刷新间隔(秒)</label>
                                                <input type="number" class="form-control" id="refreshInterval" value="5" min="1" max="60">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="pageSize" class="form-label">分页大小</label>
                                                <select class="form-select" id="pageSize">
                                                    <option value="10">10</option>
                                                    <option value="20" selected>20</option>
                                                    <option value="50">50</option>
                                                    <option value="100">100</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                                            <label class="form-check-label" for="autoRefresh">
                                                启用自动刷新
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="showNotifications" checked>
                                            <label class="form-check-label" for="showNotifications">
                                                显示系统通知
                                            </label>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存设置
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 服务器配置 -->
                    <div id="serverPanel" class="settings-panel" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-server"></i> 服务器配置</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    注意：修改服务器配置需要重启服务才能生效
                                </div>

                                <form id="serverForm">
                                    <h6>MQTT服务器</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="mqttPort" class="form-label">MQTT端口</label>
                                                <input type="number" class="form-control" id="mqttPort" value="1883">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="wsPort" class="form-label">WebSocket端口</label>
                                                <input type="number" class="form-control" id="wsPort" value="8083">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="httpPort" class="form-label">HTTP API端口</label>
                                                <input type="number" class="form-control" id="httpPort" value="10180">
                                            </div>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 保存配置
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 其他设置面板 -->
                    <div id="securityPanel" class="settings-panel" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-shield-alt"></i> 安全设置</h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">安全设置功能开发中...</p>
                            </div>
                        </div>
                    </div>

                    <div id="monitoringPanel" class="settings-panel" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-chart-line"></i> 监控设置</h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">监控设置功能开发中...</p>
                            </div>
                        </div>
                    </div>

                    <div id="notificationsPanel" class="settings-panel" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-bell"></i> 通知设置</h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">通知设置功能开发中...</p>
                            </div>
                        </div>
                    </div>

                    <div id="backupPanel" class="settings-panel" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-database"></i> 备份恢复</h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">备份恢复功能开发中...</p>
                            </div>
                        </div>
                    </div>

                    <div id="aboutPanel" class="settings-panel" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-info-circle"></i> 关于系统</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr><td><strong>系统名称:</strong></td><td>MQTT管理系统</td></tr>
                                            <tr><td><strong>版本:</strong></td><td>1.0.0</td></tr>
                                            <tr><td><strong>构建时间:</strong></td><td>2024-01-01</td></tr>
                                            <tr><td><strong>技术栈:</strong></td><td>Spring Boot + mica-mqtt</td></tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr><td><strong>开发者:</strong></td><td>MQTT Team</td></tr>
                                            <tr><td><strong>许可证:</strong></td><td>MIT License</td></tr>
                                            <tr><td><strong>官网:</strong></td><td><a href="#">mqtt-admin.com</a></td></tr>
                                            <tr><td><strong>文档:</strong></td><td><a href="#">查看文档</a></td></tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/common.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('generalForm').addEventListener('submit', saveGeneralSettings);
            document.getElementById('serverForm').addEventListener('submit', saveServerSettings);
        }

        // 显示设置面板
        function showSettingsPanel(panelName) {
            // 隐藏所有面板
            document.querySelectorAll('.settings-panel').forEach(panel => {
                panel.style.display = 'none';
            });

            // 移除所有活动状态
            document.querySelectorAll('.list-group-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中面板
            document.getElementById(panelName + 'Panel').style.display = 'block';

            // 添加活动状态
            document.querySelector(`[href="#${panelName}"]`).classList.add('active');
        }

        // 加载设置
        function loadSettings() {
            const settings = mqttUI.loadSettings();

            // 加载常规设置
            if (settings.general) {
                Object.keys(settings.general).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        if (element.type === 'checkbox') {
                            element.checked = settings.general[key];
                        } else {
                            element.value = settings.general[key];
                        }
                    }
                });
            }

            // 加载服务器设置
            if (settings.server) {
                Object.keys(settings.server).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        if (element.type === 'checkbox') {
                            element.checked = settings.server[key];
                        } else {
                            element.value = settings.server[key];
                        }
                    }
                });
            }
        }

        // 保存常规设置
        function saveGeneralSettings(event) {
            event.preventDefault();

            const settings = mqttUI.loadSettings();
            settings.general = {
                systemName: document.getElementById('systemName').value,
                language: document.getElementById('language').value,
                theme: localStorage.getItem('mqtt_theme') || 'theme-light',
                timezone: document.getElementById('timezone').value,
                refreshInterval: document.getElementById('refreshInterval').value,
                pageSize: document.getElementById('pageSize').value,
                autoRefresh: document.getElementById('autoRefresh').checked,
                showNotifications: document.getElementById('showNotifications').checked
            };

            mqttUI.saveSettings(settings);
            mqttUI.showToast('常规设置已保存', 'success');
        }

        // 保存服务器设置
        function saveServerSettings(event) {
            event.preventDefault();

            const settings = mqttUI.loadSettings();
            settings.server = {
                mqttPort: document.getElementById('mqttPort').value,
                wsPort: document.getElementById('wsPort').value,
                httpPort: document.getElementById('httpPort').value
            };

            mqttUI.saveSettings(settings);
            mqttUI.showToast('服务器设置已保存，重启服务后生效', 'warning');
        }

        // 重置所有设置
        function resetAllSettings() {
            if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
                localStorage.removeItem(CONFIG.STORAGE_KEYS.SETTINGS);
                location.reload();
            }
        }
    </script>
</body>
</html>