<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT服务器管理平台</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome CSS for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <!-- Chart.js for charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
        }
        .container-fluid {
            max-width: 1800px;
        }
        .card-header-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .card-header-mqtt {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }
        .card-header-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .card-header-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            border: none;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #0e8679 0%, #32d96a 100%);
        }
        .status-online {
            color: #28a745;
            font-weight: bold;
        }
        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }
        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }
        .mqtt-stats {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .nav-tabs .nav-link {
            font-weight: 500;
            border-radius: 10px 10px 0 0;
            margin-right: 5px;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }
        .nav-tabs .nav-link:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
            border-color: transparent;
        }
        .table-hover tbody tr:hover {
            background-color: #e3f2fd;
        }
        .connection-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .connection-online {
            background-color: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }
        .connection-offline {
            background-color: #dc3545;
        }
        .connection-warning {
            background-color: #ffc107;
        }
        .log-container {
            background-color: #1e1e1e !important;
            color: #d4d4d4 !important;
            font-family: 'Courier New', monospace !important;
            font-size: 12px !important;
            line-height: 1.4;
            padding: 10px;
        }
        .log-entry {
            margin-bottom: 2px;
            padding: 2px 5px;
            border-radius: 3px;
            word-wrap: break-word;
        }
        .log-entry.error {
            background-color: rgba(220, 53, 69, 0.1);
            border-left: 3px solid #dc3545;
        }
        .log-entry.warn {
            background-color: rgba(255, 193, 7, 0.1);
            border-left: 3px solid #ffc107;
        }
        .log-entry.info {
            background-color: rgba(23, 162, 184, 0.1);
            border-left: 3px solid #17a2b8;
        }
        .log-entry.debug {
            background-color: rgba(108, 117, 125, 0.1);
            border-left: 3px solid #6c757d;
        }
        .log-timestamp {
            color: #6c757d;
            font-weight: bold;
        }
        .log-level {
            font-weight: bold;
            padding: 1px 4px;
            border-radius: 2px;
            font-size: 10px;
        }
        .log-level.error {
            background-color: #dc3545;
            color: white;
        }
        .log-level.warn {
            background-color: #ffc107;
            color: #212529;
        }
        .log-level.info {
            background-color: #17a2b8;
            color: white;
        }
        .log-level.debug {
            background-color: #6c757d;
            color: white;
        }
        .performance-gauge {
            position: relative;
            display: inline-block;
        }
        .performance-value {
            font-size: 1.2em;
            font-weight: bold;
        }
        .performance-normal {
            color: #28a745;
        }
        .performance-warning {
            color: #ffc107;
        }
        .performance-danger {
            color: #dc3545;
        }
        .refresh-btn {
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .mqtt-card {
            transition: transform 0.2s;
            border-left: 4px solid #11998e;
        }
        .mqtt-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .log-container {
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            border-radius: 8px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-timestamp {
            color: #888;
        }
        .log-level-info {
            color: #4fc3f7;
        }
        .log-level-warn {
            color: #ffb74d;
        }
        .log-level-error {
            color: #f44336;
        }
        .log-level-debug {
            color: #81c784;
        }
        .message-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            height: 300px;
            overflow-y: auto;
            padding: 10px;
        }
        .message-item {
            border-bottom: 1px solid #e9ecef;
            padding: 8px 0;
            font-size: 0.9rem;
        }
        .message-topic {
            font-weight: bold;
            color: #495057;
        }
        .message-payload {
            color: #6c757d;
            margin-top: 4px;
        }
        .message-meta {
            font-size: 0.8rem;
            color: #adb5bd;
            margin-top: 4px;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }
        .websocket-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
        }
        .websocket-connected {
            background-color: #28a745;
        }
        .websocket-disconnected {
            background-color: #dc3545;
        }
        .websocket-connecting {
            background-color: #ffc107;
        }
        .config-item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .config-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .config-value {
            color: #6c757d;
            font-family: 'Courier New', monospace;
        }
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .metric-title {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #495057;
        }
        .metric-unit {
            font-size: 0.8rem;
            color: #adb5bd;
        }
        .tab-pane {
            min-height: 600px;
        }
        .loading-spinner {
            display: none;
        }
        .error-message {
            display: none;
        }
    </style>
</head>
<body>

<div class="container-fluid mt-4">
    <!-- WebSocket Status Indicator -->
    <div class="websocket-status">
        <span class="badge websocket-disconnected" id="wsStatus">
            <i class="fas fa-wifi me-1"></i>WebSocket断开
        </span>
    </div>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary">
            <i class="fas fa-server me-2"></i>MQTT服务器管理平台
        </h2>
        <div>
            <button class="btn btn-outline-primary me-2" onclick="refreshAllData()">
                <i class="fas fa-sync-alt" id="refreshIcon"></i> 刷新数据
            </button>
            <button class="btn btn-outline-success me-2" onclick="toggleAutoRefresh()">
                <i class="fas fa-play" id="autoRefreshIcon"></i> <span id="autoRefreshText">开启自动刷新</span>
            </button>
            <span class="badge bg-success fs-6" id="systemStatus">系统运行中</span>
        </div>
    </div>

    <!-- MQTT Statistics Dashboard -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number status-online" id="mqttConnections">0</div>
                <div class="stats-label">MQTT连接数</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number" id="mqttMessages">0</div>
                <div class="stats-label">消息总数</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number" id="mqttTokens">0</div>
                <div class="stats-label">活跃Token</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number" id="wsConnections">0</div>
                <div class="stats-label">WebSocket连接</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number status-online" id="redisStatus">正常</div>
                <div class="stats-label">Redis状态</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-number" id="systemUptime">0</div>
                <div class="stats-label">运行时间(小时)</div>
            </div>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs" id="mqttManagementTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="monitor-tab" data-bs-toggle="tab" data-bs-target="#monitor-pane" type="button" role="tab">
                <i class="fas fa-chart-line me-1"></i> 实时监控
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="connections-tab" data-bs-toggle="tab" data-bs-target="#connections-pane" type="button" role="tab">
                <i class="fas fa-network-wired me-1"></i> 连接管理
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="messages-tab" data-bs-toggle="tab" data-bs-target="#messages-pane" type="button" role="tab">
                <i class="fas fa-envelope me-1"></i> 消息管理
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="tokens-tab" data-bs-toggle="tab" data-bs-target="#tokens-pane" type="button" role="tab">
                <i class="fas fa-key me-1"></i> Token管理
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="tools-tab" data-bs-toggle="tab" data-bs-target="#tools-pane" type="button" role="tab">
                <i class="fas fa-tools me-1"></i> 系统工具
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config-pane" type="button" role="tab">
                <i class="fas fa-cog me-1"></i> 配置管理
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs-pane" type="button" role="tab">
                <i class="fas fa-file-alt me-1"></i> 日志监控
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="metrics-tab" data-bs-toggle="tab" data-bs-target="#metrics-pane" type="button" role="tab">
                <i class="fas fa-chart-bar me-1"></i> 性能监控
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="mqttManagementTabContent">
        
        <!-- Monitor Pane - 实时监控 -->
        <div class="tab-pane fade show active" id="monitor-pane" role="tabpanel">
            <div class="row mt-3">
                <!-- 左侧：实时图表 -->
                <div class="col-lg-8">
                    <!-- 连接趋势图 -->
                    <div class="card mqtt-card mb-4">
                        <div class="card-header card-header-mqtt">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>连接趋势图</h5>
                                <div>
                                    <button class="btn btn-light btn-sm me-2" onclick="resetConnectionChart()">
                                        <i class="fas fa-redo me-1"></i>重置
                                    </button>
                                    <span class="badge bg-light text-dark" id="connectionChartStatus">实时更新</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="connectionChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 消息流量图 -->
                    <div class="card mqtt-card mb-4">
                        <div class="card-header card-header-info">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>消息流量图</h5>
                                <div>
                                    <button class="btn btn-light btn-sm me-2" onclick="resetMessageChart()">
                                        <i class="fas fa-redo me-1"></i>重置
                                    </button>
                                    <span class="badge bg-light text-dark" id="messageChartStatus">实时更新</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="messageChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：实时状态 -->
                <div class="col-lg-4">
                    <!-- 系统状态 -->
                    <div class="card mqtt-card mb-4">
                        <div class="card-header card-header-custom">
                            <h5 class="mb-0"><i class="fas fa-server me-2"></i>系统状态</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="metric-card">
                                        <div class="metric-title">MQTT服务</div>
                                        <div class="metric-value status-online" id="mqttServiceStatus">运行中</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="metric-card">
                                        <div class="metric-title">Redis状态</div>
                                        <div class="metric-value status-online" id="redisServiceStatus">正常</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="metric-card">
                                        <div class="metric-title">WebSocket</div>
                                        <div class="metric-value status-offline" id="wsServiceStatus">断开</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="metric-card">
                                        <div class="metric-title">端口状态</div>
                                        <div class="metric-value" id="portStatus">1884</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时指标 -->
                    <div class="card mqtt-card mb-4">
                        <div class="card-header card-header-warning">
                            <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>实时指标</h5>
                        </div>
                        <div class="card-body">
                            <div class="metric-card">
                                <div class="metric-title">CPU使用率</div>
                                <div class="metric-value" id="cpuUsage">0%</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-title">内存使用</div>
                                <div class="metric-value" id="memoryUsage">0 MB</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-title">消息速率</div>
                                <div class="metric-value" id="messageRate">0 <span class="metric-unit">msg/s</span></div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-title">网络流量</div>
                                <div class="metric-value" id="networkTraffic">0 <span class="metric-unit">KB/s</span></div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近事件 -->
                    <div class="card mqtt-card">
                        <div class="card-header card-header-info">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-bell me-2"></i>最近事件</h5>
                                <button class="btn btn-light btn-sm" onclick="clearRecentEvents()">
                                    <i class="fas fa-trash me-1"></i>清空
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush" id="recentEventsList">
                                <div class="list-group-item text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>暂无事件
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Connections Pane - 连接管理 -->
        <div class="tab-pane fade" id="connections-pane" role="tabpanel">
            <div class="row mt-3">
                <!-- 连接统计卡片 -->
                <div class="col-12 mb-4">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number status-online" id="connectionsTotalCount">0</div>
                                <div class="stats-label">总连接数</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number status-online" id="connectionsOnlineCount">0</div>
                                <div class="stats-label">在线连接</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number" id="connectionsActiveTokens">0</div>
                                <div class="stats-label">活跃Token</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number" id="connectionsLastUpdate">--:--:--</div>
                                <div class="stats-label">最后更新</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 连接管理工具栏 -->
                <div class="col-12 mb-3">
                    <div class="card mqtt-card">
                        <div class="card-header card-header-mqtt">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-network-wired me-2"></i>连接管理</h5>
                                <div>
                                    <button class="btn btn-light btn-sm me-2" onclick="refreshConnections()">
                                        <i class="fas fa-sync-alt" id="connectionsRefreshIcon"></i> 刷新
                                    </button>
                                    <button class="btn btn-warning btn-sm me-2" onclick="cleanupInvalidConnections()">
                                        <i class="fas fa-broom"></i> 清理无效连接
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="disconnectAllConnections()" id="disconnectAllBtn">
                                        <i class="fas fa-power-off"></i> 断开所有连接
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 搜索和过滤 -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label class="form-label">搜索设备</label>
                                    <input type="text" class="form-control" id="connectionSearchInput" placeholder="设备ID、Token或ClientID">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">连接状态</label>
                                    <select class="form-select" id="connectionStatusFilter">
                                        <option value="">所有状态</option>
                                        <option value="online">在线</option>
                                        <option value="offline">离线</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">设备类型</label>
                                    <select class="form-select" id="connectionTypeFilter">
                                        <option value="">所有类型</option>
                                        <option value="sensor">传感器</option>
                                        <option value="gateway">网关</option>
                                        <option value="controller">控制器</option>
                                        <option value="mobile">移动设备</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">每页显示</label>
                                    <select class="form-select" id="connectionPageSize">
                                        <option value="10">10条</option>
                                        <option value="20" selected>20条</option>
                                        <option value="50">50条</option>
                                        <option value="100">100条</option>
                                    </select>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button class="btn btn-primary me-2" onclick="searchConnections()">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="resetConnectionFilters()">
                                        <i class="fas fa-undo"></i> 重置
                                    </button>
                                </div>
                            </div>

                            <!-- 批量操作 -->
                            <div class="row mb-3" id="batchOperationsRow" style="display: none;">
                                <div class="col-12">
                                    <div class="alert alert-info d-flex justify-content-between align-items-center">
                                        <span>
                                            <i class="fas fa-info-circle me-2"></i>
                                            已选择 <strong id="selectedConnectionsCount">0</strong> 个连接
                                        </span>
                                        <div>
                                            <button class="btn btn-warning btn-sm me-2" onclick="batchDisconnectConnections()">
                                                <i class="fas fa-power-off"></i> 批量断开
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" onclick="clearConnectionSelection()">
                                                <i class="fas fa-times"></i> 取消选择
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 连接列表 -->
                <div class="col-12">
                    <div class="card mqtt-card">
                        <div class="card-header card-header-info">
                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>在线设备列表</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="40">
                                                <input type="checkbox" class="form-check-input" id="selectAllConnections" onchange="toggleAllConnectionSelection()">
                                            </th>
                                            <th>连接状态</th>
                                            <th>ClientID</th>
                                            <th>设备ID</th>
                                            <th>设备名称</th>
                                            <th>Token</th>
                                            <th>连接时间</th>
                                            <th>IP地址</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="connectionsTableBody">
                                        <tr>
                                            <td colspan="9" class="text-center text-muted py-4">
                                                <div class="loading-spinner" id="connectionsLoadingSpinner">
                                                    <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                                                </div>
                                                <div class="error-message" id="connectionsErrorMessage">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>加载失败，请重试
                                                </div>
                                                <div id="connectionsEmptyMessage">
                                                    <i class="fas fa-info-circle me-2"></i>暂无连接数据
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer">
                            <!-- 分页导航 -->
                            <nav aria-label="连接分页">
                                <ul class="pagination justify-content-center mb-0" id="connectionsPagination">
                                    <!-- 动态生成分页 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages Pane - 消息管理 -->
        <div class="tab-pane fade" id="messages-pane" role="tabpanel">
            <div class="row mt-3">
                <!-- 左侧：消息发布 -->
                <div class="col-lg-6">
                    <!-- 消息发布表单 -->
                    <div class="card mqtt-card mb-4">
                        <div class="card-header card-header-mqtt">
                            <h5 class="mb-0"><i class="fas fa-paper-plane me-2"></i>消息发布</h5>
                        </div>
                        <div class="card-body">
                            <form id="messagePublishForm">
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label class="form-label">Topic <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="publishTopic" placeholder="例如: device/sensor/data" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">QoS</label>
                                        <select class="form-select" id="publishQos">
                                            <option value="0">0 - 最多一次</option>
                                            <option value="1" selected>1 - 至少一次</option>
                                            <option value="2">2 - 恰好一次</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="publishRetain">
                                            <label class="form-check-label" for="publishRetain">
                                                Retain 保留消息
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">消息格式</label>
                                        <select class="form-select" id="publishFormat">
                                            <option value="text">纯文本</option>
                                            <option value="json" selected>JSON</option>
                                            <option value="xml">XML</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">消息内容 <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="publishPayload" rows="6" placeholder="输入消息内容..." required></textarea>
                                    <div class="form-text">
                                        <small>支持JSON格式验证和格式化</small>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="button" class="btn btn-outline-secondary me-2" onclick="formatPayload()">
                                            <i class="fas fa-code"></i> 格式化
                                        </button>
                                        <button type="button" class="btn btn-outline-info me-2" onclick="validatePayload()">
                                            <i class="fas fa-check-circle"></i> 验证
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" onclick="clearPublishForm()">
                                            <i class="fas fa-eraser"></i> 清空
                                        </button>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-1"></i>发布消息
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 常用Topic快捷选择 -->
                    <div class="card mqtt-card">
                        <div class="card-header card-header-info">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-bookmark me-2"></i>常用Topic</h6>
                                <button class="btn btn-light btn-sm" onclick="addCustomTopic()">
                                    <i class="fas fa-plus"></i> 添加
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-wrap gap-2" id="commonTopicsList">
                                <span class="badge bg-secondary topic-badge" onclick="selectTopic('device/+/status')">
                                    device/+/status
                                </span>
                                <span class="badge bg-secondary topic-badge" onclick="selectTopic('sensor/+/data')">
                                    sensor/+/data
                                </span>
                                <span class="badge bg-secondary topic-badge" onclick="selectTopic('system/alert')">
                                    system/alert
                                </span>
                                <span class="badge bg-secondary topic-badge" onclick="selectTopic('test/message')">
                                    test/message
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：消息订阅和历史 -->
                <div class="col-lg-6">
                    <!-- 消息订阅 -->
                    <div class="card mqtt-card mb-4">
                        <div class="card-header card-header-warning">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-rss me-2"></i>消息订阅</h5>
                                <div>
                                    <button class="btn btn-light btn-sm me-2" onclick="clearSubscriptions()">
                                        <i class="fas fa-trash"></i> 清空
                                    </button>
                                    <span class="badge bg-light text-dark" id="subscriptionStatus">未订阅</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <label class="form-label">订阅Topic</label>
                                    <input type="text" class="form-control" id="subscribeTopic" placeholder="例如: device/+/data">
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button class="btn btn-success w-100" onclick="toggleSubscription()">
                                        <i class="fas fa-play" id="subscribeIcon"></i>
                                        <span id="subscribeText">开始订阅</span>
                                    </button>
                                </div>
                            </div>

                            <!-- 实时消息显示 -->
                            <div class="message-container" id="realtimeMessages">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-info-circle me-2"></i>暂无实时消息
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 消息统计 -->
                    <div class="card mqtt-card">
                        <div class="card-header card-header-custom">
                            <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>消息统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="metric-card">
                                        <div class="metric-title">已发布</div>
                                        <div class="metric-value text-primary" id="publishedCount">0</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="metric-card">
                                        <div class="metric-title">已接收</div>
                                        <div class="metric-value text-success" id="receivedCount">0</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="metric-card">
                                        <div class="metric-title">订阅数</div>
                                        <div class="metric-value text-info" id="subscriptionCount">0</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 消息历史 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card mqtt-card">
                        <div class="card-header card-header-info">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-history me-2"></i>消息历史</h5>
                                <div>
                                    <button class="btn btn-light btn-sm me-2" onclick="exportMessages()">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                    <button class="btn btn-warning btn-sm" onclick="clearMessageHistory()">
                                        <i class="fas fa-trash"></i> 清空历史
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 消息过滤 -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label class="form-label">Topic过滤</label>
                                    <input type="text" class="form-control" id="messageTopicFilter" placeholder="过滤Topic">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">消息类型</label>
                                    <select class="form-select" id="messageTypeFilter">
                                        <option value="">所有类型</option>
                                        <option value="published">已发布</option>
                                        <option value="received">已接收</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">时间范围</label>
                                    <select class="form-select" id="messageTimeFilter">
                                        <option value="">所有时间</option>
                                        <option value="1h">最近1小时</option>
                                        <option value="24h">最近24小时</option>
                                        <option value="7d">最近7天</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">每页显示</label>
                                    <select class="form-select" id="messagePageSize">
                                        <option value="20" selected>20条</option>
                                        <option value="50">50条</option>
                                        <option value="100">100条</option>
                                    </select>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button class="btn btn-primary me-2" onclick="filterMessages()">
                                        <i class="fas fa-search"></i> 过滤
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="resetMessageFilters()">
                                        <i class="fas fa-undo"></i> 重置
                                    </button>
                                </div>
                            </div>

                            <!-- 消息列表 -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="80">类型</th>
                                            <th width="200">Topic</th>
                                            <th>消息内容</th>
                                            <th width="100">QoS</th>
                                            <th width="150">时间</th>
                                            <th width="80">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="messageHistoryTableBody">
                                        <tr>
                                            <td colspan="6" class="text-center text-muted py-4">
                                                <i class="fas fa-info-circle me-2"></i>暂无消息历史
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <nav aria-label="消息分页">
                                <ul class="pagination justify-content-center" id="messageHistoryPagination">
                                    <!-- 动态生成分页 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tokens Pane - Token管理 -->
        <div class="tab-pane fade" id="tokens-pane" role="tabpanel">
            <div class="row mt-3">
                <!-- Token统计卡片 -->
                <div class="col-12 mb-4">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number" id="tokensTotalCount">0</div>
                                <div class="stats-label">总Token数</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number status-online" id="tokensValidCount">0</div>
                                <div class="stats-label">有效Token</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number status-online" id="tokensOnlineCount">0</div>
                                <div class="stats-label">在线Token</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number" id="tokensLastUpdate">--:--:--</div>
                                <div class="stats-label">最后更新</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Token生成 -->
                <div class="col-lg-4 mb-4">
                    <div class="card mqtt-card">
                        <div class="card-header card-header-mqtt">
                            <h5 class="mb-0"><i class="fas fa-plus me-2"></i>生成Token</h5>
                        </div>
                        <div class="card-body">
                            <form id="tokenGenerateForm">
                                <div class="mb-3">
                                    <label class="form-label">设备ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="tokenDeviceId" placeholder="输入设备ID" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">设备类型</label>
                                    <select class="form-select" id="tokenDeviceType">
                                        <option value="sensor">传感器</option>
                                        <option value="gateway">网关</option>
                                        <option value="controller">控制器</option>
                                        <option value="mobile">移动设备</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">设备名称</label>
                                    <input type="text" class="form-control" id="tokenDeviceName" placeholder="设备名称（可选）">
                                </div>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-key me-1"></i>生成Token
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearTokenForm()">
                                        <i class="fas fa-eraser me-1"></i>清空表单
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 批量生成 -->
                    <div class="card mqtt-card">
                        <div class="card-header card-header-warning">
                            <h6 class="mb-0"><i class="fas fa-layer-group me-2"></i>批量生成</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">设备ID前缀</label>
                                <input type="text" class="form-control" id="batchDevicePrefix" placeholder="例如: sensor_">
                            </div>
                            <div class="row mb-3">
                                <div class="col-6">
                                    <label class="form-label">起始编号</label>
                                    <input type="number" class="form-control" id="batchStartNumber" value="1" min="1">
                                </div>
                                <div class="col-6">
                                    <label class="form-label">生成数量</label>
                                    <input type="number" class="form-control" id="batchCount" value="10" min="1" max="100">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">设备类型</label>
                                <select class="form-select" id="batchDeviceType">
                                    <option value="sensor">传感器</option>
                                    <option value="gateway">网关</option>
                                    <option value="controller">控制器</option>
                                    <option value="mobile">移动设备</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                            <button class="btn btn-warning w-100" onclick="showBatchGenerateModal()">
                                <i class="fas fa-layer-group me-1"></i>批量生成
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Token管理 -->
                <div class="col-lg-8">
                    <div class="card mqtt-card">
                        <div class="card-header card-header-info">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Token管理</h5>
                                <div>
                                    <button class="btn btn-light btn-sm me-2" onclick="refreshTokens()">
                                        <i class="fas fa-sync-alt" id="tokensRefreshIcon"></i> 刷新
                                    </button>
                                    <button class="btn btn-warning btn-sm me-2" onclick="cleanupInvalidTokens()">
                                        <i class="fas fa-broom"></i> 清理无效
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="showDeleteAllTokensModal()">
                                        <i class="fas fa-trash"></i> 删除所有
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 搜索和过滤 -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label class="form-label">搜索Token</label>
                                    <input type="text" class="form-control" id="tokenSearchInput" placeholder="设备ID或Token">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Token状态</label>
                                    <select class="form-select" id="tokenStatusFilter">
                                        <option value="">所有状态</option>
                                        <option value="valid">有效</option>
                                        <option value="invalid">无效</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">在线状态</label>
                                    <select class="form-select" id="tokenOnlineFilter">
                                        <option value="">所有状态</option>
                                        <option value="online">在线</option>
                                        <option value="offline">离线</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">设备类型</label>
                                    <select class="form-select" id="tokenTypeFilter">
                                        <option value="">所有类型</option>
                                        <option value="sensor">传感器</option>
                                        <option value="gateway">网关</option>
                                        <option value="controller">控制器</option>
                                        <option value="mobile">移动设备</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button class="btn btn-primary me-2" onclick="searchTokens()">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="resetTokenFilters()">
                                        <i class="fas fa-undo"></i> 重置
                                    </button>
                                </div>
                            </div>

                            <!-- 批量操作 -->
                            <div class="row mb-3" id="tokenBatchOperationsRow" style="display: none;">
                                <div class="col-12">
                                    <div class="alert alert-info d-flex justify-content-between align-items-center">
                                        <span>
                                            <i class="fas fa-info-circle me-2"></i>
                                            已选择 <strong id="selectedTokensCount">0</strong> 个Token
                                        </span>
                                        <div>
                                            <button class="btn btn-warning btn-sm me-2" onclick="batchValidateTokens()">
                                                <i class="fas fa-check-circle"></i> 批量验证
                                            </button>
                                            <button class="btn btn-danger btn-sm me-2" onclick="batchDeleteTokens()">
                                                <i class="fas fa-trash"></i> 批量删除
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" onclick="clearTokenSelection()">
                                                <i class="fas fa-times"></i> 取消选择
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Token列表 -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="40">
                                                <input type="checkbox" class="form-check-input" id="selectAllTokens" onchange="toggleAllTokenSelection()">
                                            </th>
                                            <th>设备ID</th>
                                            <th>设备名称</th>
                                            <th>Token</th>
                                            <th>设备类型</th>
                                            <th>Token状态</th>
                                            <th>在线状态</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tokensTableBody">
                                        <tr>
                                            <td colspan="9" class="text-center text-muted py-4">
                                                <div class="loading-spinner" id="tokensLoadingSpinner">
                                                    <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                                                </div>
                                                <div class="error-message" id="tokensErrorMessage">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>加载失败，请重试
                                                </div>
                                                <div id="tokensEmptyMessage">
                                                    <i class="fas fa-info-circle me-2"></i>暂无Token数据
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <nav aria-label="Token分页">
                                <ul class="pagination justify-content-center" id="tokensPagination">
                                    <!-- 动态生成分页 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tools Pane - 系统工具 -->
        <div class="tab-pane fade" id="tools-pane" role="tabpanel">
            <div class="container-fluid">
                <!-- 系统工具标题 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <h5 class="mb-0">
                                    <i class="fas fa-tools me-2"></i>系统工具
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-0">提供Redis管理、连接清理、数据同步、系统诊断等工具功能</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统状态卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <div class="connection-indicator connection-online me-2"></div>
                                    <h6 class="mb-0">Redis状态</h6>
                                </div>
                                <h4 class="text-success" id="redis-status">检查中...</h4>
                                <small class="text-muted" id="redis-info">连接信息</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <div class="connection-indicator connection-online me-2"></div>
                                    <h6 class="mb-0">MQTT服务</h6>
                                </div>
                                <h4 class="text-success" id="mqtt-service-status">运行中</h4>
                                <small class="text-muted" id="mqtt-service-info">端口: 1883</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <div class="connection-indicator connection-online me-2"></div>
                                    <h6 class="mb-0">WebSocket</h6>
                                </div>
                                <h4 class="text-success" id="websocket-service-status">活跃</h4>
                                <small class="text-muted" id="websocket-service-info">连接数: 0</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <div class="connection-indicator connection-online me-2"></div>
                                    <h6 class="mb-0">系统健康</h6>
                                </div>
                                <h4 class="text-success" id="system-health-status">良好</h4>
                                <small class="text-muted" id="system-health-info">运行时间</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统工具操作区 -->
                <div class="row">
                    <!-- Redis管理工具 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header card-header-info">
                                <h6 class="mb-0">
                                    <i class="fas fa-database me-2"></i>Redis管理工具
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Redis健康状态</label>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-success me-2" id="redis-health-badge">健康</span>
                                        <button class="btn btn-sm btn-outline-primary" onclick="checkRedisHealth()">
                                            <i class="fas fa-sync-alt me-1"></i>检查
                                        </button>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Redis信息</label>
                                    <div class="small text-muted" id="redis-details">
                                        <div>状态: <span id="redis-ping">-</span></div>
                                        <div>连接数: <span id="redis-connections">-</span></div>
                                        <div>内存使用: <span id="redis-memory">-</span></div>
                                        <div>运行时间: <span id="redis-uptime">-</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 连接管理工具 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header card-header-warning">
                                <h6 class="mb-0">
                                    <i class="fas fa-network-wired me-2"></i>连接管理工具
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">清理无效连接</label>
                                    <p class="small text-muted">清理已断开但仍在缓存中的MQTT连接</p>
                                    <button class="btn btn-warning btn-sm" onclick="clearMqttConnections()">
                                        <i class="fas fa-broom me-1"></i>清理连接
                                    </button>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">连接统计</label>
                                    <div class="small text-muted">
                                        <div>活跃连接: <span id="active-connections">-</span></div>
                                        <div>总连接数: <span id="total-connections">-</span></div>
                                        <div>最后清理: <span id="last-cleanup">-</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据同步工具 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header card-header-mqtt">
                                <h6 class="mb-0">
                                    <i class="fas fa-sync me-2"></i>数据同步工具
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">数据一致性检查</label>
                                    <p class="small text-muted">检查数据库、Redis、MQTT连接的数据一致性</p>
                                    <button class="btn btn-success btn-sm" onclick="performSyncCheck()">
                                        <i class="fas fa-search me-1"></i>检查同步
                                    </button>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">同步状态</label>
                                    <div class="small text-muted" id="sync-status">
                                        <div>数据库设备: <span id="db-devices">-</span></div>
                                        <div>Redis令牌: <span id="redis-tokens">-</span></div>
                                        <div>MQTT连接: <span id="mqtt-connections-count">-</span></div>
                                        <div>不一致项: <span id="inconsistencies" class="text-warning">-</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统管理工具 -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <h6 class="mb-0">
                                    <i class="fas fa-cogs me-2"></i>系统管理工具
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">系统操作</label>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-info btn-sm" onclick="getSystemDiagnostics()">
                                            <i class="fas fa-stethoscope me-1"></i>系统诊断
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" onclick="restartSystem()">
                                            <i class="fas fa-redo me-1"></i>重启系统
                                        </button>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">系统信息</label>
                                    <div class="small text-muted" id="system-info">
                                        <div>运行时间: <span id="system-uptime">-</span></div>
                                        <div>Java版本: <span id="java-version">-</span></div>
                                        <div>操作系统: <span id="os-name">-</span></div>
                                        <div>处理器: <span id="processors">-</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统诊断结果 -->
                <div class="row" id="diagnostics-section" style="display: none;">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-info">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>系统诊断结果
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>JVM信息</h6>
                                        <div class="small" id="jvm-diagnostics">
                                            <!-- JVM诊断信息将在这里显示 -->
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>MQTT信息</h6>
                                        <div class="small" id="mqtt-diagnostics">
                                            <!-- MQTT诊断信息将在这里显示 -->
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>系统信息</h6>
                                        <div class="small" id="system-diagnostics">
                                            <!-- 系统诊断信息将在这里显示 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Config Pane - 配置管理 -->
        <div class="tab-pane fade" id="config-pane" role="tabpanel">
            <div class="container-fluid">
                <!-- 配置管理标题 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <h5 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>配置管理
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-0">查看和管理MQTT服务器配置、系统参数等设置信息</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配置刷新按钮 -->
                <div class="row mb-3">
                    <div class="col-12">
                        <button class="btn btn-primary" onclick="loadMqttConfig()">
                            <i class="fas fa-sync-alt me-1"></i>刷新配置
                        </button>
                        <span class="text-muted ms-3">最后更新: <span id="config-last-update">-</span></span>
                    </div>
                </div>

                <!-- MQTT服务器配置 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-mqtt">
                                <h6 class="mb-0">
                                    <i class="fas fa-server me-2"></i>MQTT服务器配置
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-sm">
                                            <tbody id="mqtt-basic-config">
                                                <tr>
                                                    <td><strong>服务状态</strong></td>
                                                    <td><span id="config-enabled" class="badge bg-success">启用</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>服务端口</strong></td>
                                                    <td><span id="config-port">1883</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>服务名称</strong></td>
                                                    <td><span id="config-name">Mica-Mqtt-Server</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>心跳超时</strong></td>
                                                    <td><span id="config-heartbeat">120000ms</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>读缓冲区大小</strong></td>
                                                    <td><span id="config-read-buffer">8KB</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>最大消息大小</strong></td>
                                                    <td><span id="config-max-message">10MB</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-sm">
                                            <tbody id="mqtt-feature-config">
                                                <tr>
                                                    <td><strong>认证启用</strong></td>
                                                    <td><span id="config-auth" class="badge bg-secondary">禁用</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>调试模式</strong></td>
                                                    <td><span id="config-debug" class="badge bg-info">启用</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>统计启用</strong></td>
                                                    <td><span id="config-stat" class="badge bg-success">启用</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Web端口</strong></td>
                                                    <td><span id="config-web-port">8083</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>WebSocket</strong></td>
                                                    <td><span id="config-websocket" class="badge bg-success">启用</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>HTTP支持</strong></td>
                                                    <td><span id="config-http" class="badge bg-secondary">禁用</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统配置信息 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header card-header-info">
                                <h6 class="mb-0">
                                    <i class="fas fa-microchip me-2"></i>JVM配置
                                </h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tbody id="jvm-config">
                                        <tr>
                                            <td><strong>Java版本</strong></td>
                                            <td><span id="jvm-version">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>JVM供应商</strong></td>
                                            <td><span id="jvm-vendor">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>最大堆内存</strong></td>
                                            <td><span id="jvm-max-memory">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>当前堆内存</strong></td>
                                            <td><span id="jvm-total-memory">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>可用内存</strong></td>
                                            <td><span id="jvm-free-memory">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>处理器数量</strong></td>
                                            <td><span id="jvm-processors">-</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header card-header-warning">
                                <h6 class="mb-0">
                                    <i class="fas fa-desktop me-2"></i>系统配置
                                </h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tbody id="system-config">
                                        <tr>
                                            <td><strong>操作系统</strong></td>
                                            <td><span id="system-os">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>系统架构</strong></td>
                                            <td><span id="system-arch">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>系统版本</strong></td>
                                            <td><span id="system-version">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>用户目录</strong></td>
                                            <td><span id="system-user-home">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>工作目录</strong></td>
                                            <td><span id="system-user-dir">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>临时目录</strong></td>
                                            <td><span id="system-temp-dir">-</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 网络配置信息 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-mqtt">
                                <h6 class="mb-0">
                                    <i class="fas fa-network-wired me-2"></i>网络配置
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>MQTT网络</h6>
                                        <table class="table table-sm">
                                            <tbody>
                                                <tr>
                                                    <td><strong>MQTT端口</strong></td>
                                                    <td><span id="network-mqtt-port">1883</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>SSL端口</strong></td>
                                                    <td><span id="network-ssl-port">8883</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>WebSocket端口</strong></td>
                                                    <td><span id="network-ws-port">8083</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>连接限制</h6>
                                        <table class="table table-sm">
                                            <tbody>
                                                <tr>
                                                    <td><strong>最大连接数</strong></td>
                                                    <td><span id="network-max-connections">1000</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>连接超时</strong></td>
                                                    <td><span id="network-timeout">30s</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>保活时间</strong></td>
                                                    <td><span id="network-keepalive">60s</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>缓冲区配置</h6>
                                        <table class="table table-sm">
                                            <tbody>
                                                <tr>
                                                    <td><strong>接收缓冲区</strong></td>
                                                    <td><span id="network-recv-buffer">8KB</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>发送缓冲区</strong></td>
                                                    <td><span id="network-send-buffer">8KB</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>消息队列</strong></td>
                                                    <td><span id="network-queue-size">1000</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配置导出功能 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <h6 class="mb-0">
                                    <i class="fas fa-download me-2"></i>配置导出
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">导出当前系统配置信息为JSON格式文件</p>
                                <button class="btn btn-success" onclick="exportConfig()">
                                    <i class="fas fa-file-export me-1"></i>导出配置
                                </button>
                                <button class="btn btn-info ms-2" onclick="copyConfigToClipboard()">
                                    <i class="fas fa-copy me-1"></i>复制到剪贴板
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs Pane - 日志监控 -->
        <div class="tab-pane fade" id="logs-pane" role="tabpanel">
            <div class="container-fluid">
                <!-- 日志监控标题 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-alt me-2"></i>日志监控
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-0">实时监控系统日志、错误统计、日志分析等功能</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <i class="fas fa-list text-primary me-2"></i>
                                    <h6 class="mb-0">总日志数</h6>
                                </div>
                                <h4 class="text-primary" id="total-logs">0</h4>
                                <small class="text-muted">累计日志条数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                    <h6 class="mb-0">错误日志</h6>
                                </div>
                                <h4 class="text-danger" id="error-logs">0</h4>
                                <small class="text-muted">错误级别日志</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <i class="fas fa-exclamation text-warning me-2"></i>
                                    <h6 class="mb-0">警告日志</h6>
                                </div>
                                <h4 class="text-warning" id="warn-logs">0</h4>
                                <small class="text-muted">警告级别日志</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <i class="fas fa-percentage text-info me-2"></i>
                                    <h6 class="mb-0">错误率</h6>
                                </div>
                                <h4 class="text-info" id="error-rate">0%</h4>
                                <small class="text-muted">当前小时错误率</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志趋势图表 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-info">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>日志趋势分析
                                </h6>
                            </div>
                            <div class="card-body">
                                <canvas id="logTrendChart" width="400" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志过滤和控制 -->
                <div class="row mb-3">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header card-header-mqtt">
                                <h6 class="mb-0">
                                    <i class="fas fa-filter me-2"></i>日志过滤
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">日志级别</label>
                                        <select class="form-select" id="logLevelFilter" onchange="filterLogs()">
                                            <option value="ALL">全部级别</option>
                                            <option value="ERROR">错误 (ERROR)</option>
                                            <option value="WARN">警告 (WARN)</option>
                                            <option value="INFO">信息 (INFO)</option>
                                            <option value="DEBUG">调试 (DEBUG)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">关键词搜索</label>
                                        <input type="text" class="form-control" id="logKeywordFilter"
                                               placeholder="输入关键词..." onkeyup="filterLogs()">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">显示数量</label>
                                        <select class="form-select" id="logLimitFilter" onchange="filterLogs()">
                                            <option value="50">最近50条</option>
                                            <option value="100" selected>最近100条</option>
                                            <option value="200">最近200条</option>
                                            <option value="500">最近500条</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header card-header-warning">
                                <h6 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>日志控制
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary btn-sm" onclick="refreshLogs()">
                                        <i class="fas fa-sync-alt me-1"></i>刷新日志
                                    </button>
                                    <button class="btn btn-success btn-sm" onclick="exportLogs()">
                                        <i class="fas fa-download me-1"></i>导出日志
                                    </button>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="autoRefreshLogs" checked>
                                        <label class="form-check-label" for="autoRefreshLogs">
                                            自动刷新 (10s)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时日志显示 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <h6 class="mb-0">
                                    <i class="fas fa-terminal me-2"></i>实时日志
                                    <span class="badge bg-light text-dark ms-2" id="logCount">0 条</span>
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <!-- 日志加载状态 -->
                                <div id="logsLoadingSpinner" class="text-center p-4" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2 text-muted">正在加载日志...</p>
                                </div>

                                <!-- 日志错误信息 -->
                                <div id="logsErrorMessage" class="alert alert-danger m-3" style="display: none;">
                                    <!-- 错误信息将在这里显示 -->
                                </div>

                                <!-- 日志内容 -->
                                <div id="logContainer" class="log-container" style="height: 400px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 12px; background-color: #1e1e1e; color: #d4d4d4;">
                                    <!-- 日志条目将在这里显示 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Metrics Pane - 性能监控 -->
        <div class="tab-pane fade" id="metrics-pane" role="tabpanel">
            <div class="container-fluid">
                <!-- 性能监控标题 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <h5 class="mb-0">
                                    <i class="fas fa-tachometer-alt me-2"></i>性能监控
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-0">监控系统性能指标、JVM状态、MQTT性能等关键指标</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 关键性能指标卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <i class="fas fa-microchip text-primary me-2"></i>
                                    <h6 class="mb-0">CPU使用率</h6>
                                </div>
                                <h4 class="text-primary" id="cpu-usage">0%</h4>
                                <small class="text-muted">当前CPU使用率</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <i class="fas fa-memory text-success me-2"></i>
                                    <h6 class="mb-0">内存使用率</h6>
                                </div>
                                <h4 class="text-success" id="memory-usage">0%</h4>
                                <small class="text-muted">JVM堆内存使用率</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <i class="fas fa-network-wired text-info me-2"></i>
                                    <h6 class="mb-0">MQTT连接</h6>
                                </div>
                                <h4 class="text-info" id="mqtt-connections">0</h4>
                                <small class="text-muted">活跃MQTT连接数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-center mb-2">
                                    <i class="fas fa-exchange-alt text-warning me-2"></i>
                                    <h6 class="mb-0">消息速率</h6>
                                </div>
                                <h4 class="text-warning" id="message-rate">0/s</h4>
                                <small class="text-muted">每秒消息数</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 性能图表 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header card-header-info">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-area me-2"></i>CPU & 内存使用率
                                </h6>
                            </div>
                            <div class="card-body">
                                <canvas id="cpuMemoryChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header card-header-mqtt">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>MQTT性能指标
                                </h6>
                            </div>
                            <div class="card-body">
                                <canvas id="mqttPerformanceChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细性能指标 -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header card-header-info">
                                <h6 class="mb-0">
                                    <i class="fas fa-server me-2"></i>JVM性能指标
                                </h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tbody>
                                        <tr>
                                            <td><strong>堆内存使用</strong></td>
                                            <td><span id="jvm-heap-used">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>堆内存总量</strong></td>
                                            <td><span id="jvm-heap-total">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>堆内存最大</strong></td>
                                            <td><span id="jvm-heap-max">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>活跃线程</strong></td>
                                            <td><span id="jvm-threads">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>处理器数量</strong></td>
                                            <td><span id="jvm-processors">-</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header card-header-mqtt">
                                <h6 class="mb-0">
                                    <i class="fas fa-broadcast-tower me-2"></i>MQTT性能指标
                                </h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tbody>
                                        <tr>
                                            <td><strong>活跃连接</strong></td>
                                            <td><span id="mqtt-active-connections">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>总连接数</strong></td>
                                            <td><span id="mqtt-total-connections">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>消息/秒</strong></td>
                                            <td><span id="mqtt-messages-per-sec">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>字节/秒</strong></td>
                                            <td><span id="mqtt-bytes-per-sec">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>主题数量</strong></td>
                                            <td><span id="mqtt-topics-count">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>订阅数量</strong></td>
                                            <td><span id="mqtt-subscriptions-count">-</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header card-header-warning">
                                <h6 class="mb-0">
                                    <i class="fas fa-desktop me-2"></i>系统性能指标
                                </h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tbody>
                                        <tr>
                                            <td><strong>CPU使用率</strong></td>
                                            <td><span id="system-cpu-usage">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>负载平均值</strong></td>
                                            <td><span id="system-load-average">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>磁盘使用率</strong></td>
                                            <td><span id="system-disk-usage">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>网络入流量</strong></td>
                                            <td><span id="system-network-in">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>网络出流量</strong></td>
                                            <td><span id="system-network-out">-</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>运行时间</strong></td>
                                            <td><span id="system-uptime">-</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 性能控制和设置 -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header card-header-custom">
                                <h6 class="mb-0">
                                    <i class="fas fa-sliders-h me-2"></i>性能阈值设置
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label class="form-label">CPU告警阈值</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="cpuThreshold" value="80" min="1" max="100">
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">内存告警阈值</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="memoryThreshold" value="85" min="1" max="100">
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">连接数告警阈值</label>
                                        <input type="number" class="form-control" id="connectionThreshold" value="1000" min="1">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">错误率告警阈值</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="errorRateThreshold" value="5" min="0" max="100" step="0.1">
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-primary" onclick="savePerformanceThresholds()">
                                        <i class="fas fa-save me-1"></i>保存设置
                                    </button>
                                    <button class="btn btn-outline-secondary ms-2" onclick="resetPerformanceThresholds()">
                                        <i class="fas fa-undo me-1"></i>重置默认
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header card-header-info">
                                <h6 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>监控控制
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary btn-sm" onclick="refreshPerformanceMetrics()">
                                        <i class="fas fa-sync-alt me-1"></i>刷新指标
                                    </button>
                                    <button class="btn btn-success btn-sm" onclick="exportPerformanceData()">
                                        <i class="fas fa-download me-1"></i>导出数据
                                    </button>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="autoRefreshMetrics" checked>
                                        <label class="form-check-label" for="autoRefreshMetrics">
                                            自动刷新 (5s)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enableAlerts" checked>
                                        <label class="form-check-label" for="enableAlerts">
                                            启用告警通知
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- 连接详情模态框 -->
<div class="modal fade" id="connectionDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>连接详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">ClientID</div>
                            <div class="config-value" id="detailClientId">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">设备ID</div>
                            <div class="config-value" id="detailDeviceId">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">设备名称</div>
                            <div class="config-value" id="detailDeviceName">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">连接状态</div>
                            <div class="config-value" id="detailConnectionStatus">-</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="config-item">
                            <div class="config-label">Token</div>
                            <div class="config-value" id="detailToken">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">连接时间</div>
                            <div class="config-value" id="detailConnectTime">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">IP地址</div>
                            <div class="config-value" id="detailIpAddress">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">设备类型</div>
                            <div class="config-value" id="detailDeviceType">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">协议版本</div>
                            <div class="config-value" id="detailProtocolVersion">MQTT 3.1.1</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-danger" onclick="disconnectFromDetails()">
                    <i class="fas fa-power-off me-1"></i>断开连接
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 断开连接确认模态框 -->
<div class="modal fade" id="disconnectConnectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i>断开连接确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要断开以下设备的MQTT连接吗？</p>
                <div class="alert alert-warning">
                    <div id="disconnectConnectionInfo">
                        <!-- 动态填充设备信息 -->
                    </div>
                </div>
                <div class="mb-3">
                    <label for="disconnectReason" class="form-label">断开原因</label>
                    <input type="text" class="form-control" id="disconnectReason" value="管理员主动断开" placeholder="请输入断开原因">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDisconnectConnection()">
                    <i class="fas fa-power-off me-1"></i>确认断开
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量断开确认模态框 -->
<div class="modal fade" id="batchDisconnectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i>批量断开确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要断开选中的 <strong id="batchDisconnectCount">0</strong> 个设备连接吗？</p>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    此操作将同时断开多个设备连接，请谨慎操作！
                </div>
                <div class="mb-3">
                    <label for="batchDisconnectReason" class="form-label">断开原因</label>
                    <input type="text" class="form-control" id="batchDisconnectReason" value="管理员批量断开" placeholder="请输入断开原因">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmBatchDisconnect()">
                    <i class="fas fa-power-off me-1"></i>确认批量断开
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 消息详情模态框 -->
<div class="modal fade" id="messageDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-envelope me-2"></i>消息详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">消息类型</div>
                            <div class="config-value" id="detailMessageType">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">QoS级别</div>
                            <div class="config-value" id="detailMessageQos">-</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="config-item">
                            <div class="config-label">Topic</div>
                            <div class="config-value" id="detailMessageTopic">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">时间戳</div>
                            <div class="config-value" id="detailMessageTime">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">消息大小</div>
                            <div class="config-value" id="detailMessageSize">-</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="config-item">
                            <div class="config-label">消息内容</div>
                            <div class="config-value">
                                <pre id="detailMessagePayload" style="background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto;">-</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="copyMessageContent()">
                    <i class="fas fa-copy me-1"></i>复制内容
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 添加自定义Topic模态框 -->
<div class="modal fade" id="addTopicModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>添加常用Topic</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="customTopicInput" class="form-label">Topic名称</label>
                    <input type="text" class="form-control" id="customTopicInput" placeholder="例如: device/sensor/temperature">
                </div>
                <div class="mb-3">
                    <label for="customTopicDescription" class="form-label">描述（可选）</label>
                    <input type="text" class="form-control" id="customTopicDescription" placeholder="Topic用途描述">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmAddTopic()">
                    <i class="fas fa-plus me-1"></i>添加
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Token详情模态框 -->
<div class="modal fade" id="tokenDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-key me-2"></i>Token详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">设备ID</div>
                            <div class="config-value" id="detailTokenDeviceId">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">设备名称</div>
                            <div class="config-value" id="detailTokenDeviceName">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">设备类型</div>
                            <div class="config-value" id="detailTokenDeviceType">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">Token状态</div>
                            <div class="config-value" id="detailTokenStatus">-</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="config-item">
                            <div class="config-label">Token值</div>
                            <div class="config-value" id="detailTokenValue">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">创建时间</div>
                            <div class="config-value" id="detailTokenCreateTime">-</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="config-item">
                            <div class="config-label">在线状态</div>
                            <div class="config-value" id="detailTokenOnlineStatus">-</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-info" onclick="copyTokenValue()">
                    <i class="fas fa-copy me-1"></i>复制Token
                </button>
                <button type="button" class="btn btn-warning" onclick="validateTokenFromDetails()">
                    <i class="fas fa-check-circle me-1"></i>验证Token
                </button>
                <button type="button" class="btn btn-danger" onclick="deleteTokenFromDetails()">
                    <i class="fas fa-trash me-1"></i>删除Token
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量生成确认模态框 -->
<div class="modal fade" id="batchGenerateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-layer-group me-2"></i>批量生成Token确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要批量生成Token吗？</p>
                <div class="alert alert-info">
                    <div id="batchGenerateInfo">
                        <!-- 动态填充批量生成信息 -->
                    </div>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    批量生成可能需要一些时间，请耐心等待。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="confirmBatchGenerate()">
                    <i class="fas fa-layer-group me-1"></i>确认生成
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除Token确认模态框 -->
<div class="modal fade" id="deleteTokenModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i>删除Token确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除以下Token吗？</p>
                <div class="alert alert-warning">
                    <div id="deleteTokenInfo">
                        <!-- 动态填充Token信息 -->
                    </div>
                </div>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    删除Token后，使用该Token的设备将无法连接到MQTT服务器！
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteToken()">
                    <i class="fas fa-trash me-1"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除Token确认模态框 -->
<div class="modal fade" id="batchDeleteTokenModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i>批量删除Token确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除选中的 <strong id="batchDeleteTokenCount">0</strong> 个Token吗？</p>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    此操作将同时删除多个Token，请谨慎操作！删除后相关设备将无法连接！
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmBatchDeleteTokens()">
                    <i class="fas fa-trash me-1"></i>确认批量删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 5 JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
// 全局变量
let autoRefreshInterval = null;
let autoRefreshEnabled = false;
let websocket = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;

// 图表相关变量
let connectionChart = null;
let messageChart = null;
let connectionData = [];
let messageData = [];
const maxDataPoints = 20; // 最多保留20个数据点

// 监控数据
let monitoringData = {
    connections: 0,
    messages: 0,
    tokens: 0,
    wsConnections: 0,
    messageRate: 0,
    networkTraffic: 0,
    cpuUsage: 0,
    memoryUsage: 0
};

// 连接管理数据
let connectionsData = {
    list: [],
    filteredList: [],
    currentPage: 1,
    pageSize: 20,
    totalPages: 0,
    selectedConnections: new Set(),
    currentConnectionForDisconnect: null
};

// 消息管理数据
let messagesData = {
    history: [],
    filteredHistory: [],
    currentPage: 1,
    pageSize: 20,
    totalPages: 0,
    isSubscribed: false,
    subscribedTopic: '',
    publishedCount: 0,
    receivedCount: 0,
    subscriptionCount: 0,
    commonTopics: ['device/+/status', 'sensor/+/data', 'system/alert', 'test/message'],
    currentMessageForDetails: null
};

// Token管理数据
let tokensData = {
    list: [],
    filteredList: [],
    currentPage: 1,
    pageSize: 20,
    totalPages: 0,
    selectedTokens: new Set(),
    currentTokenForDetails: null,
    currentTokenForDelete: null
};

// ==================== 全局配置和状态管理 ====================

// API基础URL
const API_BASE_URL = 'http://dev.inksyun.com:31080/eam';
const MQTT_API_BASE = `${API_BASE_URL}/api/mqtt-management`;
const MQTT_MANAGEMENT_API = `${API_BASE_URL}/api/mqtt-management`;
const DEVICE_TOKEN_API = `${API_BASE_URL}/api/device-tokens`;
const AUTHORIZATION = 'b8';

// 全局状态管理
let globalState = {
    isInitialized: false,
    autoRefreshEnabled: true,
    refreshIntervals: new Map(),
    activeRequests: new Set(),
    errorCount: 0,
    lastErrorTime: null,
    retryCount: new Map()
};

// 全局配置
const CONFIG = {
    AUTO_REFRESH_INTERVAL: 30000, // 30秒
    FAST_REFRESH_INTERVAL: 5000,  // 5秒
    TOAST_DURATION: 3000,         // 3秒
    MAX_RETRY_COUNT: 3,           // 最大重试次数
    REQUEST_TIMEOUT: 10000,       // 请求超时时间
    MAX_LOG_ENTRIES: 1000,        // 最大日志条数
    MAX_CHART_POINTS: 20,         // 图表最大数据点
    DEBOUNCE_DELAY: 300           // 防抖延迟
};

// ==================== 工具函数 ====================

// 显示Toast消息 (复用device-management.html的实现)
function showToast(message, type = 'info') {
    // 创建简单的提示框
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed top-0 end-0 m-3`;
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${getToastIcon(type)} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" onclick="this.closest('.alert').remove()"></button>
        </div>
    `;
    document.body.appendChild(toast);

    // 自动消失
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, CONFIG.TOAST_DURATION);
}

// 获取Toast图标
function getToastIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle',
        'danger': 'times-circle'
    };
    return icons[type] || 'info-circle';
}

// 显示确认对话框
function showConfirmDialog(message, onConfirm, onCancel = null) {
    if (confirm(message)) {
        if (typeof onConfirm === 'function') {
            onConfirm();
        }
        return true;
    } else {
        if (typeof onCancel === 'function') {
            onCancel();
        }
        return false;
    }
}

// 显示加载状态
function showLoading(elementId, show = true) {
    const element = document.getElementById(elementId);
    if (!element) return;

    if (show) {
        element.innerHTML = `
            <div class="d-flex justify-content-center align-items-center p-4">
                <div class="spinner-border text-primary me-2" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <span>加载中...</span>
            </div>
        `;
    }
}

// 显示错误信息
function showError(elementId, message) {
    const element = document.getElementById(elementId);
    if (!element) return;

    element.innerHTML = `
        <div class="alert alert-danger m-3">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="location.reload()">
                <i class="fas fa-redo me-1"></i>重新加载
            </button>
        </div>
    `;
}

// 显示空数据状态
function showEmptyState(elementId, message = '暂无数据') {
    const element = document.getElementById(elementId);
    if (!element) return;

    element.innerHTML = `
        <div class="text-center text-muted p-4">
            <i class="fas fa-inbox fa-3x mb-3"></i>
            <p>${message}</p>
        </div>
    `;
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 格式化时间
function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 格式化相对时间
function formatRelativeTime(timestamp) {
    const now = new Date();
    const date = new Date(timestamp);
    const diff = now - date;

    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return `${seconds}秒前`;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// ==================== 错误处理和重试机制 ====================

// 统一的API请求函数
async function apiRequest(url, options = {}, retryCount = 0) {
    const requestId = Date.now() + Math.random();
    globalState.activeRequests.add(requestId);

    try {
        // 设置默认选项
        const defaultOptions = {
            headers: {
                'authorization': AUTHORIZATION,
                'Content-Type': 'application/json'
            },
            timeout: CONFIG.REQUEST_TIMEOUT
        };

        const finalOptions = { ...defaultOptions, ...options };

        // 创建带超时的fetch请求
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), finalOptions.timeout);

        const response = await fetch(url, {
            ...finalOptions,
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        // 重置错误计数
        globalState.errorCount = 0;
        globalState.retryCount.delete(url);

        return result;

    } catch (error) {
        console.error(`API请求失败 (${url}):`, error);

        // 增加错误计数
        globalState.errorCount++;
        globalState.lastErrorTime = new Date();

        // 重试逻辑
        if (retryCount < CONFIG.MAX_RETRY_COUNT && error.name !== 'AbortError') {
            const currentRetry = globalState.retryCount.get(url) || 0;
            globalState.retryCount.set(url, currentRetry + 1);

            console.log(`重试请求 (${retryCount + 1}/${CONFIG.MAX_RETRY_COUNT}): ${url}`);
            await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
            return apiRequest(url, options, retryCount + 1);
        }

        throw error;

    } finally {
        globalState.activeRequests.delete(requestId);
    }
}

// 错误边界处理
function handleError(error, context = '操作') {
    console.error(`${context}失败:`, error);

    let message = `${context}失败`;

    if (error.name === 'AbortError') {
        message = `${context}超时，请检查网络连接`;
    } else if (error.message.includes('HTTP 401')) {
        message = '认证失败，请检查授权信息';
    } else if (error.message.includes('HTTP 403')) {
        message = '权限不足，无法执行此操作';
    } else if (error.message.includes('HTTP 404')) {
        message = '请求的资源不存在';
    } else if (error.message.includes('HTTP 500')) {
        message = '服务器内部错误，请稍后重试';
    } else if (error.message.includes('Failed to fetch')) {
        message = '网络连接失败，请检查网络设置';
    } else if (error.message) {
        message = `${context}失败: ${error.message}`;
    }

    showToast(message, 'error');
    return message;
}

// 数据验证函数
function validateInput(value, rules = {}) {
    const errors = [];

    // 必填验证
    if (rules.required && (!value || value.toString().trim() === '')) {
        errors.push('此字段为必填项');
    }

    // 长度验证
    if (value && rules.minLength && value.length < rules.minLength) {
        errors.push(`最少需要${rules.minLength}个字符`);
    }

    if (value && rules.maxLength && value.length > rules.maxLength) {
        errors.push(`最多允许${rules.maxLength}个字符`);
    }

    // 数字验证
    if (value && rules.type === 'number') {
        const num = Number(value);
        if (isNaN(num)) {
            errors.push('必须是有效的数字');
        } else {
            if (rules.min !== undefined && num < rules.min) {
                errors.push(`数值不能小于${rules.min}`);
            }
            if (rules.max !== undefined && num > rules.max) {
                errors.push(`数值不能大于${rules.max}`);
            }
        }
    }

    // 邮箱验证
    if (value && rules.type === 'email') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            errors.push('请输入有效的邮箱地址');
        }
    }

    // URL验证
    if (value && rules.type === 'url') {
        try {
            new URL(value);
        } catch {
            errors.push('请输入有效的URL地址');
        }
    }

    return errors;
}

// 表单验证
function validateForm(formId, rules = {}) {
    const form = document.getElementById(formId);
    if (!form) return { isValid: false, errors: ['表单不存在'] };

    const formData = new FormData(form);
    const errors = {};
    let isValid = true;

    for (const [fieldName, fieldRules] of Object.entries(rules)) {
        const value = formData.get(fieldName);
        const fieldErrors = validateInput(value, fieldRules);

        if (fieldErrors.length > 0) {
            errors[fieldName] = fieldErrors;
            isValid = false;

            // 显示字段错误
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.add('is-invalid');

                // 移除现有的错误信息
                const existingError = field.parentNode.querySelector('.invalid-feedback');
                if (existingError) {
                    existingError.remove();
                }

                // 添加新的错误信息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = fieldErrors[0];
                field.parentNode.appendChild(errorDiv);
            }
        } else {
            // 清除字段错误
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.remove('is-invalid');
                const existingError = field.parentNode.querySelector('.invalid-feedback');
                if (existingError) {
                    existingError.remove();
                }
            }
        }
    }

    return { isValid, errors };
}

// 清除表单验证状态
function clearFormValidation(formId) {
    const form = document.getElementById(formId);
    if (!form) return;

    // 移除所有验证样式
    form.querySelectorAll('.is-invalid').forEach(field => {
        field.classList.remove('is-invalid');
    });

    // 移除所有错误信息
    form.querySelectorAll('.invalid-feedback').forEach(error => {
        error.remove();
    });
}

// ==================== 性能优化和内存管理 ====================

// 清理定时器
function clearAllIntervals() {
    globalState.refreshIntervals.forEach((intervalId, key) => {
        clearInterval(intervalId);
        console.log(`清理定时器: ${key}`);
    });
    globalState.refreshIntervals.clear();
}

// 设置定时器
function setManagedInterval(key, callback, interval) {
    // 清除现有定时器
    if (globalState.refreshIntervals.has(key)) {
        clearInterval(globalState.refreshIntervals.get(key));
    }

    // 设置新定时器
    const intervalId = setInterval(callback, interval);
    globalState.refreshIntervals.set(key, intervalId);

    return intervalId;
}

// 清理特定定时器
function clearManagedInterval(key) {
    if (globalState.refreshIntervals.has(key)) {
        clearInterval(globalState.refreshIntervals.get(key));
        globalState.refreshIntervals.delete(key);
    }
}

// 限制数组大小
function limitArraySize(array, maxSize) {
    while (array.length > maxSize) {
        array.shift();
    }
    return array;
}

// 清理图表数据
function cleanupChartData(chart, maxPoints = CONFIG.MAX_CHART_POINTS) {
    if (!chart || !chart.data) return;

    chart.data.labels = limitArraySize(chart.data.labels, maxPoints);
    chart.data.datasets.forEach(dataset => {
        dataset.data = limitArraySize(dataset.data, maxPoints);
    });
}

// 内存使用监控
function monitorMemoryUsage() {
    if (performance.memory) {
        const memory = performance.memory;
        const usage = {
            used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
        };

        console.log(`内存使用: ${usage.used}MB / ${usage.total}MB (限制: ${usage.limit}MB)`);

        // 内存使用过高时的警告
        if (usage.used / usage.limit > 0.8) {
            console.warn('内存使用率过高，建议刷新页面');
            showToast('内存使用率过高，建议刷新页面', 'warning');
        }

        return usage;
    }
    return null;
}

// 页面可见性检测
function handleVisibilityChange() {
    if (document.hidden) {
        // 页面隐藏时暂停自动刷新
        globalState.autoRefreshEnabled = false;
        console.log('页面隐藏，暂停自动刷新');
    } else {
        // 页面显示时恢复自动刷新
        globalState.autoRefreshEnabled = true;
        console.log('页面显示，恢复自动刷新');

        // 立即刷新一次数据
        if (typeof refreshAllData === 'function') {
            refreshAllData();
        }
    }
}

// 网络状态检测
function handleNetworkChange() {
    if (navigator.onLine) {
        showToast('网络连接已恢复', 'success');
        globalState.autoRefreshEnabled = true;

        // 网络恢复后立即刷新数据
        if (typeof refreshAllData === 'function') {
            refreshAllData();
        }
    } else {
        showToast('网络连接已断开', 'warning');
        globalState.autoRefreshEnabled = false;
    }
}

// ==================== 键盘快捷键支持 ====================

// 键盘快捷键映射
const KEYBOARD_SHORTCUTS = {
    'F5': () => location.reload(),
    'Ctrl+R': () => location.reload(),
    'Ctrl+Shift+R': () => {
        if (typeof refreshAllData === 'function') {
            refreshAllData();
        }
    },
    'Escape': () => {
        // 关闭所有模态框
        document.querySelectorAll('.modal.show').forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });
    },
    'Ctrl+1': () => switchTab('monitoring-tab'),
    'Ctrl+2': () => switchTab('connections-tab'),
    'Ctrl+3': () => switchTab('messages-tab'),
    'Ctrl+4': () => switchTab('tokens-tab'),
    'Ctrl+5': () => switchTab('tools-tab'),
    'Ctrl+6': () => switchTab('config-tab'),
    'Ctrl+7': () => switchTab('logs-tab'),
    'Ctrl+8': () => switchTab('metrics-tab')
};

// 处理键盘事件
function handleKeyboardShortcut(event) {
    const key = [];

    if (event.ctrlKey) key.push('Ctrl');
    if (event.shiftKey) key.push('Shift');
    if (event.altKey) key.push('Alt');

    if (event.key === 'F5') {
        key.push('F5');
    } else if (event.key === 'Escape') {
        key.push('Escape');
    } else if (event.key >= '1' && event.key <= '8') {
        key.push(event.key);
    } else if (event.key.toLowerCase() === 'r') {
        key.push('R');
    }

    const shortcut = key.join('+');

    if (KEYBOARD_SHORTCUTS[shortcut]) {
        event.preventDefault();
        KEYBOARD_SHORTCUTS[shortcut]();
    }
}

// 切换标签页
function switchTab(tabId) {
    const tab = document.getElementById(tabId);
    if (tab) {
        tab.click();
    }
}

// ==================== 数据缓存和本地存储 ====================

// 本地存储键名
const STORAGE_KEYS = {
    USER_PREFERENCES: 'mqtt_management_preferences',
    PERFORMANCE_THRESHOLDS: 'mqtt_performance_thresholds',
    CHART_SETTINGS: 'mqtt_chart_settings',
    FILTER_SETTINGS: 'mqtt_filter_settings'
};

// 保存用户偏好
function saveUserPreferences(preferences) {
    try {
        localStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
    } catch (error) {
        console.warn('保存用户偏好失败:', error);
    }
}

// 加载用户偏好
function loadUserPreferences() {
    try {
        const preferences = localStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
        return preferences ? JSON.parse(preferences) : {};
    } catch (error) {
        console.warn('加载用户偏好失败:', error);
        return {};
    }
}

// 保存到本地存储
function saveToStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.warn(`保存数据到本地存储失败 (${key}):`, error);
        return false;
    }
}

// 从本地存储加载
function loadFromStorage(key, defaultValue = null) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : defaultValue;
    } catch (error) {
        console.warn(`从本地存储加载数据失败 (${key}):`, error);
        return defaultValue;
    }
}

// 清理本地存储
function clearStorage() {
    try {
        Object.values(STORAGE_KEYS).forEach(key => {
            localStorage.removeItem(key);
        });
        showToast('本地存储已清理', 'success');
    } catch (error) {
        console.warn('清理本地存储失败:', error);
        showToast('清理本地存储失败', 'error');
    }
}

// ==================== 页面初始化和事件监听 ====================

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    try {
        initializePage();
        initializeWebSocket();
        setupEventListeners();
        loadUserPreferences();

        // 标记为已初始化
        globalState.isInitialized = true;

        console.log('MQTT管理页面初始化完成');
    } catch (error) {
        handleError(error, '页面初始化');
    }
});

// 设置事件监听器
function setupEventListeners() {
    // 页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 网络状态变化
    window.addEventListener('online', handleNetworkChange);
    window.addEventListener('offline', handleNetworkChange);

    // 键盘快捷键
    document.addEventListener('keydown', handleKeyboardShortcut);

    // 页面卸载时清理资源
    window.addEventListener('beforeunload', function() {
        cleanup();
    });

    // 错误处理
    window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error);
        handleError(event.error, '页面运行');
    });

    // Promise错误处理
    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise错误:', event.reason);
        handleError(event.reason, 'Promise');
        event.preventDefault();
    });
}

// 清理资源
function cleanup() {
    console.log('清理页面资源...');

    // 清理所有定时器
    clearAllIntervals();

    // 关闭WebSocket连接
    if (websocket) {
        websocket.close();
    }

    // 清理图表
    if (connectionChart) {
        connectionChart.destroy();
    }
    if (messageChart) {
        messageChart.destroy();
    }
    if (logTrendChart) {
        logTrendChart.destroy();
    }
    if (cpuMemoryChart) {
        cpuMemoryChart.destroy();
    }
    if (mqttPerformanceChart) {
        mqttPerformanceChart.destroy();
    }

    // 取消所有活跃请求
    globalState.activeRequests.clear();
}

// 刷新所有数据
function refreshAllData() {
    if (!globalState.autoRefreshEnabled) {
        console.log('自动刷新已禁用，跳过刷新');
        return;
    }

    console.log('刷新所有数据...');

    try {
        // 刷新各个模块的数据
        if (typeof loadMqttStatus === 'function') {
            loadMqttStatus();
        }
        if (typeof loadConnections === 'function') {
            loadConnections();
        }
        if (typeof loadMessages === 'function') {
            loadMessages();
        }
        if (typeof loadTokens === 'function') {
            loadTokens();
        }
        if (typeof loadLogStats === 'function') {
            loadLogStats();
        }
        if (typeof loadPerformanceMetrics === 'function') {
            loadPerformanceMetrics();
        }

        showToast('数据刷新完成', 'success');
    } catch (error) {
        handleError(error, '数据刷新');
    }
}

// 初始化页面
function initializePage() {
    console.log('MQTT管理平台初始化...');
    initializeCharts();
    refreshAllData();
    loadMonitoringData();
}

// 刷新所有数据
function refreshAllData() {
    const refreshIcon = document.getElementById('refreshIcon');
    refreshIcon.classList.add('refresh-btn');

    console.log('刷新所有数据...');

    // 刷新监控数据
    loadMonitoringData();
    loadSystemStatus();
    loadMetrics();

    // 刷新连接数据
    loadConnectionsData();

    // 刷新消息数据
    updateMessageStats();

    // 刷新Token数据
    loadTokensData();

    setTimeout(() => {
        refreshIcon.classList.remove('refresh-btn');
    }, 2000);
}

// 切换自动刷新
function toggleAutoRefresh() {
    const icon = document.getElementById('autoRefreshIcon');
    const text = document.getElementById('autoRefreshText');
    
    if (autoRefreshEnabled) {
        // 停止自动刷新
        clearInterval(autoRefreshInterval);
        autoRefreshEnabled = false;
        icon.className = 'fas fa-play';
        text.textContent = '开启自动刷新';
    } else {
        // 开始自动刷新
        autoRefreshInterval = setInterval(refreshAllData, 10000); // 10秒刷新一次
        autoRefreshEnabled = true;
        icon.className = 'fas fa-pause';
        text.textContent = '停止自动刷新';
    }
}

// 初始化WebSocket连接
function initializeWebSocket() {
    const wsUrl = CONFIG.WEBSOCKET.URL;
    
    try {
        websocket = new WebSocket(wsUrl);
        
        websocket.onopen = function(event) {
            console.log('WebSocket连接已建立');
            updateWebSocketStatus('connected');
            reconnectAttempts = 0;
        };
        
        websocket.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            } catch (e) {
                console.error('解析WebSocket消息失败:', e);
            }
        };
        
        websocket.onclose = function(event) {
            console.log('WebSocket连接已关闭');
            updateWebSocketStatus('disconnected');
            
            // 尝试重连
            if (reconnectAttempts < maxReconnectAttempts) {
                setTimeout(() => {
                    reconnectAttempts++;
                    console.log(`尝试重连WebSocket (${reconnectAttempts}/${maxReconnectAttempts})`);
                    initializeWebSocket();
                }, 3000);
            }
        };
        
        websocket.onerror = function(error) {
            console.error('WebSocket错误:', error);
            updateWebSocketStatus('error');
        };
        
    } catch (e) {
        console.error('创建WebSocket连接失败:', e);
        updateWebSocketStatus('error');
    }
}

// 处理WebSocket消息
function handleWebSocketMessage(data) {
    console.log('收到WebSocket消息:', data);
    
    switch (data.type) {
        case 'welcome':
            console.log('WebSocket欢迎消息:', data.message);
            break;
        case 'mqtt_connection':
            handleMqttConnectionEvent(data);
            break;
        case 'mqtt_message':
            handleMqttMessageEvent(data);
            break;
        case 'system_log':
            handleSystemLogEvent(data);
            break;
        case 'connection_stats':
            handleConnectionStatsEvent(data);
            break;
        default:
            console.log('未知的WebSocket消息类型:', data.type);
    }
}

// 处理MQTT连接事件
function handleMqttConnectionEvent(data) {
    console.log('MQTT连接事件:', data);

    if (data.event === 'client_online') {
        addRecentEvent('success', `设备上线: ${data.data.clientId}`);
        // 更新连接数（延迟刷新以获取最新数据）
        setTimeout(loadMonitoringData, 1000);
    } else if (data.event === 'client_offline') {
        addRecentEvent('warning', `设备下线: ${data.data.clientId}`);
        setTimeout(loadMonitoringData, 1000);
    }
}

// 处理MQTT消息事件
function handleMqttMessageEvent(data) {
    console.log('MQTT消息事件:', data);

    // 更新消息统计
    monitoringData.messages++;
    document.getElementById('mqttMessages').textContent = monitoringData.messages;

    // 添加消息事件
    addRecentEvent('info', `收到消息: ${data.topic} (${data.clientId})`);

    // 更新消息速率
    monitoringData.messageRate++;

    // 处理消息管理相关逻辑
    handleReceivedMessage(data);
}

// 处理系统日志事件
function handleSystemLogEvent(data) {
    console.log('系统日志事件:', data);

    // 根据日志级别添加到最近事件
    let eventLevel = 'info';
    if (data.level === 'ERROR') eventLevel = 'error';
    else if (data.level === 'WARN') eventLevel = 'warning';
    else if (data.level === 'INFO') eventLevel = 'success';

    addRecentEvent(eventLevel, `${data.source}: ${data.message}`);
}

// 处理连接统计事件
function handleConnectionStatsEvent(data) {
    console.log('连接统计事件:', data);
    // 更新WebSocket连接数显示
    if (data.totalConnections !== undefined) {
        document.getElementById('wsConnections').textContent = data.totalConnections;
    }
}

// 更新WebSocket状态显示
function updateWebSocketStatus(status) {
    const statusElement = document.getElementById('wsStatus');
    const wsServiceStatus = document.getElementById('wsServiceStatus');

    switch (status) {
        case 'connected':
            statusElement.className = 'badge websocket-connected';
            statusElement.innerHTML = '<i class="fas fa-wifi me-1"></i>WebSocket已连接';
            wsServiceStatus.textContent = '已连接';
            wsServiceStatus.className = 'metric-value status-online';
            addRecentEvent('success', 'WebSocket连接已建立');
            break;
        case 'disconnected':
            statusElement.className = 'badge websocket-disconnected';
            statusElement.innerHTML = '<i class="fas fa-wifi me-1"></i>WebSocket断开';
            wsServiceStatus.textContent = '断开';
            wsServiceStatus.className = 'metric-value status-offline';
            break;
        case 'connecting':
            statusElement.className = 'badge websocket-connecting';
            statusElement.innerHTML = '<i class="fas fa-wifi me-1"></i>WebSocket连接中';
            wsServiceStatus.textContent = '连接中';
            wsServiceStatus.className = 'metric-value status-warning';
            break;
        case 'error':
            statusElement.className = 'badge websocket-disconnected';
            statusElement.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>WebSocket错误';
            wsServiceStatus.textContent = '错误';
            wsServiceStatus.className = 'metric-value status-offline';
            addRecentEvent('error', 'WebSocket连接错误');
            break;
    }
}

// ==================== 监控面板功能 ====================

// 初始化图表
function initializeCharts() {
    // 初始化连接趋势图
    const connectionCtx = document.getElementById('connectionChart').getContext('2d');
    connectionChart = new Chart(connectionCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'MQTT连接数',
                data: [],
                borderColor: '#11998e',
                backgroundColor: 'rgba(17, 153, 142, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }, {
                label: 'WebSocket连接数',
                data: [],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            animation: {
                duration: 1000
            }
        }
    });

    // 初始化消息流量图
    const messageCtx = document.getElementById('messageChart').getContext('2d');
    messageChart = new Chart(messageCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: '接收消息数',
                data: [],
                backgroundColor: '#4facfe',
                borderColor: '#4facfe',
                borderWidth: 1
            }, {
                label: '发送消息数',
                data: [],
                backgroundColor: '#00f2fe',
                borderColor: '#00f2fe',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// 加载监控数据
async function loadMonitoringData() {
    try {
        // 获取MQTT连接状态
        const connectionResponse = await fetch(`${DEVICE_TOKEN_API}/connection-status`, {
            headers: { 'authorization': AUTHORIZATION }
        });

        if (connectionResponse.ok) {
            const connectionData = await connectionResponse.json();
            updateConnectionStats(connectionData);
        }

        // 获取WebSocket状态
        const wsResponse = await fetch(`${MQTT_API_BASE}/websocket-status`, {
            headers: { 'authorization': AUTHORIZATION }
        });

        if (wsResponse.ok) {
            const wsData = await wsResponse.json();
            updateWebSocketStats(wsData);
        }

        // 更新图表数据
        updateChartData();

    } catch (error) {
        console.error('加载监控数据失败:', error);
        addRecentEvent('error', '监控数据加载失败: ' + error.message);
    }
}

// 加载系统状态
async function loadSystemStatus() {
    try {
        // 获取MQTT服务状态
        const statusResponse = await fetch(`${MQTT_API_BASE}/status`, {
            headers: { 'authorization': AUTHORIZATION }
        });

        if (statusResponse.ok) {
            const statusData = await statusResponse.json();
            updateSystemStatus(statusData);
        }

        // 获取Redis健康状态
        const redisResponse = await fetch(`${DEVICE_TOKEN_API}/redis-health`, {
            headers: { 'authorization': AUTHORIZATION }
        });

        if (redisResponse.ok) {
            const redisData = await redisResponse.json();
            updateRedisStatus(redisData);
        }

    } catch (error) {
        console.error('加载系统状态失败:', error);
        addRecentEvent('error', '系统状态加载失败: ' + error.message);
    }
}

// 加载性能指标
async function loadMetrics() {
    try {
        const response = await fetch(`${MQTT_API_BASE}/metrics`, {
            headers: { 'authorization': AUTHORIZATION }
        });

        if (response.ok) {
            const data = await response.json();
            updateMetrics(data);
        }

    } catch (error) {
        console.error('加载性能指标失败:', error);
        addRecentEvent('error', '性能指标加载失败: ' + error.message);
    }
}

// 更新连接统计
function updateConnectionStats(data) {
    monitoringData.connections = data.connectionCount || 0;
    monitoringData.tokens = data.connectedTokens ? data.connectedTokens.length : 0;

    // 更新顶部统计卡片
    document.getElementById('mqttConnections').textContent = monitoringData.connections;
    document.getElementById('mqttTokens').textContent = monitoringData.tokens;

    addRecentEvent('info', `MQTT连接数更新: ${monitoringData.connections}`);
}

// 更新WebSocket统计
function updateWebSocketStats(data) {
    if (data.success && data.websocketStats) {
        monitoringData.wsConnections = data.websocketStats.totalConnections || 0;
        document.getElementById('wsConnections').textContent = monitoringData.wsConnections;
    }
}

// 更新系统状态
function updateSystemStatus(data) {
    if (data.success && data.status) {
        const status = data.status;

        // 更新MQTT服务状态
        const mqttStatus = status.serverRunning ? '运行中' : '已停止';
        const mqttClass = status.serverRunning ? 'status-online' : 'status-offline';
        document.getElementById('mqttServiceStatus').textContent = mqttStatus;
        document.getElementById('mqttServiceStatus').className = `metric-value ${mqttClass}`;

        // 更新端口状态
        document.getElementById('portStatus').textContent = status.port || '1884';

        // 更新内存使用
        if (status.memory) {
            const usedMemory = Math.round(status.memory.usedMemory / 1024 / 1024);
            monitoringData.memoryUsage = usedMemory;
            document.getElementById('memoryUsage').textContent = `${usedMemory} MB`;
        }
    }
}

// 更新Redis状态
function updateRedisStatus(data) {
    const redisStatus = data.healthy ? '正常' : '异常';
    const redisClass = data.healthy ? 'status-online' : 'status-offline';

    document.getElementById('redisServiceStatus').textContent = redisStatus;
    document.getElementById('redisServiceStatus').className = `metric-value ${redisClass}`;
    document.getElementById('redisStatus').textContent = redisStatus;
    document.getElementById('redisStatus').className = `stats-number ${redisClass}`;
}

// 更新性能指标
function updateMetrics(data) {
    if (data.success && data.metrics) {
        const metrics = data.metrics;

        // 更新CPU使用率（模拟数据）
        monitoringData.cpuUsage = Math.random() * 100;
        document.getElementById('cpuUsage').textContent = `${monitoringData.cpuUsage.toFixed(1)}%`;

        // 更新消息速率（模拟数据）
        monitoringData.messageRate = Math.floor(Math.random() * 50);
        document.getElementById('messageRate').textContent = monitoringData.messageRate;

        // 更新网络流量（模拟数据）
        monitoringData.networkTraffic = Math.floor(Math.random() * 1000);
        document.getElementById('networkTraffic').textContent = monitoringData.networkTraffic;
    }
}

// 更新图表数据
function updateChartData() {
    const now = new Date();
    const timeLabel = now.toLocaleTimeString();

    // 添加新数据点
    connectionChart.data.labels.push(timeLabel);
    connectionChart.data.datasets[0].data.push(monitoringData.connections);
    connectionChart.data.datasets[1].data.push(monitoringData.wsConnections);

    messageChart.data.labels.push(timeLabel);
    messageChart.data.datasets[0].data.push(monitoringData.messageRate);
    messageChart.data.datasets[1].data.push(Math.floor(monitoringData.messageRate * 0.8));

    // 限制数据点数量
    if (connectionChart.data.labels.length > maxDataPoints) {
        connectionChart.data.labels.shift();
        connectionChart.data.datasets[0].data.shift();
        connectionChart.data.datasets[1].data.shift();
    }

    if (messageChart.data.labels.length > maxDataPoints) {
        messageChart.data.labels.shift();
        messageChart.data.datasets[0].data.shift();
        messageChart.data.datasets[1].data.shift();
    }

    // 更新图表
    connectionChart.update('none');
    messageChart.update('none');
}

// 重置连接图表
function resetConnectionChart() {
    connectionChart.data.labels = [];
    connectionChart.data.datasets[0].data = [];
    connectionChart.data.datasets[1].data = [];
    connectionChart.update();

    document.getElementById('connectionChartStatus').textContent = '已重置';
    setTimeout(() => {
        document.getElementById('connectionChartStatus').textContent = '实时更新';
    }, 2000);
}

// 重置消息图表
function resetMessageChart() {
    messageChart.data.labels = [];
    messageChart.data.datasets[0].data = [];
    messageChart.data.datasets[1].data = [];
    messageChart.update();

    document.getElementById('messageChartStatus').textContent = '已重置';
    setTimeout(() => {
        document.getElementById('messageChartStatus').textContent = '实时更新';
    }, 2000);
}

// 添加最近事件
function addRecentEvent(level, message) {
    const eventsList = document.getElementById('recentEventsList');
    const now = new Date();
    const timeStr = now.toLocaleTimeString();

    // 移除"暂无事件"提示
    if (eventsList.children.length === 1 && eventsList.children[0].textContent.includes('暂无事件')) {
        eventsList.innerHTML = '';
    }

    // 创建事件项
    const eventItem = document.createElement('div');
    eventItem.className = 'list-group-item';

    let iconClass = 'fas fa-info-circle';
    let textClass = 'text-info';

    switch (level) {
        case 'error':
            iconClass = 'fas fa-exclamation-triangle';
            textClass = 'text-danger';
            break;
        case 'warning':
            iconClass = 'fas fa-exclamation-circle';
            textClass = 'text-warning';
            break;
        case 'success':
            iconClass = 'fas fa-check-circle';
            textClass = 'text-success';
            break;
    }

    eventItem.innerHTML = `
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <i class="${iconClass} ${textClass} me-2"></i>
                <span class="fw-bold">${timeStr}</span>
                <div class="mt-1 text-muted small">${message}</div>
            </div>
        </div>
    `;

    // 添加到列表顶部
    eventsList.insertBefore(eventItem, eventsList.firstChild);

    // 限制事件数量
    while (eventsList.children.length > 10) {
        eventsList.removeChild(eventsList.lastChild);
    }
}

// 清空最近事件
function clearRecentEvents() {
    const eventsList = document.getElementById('recentEventsList');
    eventsList.innerHTML = `
        <div class="list-group-item text-center text-muted">
            <i class="fas fa-info-circle me-2"></i>暂无事件
        </div>
    `;
}

// ==================== 连接管理功能 ====================

// 加载连接数据
async function loadConnectionsData() {
    try {
        showConnectionsLoading(true);

        const response = await fetch(`${DEVICE_TOKEN_API}/connection-status`, {
            headers: { 'authorization': AUTHORIZATION }
        });

        if (response.ok) {
            const data = await response.json();
            processConnectionsData(data);
            updateConnectionsStats(data);
            renderConnectionsTable();
        } else {
            showConnectionsError('加载连接数据失败');
        }

    } catch (error) {
        console.error('加载连接数据失败:', error);
        showConnectionsError('网络错误: ' + error.message);
    } finally {
        showConnectionsLoading(false);
    }
}

// 处理连接数据
function processConnectionsData(data) {
    connectionsData.list = [];

    if (data.connectedTokens && data.connectedTokens.length > 0) {
        data.connectedTokens.forEach(token => {
            connectionsData.list.push({
                clientId: token.clientId || `client_${token.deviceId}`,
                deviceId: token.deviceId,
                deviceName: token.deviceName || '未知设备',
                token: token.token,
                connectionTime: token.connectionTime || new Date().toISOString(),
                ipAddress: token.ipAddress || '未知',
                deviceType: token.deviceType || 'unknown',
                status: 'online'
            });
        });
    }

    // 应用过滤器
    applyConnectionsFilter();
}

// 更新连接统计
function updateConnectionsStats(data) {
    const totalCount = data.connectionCount || 0;
    const onlineCount = data.connectedTokens ? data.connectedTokens.length : 0;
    const activeTokens = onlineCount;

    document.getElementById('connectionsTotalCount').textContent = totalCount;
    document.getElementById('connectionsOnlineCount').textContent = onlineCount;
    document.getElementById('connectionsActiveTokens').textContent = activeTokens;
    document.getElementById('connectionsLastUpdate').textContent = new Date().toLocaleTimeString();
}

// 应用连接过滤器
function applyConnectionsFilter() {
    const searchText = document.getElementById('connectionSearchInput').value.toLowerCase();
    const statusFilter = document.getElementById('connectionStatusFilter').value;
    const typeFilter = document.getElementById('connectionTypeFilter').value;

    connectionsData.filteredList = connectionsData.list.filter(connection => {
        // 搜索过滤
        const matchesSearch = !searchText ||
            connection.deviceId.toLowerCase().includes(searchText) ||
            connection.clientId.toLowerCase().includes(searchText) ||
            connection.token.toLowerCase().includes(searchText) ||
            connection.deviceName.toLowerCase().includes(searchText);

        // 状态过滤
        const matchesStatus = !statusFilter || connection.status === statusFilter;

        // 类型过滤
        const matchesType = !typeFilter || connection.deviceType === typeFilter;

        return matchesSearch && matchesStatus && matchesType;
    });

    // 计算分页
    connectionsData.pageSize = parseInt(document.getElementById('connectionPageSize').value);
    connectionsData.totalPages = Math.ceil(connectionsData.filteredList.length / connectionsData.pageSize);

    // 确保当前页在有效范围内
    if (connectionsData.currentPage > connectionsData.totalPages) {
        connectionsData.currentPage = Math.max(1, connectionsData.totalPages);
    }
}

// 渲染连接表格
function renderConnectionsTable() {
    const tbody = document.getElementById('connectionsTableBody');

    if (connectionsData.filteredList.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted py-4">
                    <i class="fas fa-info-circle me-2"></i>暂无连接数据
                </td>
            </tr>
        `;
        renderConnectionsPagination();
        return;
    }

    // 计算当前页数据
    const startIndex = (connectionsData.currentPage - 1) * connectionsData.pageSize;
    const endIndex = startIndex + connectionsData.pageSize;
    const pageData = connectionsData.filteredList.slice(startIndex, endIndex);

    tbody.innerHTML = pageData.map(connection => {
        const isSelected = connectionsData.selectedConnections.has(connection.token);
        const statusClass = connection.status === 'online' ? 'connection-online' : 'connection-offline';
        const statusText = connection.status === 'online' ? '在线' : '离线';

        return `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input connection-checkbox"
                           value="${connection.token}" ${isSelected ? 'checked' : ''}
                           onchange="toggleConnectionSelection('${connection.token}')">
                </td>
                <td>
                    <span class="connection-indicator ${statusClass}"></span>
                    ${statusText}
                </td>
                <td><code>${connection.clientId}</code></td>
                <td><strong>${connection.deviceId}</strong></td>
                <td>${connection.deviceName}</td>
                <td>
                    <code class="text-muted">${connection.token.substring(0, 20)}...</code>
                    <button class="btn btn-outline-secondary btn-sm ms-1" onclick="copyToClipboard('${connection.token}')">
                        <i class="fas fa-copy"></i>
                    </button>
                </td>
                <td>${formatDateTime(connection.connectionTime)}</td>
                <td>${connection.ipAddress}</td>
                <td>
                    <button class="btn btn-info btn-sm me-1" onclick="showConnectionDetails('${connection.token}')">
                        <i class="fas fa-info-circle"></i>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="showDisconnectModal('${connection.token}')">
                        <i class="fas fa-power-off"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');

    renderConnectionsPagination();
    updateBatchOperationsVisibility();
}

// 渲染分页
function renderConnectionsPagination() {
    const pagination = document.getElementById('connectionsPagination');

    if (connectionsData.totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHtml = '';

    // 上一页
    paginationHtml += `
        <li class="page-item ${connectionsData.currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToConnectionsPage(${connectionsData.currentPage - 1})">上一页</a>
        </li>
    `;

    // 页码
    const startPage = Math.max(1, connectionsData.currentPage - 2);
    const endPage = Math.min(connectionsData.totalPages, connectionsData.currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === connectionsData.currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="goToConnectionsPage(${i})">${i}</a>
            </li>
        `;
    }

    // 下一页
    paginationHtml += `
        <li class="page-item ${connectionsData.currentPage === connectionsData.totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToConnectionsPage(${connectionsData.currentPage + 1})">下一页</a>
        </li>
    `;

    pagination.innerHTML = paginationHtml;
}

// 跳转到指定页
function goToConnectionsPage(page) {
    if (page >= 1 && page <= connectionsData.totalPages) {
        connectionsData.currentPage = page;
        renderConnectionsTable();
    }
}

// 显示加载状态
function showConnectionsLoading(show) {
    const spinner = document.getElementById('connectionsLoadingSpinner');
    const error = document.getElementById('connectionsErrorMessage');
    const empty = document.getElementById('connectionsEmptyMessage');

    if (show) {
        spinner.style.display = 'block';
        error.style.display = 'none';
        empty.style.display = 'none';
    } else {
        spinner.style.display = 'none';
    }
}

// 显示错误信息
function showConnectionsError(message) {
    const spinner = document.getElementById('connectionsLoadingSpinner');
    const error = document.getElementById('connectionsErrorMessage');
    const empty = document.getElementById('connectionsEmptyMessage');

    spinner.style.display = 'none';
    error.style.display = 'block';
    error.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${message}`;
    empty.style.display = 'none';
}

// 刷新连接数据
function refreshConnections() {
    const refreshIcon = document.getElementById('connectionsRefreshIcon');
    refreshIcon.classList.add('refresh-btn');

    loadConnectionsData().finally(() => {
        refreshIcon.classList.remove('refresh-btn');
    });
}

// 搜索连接
function searchConnections() {
    connectionsData.currentPage = 1;
    applyConnectionsFilter();
    renderConnectionsTable();
}

// 重置过滤器
function resetConnectionFilters() {
    document.getElementById('connectionSearchInput').value = '';
    document.getElementById('connectionStatusFilter').value = '';
    document.getElementById('connectionTypeFilter').value = '';
    document.getElementById('connectionPageSize').value = '20';

    connectionsData.currentPage = 1;
    connectionsData.pageSize = 20;
    applyConnectionsFilter();
    renderConnectionsTable();
}

// 切换连接选择
function toggleConnectionSelection(token) {
    if (connectionsData.selectedConnections.has(token)) {
        connectionsData.selectedConnections.delete(token);
    } else {
        connectionsData.selectedConnections.add(token);
    }
    updateBatchOperationsVisibility();
    updateSelectAllCheckbox();
}

// 切换全选
function toggleAllConnectionSelection() {
    const selectAll = document.getElementById('selectAllConnections');
    const checkboxes = document.querySelectorAll('.connection-checkbox');

    if (selectAll.checked) {
        // 全选当前页
        checkboxes.forEach(checkbox => {
            connectionsData.selectedConnections.add(checkbox.value);
            checkbox.checked = true;
        });
    } else {
        // 取消全选
        checkboxes.forEach(checkbox => {
            connectionsData.selectedConnections.delete(checkbox.value);
            checkbox.checked = false;
        });
    }

    updateBatchOperationsVisibility();
}

// 更新全选复选框状态
function updateSelectAllCheckbox() {
    const selectAll = document.getElementById('selectAllConnections');
    const checkboxes = document.querySelectorAll('.connection-checkbox');
    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

    if (checkedCount === 0) {
        selectAll.checked = false;
        selectAll.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAll.checked = true;
        selectAll.indeterminate = false;
    } else {
        selectAll.checked = false;
        selectAll.indeterminate = true;
    }
}

// 更新批量操作可见性
function updateBatchOperationsVisibility() {
    const batchRow = document.getElementById('batchOperationsRow');
    const selectedCount = connectionsData.selectedConnections.size;

    if (selectedCount > 0) {
        batchRow.style.display = 'block';
        document.getElementById('selectedConnectionsCount').textContent = selectedCount;
    } else {
        batchRow.style.display = 'none';
    }
}

// 清除选择
function clearConnectionSelection() {
    connectionsData.selectedConnections.clear();
    document.querySelectorAll('.connection-checkbox').forEach(cb => cb.checked = false);
    document.getElementById('selectAllConnections').checked = false;
    updateBatchOperationsVisibility();
}

// 显示连接详情
function showConnectionDetails(token) {
    const connection = connectionsData.list.find(c => c.token === token);
    if (!connection) return;

    // 填充详情数据
    document.getElementById('detailClientId').textContent = connection.clientId;
    document.getElementById('detailDeviceId').textContent = connection.deviceId;
    document.getElementById('detailDeviceName').textContent = connection.deviceName;
    document.getElementById('detailConnectionStatus').textContent = connection.status === 'online' ? '在线' : '离线';
    document.getElementById('detailToken').textContent = connection.token;
    document.getElementById('detailConnectTime').textContent = formatDateTime(connection.connectionTime);
    document.getElementById('detailIpAddress').textContent = connection.ipAddress;
    document.getElementById('detailDeviceType').textContent = connection.deviceType;

    // 保存当前连接信息
    connectionsData.currentConnectionForDisconnect = connection;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('connectionDetailsModal'));
    modal.show();
}

// 从详情页断开连接
function disconnectFromDetails() {
    if (connectionsData.currentConnectionForDisconnect) {
        showDisconnectModal(connectionsData.currentConnectionForDisconnect.token);
        // 关闭详情模态框
        const detailsModal = bootstrap.Modal.getInstance(document.getElementById('connectionDetailsModal'));
        if (detailsModal) {
            detailsModal.hide();
        }
    }
}

// 显示断开连接模态框
function showDisconnectModal(token) {
    const connection = connectionsData.list.find(c => c.token === token);
    if (!connection) return;

    connectionsData.currentConnectionForDisconnect = connection;

    // 填充设备信息
    document.getElementById('disconnectConnectionInfo').innerHTML = `
        <strong>设备ID:</strong> ${connection.deviceId}<br>
        <strong>设备名称:</strong> ${connection.deviceName}<br>
        <strong>ClientID:</strong> ${connection.clientId}<br>
        <strong>Token:</strong> ${connection.token.substring(0, 30)}...
    `;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('disconnectConnectionModal'));
    modal.show();
}

// 确认断开连接
async function confirmDisconnectConnection() {
    if (!connectionsData.currentConnectionForDisconnect) return;

    const connection = connectionsData.currentConnectionForDisconnect;
    const reason = document.getElementById('disconnectReason').value;

    try {
        const response = await fetch(`${DEVICE_TOKEN_API}/disconnect-by-token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'authorization': AUTHORIZATION
            },
            body: JSON.stringify({
                token: connection.token,
                reason: reason
            })
        });

        const result = await response.json();

        if (result.success) {
            addRecentEvent('success', `设备连接已断开: ${connection.deviceId}`);

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('disconnectConnectionModal'));
            modal.hide();

            // 刷新连接列表
            setTimeout(() => {
                loadConnectionsData();
            }, 1000);
        } else {
            addRecentEvent('error', '断开连接失败: ' + result.message);
        }

    } catch (error) {
        console.error('断开连接失败:', error);
        addRecentEvent('error', '断开连接失败: ' + error.message);
    }
}

// 批量断开连接
function batchDisconnectConnections() {
    if (connectionsData.selectedConnections.size === 0) return;

    document.getElementById('batchDisconnectCount').textContent = connectionsData.selectedConnections.size;

    const modal = new bootstrap.Modal(document.getElementById('batchDisconnectModal'));
    modal.show();
}

// 确认批量断开
async function confirmBatchDisconnect() {
    const selectedTokens = Array.from(connectionsData.selectedConnections);
    const reason = document.getElementById('batchDisconnectReason').value;

    if (selectedTokens.length === 0) return;

    try {
        const promises = selectedTokens.map(token =>
            fetch(`${DEVICE_TOKEN_API}/disconnect-by-token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'authorization': AUTHORIZATION
                },
                body: JSON.stringify({ token, reason })
            }).then(response => response.json())
        );

        const results = await Promise.all(promises);
        const successCount = results.filter(r => r.success).length;

        addRecentEvent('success', `批量断开完成: ${successCount}/${selectedTokens.length} 个连接`);

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('batchDisconnectModal'));
        modal.hide();

        // 清除选择并刷新列表
        clearConnectionSelection();
        setTimeout(() => {
            loadConnectionsData();
        }, 1000);

    } catch (error) {
        console.error('批量断开失败:', error);
        addRecentEvent('error', '批量断开失败: ' + error.message);
    }
}

// 断开所有连接
function disconnectAllConnections() {
    if (connectionsData.list.length === 0) {
        addRecentEvent('warning', '当前没有活跃连接');
        return;
    }

    if (confirm(`确定要断开所有 ${connectionsData.list.length} 个连接吗？此操作不可撤销！`)) {
        // 选择所有连接
        connectionsData.selectedConnections.clear();
        connectionsData.list.forEach(conn => {
            connectionsData.selectedConnections.add(conn.token);
        });

        // 显示批量断开模态框
        batchDisconnectConnections();
    }
}

// 清理无效连接
async function cleanupInvalidConnections() {
    try {
        const response = await fetch(`${DEVICE_TOKEN_API}/cleanup-connections`, {
            method: 'POST',
            headers: { 'authorization': AUTHORIZATION }
        });

        const result = await response.json();

        if (result.success) {
            addRecentEvent('success', '无效连接清理完成');
            loadConnectionsData();
        } else {
            addRecentEvent('error', '清理无效连接失败: ' + result.message);
        }

    } catch (error) {
        console.error('清理无效连接失败:', error);
        addRecentEvent('error', '清理无效连接失败: ' + error.message);
    }
}

// 工具函数：格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 工具函数：复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        addRecentEvent('success', 'Token已复制到剪贴板');
    }).catch(err => {
        console.error('复制失败:', err);
        addRecentEvent('error', '复制失败');
    });
}

// ==================== 消息管理功能 ====================

// 初始化消息管理
function initializeMessageManagement() {
    // 绑定表单提交事件
    document.getElementById('messagePublishForm').addEventListener('submit', function(e) {
        e.preventDefault();
        publishMessage();
    });

    // 初始化常用Topic
    renderCommonTopics();

    // 更新消息统计
    updateMessageStats();
}

// 发布消息
async function publishMessage() {
    const topic = document.getElementById('publishTopic').value.trim();
    const payload = document.getElementById('publishPayload').value;
    const qos = parseInt(document.getElementById('publishQos').value);
    const retain = document.getElementById('publishRetain').checked;

    if (!topic) {
        addRecentEvent('error', '请输入Topic');
        return;
    }

    if (!payload) {
        addRecentEvent('error', '请输入消息内容');
        return;
    }

    try {
        const response = await fetch(`${MQTT_API_BASE}/publish`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'authorization': AUTHORIZATION
            },
            body: new URLSearchParams({
                topic: topic,
                payload: payload,
                qos: qos,
                retain: retain
            })
        });

        const result = await response.json();

        if (result.success) {
            addRecentEvent('success', `消息发布成功: ${topic}`);

            // 添加到消息历史
            addMessageToHistory({
                type: 'published',
                topic: topic,
                payload: payload,
                qos: qos,
                retain: retain,
                timestamp: new Date().toISOString()
            });

            // 更新统计
            messagesData.publishedCount++;
            updateMessageStats();

            // 清空表单（可选）
            // clearPublishForm();

        } else {
            addRecentEvent('error', '消息发布失败: ' + result.message);
        }

    } catch (error) {
        console.error('发布消息失败:', error);
        addRecentEvent('error', '发布消息失败: ' + error.message);
    }
}

// 处理接收到的消息
function handleReceivedMessage(data) {
    // 检查是否匹配订阅的Topic
    if (messagesData.isSubscribed && isTopicMatch(data.topic, messagesData.subscribedTopic)) {
        // 添加到实时消息显示
        addRealtimeMessage(data);
    }

    // 添加到消息历史
    addMessageToHistory({
        type: 'received',
        topic: data.topic,
        payload: data.payload,
        clientId: data.clientId,
        token: data.token,
        timestamp: data.timestamp || new Date().toISOString()
    });

    // 更新统计
    messagesData.receivedCount++;
    updateMessageStats();
}

// 切换订阅状态
function toggleSubscription() {
    const topic = document.getElementById('subscribeTopic').value.trim();

    if (!messagesData.isSubscribed) {
        // 开始订阅
        if (!topic) {
            addRecentEvent('error', '请输入订阅Topic');
            return;
        }

        startSubscription(topic);
    } else {
        // 停止订阅
        stopSubscription();
    }
}

// 开始订阅
function startSubscription(topic) {
    messagesData.isSubscribed = true;
    messagesData.subscribedTopic = topic;
    messagesData.subscriptionCount++;

    // 更新UI
    document.getElementById('subscribeIcon').className = 'fas fa-stop';
    document.getElementById('subscribeText').textContent = '停止订阅';
    document.getElementById('subscriptionStatus').textContent = `订阅中: ${topic}`;
    document.getElementById('subscriptionStatus').className = 'badge bg-success';

    // 清空实时消息显示
    const container = document.getElementById('realtimeMessages');
    container.innerHTML = `
        <div class="text-center text-success py-2">
            <i class="fas fa-rss me-2"></i>正在监听: ${topic}
        </div>
    `;

    addRecentEvent('success', `开始订阅: ${topic}`);
    updateMessageStats();
}

// 停止订阅
function stopSubscription() {
    const topic = messagesData.subscribedTopic;

    messagesData.isSubscribed = false;
    messagesData.subscribedTopic = '';

    // 更新UI
    document.getElementById('subscribeIcon').className = 'fas fa-play';
    document.getElementById('subscribeText').textContent = '开始订阅';
    document.getElementById('subscriptionStatus').textContent = '未订阅';
    document.getElementById('subscriptionStatus').className = 'badge bg-light text-dark';

    addRecentEvent('info', `停止订阅: ${topic}`);
    updateMessageStats();
}

// 添加实时消息
function addRealtimeMessage(data) {
    const container = document.getElementById('realtimeMessages');

    // 移除"正在监听"提示
    if (container.children.length === 1 && container.children[0].textContent.includes('正在监听')) {
        container.innerHTML = '';
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = 'message-item';
    messageDiv.innerHTML = `
        <div class="message-topic">${data.topic}</div>
        <div class="message-payload">${truncateText(data.payload, 100)}</div>
        <div class="message-meta">
            <span class="text-muted">${formatDateTime(data.timestamp)}</span>
            ${data.clientId ? `<span class="ms-2 text-info">${data.clientId}</span>` : ''}
        </div>
    `;

    // 添加到顶部
    container.insertBefore(messageDiv, container.firstChild);

    // 限制显示数量
    while (container.children.length > 50) {
        container.removeChild(container.lastChild);
    }

    // 滚动到顶部
    container.scrollTop = 0;
}

// 添加消息到历史记录
function addMessageToHistory(message) {
    message.id = Date.now() + Math.random();
    messagesData.history.unshift(message);

    // 限制历史记录数量
    if (messagesData.history.length > 1000) {
        messagesData.history = messagesData.history.slice(0, 1000);
    }

    // 重新应用过滤器和渲染
    applyMessageFilters();
    renderMessageHistory();
}

// 更新消息统计
function updateMessageStats() {
    document.getElementById('publishedCount').textContent = messagesData.publishedCount;
    document.getElementById('receivedCount').textContent = messagesData.receivedCount;
    document.getElementById('subscriptionCount').textContent = messagesData.subscriptionCount;
}

// 应用消息过滤器
function applyMessageFilters() {
    const topicFilter = document.getElementById('messageTopicFilter').value.toLowerCase();
    const typeFilter = document.getElementById('messageTypeFilter').value;
    const timeFilter = document.getElementById('messageTimeFilter').value;

    messagesData.filteredHistory = messagesData.history.filter(message => {
        // Topic过滤
        const matchesTopic = !topicFilter || message.topic.toLowerCase().includes(topicFilter);

        // 类型过滤
        const matchesType = !typeFilter || message.type === typeFilter;

        // 时间过滤
        let matchesTime = true;
        if (timeFilter) {
            const messageTime = new Date(message.timestamp);
            const now = new Date();
            const diffMs = now - messageTime;

            switch (timeFilter) {
                case '1h':
                    matchesTime = diffMs <= 60 * 60 * 1000;
                    break;
                case '24h':
                    matchesTime = diffMs <= 24 * 60 * 60 * 1000;
                    break;
                case '7d':
                    matchesTime = diffMs <= 7 * 24 * 60 * 60 * 1000;
                    break;
            }
        }

        return matchesTopic && matchesType && matchesTime;
    });

    // 计算分页
    messagesData.pageSize = parseInt(document.getElementById('messagePageSize').value);
    messagesData.totalPages = Math.ceil(messagesData.filteredHistory.length / messagesData.pageSize);

    // 确保当前页在有效范围内
    if (messagesData.currentPage > messagesData.totalPages) {
        messagesData.currentPage = Math.max(1, messagesData.totalPages);
    }
}

// 渲染消息历史
function renderMessageHistory() {
    const tbody = document.getElementById('messageHistoryTableBody');

    if (messagesData.filteredHistory.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="fas fa-info-circle me-2"></i>暂无消息历史
                </td>
            </tr>
        `;
        renderMessagePagination();
        return;
    }

    // 计算当前页数据
    const startIndex = (messagesData.currentPage - 1) * messagesData.pageSize;
    const endIndex = startIndex + messagesData.pageSize;
    const pageData = messagesData.filteredHistory.slice(startIndex, endIndex);

    tbody.innerHTML = pageData.map(message => {
        const typeClass = message.type === 'published' ? 'text-primary' : 'text-success';
        const typeIcon = message.type === 'published' ? 'fas fa-arrow-up' : 'fas fa-arrow-down';
        const typeText = message.type === 'published' ? '发布' : '接收';

        return `
            <tr>
                <td>
                    <span class="${typeClass}">
                        <i class="${typeIcon} me-1"></i>${typeText}
                    </span>
                </td>
                <td><code>${message.topic}</code></td>
                <td>
                    <div class="text-truncate" style="max-width: 300px;" title="${message.payload}">
                        ${message.payload}
                    </div>
                </td>
                <td>${message.qos || '-'}</td>
                <td>${formatDateTime(message.timestamp)}</td>
                <td>
                    <button class="btn btn-info btn-sm" onclick="showMessageDetails('${message.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');

    renderMessagePagination();
}

// 渲染消息分页
function renderMessagePagination() {
    const pagination = document.getElementById('messageHistoryPagination');

    if (messagesData.totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHtml = '';

    // 上一页
    paginationHtml += `
        <li class="page-item ${messagesData.currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToMessagePage(${messagesData.currentPage - 1})">上一页</a>
        </li>
    `;

    // 页码
    const startPage = Math.max(1, messagesData.currentPage - 2);
    const endPage = Math.min(messagesData.totalPages, messagesData.currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === messagesData.currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="goToMessagePage(${i})">${i}</a>
            </li>
        `;
    }

    // 下一页
    paginationHtml += `
        <li class="page-item ${messagesData.currentPage === messagesData.totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToMessagePage(${messagesData.currentPage + 1})">下一页</a>
        </li>
    `;

    pagination.innerHTML = paginationHtml;
}

// 跳转到指定页
function goToMessagePage(page) {
    if (page >= 1 && page <= messagesData.totalPages) {
        messagesData.currentPage = page;
        renderMessageHistory();
    }
}

// 过滤消息
function filterMessages() {
    messagesData.currentPage = 1;
    applyMessageFilters();
    renderMessageHistory();
}

// 重置消息过滤器
function resetMessageFilters() {
    document.getElementById('messageTopicFilter').value = '';
    document.getElementById('messageTypeFilter').value = '';
    document.getElementById('messageTimeFilter').value = '';
    document.getElementById('messagePageSize').value = '20';

    messagesData.currentPage = 1;
    messagesData.pageSize = 20;
    applyMessageFilters();
    renderMessageHistory();
}

// 显示消息详情
function showMessageDetails(messageId) {
    const message = messagesData.history.find(m => m.id == messageId);
    if (!message) return;

    messagesData.currentMessageForDetails = message;

    // 填充详情数据
    document.getElementById('detailMessageType').textContent = message.type === 'published' ? '发布消息' : '接收消息';
    document.getElementById('detailMessageQos').textContent = message.qos || '-';
    document.getElementById('detailMessageTopic').textContent = message.topic;
    document.getElementById('detailMessageTime').textContent = formatDateTime(message.timestamp);
    document.getElementById('detailMessageSize').textContent = formatBytes(new Blob([message.payload]).size);
    document.getElementById('detailMessagePayload').textContent = message.payload;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('messageDetailsModal'));
    modal.show();
}

// 复制消息内容
function copyMessageContent() {
    if (messagesData.currentMessageForDetails) {
        copyToClipboard(messagesData.currentMessageForDetails.payload);
    }
}

// 格式化消息内容
function formatPayload() {
    const payload = document.getElementById('publishPayload').value;
    const format = document.getElementById('publishFormat').value;

    try {
        let formatted = payload;

        if (format === 'json') {
            const parsed = JSON.parse(payload);
            formatted = JSON.stringify(parsed, null, 2);
        } else if (format === 'xml') {
            // 简单的XML格式化（实际项目中可能需要更复杂的XML解析器）
            formatted = payload.replace(/></g, '>\n<');
        }

        document.getElementById('publishPayload').value = formatted;
        addRecentEvent('success', '消息内容格式化完成');

    } catch (error) {
        addRecentEvent('error', '格式化失败: ' + error.message);
    }
}

// 验证消息内容
function validatePayload() {
    const payload = document.getElementById('publishPayload').value;
    const format = document.getElementById('publishFormat').value;

    try {
        if (format === 'json') {
            JSON.parse(payload);
            addRecentEvent('success', 'JSON格式验证通过');
        } else if (format === 'xml') {
            // 简单的XML验证
            const parser = new DOMParser();
            const doc = parser.parseFromString(payload, 'text/xml');
            if (doc.getElementsByTagName('parsererror').length > 0) {
                throw new Error('XML格式错误');
            }
            addRecentEvent('success', 'XML格式验证通过');
        } else {
            addRecentEvent('success', '文本格式验证通过');
        }

    } catch (error) {
        addRecentEvent('error', '格式验证失败: ' + error.message);
    }
}

// 清空发布表单
function clearPublishForm() {
    document.getElementById('publishTopic').value = '';
    document.getElementById('publishPayload').value = '';
    document.getElementById('publishQos').value = '1';
    document.getElementById('publishRetain').checked = false;
    document.getElementById('publishFormat').value = 'json';

    addRecentEvent('info', '发布表单已清空');
}

// 选择Topic
function selectTopic(topic) {
    document.getElementById('publishTopic').value = topic;
    addRecentEvent('info', `已选择Topic: ${topic}`);
}

// 渲染常用Topic
function renderCommonTopics() {
    const container = document.getElementById('commonTopicsList');
    container.innerHTML = messagesData.commonTopics.map(topic =>
        `<span class="badge bg-secondary topic-badge" onclick="selectTopic('${topic}')" style="cursor: pointer;">
            ${topic}
        </span>`
    ).join('');
}

// 添加自定义Topic
function addCustomTopic() {
    const modal = new bootstrap.Modal(document.getElementById('addTopicModal'));
    modal.show();
}

// 确认添加Topic
function confirmAddTopic() {
    const topic = document.getElementById('customTopicInput').value.trim();
    const description = document.getElementById('customTopicDescription').value.trim();

    if (!topic) {
        addRecentEvent('error', '请输入Topic名称');
        return;
    }

    if (messagesData.commonTopics.includes(topic)) {
        addRecentEvent('warning', 'Topic已存在');
        return;
    }

    messagesData.commonTopics.push(topic);
    renderCommonTopics();

    // 清空表单
    document.getElementById('customTopicInput').value = '';
    document.getElementById('customTopicDescription').value = '';

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('addTopicModal'));
    modal.hide();

    addRecentEvent('success', `已添加常用Topic: ${topic}`);
}

// 清空订阅
function clearSubscriptions() {
    if (messagesData.isSubscribed) {
        stopSubscription();
    }

    document.getElementById('subscribeTopic').value = '';
    document.getElementById('realtimeMessages').innerHTML = `
        <div class="text-center text-muted py-4">
            <i class="fas fa-info-circle me-2"></i>暂无实时消息
        </div>
    `;

    addRecentEvent('info', '订阅已清空');
}

// 清空消息历史
function clearMessageHistory() {
    if (confirm('确定要清空所有消息历史吗？此操作不可撤销！')) {
        messagesData.history = [];
        messagesData.filteredHistory = [];
        messagesData.publishedCount = 0;
        messagesData.receivedCount = 0;

        updateMessageStats();
        renderMessageHistory();

        addRecentEvent('success', '消息历史已清空');
    }
}

// 导出消息
function exportMessages() {
    if (messagesData.filteredHistory.length === 0) {
        addRecentEvent('warning', '没有可导出的消息');
        return;
    }

    const data = messagesData.filteredHistory.map(message => ({
        类型: message.type === 'published' ? '发布' : '接收',
        Topic: message.topic,
        消息内容: message.payload,
        QoS: message.qos || '-',
        时间: formatDateTime(message.timestamp),
        ClientID: message.clientId || '-'
    }));

    const csv = convertToCSV(data);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `mqtt_messages_${new Date().toISOString().slice(0, 10)}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        addRecentEvent('success', '消息导出完成');
    }
}

// 工具函数：Topic匹配
function isTopicMatch(messageTopic, subscriptionTopic) {
    // 简单的MQTT Topic匹配实现
    const subParts = subscriptionTopic.split('/');
    const msgParts = messageTopic.split('/');

    for (let i = 0; i < subParts.length; i++) {
        if (subParts[i] === '#') {
            return true; // # 匹配所有后续级别
        }
        if (subParts[i] === '+') {
            continue; // + 匹配单个级别
        }
        if (subParts[i] !== msgParts[i]) {
            return false;
        }
    }

    return subParts.length === msgParts.length;
}

// 工具函数：截断文本
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// 工具函数：格式化字节数
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函数：转换为CSV
function convertToCSV(data) {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    return '\uFEFF' + csvContent; // 添加BOM以支持中文
}

// 修改初始化页面函数
const originalInitializePage = initializePage;
function initializePage() {
    originalInitializePage();
    initializeMessageManagement();
}

// ==================== Token管理功能 ====================

// 初始化Token管理
function initializeTokenManagement() {
    // 绑定表单提交事件
    document.getElementById('tokenGenerateForm').addEventListener('submit', function(e) {
        e.preventDefault();
        generateSingleToken();
    });

    // 加载Token数据
    loadTokensData();
}

// 加载Token数据
async function loadTokensData() {
    try {
        showTokensLoading(true);

        const response = await fetch(`${DEVICE_TOKEN_API}`, {
            headers: { 'authorization': AUTHORIZATION }
        });

        if (response.ok) {
            const data = await response.json();
            await processTokensData(data);
            updateTokensStats();
            renderTokensTable();
        } else {
            showTokensError('加载Token数据失败');
        }

    } catch (error) {
        console.error('加载Token数据失败:', error);
        showTokensError('网络错误: ' + error.message);
    } finally {
        showTokensLoading(false);
    }
}

// 处理Token数据
async function processTokensData(data) {
    tokensData.list = [];

    if (data && typeof data === 'object') {
        for (const [token, deviceId] of Object.entries(data)) {
            // 验证Token状态
            const isValid = await validateTokenStatus(token);
            // 检查在线状态
            const isOnline = await checkTokenOnlineStatus(token);

            tokensData.list.push({
                token: token,
                deviceId: deviceId,
                deviceName: `设备_${deviceId}`, // 可以从其他API获取真实设备名称
                deviceType: 'sensor', // 默认类型，可以从设备信息获取
                isValid: isValid,
                isOnline: isOnline,
                createTime: new Date().toISOString() // 实际应该从API获取
            });
        }
    }

    // 应用过滤器
    applyTokensFilter();
}

// 验证Token状态
async function validateTokenStatus(token) {
    try {
        const response = await fetch(`${DEVICE_TOKEN_API}/validate?token=${encodeURIComponent(token)}`, {
            headers: { 'authorization': AUTHORIZATION }
        });

        if (response.ok) {
            const result = await response.json();
            return result.valid;
        }
    } catch (error) {
        console.error('验证Token失败:', error);
    }
    return false;
}

// 检查Token在线状态
async function checkTokenOnlineStatus(token) {
    try {
        const response = await fetch(`${DEVICE_TOKEN_API}/connection-status`, {
            headers: { 'authorization': AUTHORIZATION }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.connectedTokens) {
                return data.connectedTokens.some(connectedToken => connectedToken.token === token);
            }
        }
    } catch (error) {
        console.error('检查在线状态失败:', error);
    }
    return false;
}

// 更新Token统计
function updateTokensStats() {
    const totalCount = tokensData.list.length;
    const validCount = tokensData.list.filter(token => token.isValid).length;
    const onlineCount = tokensData.list.filter(token => token.isOnline).length;

    document.getElementById('tokensTotalCount').textContent = totalCount;
    document.getElementById('tokensValidCount').textContent = validCount;
    document.getElementById('tokensOnlineCount').textContent = onlineCount;
    document.getElementById('tokensLastUpdate').textContent = new Date().toLocaleTimeString();
}

// 应用Token过滤器
function applyTokensFilter() {
    const searchText = document.getElementById('tokenSearchInput').value.toLowerCase();
    const statusFilter = document.getElementById('tokenStatusFilter').value;
    const onlineFilter = document.getElementById('tokenOnlineFilter').value;
    const typeFilter = document.getElementById('tokenTypeFilter').value;

    tokensData.filteredList = tokensData.list.filter(token => {
        // 搜索过滤
        const matchesSearch = !searchText ||
            token.deviceId.toLowerCase().includes(searchText) ||
            token.token.toLowerCase().includes(searchText) ||
            token.deviceName.toLowerCase().includes(searchText);

        // 状态过滤
        const matchesStatus = !statusFilter ||
            (statusFilter === 'valid' && token.isValid) ||
            (statusFilter === 'invalid' && !token.isValid);

        // 在线状态过滤
        const matchesOnline = !onlineFilter ||
            (onlineFilter === 'online' && token.isOnline) ||
            (onlineFilter === 'offline' && !token.isOnline);

        // 类型过滤
        const matchesType = !typeFilter || token.deviceType === typeFilter;

        return matchesSearch && matchesStatus && matchesOnline && matchesType;
    });

    // 计算分页
    tokensData.pageSize = 20; // 固定每页20条
    tokensData.totalPages = Math.ceil(tokensData.filteredList.length / tokensData.pageSize);

    // 确保当前页在有效范围内
    if (tokensData.currentPage > tokensData.totalPages) {
        tokensData.currentPage = Math.max(1, tokensData.totalPages);
    }
}

// 渲染Token表格
function renderTokensTable() {
    const tbody = document.getElementById('tokensTableBody');

    if (tokensData.filteredList.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted py-4">
                    <i class="fas fa-info-circle me-2"></i>暂无Token数据
                </td>
            </tr>
        `;
        renderTokensPagination();
        return;
    }

    // 计算当前页数据
    const startIndex = (tokensData.currentPage - 1) * tokensData.pageSize;
    const endIndex = startIndex + tokensData.pageSize;
    const pageData = tokensData.filteredList.slice(startIndex, endIndex);

    tbody.innerHTML = pageData.map(token => {
        const isSelected = tokensData.selectedTokens.has(token.token);
        const statusClass = token.isValid ? 'status-online' : 'status-offline';
        const statusText = token.isValid ? '有效' : '无效';
        const onlineClass = token.isOnline ? 'status-online' : 'status-offline';
        const onlineText = token.isOnline ? '在线' : '离线';

        return `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input token-checkbox"
                           value="${token.token}" ${isSelected ? 'checked' : ''}
                           onchange="toggleTokenSelection('${token.token}')">
                </td>
                <td><strong>${token.deviceId}</strong></td>
                <td>${token.deviceName}</td>
                <td>
                    <code class="text-muted">${token.token.substring(0, 20)}...</code>
                    <button class="btn btn-outline-secondary btn-sm ms-1" onclick="copyToClipboard('${token.token}')">
                        <i class="fas fa-copy"></i>
                    </button>
                </td>
                <td>
                    <span class="badge bg-secondary">${token.deviceType}</span>
                </td>
                <td>
                    <span class="${statusClass}">${statusText}</span>
                </td>
                <td>
                    <span class="${onlineClass}">${onlineText}</span>
                </td>
                <td>${formatDateTime(token.createTime)}</td>
                <td>
                    <button class="btn btn-info btn-sm me-1" onclick="showTokenDetails('${token.token}')">
                        <i class="fas fa-info-circle"></i>
                    </button>
                    <button class="btn btn-warning btn-sm me-1" onclick="validateTokenManually('${token.token}')">
                        <i class="fas fa-check-circle"></i>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="showDeleteTokenModal('${token.token}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');

    renderTokensPagination();
    updateTokenBatchOperationsVisibility();
}

// 渲染Token分页
function renderTokensPagination() {
    const pagination = document.getElementById('tokensPagination');

    if (tokensData.totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHtml = '';

    // 上一页
    paginationHtml += `
        <li class="page-item ${tokensData.currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToTokenPage(${tokensData.currentPage - 1})">上一页</a>
        </li>
    `;

    // 页码
    const startPage = Math.max(1, tokensData.currentPage - 2);
    const endPage = Math.min(tokensData.totalPages, tokensData.currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === tokensData.currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="goToTokenPage(${i})">${i}</a>
            </li>
        `;
    }

    // 下一页
    paginationHtml += `
        <li class="page-item ${tokensData.currentPage === tokensData.totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToTokenPage(${tokensData.currentPage + 1})">下一页</a>
        </li>
    `;

    pagination.innerHTML = paginationHtml;
}

// 跳转到指定页
function goToTokenPage(page) {
    if (page >= 1 && page <= tokensData.totalPages) {
        tokensData.currentPage = page;
        renderTokensTable();
    }
}

// 显示加载状态
function showTokensLoading(show) {
    const spinner = document.getElementById('tokensLoadingSpinner');
    const error = document.getElementById('tokensErrorMessage');
    const empty = document.getElementById('tokensEmptyMessage');

    if (show) {
        spinner.style.display = 'block';
        error.style.display = 'none';
        empty.style.display = 'none';
    } else {
        spinner.style.display = 'none';
    }
}

// 显示错误信息
function showTokensError(message) {
    const spinner = document.getElementById('tokensLoadingSpinner');
    const error = document.getElementById('tokensErrorMessage');
    const empty = document.getElementById('tokensEmptyMessage');

    spinner.style.display = 'none';
    error.style.display = 'block';
    error.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${message}`;
    empty.style.display = 'none';
}

// 生成单个Token
async function generateSingleToken() {
    const deviceId = document.getElementById('tokenDeviceId').value.trim();
    const deviceType = document.getElementById('tokenDeviceType').value;
    const deviceName = document.getElementById('tokenDeviceName').value.trim();

    if (!deviceId) {
        addRecentEvent('error', '请输入设备ID');
        return;
    }

    try {
        const response = await fetch(`${DEVICE_TOKEN_API}/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'authorization': AUTHORIZATION
            },
            body: new URLSearchParams({
                deviceId: deviceId,
                deviceType: deviceType
            })
        });

        const result = await response.json();

        if (result.success) {
            addRecentEvent('success', `Token生成成功: ${deviceId}`);

            // 清空表单
            clearTokenForm();

            // 刷新Token列表
            setTimeout(() => {
                loadTokensData();
            }, 1000);
        } else {
            addRecentEvent('error', 'Token生成失败: ' + result.message);
        }

    } catch (error) {
        console.error('生成Token失败:', error);
        addRecentEvent('error', '生成Token失败: ' + error.message);
    }
}

// 清空Token表单
function clearTokenForm() {
    document.getElementById('tokenDeviceId').value = '';
    document.getElementById('tokenDeviceType').value = 'sensor';
    document.getElementById('tokenDeviceName').value = '';
}

// 刷新Token数据
function refreshTokens() {
    const refreshIcon = document.getElementById('tokensRefreshIcon');
    refreshIcon.classList.add('refresh-btn');

    loadTokensData().finally(() => {
        refreshIcon.classList.remove('refresh-btn');
    });
}

// 搜索Token
function searchTokens() {
    tokensData.currentPage = 1;
    applyTokensFilter();
    renderTokensTable();
}

// 重置Token过滤器
function resetTokenFilters() {
    document.getElementById('tokenSearchInput').value = '';
    document.getElementById('tokenStatusFilter').value = '';
    document.getElementById('tokenOnlineFilter').value = '';
    document.getElementById('tokenTypeFilter').value = '';

    tokensData.currentPage = 1;
    applyTokensFilter();
    renderTokensTable();
}

// 切换Token选择
function toggleTokenSelection(token) {
    if (tokensData.selectedTokens.has(token)) {
        tokensData.selectedTokens.delete(token);
    } else {
        tokensData.selectedTokens.add(token);
    }
    updateTokenBatchOperationsVisibility();
    updateSelectAllTokensCheckbox();
}

// 切换全选Token
function toggleAllTokenSelection() {
    const selectAll = document.getElementById('selectAllTokens');
    const checkboxes = document.querySelectorAll('.token-checkbox');

    if (selectAll.checked) {
        // 全选当前页
        checkboxes.forEach(checkbox => {
            tokensData.selectedTokens.add(checkbox.value);
            checkbox.checked = true;
        });
    } else {
        // 取消全选
        checkboxes.forEach(checkbox => {
            tokensData.selectedTokens.delete(checkbox.value);
            checkbox.checked = false;
        });
    }

    updateTokenBatchOperationsVisibility();
}

// 更新全选复选框状态
function updateSelectAllTokensCheckbox() {
    const selectAll = document.getElementById('selectAllTokens');
    const checkboxes = document.querySelectorAll('.token-checkbox');
    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

    if (checkedCount === 0) {
        selectAll.checked = false;
        selectAll.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAll.checked = true;
        selectAll.indeterminate = false;
    } else {
        selectAll.checked = false;
        selectAll.indeterminate = true;
    }
}

// 更新批量操作可见性
function updateTokenBatchOperationsVisibility() {
    const batchRow = document.getElementById('tokenBatchOperationsRow');
    const selectedCount = tokensData.selectedTokens.size;

    if (selectedCount > 0) {
        batchRow.style.display = 'block';
        document.getElementById('selectedTokensCount').textContent = selectedCount;
    } else {
        batchRow.style.display = 'none';
    }
}

// 清除Token选择
function clearTokenSelection() {
    tokensData.selectedTokens.clear();
    document.querySelectorAll('.token-checkbox').forEach(cb => cb.checked = false);
    document.getElementById('selectAllTokens').checked = false;
    updateTokenBatchOperationsVisibility();
}

// 显示Token详情
function showTokenDetails(token) {
    const tokenData = tokensData.list.find(t => t.token === token);
    if (!tokenData) return;

    tokensData.currentTokenForDetails = tokenData;

    // 填充详情数据
    document.getElementById('detailTokenDeviceId').textContent = tokenData.deviceId;
    document.getElementById('detailTokenDeviceName').textContent = tokenData.deviceName;
    document.getElementById('detailTokenDeviceType').textContent = tokenData.deviceType;
    document.getElementById('detailTokenStatus').textContent = tokenData.isValid ? '有效' : '无效';
    document.getElementById('detailTokenValue').textContent = tokenData.token;
    document.getElementById('detailTokenCreateTime').textContent = formatDateTime(tokenData.createTime);
    document.getElementById('detailTokenOnlineStatus').textContent = tokenData.isOnline ? '在线' : '离线';

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('tokenDetailsModal'));
    modal.show();
}

// 复制Token值
function copyTokenValue() {
    if (tokensData.currentTokenForDetails) {
        copyToClipboard(tokensData.currentTokenForDetails.token);
    }
}

// 从详情页验证Token
function validateTokenFromDetails() {
    if (tokensData.currentTokenForDetails) {
        validateTokenManually(tokensData.currentTokenForDetails.token);
    }
}

// 从详情页删除Token
function deleteTokenFromDetails() {
    if (tokensData.currentTokenForDetails) {
        showDeleteTokenModal(tokensData.currentTokenForDetails.token);
        // 关闭详情模态框
        const detailsModal = bootstrap.Modal.getInstance(document.getElementById('tokenDetailsModal'));
        if (detailsModal) {
            detailsModal.hide();
        }
    }
}

// 手动验证Token
async function validateTokenManually(token) {
    try {
        const isValid = await validateTokenStatus(token);
        addRecentEvent(isValid ? 'success' : 'warning', `Token ${isValid ? '有效' : '无效'}: ${token.substring(0, 20)}...`);

        // 更新本地数据
        const tokenData = tokensData.list.find(t => t.token === token);
        if (tokenData) {
            tokenData.isValid = isValid;
            renderTokensTable();
        }

    } catch (error) {
        addRecentEvent('error', 'Token验证失败: ' + error.message);
    }
}

// 显示删除Token模态框
function showDeleteTokenModal(token) {
    const tokenData = tokensData.list.find(t => t.token === token);
    if (!tokenData) return;

    tokensData.currentTokenForDelete = tokenData;

    // 填充Token信息
    document.getElementById('deleteTokenInfo').innerHTML = `
        <strong>设备ID:</strong> ${tokenData.deviceId}<br>
        <strong>设备名称:</strong> ${tokenData.deviceName}<br>
        <strong>Token:</strong> ${tokenData.token.substring(0, 30)}...
    `;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('deleteTokenModal'));
    modal.show();
}

// 确认删除Token
async function confirmDeleteToken() {
    if (!tokensData.currentTokenForDelete) return;

    const tokenData = tokensData.currentTokenForDelete;

    try {
        const response = await fetch(`${DEVICE_TOKEN_API}?token=${encodeURIComponent(tokenData.token)}`, {
            method: 'DELETE',
            headers: { 'authorization': AUTHORIZATION }
        });

        const result = await response.json();

        if (result.success) {
            addRecentEvent('success', `Token删除成功: ${tokenData.deviceId}`);

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteTokenModal'));
            modal.hide();

            // 刷新Token列表
            setTimeout(() => {
                loadTokensData();
            }, 1000);
        } else {
            addRecentEvent('error', 'Token删除失败: ' + result.message);
        }

    } catch (error) {
        console.error('删除Token失败:', error);
        addRecentEvent('error', '删除Token失败: ' + error.message);
    }
}

// 批量验证Token
async function batchValidateTokens() {
    const selectedTokens = Array.from(tokensData.selectedTokens);

    if (selectedTokens.length === 0) return;

    addRecentEvent('info', `开始批量验证 ${selectedTokens.length} 个Token...`);

    try {
        let validCount = 0;
        let invalidCount = 0;

        for (const token of selectedTokens) {
            const isValid = await validateTokenStatus(token);
            if (isValid) {
                validCount++;
            } else {
                invalidCount++;
            }

            // 更新本地数据
            const tokenData = tokensData.list.find(t => t.token === token);
            if (tokenData) {
                tokenData.isValid = isValid;
            }
        }

        addRecentEvent('success', `批量验证完成: ${validCount} 个有效, ${invalidCount} 个无效`);
        renderTokensTable();

    } catch (error) {
        addRecentEvent('error', '批量验证失败: ' + error.message);
    }
}

// 批量删除Token
function batchDeleteTokens() {
    if (tokensData.selectedTokens.size === 0) return;

    document.getElementById('batchDeleteTokenCount').textContent = tokensData.selectedTokens.size;

    const modal = new bootstrap.Modal(document.getElementById('batchDeleteTokenModal'));
    modal.show();
}

// 确认批量删除Token
async function confirmBatchDeleteTokens() {
    const selectedTokens = Array.from(tokensData.selectedTokens);

    if (selectedTokens.length === 0) return;

    try {
        const promises = selectedTokens.map(token =>
            fetch(`${DEVICE_TOKEN_API}?token=${encodeURIComponent(token)}`, {
                method: 'DELETE',
                headers: { 'authorization': AUTHORIZATION }
            }).then(response => response.json())
        );

        const results = await Promise.all(promises);
        const successCount = results.filter(r => r.success).length;

        addRecentEvent('success', `批量删除完成: ${successCount}/${selectedTokens.length} 个Token`);

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('batchDeleteTokenModal'));
        modal.hide();

        // 清除选择并刷新列表
        clearTokenSelection();
        setTimeout(() => {
            loadTokensData();
        }, 1000);

    } catch (error) {
        console.error('批量删除失败:', error);
        addRecentEvent('error', '批量删除失败: ' + error.message);
    }
}

// 显示批量生成模态框
function showBatchGenerateModal() {
    const prefix = document.getElementById('batchDevicePrefix').value.trim();
    const startNumber = parseInt(document.getElementById('batchStartNumber').value);
    const count = parseInt(document.getElementById('batchCount').value);
    const deviceType = document.getElementById('batchDeviceType').value;

    if (!prefix) {
        addRecentEvent('error', '请输入设备ID前缀');
        return;
    }

    if (count > 100) {
        addRecentEvent('error', '批量生成数量不能超过100个');
        return;
    }

    // 填充批量生成信息
    document.getElementById('batchGenerateInfo').innerHTML = `
        <strong>设备ID前缀:</strong> ${prefix}<br>
        <strong>起始编号:</strong> ${startNumber}<br>
        <strong>生成数量:</strong> ${count}<br>
        <strong>设备类型:</strong> ${deviceType}<br>
        <strong>设备ID示例:</strong> ${prefix}${startNumber}, ${prefix}${startNumber + 1}, ...
    `;

    const modal = new bootstrap.Modal(document.getElementById('batchGenerateModal'));
    modal.show();
}

// 确认批量生成Token
async function confirmBatchGenerate() {
    const prefix = document.getElementById('batchDevicePrefix').value.trim();
    const startNumber = parseInt(document.getElementById('batchStartNumber').value);
    const count = parseInt(document.getElementById('batchCount').value);
    const deviceType = document.getElementById('batchDeviceType').value;

    try {
        addRecentEvent('info', `开始批量生成 ${count} 个Token...`);

        let successCount = 0;
        let failCount = 0;

        for (let i = 0; i < count; i++) {
            const deviceId = `${prefix}${startNumber + i}`;

            try {
                const response = await fetch(`${DEVICE_TOKEN_API}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'authorization': AUTHORIZATION
                    },
                    body: new URLSearchParams({
                        deviceId: deviceId,
                        deviceType: deviceType
                    })
                });

                const result = await response.json();

                if (result.success) {
                    successCount++;
                } else {
                    failCount++;
                }

            } catch (error) {
                failCount++;
                console.error(`生成Token失败 (${deviceId}):`, error);
            }

            // 添加小延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        addRecentEvent('success', `批量生成完成: ${successCount} 个成功, ${failCount} 个失败`);

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('batchGenerateModal'));
        modal.hide();

        // 清空批量生成表单
        document.getElementById('batchDevicePrefix').value = '';
        document.getElementById('batchStartNumber').value = '1';
        document.getElementById('batchCount').value = '10';

        // 刷新Token列表
        setTimeout(() => {
            loadTokensData();
        }, 1000);

    } catch (error) {
        console.error('批量生成失败:', error);
        addRecentEvent('error', '批量生成失败: ' + error.message);
    }
}

// 清理无效Token
async function cleanupInvalidTokens() {
    if (confirm('确定要清理所有无效Token吗？此操作不可撤销！')) {
        try {
            addRecentEvent('info', '开始清理无效Token...');

            const invalidTokens = tokensData.list.filter(token => !token.isValid);

            if (invalidTokens.length === 0) {
                addRecentEvent('info', '没有发现无效Token');
                return;
            }

            let successCount = 0;
            let failCount = 0;

            for (const tokenData of invalidTokens) {
                try {
                    const response = await fetch(`${DEVICE_TOKEN_API}?token=${encodeURIComponent(tokenData.token)}`, {
                        method: 'DELETE',
                        headers: { 'authorization': AUTHORIZATION }
                    });

                    const result = await response.json();

                    if (result.success) {
                        successCount++;
                    } else {
                        failCount++;
                    }

                } catch (error) {
                    failCount++;
                    console.error(`删除无效Token失败 (${tokenData.deviceId}):`, error);
                }
            }

            addRecentEvent('success', `清理完成: ${successCount} 个成功, ${failCount} 个失败`);

            // 刷新Token列表
            setTimeout(() => {
                loadTokensData();
            }, 1000);

        } catch (error) {
            console.error('清理无效Token失败:', error);
            addRecentEvent('error', '清理无效Token失败: ' + error.message);
        }
    }
}

// 显示删除所有Token模态框
function showDeleteAllTokensModal() {
    if (tokensData.list.length === 0) {
        addRecentEvent('warning', '当前没有Token');
        return;
    }

    if (confirm(`确定要删除所有 ${tokensData.list.length} 个Token吗？此操作不可撤销！`)) {
        // 选择所有Token
        tokensData.selectedTokens.clear();
        tokensData.list.forEach(token => {
            tokensData.selectedTokens.add(token.token);
        });

        // 显示批量删除模态框
        batchDeleteTokens();
    }
}

// ==================== 系统工具相关函数 ====================

// 检查Redis健康状态
async function checkRedisHealth() {
    try {
        const response = await fetch(`${MQTT_MANAGEMENT_API}/tools/redis-health`, {
            method: 'GET',
            headers: {
                'authorization': AUTHORIZATION
            }
        });

        const result = await response.json();

        if (result.success) {
            const redis = result.redis;

            // 更新Redis状态显示
            document.getElementById('redis-status').textContent = redis.status === 'UP' ? '正常' : '异常';
            document.getElementById('redis-status').className = redis.status === 'UP' ? 'text-success' : 'text-danger';
            document.getElementById('redis-info').textContent = `连接: ${redis.connections}`;

            // 更新Redis健康徽章
            const badge = document.getElementById('redis-health-badge');
            badge.textContent = redis.status === 'UP' ? '健康' : '异常';
            badge.className = redis.status === 'UP' ? 'badge bg-success me-2' : 'badge bg-danger me-2';

            // 更新详细信息
            document.getElementById('redis-ping').textContent = redis.ping;
            document.getElementById('redis-connections').textContent = redis.connections;
            document.getElementById('redis-memory').textContent = redis.memory_usage;
            document.getElementById('redis-uptime').textContent = redis.uptime;

            addRecentEvent('success', 'Redis健康检查完成');
        } else {
            addRecentEvent('error', 'Redis健康检查失败: ' + result.message);
        }
    } catch (error) {
        addRecentEvent('error', 'Redis健康检查失败: ' + error.message);
    }
}

// 清理MQTT连接
async function clearMqttConnections() {
    if (!confirm('确定要清理无效的MQTT连接吗？')) {
        return;
    }

    try {
        const response = await fetch(`${MQTT_MANAGEMENT_API}/tools/clear-connections`, {
            method: 'POST',
            headers: {
                'authorization': AUTHORIZATION
            }
        });

        const result = await response.json();

        if (result.success) {
            addRecentEvent('success', `清理完成，清理了 ${result.clearedConnections} 个无效连接`);

            // 更新连接统计
            document.getElementById('last-cleanup').textContent = new Date().toLocaleString();

            // 刷新连接管理数据
            if (typeof loadConnections === 'function') {
                loadConnections();
            }
        } else {
            addRecentEvent('error', '清理MQTT连接失败: ' + result.message);
        }
    } catch (error) {
        addRecentEvent('error', '清理MQTT连接失败: ' + error.message);
    }
}

// 执行数据同步检查
async function performSyncCheck() {
    try {
        const response = await fetch(`${MQTT_MANAGEMENT_API}/tools/sync-check`, {
            method: 'POST',
            headers: {
                'authorization': AUTHORIZATION
            }
        });

        const result = await response.json();

        if (result.success) {
            const syncResult = result.syncResult;

            // 更新同步状态显示
            document.getElementById('db-devices').textContent = syncResult.database_devices;
            document.getElementById('redis-tokens').textContent = syncResult.redis_tokens;
            document.getElementById('mqtt-connections-count').textContent = syncResult.mqtt_connections;

            const inconsistencies = document.getElementById('inconsistencies');
            inconsistencies.textContent = syncResult.inconsistencies;
            inconsistencies.className = syncResult.inconsistencies > 0 ? 'text-warning' : 'text-success';

            addRecentEvent('success', `数据同步检查完成，发现 ${syncResult.inconsistencies} 个不一致项`);
        } else {
            addRecentEvent('error', '数据同步检查失败: ' + result.message);
        }
    } catch (error) {
        addRecentEvent('error', '数据同步检查失败: ' + error.message);
    }
}

// 获取系统诊断信息
async function getSystemDiagnostics() {
    try {
        const response = await fetch(`${MQTT_MANAGEMENT_API}/tools/diagnostics`, {
            method: 'GET',
            headers: {
                'authorization': AUTHORIZATION
            }
        });

        const result = await response.json();

        if (result.success) {
            const diagnostics = result.diagnostics;

            // 更新系统信息显示
            const jvm = diagnostics.jvm;
            document.getElementById('system-uptime').textContent = diagnostics.system.uptime;
            document.getElementById('java-version').textContent = diagnostics.system.java_version;
            document.getElementById('os-name').textContent = diagnostics.system.os_name;
            document.getElementById('processors').textContent = jvm.processors;

            // 显示诊断结果区域
            const diagnosticsSection = document.getElementById('diagnostics-section');
            diagnosticsSection.style.display = 'block';

            // 填充JVM诊断信息
            const jvmDiagnostics = document.getElementById('jvm-diagnostics');
            jvmDiagnostics.innerHTML = `
                <div>堆内存使用: ${jvm.heap_used}</div>
                <div>堆内存总量: ${jvm.heap_total}</div>
                <div>堆内存最大: ${jvm.heap_max}</div>
                <div>活跃线程: ${jvm.threads}</div>
                <div>处理器数量: ${jvm.processors}</div>
            `;

            // 填充MQTT诊断信息
            const mqttDiagnostics = document.getElementById('mqtt-diagnostics');
            const mqtt = diagnostics.mqtt;
            mqttDiagnostics.innerHTML = `
                <div>服务启用: ${mqtt.server_enabled ? '是' : '否'}</div>
                <div>端口: ${mqtt.port}</div>
                <div>认证启用: ${mqtt.auth_enabled ? '是' : '否'}</div>
                <div>调试模式: ${mqtt.debug_enabled ? '是' : '否'}</div>
                <div>WebSocket: ${mqtt.websocket_enabled ? '是' : '否'}</div>
            `;

            // 填充系统诊断信息
            const systemDiagnostics = document.getElementById('system-diagnostics');
            const system = diagnostics.system;
            systemDiagnostics.innerHTML = `
                <div>运行时间: ${system.uptime}</div>
                <div>Java版本: ${system.java_version}</div>
                <div>操作系统: ${system.os_name}</div>
                <div>系统架构: ${system.os_arch}</div>
            `;

            addRecentEvent('success', '系统诊断信息获取成功');
        } else {
            addRecentEvent('error', '获取系统诊断信息失败: ' + result.message);
        }
    } catch (error) {
        addRecentEvent('error', '获取系统诊断信息失败: ' + error.message);
    }
}

// 重启系统
async function restartSystem() {
    if (!confirm('确定要重启系统吗？这将断开所有连接并重启服务！')) {
        return;
    }

    try {
        const response = await fetch(`${MQTT_MANAGEMENT_API}/tools/restart`, {
            method: 'POST',
            headers: {
                'authorization': AUTHORIZATION
            }
        });

        const result = await response.json();

        if (result.success) {
            addRecentEvent('warning', `系统重启请求已提交，将在${result.restartDelay}后重启`);
        } else {
            addRecentEvent('error', '系统重启请求失败: ' + result.message);
        }
    } catch (error) {
        addRecentEvent('error', '系统重启请求失败: ' + error.message);
    }
}

// ==================== 配置管理相关函数 ====================

// 加载MQTT配置信息
async function loadMqttConfig() {
    try {
        const response = await fetch(`${MQTT_MANAGEMENT_API}/config`, {
            method: 'GET',
            headers: {
                'authorization': AUTHORIZATION
            }
        });

        const result = await response.json();

        if (result.success) {
            const config = result.config;

            // 更新基础配置
            document.getElementById('config-enabled').textContent = config.enabled ? '启用' : '禁用';
            document.getElementById('config-enabled').className = config.enabled ? 'badge bg-success' : 'badge bg-secondary';
            document.getElementById('config-port').textContent = config.port;
            document.getElementById('config-name').textContent = config.name;
            document.getElementById('config-heartbeat').textContent = config.heartbeatTimeout + 'ms';
            document.getElementById('config-read-buffer').textContent = config.readBufferSize;
            document.getElementById('config-max-message').textContent = config.maxBytesInMessage;

            // 更新功能配置
            document.getElementById('config-auth').textContent = config.authEnabled ? '启用' : '禁用';
            document.getElementById('config-auth').className = config.authEnabled ? 'badge bg-success' : 'badge bg-secondary';
            document.getElementById('config-debug').textContent = config.debugEnabled ? '启用' : '禁用';
            document.getElementById('config-debug').className = config.debugEnabled ? 'badge bg-info' : 'badge bg-secondary';
            document.getElementById('config-stat').textContent = config.statEnabled ? '启用' : '禁用';
            document.getElementById('config-stat').className = config.statEnabled ? 'badge bg-success' : 'badge bg-secondary';
            document.getElementById('config-web-port').textContent = config.webPort;
            document.getElementById('config-websocket').textContent = config.websocketEnabled ? '启用' : '禁用';
            document.getElementById('config-websocket').className = config.websocketEnabled ? 'badge bg-success' : 'badge bg-secondary';
            document.getElementById('config-http').textContent = config.httpEnabled ? '启用' : '禁用';
            document.getElementById('config-http').className = config.httpEnabled ? 'badge bg-success' : 'badge bg-secondary';

            // 更新网络配置
            document.getElementById('network-mqtt-port').textContent = config.port;
            document.getElementById('network-ws-port').textContent = config.webPort;
            document.getElementById('network-recv-buffer').textContent = config.readBufferSize;
            document.getElementById('network-send-buffer').textContent = config.readBufferSize;

            // 更新最后更新时间
            document.getElementById('config-last-update').textContent = new Date().toLocaleString();

            addRecentEvent('success', 'MQTT配置加载成功');
        } else {
            addRecentEvent('error', '加载MQTT配置失败: ' + result.message);
        }
    } catch (error) {
        addRecentEvent('error', '加载MQTT配置失败: ' + error.message);
    }
}

// 加载系统配置信息
async function loadSystemConfig() {
    try {
        // 获取系统诊断信息来填充系统配置
        const response = await fetch(`${MQTT_MANAGEMENT_API}/tools/diagnostics`, {
            method: 'GET',
            headers: {
                'authorization': AUTHORIZATION
            }
        });

        const result = await response.json();

        if (result.success) {
            const diagnostics = result.diagnostics;

            // 更新JVM配置
            const jvm = diagnostics.jvm;
            document.getElementById('jvm-version').textContent = diagnostics.system.java_version;
            document.getElementById('jvm-vendor').textContent = 'Oracle Corporation'; // 模拟数据
            document.getElementById('jvm-max-memory').textContent = jvm.heap_max;
            document.getElementById('jvm-total-memory').textContent = jvm.heap_total;
            document.getElementById('jvm-free-memory').textContent = (parseInt(jvm.heap_total) - parseInt(jvm.heap_used)) + 'MB';
            document.getElementById('jvm-processors').textContent = jvm.processors;

            // 更新系统配置
            const system = diagnostics.system;
            document.getElementById('system-os').textContent = system.os_name;
            document.getElementById('system-arch').textContent = system.os_arch;
            document.getElementById('system-version').textContent = '10.0'; // 模拟数据
            document.getElementById('system-user-home').textContent = '/home/<USER>'; // 模拟数据
            document.getElementById('system-user-dir').textContent = '/app'; // 模拟数据
            document.getElementById('system-temp-dir').textContent = '/tmp'; // 模拟数据

            addRecentEvent('success', '系统配置加载成功');
        } else {
            addRecentEvent('error', '加载系统配置失败: ' + result.message);
        }
    } catch (error) {
        addRecentEvent('error', '加载系统配置失败: ' + error.message);
    }
}

// 导出配置
function exportConfig() {
    // 收集所有配置信息
    const config = {
        mqtt: {
            enabled: document.getElementById('config-enabled').textContent,
            port: document.getElementById('config-port').textContent,
            name: document.getElementById('config-name').textContent,
            heartbeatTimeout: document.getElementById('config-heartbeat').textContent,
            readBufferSize: document.getElementById('config-read-buffer').textContent,
            maxBytesInMessage: document.getElementById('config-max-message').textContent,
            authEnabled: document.getElementById('config-auth').textContent,
            debugEnabled: document.getElementById('config-debug').textContent,
            statEnabled: document.getElementById('config-stat').textContent,
            webPort: document.getElementById('config-web-port').textContent,
            websocketEnabled: document.getElementById('config-websocket').textContent,
            httpEnabled: document.getElementById('config-http').textContent
        },
        jvm: {
            version: document.getElementById('jvm-version').textContent,
            vendor: document.getElementById('jvm-vendor').textContent,
            maxMemory: document.getElementById('jvm-max-memory').textContent,
            totalMemory: document.getElementById('jvm-total-memory').textContent,
            freeMemory: document.getElementById('jvm-free-memory').textContent,
            processors: document.getElementById('jvm-processors').textContent
        },
        system: {
            os: document.getElementById('system-os').textContent,
            arch: document.getElementById('system-arch').textContent,
            version: document.getElementById('system-version').textContent,
            userHome: document.getElementById('system-user-home').textContent,
            userDir: document.getElementById('system-user-dir').textContent,
            tempDir: document.getElementById('system-temp-dir').textContent
        },
        network: {
            mqttPort: document.getElementById('network-mqtt-port').textContent,
            sslPort: document.getElementById('network-ssl-port').textContent,
            wsPort: document.getElementById('network-ws-port').textContent,
            maxConnections: document.getElementById('network-max-connections').textContent,
            timeout: document.getElementById('network-timeout').textContent,
            keepalive: document.getElementById('network-keepalive').textContent,
            recvBuffer: document.getElementById('network-recv-buffer').textContent,
            sendBuffer: document.getElementById('network-send-buffer').textContent,
            queueSize: document.getElementById('network-queue-size').textContent
        },
        exportTime: new Date().toISOString()
    };

    // 创建下载链接
    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `mqtt-config-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);

    addRecentEvent('success', '配置文件导出成功');
}

// 复制配置到剪贴板
async function copyConfigToClipboard() {
    // 收集所有配置信息（与导出配置相同的逻辑）
    const config = {
        mqtt: {
            enabled: document.getElementById('config-enabled').textContent,
            port: document.getElementById('config-port').textContent,
            name: document.getElementById('config-name').textContent,
            heartbeatTimeout: document.getElementById('config-heartbeat').textContent,
            readBufferSize: document.getElementById('config-read-buffer').textContent,
            maxBytesInMessage: document.getElementById('config-max-message').textContent,
            authEnabled: document.getElementById('config-auth').textContent,
            debugEnabled: document.getElementById('config-debug').textContent,
            statEnabled: document.getElementById('config-stat').textContent,
            webPort: document.getElementById('config-web-port').textContent,
            websocketEnabled: document.getElementById('config-websocket').textContent,
            httpEnabled: document.getElementById('config-http').textContent
        },
        exportTime: new Date().toISOString()
    };

    try {
        await navigator.clipboard.writeText(JSON.stringify(config, null, 2));
        addRecentEvent('success', '配置信息已复制到剪贴板');
    } catch (error) {
        addRecentEvent('error', '复制到剪贴板失败: ' + error.message);
    }
}

// ==================== 日志监控相关函数 ====================

// 日志数据存储
let logsData = {
    logs: [],
    stats: {},
    autoRefresh: true,
    refreshInterval: null
};

// 日志趋势图表
let logTrendChart = null;

// 加载日志统计信息
async function loadLogStats() {
    try {
        const response = await fetch(`${MQTT_MANAGEMENT_API}/logs/stats`, {
            method: 'GET',
            headers: {
                'authorization': AUTHORIZATION
            }
        });

        const result = await response.json();

        if (result.success) {
            const stats = result.logStats;
            logsData.stats = stats;

            // 更新统计卡片
            document.getElementById('total-logs').textContent = stats.total_logs.toLocaleString();
            document.getElementById('error-logs').textContent = stats.error_logs.toLocaleString();
            document.getElementById('warn-logs').textContent = stats.warn_logs.toLocaleString();
            document.getElementById('error-rate').textContent = (stats.error_rates.current_hour * 100).toFixed(2) + '%';

            // 更新日志趋势图表
            updateLogTrendChart(stats);

        } else {
            addRecentEvent('error', '加载日志统计失败: ' + result.message);
        }
    } catch (error) {
        addRecentEvent('error', '加载日志统计失败: ' + error.message);
    }
}

// 加载最近日志
async function loadRecentLogs() {
    const level = document.getElementById('logLevelFilter').value;
    const keyword = document.getElementById('logKeywordFilter').value;
    const limit = document.getElementById('logLimitFilter').value;

    try {
        showLogsLoading(true);

        const response = await fetch(`${MQTT_MANAGEMENT_API}/logs/recent?level=${level}&keyword=${encodeURIComponent(keyword)}&limit=${limit}`, {
            method: 'GET',
            headers: {
                'authorization': AUTHORIZATION
            }
        });

        const result = await response.json();

        if (result.success) {
            logsData.logs = result.logs;
            renderLogs();

            // 更新日志计数
            document.getElementById('logCount').textContent = `${result.logs.length} 条`;

        } else {
            showLogsError('加载日志失败: ' + result.message);
        }
    } catch (error) {
        showLogsError('加载日志失败: ' + error.message);
    } finally {
        showLogsLoading(false);
    }
}

// 渲染日志列表
function renderLogs() {
    const container = document.getElementById('logContainer');

    if (logsData.logs.length === 0) {
        container.innerHTML = '<div class="text-center text-muted p-4">暂无日志数据</div>';
        return;
    }

    let html = '';
    logsData.logs.forEach(log => {
        const levelClass = log.level.toLowerCase();
        html += `
            <div class="log-entry ${levelClass}">
                <span class="log-timestamp">${formatLogTimestamp(log.timestamp)}</span>
                <span class="log-level ${levelClass}">${log.level}</span>
                <span class="text-muted">[${log.thread}]</span>
                <span class="text-info">${log.logger}</span>
                - ${log.message}
            </div>
        `;
    });

    container.innerHTML = html;

    // 自动滚动到底部
    container.scrollTop = container.scrollHeight;
}

// 格式化日志时间戳
function formatLogTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        fractionalSecondDigits: 3
    });
}

// 过滤日志
function filterLogs() {
    loadRecentLogs();
}

// 刷新日志
function refreshLogs() {
    loadLogStats();
    loadRecentLogs();
}

// 导出日志
function exportLogs() {
    if (logsData.logs.length === 0) {
        addRecentEvent('warning', '没有日志数据可导出');
        return;
    }

    // 准备导出数据
    const exportData = {
        exportTime: new Date().toISOString(),
        stats: logsData.stats,
        logs: logsData.logs
    };

    // 创建下载链接
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `mqtt-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);

    addRecentEvent('success', '日志文件导出成功');
}

// 更新日志趋势图表
function updateLogTrendChart(stats) {
    const ctx = document.getElementById('logTrendChart').getContext('2d');

    if (logTrendChart) {
        logTrendChart.destroy();
    }

    // 生成最近24小时的数据点
    const labels = [];
    const errorData = [];
    const warnData = [];
    const infoData = [];

    for (let i = 23; i >= 0; i--) {
        const hour = new Date();
        hour.setHours(hour.getHours() - i);
        labels.push(hour.getHours() + ':00');

        // 模拟数据
        errorData.push(Math.floor(Math.random() * 10) + 1);
        warnData.push(Math.floor(Math.random() * 30) + 5);
        infoData.push(Math.floor(Math.random() * 100) + 20);
    }

    logTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '错误',
                data: errorData,
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4
            }, {
                label: '警告',
                data: warnData,
                borderColor: '#ffc107',
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                tension: 0.4
            }, {
                label: '信息',
                data: infoData,
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// 显示日志加载状态
function showLogsLoading(show) {
    const spinner = document.getElementById('logsLoadingSpinner');
    const error = document.getElementById('logsErrorMessage');

    if (show) {
        spinner.style.display = 'block';
        error.style.display = 'none';
    } else {
        spinner.style.display = 'none';
    }
}

// 显示日志错误信息
function showLogsError(message) {
    const spinner = document.getElementById('logsLoadingSpinner');
    const error = document.getElementById('logsErrorMessage');

    spinner.style.display = 'none';
    error.style.display = 'block';
    error.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${message}`;
}

// 初始化日志监控
function initializeLogMonitoring() {
    // 加载初始数据
    loadLogStats();
    loadRecentLogs();

    // 设置自动刷新
    const autoRefreshCheckbox = document.getElementById('autoRefreshLogs');
    if (autoRefreshCheckbox.checked) {
        logsData.refreshInterval = setInterval(() => {
            if (logsData.autoRefresh) {
                refreshLogs();
            }
        }, 10000); // 每10秒刷新
    }

    // 监听自动刷新复选框变化
    autoRefreshCheckbox.addEventListener('change', function() {
        logsData.autoRefresh = this.checked;
        if (this.checked && !logsData.refreshInterval) {
            logsData.refreshInterval = setInterval(() => {
                if (logsData.autoRefresh) {
                    refreshLogs();
                }
            }, 10000);
        } else if (!this.checked && logsData.refreshInterval) {
            clearInterval(logsData.refreshInterval);
            logsData.refreshInterval = null;
        }
    });
}

// 初始化系统工具
function initializeSystemTools() {
    // 自动检查Redis健康状态
    checkRedisHealth();

    // 加载MQTT配置
    loadMqttConfig();

    // 加载系统配置
    loadSystemConfig();

    // 定期更新系统状态（每30秒）
    setInterval(() => {
        checkRedisHealth();
    }, 30000);
}

// ==================== 性能监控相关函数 ====================

// 性能监控数据存储
let performanceData = {
    metrics: {},
    autoRefresh: true,
    refreshInterval: null,
    thresholds: {
        cpu: 80,
        memory: 85,
        connections: 1000,
        errorRate: 5
    }
};

// 性能图表
let cpuMemoryChart = null;
let mqttPerformanceChart = null;

// 加载性能指标
async function loadPerformanceMetrics() {
    try {
        const response = await fetch(`${MQTT_MANAGEMENT_API}/performance/detailed`, {
            method: 'GET',
            headers: {
                'authorization': AUTHORIZATION
            }
        });

        const result = await response.json();

        if (result.success) {
            const performance = result.performance;
            performanceData.metrics = performance;

            // 更新关键指标卡片
            updatePerformanceCards(performance);

            // 更新详细指标表格
            updatePerformanceTables(performance);

            // 更新性能图表
            updatePerformanceCharts(performance);

            // 检查告警阈值
            checkPerformanceAlerts(performance);

        } else {
            addRecentEvent('error', '加载性能指标失败: ' + result.message);
        }
    } catch (error) {
        addRecentEvent('error', '加载性能指标失败: ' + error.message);
    }
}

// 更新性能指标卡片
function updatePerformanceCards(performance) {
    const jvm = performance.jvm;
    const mqtt = performance.mqtt;
    const system = performance.system;

    // CPU使用率
    const cpuUsage = system.cpu_usage_percent;
    document.getElementById('cpu-usage').textContent = cpuUsage.toFixed(1) + '%';
    document.getElementById('cpu-usage').className = getPerformanceClass(cpuUsage, performanceData.thresholds.cpu);

    // 内存使用率
    const memoryUsage = jvm.heap_usage_percent;
    document.getElementById('memory-usage').textContent = memoryUsage.toFixed(1) + '%';
    document.getElementById('memory-usage').className = getPerformanceClass(memoryUsage, performanceData.thresholds.memory);

    // MQTT连接数
    document.getElementById('mqtt-connections').textContent = mqtt.connections_active;
    document.getElementById('mqtt-connections').className = getPerformanceClass(mqtt.connections_active, performanceData.thresholds.connections, true);

    // 消息速率
    document.getElementById('message-rate').textContent = mqtt.messages_per_second.toFixed(1) + '/s';
}

// 更新性能指标表格
function updatePerformanceTables(performance) {
    const jvm = performance.jvm;
    const mqtt = performance.mqtt;
    const system = performance.system;
    const app = performance.application;

    // JVM指标
    document.getElementById('jvm-heap-used').textContent = formatBytes(jvm.heap_used_bytes);
    document.getElementById('jvm-heap-total').textContent = formatBytes(jvm.heap_total_bytes);
    document.getElementById('jvm-heap-max').textContent = formatBytes(jvm.heap_max_bytes);
    document.getElementById('jvm-threads').textContent = jvm.threads_active;
    document.getElementById('jvm-processors').textContent = jvm.processors;

    // MQTT指标
    document.getElementById('mqtt-active-connections').textContent = mqtt.connections_active;
    document.getElementById('mqtt-total-connections').textContent = mqtt.connections_total;
    document.getElementById('mqtt-messages-per-sec').textContent = mqtt.messages_per_second.toFixed(1);
    document.getElementById('mqtt-bytes-per-sec').textContent = formatBytes(mqtt.bytes_per_second) + '/s';
    document.getElementById('mqtt-topics-count').textContent = mqtt.topics_count;
    document.getElementById('mqtt-subscriptions-count').textContent = mqtt.subscriptions_count;

    // 系统指标
    document.getElementById('system-cpu-usage').textContent = system.cpu_usage_percent.toFixed(1) + '%';
    document.getElementById('system-load-average').textContent = system.load_average.toFixed(2);
    document.getElementById('system-disk-usage').textContent = system.disk_usage_percent.toFixed(1) + '%';
    document.getElementById('system-network-in').textContent = formatBytes(system.network_in_bytes_per_sec) + '/s';
    document.getElementById('system-network-out').textContent = formatBytes(system.network_out_bytes_per_sec) + '/s';
    document.getElementById('system-uptime').textContent = formatUptime(app.uptime_seconds);
}

// 更新性能图表
function updatePerformanceCharts(performance) {
    updateCpuMemoryChart(performance);
    updateMqttPerformanceChart(performance);
}

// 更新CPU和内存图表
function updateCpuMemoryChart(performance) {
    const ctx = document.getElementById('cpuMemoryChart').getContext('2d');

    if (cpuMemoryChart) {
        // 更新现有图表数据
        const now = new Date();
        const timeLabel = now.toLocaleTimeString('zh-CN', { hour12: false });

        // 添加新数据点
        cpuMemoryChart.data.labels.push(timeLabel);
        cpuMemoryChart.data.datasets[0].data.push(performance.system.cpu_usage_percent);
        cpuMemoryChart.data.datasets[1].data.push(performance.jvm.heap_usage_percent);

        // 保持最多20个数据点
        if (cpuMemoryChart.data.labels.length > 20) {
            cpuMemoryChart.data.labels.shift();
            cpuMemoryChart.data.datasets[0].data.shift();
            cpuMemoryChart.data.datasets[1].data.shift();
        }

        cpuMemoryChart.update('none');
    } else {
        // 创建新图表
        const labels = [];
        const cpuData = [];
        const memoryData = [];

        // 生成初始数据点
        for (let i = 19; i >= 0; i--) {
            const time = new Date();
            time.setSeconds(time.getSeconds() - i * 5);
            labels.push(time.toLocaleTimeString('zh-CN', { hour12: false }));
            cpuData.push(Math.random() * 30 + 10);
            memoryData.push(Math.random() * 40 + 30);
        }

        cpuMemoryChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'CPU使用率 (%)',
                    data: cpuData,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: '内存使用率 (%)',
                    data: memoryData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }
}

// 更新MQTT性能图表
function updateMqttPerformanceChart(performance) {
    const ctx = document.getElementById('mqttPerformanceChart').getContext('2d');

    if (mqttPerformanceChart) {
        // 更新现有图表数据
        const now = new Date();
        const timeLabel = now.toLocaleTimeString('zh-CN', { hour12: false });

        // 添加新数据点
        mqttPerformanceChart.data.labels.push(timeLabel);
        mqttPerformanceChart.data.datasets[0].data.push(performance.mqtt.connections_active);
        mqttPerformanceChart.data.datasets[1].data.push(performance.mqtt.messages_per_second);

        // 保持最多20个数据点
        if (mqttPerformanceChart.data.labels.length > 20) {
            mqttPerformanceChart.data.labels.shift();
            mqttPerformanceChart.data.datasets[0].data.shift();
            mqttPerformanceChart.data.datasets[1].data.shift();
        }

        mqttPerformanceChart.update('none');
    } else {
        // 创建新图表
        const labels = [];
        const connectionsData = [];
        const messagesData = [];

        // 生成初始数据点
        for (let i = 19; i >= 0; i--) {
            const time = new Date();
            time.setSeconds(time.getSeconds() - i * 5);
            labels.push(time.toLocaleTimeString('zh-CN', { hour12: false }));
            connectionsData.push(Math.floor(Math.random() * 50) + 100);
            messagesData.push(Math.random() * 30 + 10);
        }

        mqttPerformanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: '活跃连接数',
                    data: connectionsData,
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y'
                }, {
                    label: '消息/秒',
                    data: messagesData,
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: true,
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }
}

// 检查性能告警
function checkPerformanceAlerts(performance) {
    if (!document.getElementById('enableAlerts').checked) {
        return;
    }

    const alerts = [];

    // CPU告警
    if (performance.system.cpu_usage_percent > performanceData.thresholds.cpu) {
        alerts.push(`CPU使用率过高: ${performance.system.cpu_usage_percent.toFixed(1)}%`);
    }

    // 内存告警
    if (performance.jvm.heap_usage_percent > performanceData.thresholds.memory) {
        alerts.push(`内存使用率过高: ${performance.jvm.heap_usage_percent.toFixed(1)}%`);
    }

    // 连接数告警
    if (performance.mqtt.connections_active > performanceData.thresholds.connections) {
        alerts.push(`MQTT连接数过多: ${performance.mqtt.connections_active}`);
    }

    // 发送告警
    alerts.forEach(alert => {
        addRecentEvent('warning', alert);
    });
}

// 获取性能状态样式类
function getPerformanceClass(value, threshold, reverse = false) {
    if (reverse) {
        // 对于连接数等指标，超过阈值才是警告
        if (value > threshold) {
            return 'text-warning';
        } else if (value > threshold * 0.8) {
            return 'text-info';
        } else {
            return 'text-success';
        }
    } else {
        // 对于CPU、内存等指标，超过阈值是危险
        if (value > threshold) {
            return 'text-danger';
        } else if (value > threshold * 0.8) {
            return 'text-warning';
        } else {
            return 'text-success';
        }
    }
}

// 格式化字节数
function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化运行时间
function formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
        return `${days}天 ${hours}小时`;
    } else if (hours > 0) {
        return `${hours}小时 ${minutes}分钟`;
    } else {
        return `${minutes}分钟`;
    }
}

// 刷新性能指标
function refreshPerformanceMetrics() {
    loadPerformanceMetrics();
}

// 导出性能数据
function exportPerformanceData() {
    if (!performanceData.metrics || Object.keys(performanceData.metrics).length === 0) {
        addRecentEvent('warning', '没有性能数据可导出');
        return;
    }

    // 准备导出数据
    const exportData = {
        exportTime: new Date().toISOString(),
        thresholds: performanceData.thresholds,
        metrics: performanceData.metrics
    };

    // 创建下载链接
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `mqtt-performance-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);

    addRecentEvent('success', '性能数据导出成功');
}

// 保存性能阈值设置
function savePerformanceThresholds() {
    const cpuThreshold = parseFloat(document.getElementById('cpuThreshold').value);
    const memoryThreshold = parseFloat(document.getElementById('memoryThreshold').value);
    const connectionThreshold = parseInt(document.getElementById('connectionThreshold').value);
    const errorRateThreshold = parseFloat(document.getElementById('errorRateThreshold').value);

    // 验证输入
    if (isNaN(cpuThreshold) || cpuThreshold < 1 || cpuThreshold > 100) {
        addRecentEvent('error', 'CPU阈值必须在1-100之间');
        return;
    }

    if (isNaN(memoryThreshold) || memoryThreshold < 1 || memoryThreshold > 100) {
        addRecentEvent('error', '内存阈值必须在1-100之间');
        return;
    }

    if (isNaN(connectionThreshold) || connectionThreshold < 1) {
        addRecentEvent('error', '连接数阈值必须大于0');
        return;
    }

    if (isNaN(errorRateThreshold) || errorRateThreshold < 0 || errorRateThreshold > 100) {
        addRecentEvent('error', '错误率阈值必须在0-100之间');
        return;
    }

    // 保存设置
    performanceData.thresholds = {
        cpu: cpuThreshold,
        memory: memoryThreshold,
        connections: connectionThreshold,
        errorRate: errorRateThreshold
    };

    // 保存到本地存储
    localStorage.setItem('mqttPerformanceThresholds', JSON.stringify(performanceData.thresholds));

    addRecentEvent('success', '性能阈值设置已保存');
}

// 重置性能阈值
function resetPerformanceThresholds() {
    document.getElementById('cpuThreshold').value = 80;
    document.getElementById('memoryThreshold').value = 85;
    document.getElementById('connectionThreshold').value = 1000;
    document.getElementById('errorRateThreshold').value = 5;

    performanceData.thresholds = {
        cpu: 80,
        memory: 85,
        connections: 1000,
        errorRate: 5
    };

    // 清除本地存储
    localStorage.removeItem('mqttPerformanceThresholds');

    addRecentEvent('success', '性能阈值已重置为默认值');
}

// 初始化性能监控
function initializePerformanceMonitoring() {
    // 从本地存储加载阈值设置
    const savedThresholds = localStorage.getItem('mqttPerformanceThresholds');
    if (savedThresholds) {
        try {
            performanceData.thresholds = JSON.parse(savedThresholds);

            // 更新界面
            document.getElementById('cpuThreshold').value = performanceData.thresholds.cpu;
            document.getElementById('memoryThreshold').value = performanceData.thresholds.memory;
            document.getElementById('connectionThreshold').value = performanceData.thresholds.connections;
            document.getElementById('errorRateThreshold').value = performanceData.thresholds.errorRate;
        } catch (e) {
            console.warn('加载性能阈值设置失败:', e);
        }
    }

    // 加载初始数据
    loadPerformanceMetrics();

    // 设置自动刷新
    const autoRefreshCheckbox = document.getElementById('autoRefreshMetrics');
    if (autoRefreshCheckbox.checked) {
        performanceData.refreshInterval = setInterval(() => {
            if (performanceData.autoRefresh) {
                loadPerformanceMetrics();
            }
        }, 5000); // 每5秒刷新
    }

    // 监听自动刷新复选框变化
    autoRefreshCheckbox.addEventListener('change', function() {
        performanceData.autoRefresh = this.checked;
        if (this.checked && !performanceData.refreshInterval) {
            performanceData.refreshInterval = setInterval(() => {
                if (performanceData.autoRefresh) {
                    loadPerformanceMetrics();
                }
            }, 5000);
        } else if (!this.checked && performanceData.refreshInterval) {
            clearInterval(performanceData.refreshInterval);
            performanceData.refreshInterval = null;
        }
    });
}

// 修改初始化页面函数
const originalInitializePage2 = initializePage;
function initializePage() {
    originalInitializePage2();
    initializeTokenManagement();
    initializeSystemTools();
    initializeLogMonitoring();
    initializePerformanceMonitoring();
}

// 页面卸载时关闭WebSocket连接
window.addEventListener('beforeunload', function() {
    if (websocket) {
        websocket.close();
    }
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
</script>

</body>
</html>
