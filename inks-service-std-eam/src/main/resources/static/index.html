<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT服务端管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/common.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-server"></i> MQTT管理</h3>
            <div class="subtitle">服务端控制台</div>
        </div>

        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="index.html" class="nav-link active">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="clients.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>客户端管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="publish.html" class="nav-link">
                    <i class="fas fa-paper-plane"></i>
                    <span>消息发布</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="subscriptions.html" class="nav-link">
                    <i class="fas fa-rss"></i>
                    <span>订阅管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="messages.html" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    <span>消息管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-messages.html" class="nav-link">
                    <i class="fas fa-bolt"></i>
                    <span>实时消息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-monitor.html" class="nav-link">
                    <i class="fas fa-eye"></i>
                    <span>实时监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="stats.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计信息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <div class="content-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-home"></i> 欢迎使用MQTT管理系统</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item active">首页</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-outline-light me-2" onclick="loadStats()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <div class="form-check form-switch d-inline-block align-middle me-2">
                        <input class="form-check-input" type="checkbox" role="switch" id="autoRefreshSwitch">
                        <label class="form-check-label text-white" for="autoRefreshSwitch">自动刷新</label>
                    </div>
                    <button class="btn btn-outline-light d-md-none sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="content-body">
            <!-- 快速统计 -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-primary">
                            <i class="fas fa-link"></i>
                        </div>
                        <div class="stat-value text-primary" id="totalConnections">-</div>
                        <div class="stat-label">总连接数</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-success">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-value text-success" id="totalMessages">-</div>
                        <div class="stat-label">消息总数</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-info">
                            <i class="fas fa-rss"></i>
                        </div>
                        <div class="stat-value text-info" id="totalSubscriptions">-</div>
                        <div class="stat-label">订阅总数</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-warning">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-value text-warning" id="totalTopics">-</div>
                        <div class="stat-label">主题总数</div>
                    </div>
                </div>
            </div>

            <!-- 功能导航 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-th-large"></i> 功能导航</h5>
                        </div>
                        <div class="card-body">
                            <div class="row" id="navigationGrid">
                                <!-- 动态生成导航卡片 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle"></i> 系统信息</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>服务器状态:</strong></td>
                                    <td><span class="badge bg-success">运行中</span></td>
                                </tr>
                                <tr>
                                    <td><strong>MQTT端口:</strong></td>
                                    <td>1883</td>
                                </tr>
                                <tr>
                                    <td><strong>WebSocket端口:</strong></td>
                                    <td>8083</td>
                                </tr>
                                <tr>
                                    <td><strong>HTTP API端口:</strong></td>
                                    <td>10180</td>
                                </tr>
                                <tr>
                                    <td><strong>启动时间:</strong></td>
                                    <td id="serverStartTime">-</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-clock"></i> 最近活动</h5>
                        </div>
                        <div class="card-body">
                            <div id="recentActivity">
                                <div class="text-muted text-center py-3">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/common.js"></script>
    <script>
        let autoRefreshInterval = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            loadStats();
            generateNavigationGrid();
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('autoRefreshSwitch').addEventListener('change', function() {
                if (this.checked) {
                    if (!autoRefreshInterval) {
                        autoRefreshInterval = setInterval(loadStats, CONFIG.UI.REFRESH_INTERVAL);
                    }
                } else {
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                        autoRefreshInterval = null;
                    }
                }
            });
        }

        // 初始化页面
        function initializePage() {
            document.getElementById('serverStartTime').textContent = new Date().toLocaleString('zh-CN');
        }

        // 加载统计数据
        async function loadStats() {
            try {
                const stats = await mqttUI.apiCall('GET', '/stats');
                if (stats && stats.code === 1) {
                    updateStatsDisplay(stats.data);
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 更新统计显示
        function updateStatsDisplay(data) {
            document.getElementById('totalConnections').textContent = mqttUI.formatNumber(data.connections || 0);
            document.getElementById('totalMessages').textContent = mqttUI.formatNumber(data.messages || 0);
            document.getElementById('totalSubscriptions').textContent = mqttUI.formatNumber(data.subscriptions || 0);
            document.getElementById('totalTopics').textContent = mqttUI.formatNumber(data.topics || 0);
        }

        // 生成导航网格
        function generateNavigationGrid() {
            const grid = document.getElementById('navigationGrid');
            const navigation = CONFIG.NAVIGATION;

            navigation.forEach(item => {
                const col = document.createElement('div');
                col.className = 'col-md-4 col-lg-3 mb-3';
                col.innerHTML = `
                    <div class="card h-100 nav-card" style="cursor: pointer;" onclick="window.location.href='${item.url}'">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="${item.icon}" style="font-size: 2rem; color: var(--primary-color);"></i>
                            </div>
                            <h6 class="card-title">${item.title}</h6>
                            <p class="card-text text-muted small">${item.description}</p>
                        </div>
                    </div>
                `;
                grid.appendChild(col);
            });
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>

    <style>
        .nav-card {
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.2);
            border-color: var(--primary-color);
        }
    </style>
</body>
</html>
