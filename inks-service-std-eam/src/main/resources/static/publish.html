<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息发布 - MQTT管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/common.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-server"></i> MQTT管理</h3>
            <div class="subtitle">服务端控制台</div>
        </div>
        
        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="clients.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>客户端管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="publish.html" class="nav-link active">
                    <i class="fas fa-paper-plane"></i>
                    <span>消息发布</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="subscriptions.html" class="nav-link">
                    <i class="fas fa-rss"></i>
                    <span>订阅管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="messages.html" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    <span>消息管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-messages.html" class="nav-link">
                    <i class="fas fa-bolt"></i>
                    <span>实时消息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-monitor.html" class="nav-link">
                    <i class="fas fa-eye"></i>
                    <span>实时监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="stats.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计信息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <div class="content-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-paper-plane"></i> 消息发布</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html">首页</a></li>
                            <li class="breadcrumb-item active">消息发布</li>
                        </ol>
                    </nav>
                </div>
                <button class="btn btn-outline-light d-md-none sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>

        <div class="content-body">
            <!-- 发布模式选择 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="publishMode" id="singleMode" value="single" checked>
                                <label class="btn btn-outline-primary" for="singleMode">
                                    <i class="fas fa-paper-plane"></i> 单条发布
                                </label>
                                
                                <input type="radio" class="btn-check" name="publishMode" id="batchMode" value="batch">
                                <label class="btn btn-outline-primary" for="batchMode">
                                    <i class="fas fa-layer-group"></i> 批量发布
                                </label>
                                
                                <input type="radio" class="btn-check" name="publishMode" id="templateMode" value="template">
                                <label class="btn btn-outline-primary" for="templateMode">
                                    <i class="fas fa-file-alt"></i> 模板发布
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 单条发布 -->
            <div id="singlePublishPanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-paper-plane"></i> 单条消息发布</h5>
                    </div>
                    <div class="card-body">
                        <form id="singlePublishForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="singleTopic" class="form-label">主题 *</label>
                                        <input type="text" class="form-control" id="singleTopic" 
                                               placeholder="例如: sensor/temperature" required>
                                        <div class="form-text">MQTT主题，支持多级主题结构</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="singleQos" class="form-label">QoS等级</label>
                                        <select class="form-select" id="singleQos">
                                            <option value="0">QoS 0 - 最多一次</option>
                                            <option value="1">QoS 1 - 至少一次</option>
                                            <option value="2">QoS 2 - 恰好一次</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="singleEncoding" class="form-label">编码方式</label>
                                        <select class="form-select" id="singleEncoding">
                                            <option value="plain">纯文本</option>
                                            <option value="hex">HEX编码</option>
                                            <option value="base64">Base64编码</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="singleClientId" class="form-label">客户端ID *</label>
                                        <input type="text" class="form-control" id="singleClientId" 
                                               value="httpApi" required>
                                        <div class="form-text">发布者标识，建议使用httpApi</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="singleRetain">
                                            <label class="form-check-label" for="singleRetain">
                                                保留消息
                                            </label>
                                            <div class="form-text">服务器会保留该消息，新订阅者会立即收到</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="singlePayload" class="form-label">消息内容 *</label>
                                <textarea class="form-control" id="singlePayload" rows="6" 
                                          placeholder="输入消息内容..." required></textarea>
                                <div class="form-text">
                                    <span id="payloadLength">0</span> 字符
                                    <button type="button" class="btn btn-sm btn-outline-secondary ms-2" 
                                            onclick="formatJSON('singlePayload')">
                                        <i class="fas fa-code"></i> 格式化JSON
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ms-1" 
                                            onclick="clearPayload('singlePayload')">
                                        <i class="fas fa-trash"></i> 清空
                                    </button>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" onclick="resetSingleForm()">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> 发布消息
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 批量发布 -->
            <div id="batchPublishPanel" style="display: none;">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-layer-group"></i> 批量消息发布</h5>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addBatchMessage()">
                            <i class="fas fa-plus"></i> 添加消息
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="batchMessages">
                            <!-- 动态生成批量消息表单 -->
                        </div>
                        
                        <div class="mt-3 d-flex justify-content-between">
                            <div>
                                <span class="text-muted">共 <span id="batchCount">0</span> 条消息</span>
                            </div>
                            <div>
                                <button type="button" class="btn btn-outline-secondary me-2" onclick="clearBatchMessages()">
                                    <i class="fas fa-trash"></i> 清空全部
                                </button>
                                <button type="button" class="btn btn-primary" onclick="publishBatchMessages()">
                                    <i class="fas fa-paper-plane"></i> 批量发布
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模板发布 -->
            <div id="templatePublishPanel" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-file-alt"></i> 模板消息发布</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="templateSelect" class="form-label">选择模板</label>
                                <select class="form-select" id="templateSelect" onchange="loadTemplate()">
                                    <option value="">请选择模板</option>
                                    <option value="sensor">传感器数据</option>
                                    <option value="device">设备状态</option>
                                    <option value="alert">告警消息</option>
                                    <option value="custom">自定义模板</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="templateInterval" class="form-label">发送间隔(秒)</label>
                                <input type="number" class="form-control" id="templateInterval" 
                                       value="5" min="1" max="3600">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="templateContent" class="form-label">模板内容</label>
                            <textarea class="form-control" id="templateContent" rows="8" 
                                      placeholder="选择模板或输入自定义内容..."></textarea>
                            <div class="form-text">支持变量替换: {{timestamp}}, {{random}}, {{counter}}</div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="templateTopic" class="form-label">主题模板</label>
                                <input type="text" class="form-control" id="templateTopic" 
                                       placeholder="例如: sensor/{{deviceId}}/data">
                            </div>
                            <div class="col-md-4">
                                <label for="templateCount" class="form-label">发送次数</label>
                                <input type="number" class="form-control" id="templateCount" 
                                       value="10" min="1" max="1000">
                            </div>
                            <div class="col-md-4">
                                <label for="templateClientId" class="form-label">客户端ID前缀</label>
                                <input type="text" class="form-control" id="templateClientId" 
                                       value="template">
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" onclick="stopTemplatePublish()">
                                <i class="fas fa-stop"></i> 停止发布
                            </button>
                            <button type="button" class="btn btn-primary" onclick="startTemplatePublish()">
                                <i class="fas fa-play"></i> 开始发布
                            </button>
                        </div>
                        
                        <div class="mt-3">
                            <div class="progress" style="display: none;" id="templateProgress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="mt-2 text-muted" id="templateStatus"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 发布历史 -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-history"></i> 发布历史</h5>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearHistory()">
                        <i class="fas fa-trash"></i> 清空历史
                    </button>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    <div id="publishHistory">
                        <div class="text-muted text-center py-3">暂无发布记录</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/common.js"></script>
    <script>
        let batchMessageCount = 0;
        let publishHistory = [];
        let templatePublishInterval = null;
        let templateCounter = 0;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            loadPublishHistory();
            addBatchMessage(); // 默认添加一条批量消息
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 发布模式切换
            document.querySelectorAll('input[name="publishMode"]').forEach(radio => {
                radio.addEventListener('change', switchPublishMode);
            });

            // 单条发布表单
            document.getElementById('singlePublishForm').addEventListener('submit', handleSinglePublish);

            // 消息内容长度统计
            document.getElementById('singlePayload').addEventListener('input', updatePayloadLength);
        }

        // 切换发布模式
        function switchPublishMode() {
            const mode = document.querySelector('input[name="publishMode"]:checked').value;

            document.getElementById('singlePublishPanel').style.display = mode === 'single' ? 'block' : 'none';
            document.getElementById('batchPublishPanel').style.display = mode === 'batch' ? 'block' : 'none';
            document.getElementById('templatePublishPanel').style.display = mode === 'template' ? 'block' : 'none';
        }

        // 处理单条发布
        async function handleSinglePublish(event) {
            event.preventDefault();

            const formData = {
                topic: document.getElementById('singleTopic').value,
                payload: document.getElementById('singlePayload').value,
                qos: parseInt(document.getElementById('singleQos').value),
                retain: document.getElementById('singleRetain').checked,
                encoding: document.getElementById('singleEncoding').value,
                clientId: document.getElementById('singleClientId').value
            };

            if (!mqttUI.validateTopic(formData.topic)) {
                mqttUI.showToast('主题格式不正确', 'error');
                return;
            }

            try {
                const response = await mqttUI.apiCall('POST', '/mqtt/publish', formData);
                if (response && response.code === 1) {
                    mqttUI.showToast('消息发布成功', 'success');
                    addToHistory('single', formData);
                    resetSingleForm();
                }
            } catch (error) {
                console.error('发布消息失败:', error);
                mqttUI.showToast('发布消息失败', 'error');
            }
        }

        // 更新消息内容长度
        function updatePayloadLength() {
            const payload = document.getElementById('singlePayload').value;
            document.getElementById('payloadLength').textContent = payload.length;
        }

        // 格式化JSON
        function formatJSON(textareaId) {
            const textarea = document.getElementById(textareaId);
            try {
                const formatted = JSON.stringify(JSON.parse(textarea.value), null, 2);
                textarea.value = formatted;
                updatePayloadLength();
            } catch (error) {
                mqttUI.showToast('JSON格式不正确', 'error');
            }
        }

        // 清空消息内容
        function clearPayload(textareaId) {
            document.getElementById(textareaId).value = '';
            updatePayloadLength();
        }

        // 重置单条发布表单
        function resetSingleForm() {
            document.getElementById('singlePublishForm').reset();
            document.getElementById('singleClientId').value = 'httpApi';
            updatePayloadLength();
        }

        // 添加批量消息
        function addBatchMessage() {
            batchMessageCount++;
            const container = document.getElementById('batchMessages');

            const messageDiv = document.createElement('div');
            messageDiv.className = 'batch-message-item border rounded p-3 mb-3';
            messageDiv.id = `batchMessage${batchMessageCount}`;
            messageDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">消息 #${batchMessageCount}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                            onclick="removeBatchMessage(${batchMessageCount})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-2">
                            <label class="form-label">主题</label>
                            <input type="text" class="form-control form-control-sm"
                                   id="batchTopic${batchMessageCount}" placeholder="主题" required>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-2">
                            <label class="form-label">QoS</label>
                            <select class="form-select form-select-sm" id="batchQos${batchMessageCount}">
                                <option value="0">0</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-2">
                            <label class="form-label">编码</label>
                            <select class="form-select form-select-sm" id="batchEncoding${batchMessageCount}">
                                <option value="plain">纯文本</option>
                                <option value="hex">HEX</option>
                                <option value="base64">Base64</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-2">
                            <label class="form-label">客户端ID</label>
                            <input type="text" class="form-control form-control-sm"
                                   id="batchClientId${batchMessageCount}" value="httpApi" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10">
                        <div class="mb-2">
                            <label class="form-label">消息内容</label>
                            <textarea class="form-control form-control-sm"
                                      id="batchPayload${batchMessageCount}" rows="2"
                                      placeholder="消息内容" required></textarea>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-2">
                            <label class="form-label">保留</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                       id="batchRetain${batchMessageCount}">
                                <label class="form-check-label" for="batchRetain${batchMessageCount}">
                                    保留消息
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.appendChild(messageDiv);
            updateBatchCount();
        }

        // 移除批量消息
        function removeBatchMessage(id) {
            const element = document.getElementById(`batchMessage${id}`);
            if (element) {
                element.remove();
                updateBatchCount();
            }
        }

        // 更新批量消息计数
        function updateBatchCount() {
            const count = document.querySelectorAll('.batch-message-item').length;
            document.getElementById('batchCount').textContent = count;
        }

        // 清空批量消息
        function clearBatchMessages() {
            document.getElementById('batchMessages').innerHTML = '';
            batchMessageCount = 0;
            updateBatchCount();
            addBatchMessage(); // 保留一条
        }

        // 发布批量消息
        async function publishBatchMessages() {
            const messageItems = document.querySelectorAll('.batch-message-item');
            const messages = [];

            for (const item of messageItems) {
                const id = item.id.replace('batchMessage', '');
                const message = {
                    topic: document.getElementById(`batchTopic${id}`).value,
                    payload: document.getElementById(`batchPayload${id}`).value,
                    qos: parseInt(document.getElementById(`batchQos${id}`).value),
                    retain: document.getElementById(`batchRetain${id}`).checked,
                    encoding: document.getElementById(`batchEncoding${id}`).value,
                    clientId: document.getElementById(`batchClientId${id}`).value
                };

                if (!message.topic || !message.payload) {
                    mqttUI.showToast(`消息 #${id} 的主题或内容不能为空`, 'error');
                    return;
                }

                if (!mqttUI.validateTopic(message.topic)) {
                    mqttUI.showToast(`消息 #${id} 的主题格式不正确`, 'error');
                    return;
                }

                messages.push(message);
            }

            if (messages.length === 0) {
                mqttUI.showToast('没有要发布的消息', 'warning');
                return;
            }

            try {
                const response = await mqttUI.apiCall('POST', '/mqtt/publish/batch', messages);
                if (response && response.code === 1) {
                    mqttUI.showToast(`成功发布 ${messages.length} 条消息`, 'success');
                    addToHistory('batch', { count: messages.length, messages });
                    clearBatchMessages();
                }
            } catch (error) {
                console.error('批量发布失败:', error);
                mqttUI.showToast('批量发布失败', 'error');
            }
        }

        // 加载模板
        function loadTemplate() {
            const templateType = document.getElementById('templateSelect').value;
            const templates = {
                sensor: {
                    topic: 'sensor/{{deviceId}}/data',
                    content: JSON.stringify({
                        deviceId: '{{deviceId}}',
                        timestamp: '{{timestamp}}',
                        temperature: '{{random:15:35}}',
                        humidity: '{{random:40:80}}',
                        status: 'online'
                    }, null, 2)
                },
                device: {
                    topic: 'device/{{deviceId}}/status',
                    content: JSON.stringify({
                        deviceId: '{{deviceId}}',
                        timestamp: '{{timestamp}}',
                        status: '{{random:online,offline,maintenance}}',
                        battery: '{{random:0:100}}',
                        signal: '{{random:1:5}}'
                    }, null, 2)
                },
                alert: {
                    topic: 'alert/{{deviceId}}/warning',
                    content: JSON.stringify({
                        deviceId: '{{deviceId}}',
                        timestamp: '{{timestamp}}',
                        level: '{{random:info,warning,error}}',
                        message: 'Alert message {{counter}}',
                        code: '{{random:1000:9999}}'
                    }, null, 2)
                }
            };

            if (templates[templateType]) {
                document.getElementById('templateTopic').value = templates[templateType].topic;
                document.getElementById('templateContent').value = templates[templateType].content;
            } else {
                document.getElementById('templateTopic').value = '';
                document.getElementById('templateContent').value = '';
            }
        }

        // 开始模板发布
        function startTemplatePublish() {
            const topic = document.getElementById('templateTopic').value;
            const content = document.getElementById('templateContent').value;
            const interval = parseInt(document.getElementById('templateInterval').value) * 1000;
            const count = parseInt(document.getElementById('templateCount').value);
            const clientIdPrefix = document.getElementById('templateClientId').value;

            if (!topic || !content) {
                mqttUI.showToast('主题和内容不能为空', 'error');
                return;
            }

            templateCounter = 0;
            const progress = document.getElementById('templateProgress');
            const progressBar = progress.querySelector('.progress-bar');
            const status = document.getElementById('templateStatus');

            progress.style.display = 'block';
            status.textContent = '准备发布...';

            templatePublishInterval = setInterval(async () => {
                if (templateCounter >= count) {
                    stopTemplatePublish();
                    return;
                }

                templateCounter++;
                const processedTopic = processTemplate(topic, templateCounter, clientIdPrefix);
                const processedContent = processTemplate(content, templateCounter, clientIdPrefix);

                try {
                    const message = {
                        topic: processedTopic,
                        payload: processedContent,
                        qos: 0,
                        retain: false,
                        encoding: 'plain',
                        clientId: `${clientIdPrefix}_${templateCounter}`
                    };

                    await mqttUI.apiCall('POST', '/mqtt/publish', message);

                    const percentage = (templateCounter / count) * 100;
                    progressBar.style.width = `${percentage}%`;
                    status.textContent = `已发布 ${templateCounter}/${count} 条消息`;

                } catch (error) {
                    console.error('模板发布失败:', error);
                }
            }, interval);
        }

        // 停止模板发布
        function stopTemplatePublish() {
            if (templatePublishInterval) {
                clearInterval(templatePublishInterval);
                templatePublishInterval = null;
            }

            document.getElementById('templateProgress').style.display = 'none';
            document.getElementById('templateStatus').textContent = '发布已停止';

            if (templateCounter > 0) {
                mqttUI.showToast(`模板发布完成，共发布 ${templateCounter} 条消息`, 'success');
                addToHistory('template', { count: templateCounter });
            }
        }

        // 处理模板变量
        function processTemplate(template, counter, clientIdPrefix) {
            return template
                .replace(/\{\{timestamp\}\}/g, Date.now())
                .replace(/\{\{counter\}\}/g, counter)
                .replace(/\{\{deviceId\}\}/g, `${clientIdPrefix}_${counter}`)
                .replace(/\{\{random:(\d+):(\d+)\}\}/g, (match, min, max) => {
                    return Math.floor(Math.random() * (max - min + 1)) + parseInt(min);
                })
                .replace(/\{\{random:([^}]+)\}\}/g, (match, options) => {
                    const choices = options.split(',');
                    return choices[Math.floor(Math.random() * choices.length)];
                });
        }

        // 添加到历史记录
        function addToHistory(type, data) {
            const historyItem = {
                id: Date.now(),
                type,
                timestamp: new Date(),
                data
            };

            publishHistory.unshift(historyItem);
            if (publishHistory.length > 50) {
                publishHistory = publishHistory.slice(0, 50);
            }

            savePublishHistory();
            renderPublishHistory();
        }

        // 渲染发布历史
        function renderPublishHistory() {
            const container = document.getElementById('publishHistory');

            if (publishHistory.length === 0) {
                container.innerHTML = '<div class="text-muted text-center py-3">暂无发布记录</div>';
                return;
            }

            container.innerHTML = publishHistory.map(item => {
                let description = '';
                switch (item.type) {
                    case 'single':
                        description = `主题: ${item.data.topic}`;
                        break;
                    case 'batch':
                        description = `批量发布 ${item.data.count} 条消息`;
                        break;
                    case 'template':
                        description = `模板发布 ${item.data.count} 条消息`;
                        break;
                }

                return `
                    <div class="d-flex align-items-center mb-2 p-2 rounded" style="background: var(--bg-darker);">
                        <div class="me-3">
                            <i class="fas fa-${item.type === 'single' ? 'paper-plane' : item.type === 'batch' ? 'layer-group' : 'file-alt'} text-primary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold">${description}</div>
                            <small class="text-muted">${mqttUI.formatTime(item.timestamp)}</small>
                        </div>
                        <div>
                            <span class="badge bg-success">${item.type}</span>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 清空历史记录
        function clearHistory() {
            if (confirm('确定要清空所有发布历史吗？')) {
                publishHistory = [];
                savePublishHistory();
                renderPublishHistory();
                mqttUI.showToast('历史记录已清空', 'success');
            }
        }

        // 保存发布历史到本地存储
        function savePublishHistory() {
            localStorage.setItem('mqtt_publish_history', JSON.stringify(publishHistory));
        }

        // 从本地存储加载发布历史
        function loadPublishHistory() {
            const saved = localStorage.getItem('mqtt_publish_history');
            if (saved) {
                publishHistory = JSON.parse(saved);
                publishHistory.forEach(item => {
                    item.timestamp = new Date(item.timestamp);
                });
                renderPublishHistory();
            }
        }
    </script>
</body>
</html>
