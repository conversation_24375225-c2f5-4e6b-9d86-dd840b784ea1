/* MQTT管理界面通用样式 - 浅色主题 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --bg-light: #ffffff;
    --bg-lighter: #f8f9fa;
    --bg-darker: #e9ecef;
    --text-dark: #212529;
    --text-light: #ffffff;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --card-bg: #ffffff;
    --sidebar-width: 250px;
}

/* 浅色主题下的通用样式改进 */
.theme-light,
body:not(.theme-dark) {
    /* 确保所有文本在浅色背景下有良好的对比度 */
}

.theme-light .content-header,
body:not(.theme-dark) .content-header {
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-lighter) 100%);
    color: var(--text-dark);
}

.theme-light .content-header h1,
body:not(.theme-dark) .content-header h1 {
    color: var(--text-dark) !important;
}

.theme-light .form-check-label,
body:not(.theme-dark) .form-check-label {
    color: var(--text-dark) !important;
}

/* 浅色主题下的徽章样式修复 */
.theme-light .badge,
body:not(.theme-dark) .badge {
    color: var(--text-light) !important;
}

.theme-light .badge-light,
body:not(.theme-dark) .badge-light {
    color: var(--text-dark) !important;
    background-color: var(--bg-darker) !important;
}

/* 浅色主题下的导航链接修复 */
.theme-light .nav-link,
body:not(.theme-dark) .nav-link {
    color: var(--text-dark) !important;
}

.theme-light .nav-link:hover,
body:not(.theme-dark) .nav-link:hover {
    color: var(--primary-color) !important;
}

/* 浅色主题下的下拉菜单修复 */
.theme-light .dropdown-menu,
body:not(.theme-dark) .dropdown-menu {
    background-color: var(--bg-light) !important;
    border-color: var(--border-color) !important;
}

.theme-light .dropdown-item,
body:not(.theme-dark) .dropdown-item {
    color: var(--text-dark) !important;
}

.theme-light .dropdown-item:hover,
body:not(.theme-dark) .dropdown-item:hover {
    background-color: var(--bg-darker) !important;
    color: var(--text-dark) !important;
}

/* 深色主题样式 */
.theme-dark {
    --primary-color: #60a5fa;
    --secondary-color: #d1d5db;
    --success-color: #34d399;
    --danger-color: #f87171;
    --warning-color: #fbbf24;
    --info-color: #60a5fa;
    --dark-color: #374151;
    --light-color: #f9fafb;
    --bg-light: #374151;
    --bg-lighter: #1f2937;
    --bg-darker: #111827;
    --text-dark: #f9fafb;
    --text-light: #f9fafb;
    --text-muted: #d1d5db;
    --border-color: #6b7280;
    --card-bg: #374151;
}

/* 深色主题下的特殊样式 */
.theme-dark .card-header {
    color: var(--text-light);
    background: linear-gradient(135deg, var(--bg-darker) 0%, var(--bg-light) 100%);
}

.theme-dark .card-title {
    color: var(--text-light) !important;
}

.theme-dark .card-text {
    color: var(--text-muted) !important;
}

.theme-dark .text-muted {
    color: var(--text-muted) !important;
}

.theme-dark .breadcrumb-item {
    color: var(--text-muted) !important;
}

.theme-dark .breadcrumb-item.active {
    color: var(--primary-color) !important;
}

.theme-dark .nav-link {
    color: var(--text-light) !important;
}

.theme-dark .sidebar-header .subtitle {
    color: var(--text-muted) !important;
}

.theme-dark .stat-label {
    color: var(--text-muted) !important;
}

.theme-dark .stat-value {
    color: var(--text-light) !important;
}

/* 深色主题下的表格样式 */
.theme-dark .table {
    color: var(--text-light) !important;
}

.theme-dark .table th {
    color: var(--text-light) !important;
    background-color: var(--bg-darker) !important;
    border-color: var(--border-color) !important;
}

.theme-dark .table td {
    color: var(--text-light) !important;
    border-color: var(--border-color) !important;
}

.theme-dark .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

/* 深色主题下的表单样式 */
.theme-dark .form-control {
    background-color: var(--bg-darker) !important;
    border-color: var(--border-color) !important;
    color: var(--text-light) !important;
}

.theme-dark .form-control:focus {
    background-color: var(--bg-darker) !important;
    border-color: var(--primary-color) !important;
    color: var(--text-light) !important;
    box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25) !important;
}

.theme-dark .form-label {
    color: var(--text-light) !important;
}

.theme-dark .form-select {
    background-color: var(--bg-darker) !important;
    border-color: var(--border-color) !important;
    color: var(--text-light) !important;
}

/* 浅色主题下的outline按钮样式 */
.theme-light .btn-outline-info,
body:not(.theme-dark) .btn-outline-info {
    color: var(--info-color) !important;
    border-color: var(--info-color) !important;
}

.theme-light .btn-outline-info:hover,
body:not(.theme-dark) .btn-outline-info:hover {
    background-color: var(--info-color) !important;
    color: var(--text-light) !important;
}

.theme-light .btn-outline-danger,
body:not(.theme-dark) .btn-outline-danger {
    color: var(--danger-color) !important;
    border-color: var(--danger-color) !important;
}

.theme-light .btn-outline-danger:hover,
body:not(.theme-dark) .btn-outline-danger:hover {
    background-color: var(--danger-color) !important;
    color: var(--text-light) !important;
}

/* 深色主题下的按钮样式 */
.theme-dark .btn-outline-primary {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.theme-dark .btn-outline-primary:hover {
    background-color: var(--primary-color) !important;
    color: var(--text-light) !important;
}

.theme-dark .btn-outline-secondary {
    color: var(--text-light) !important;
    border-color: var(--border-color) !important;
}

.theme-dark .btn-outline-secondary:hover {
    background-color: var(--border-color) !important;
    color: var(--text-dark) !important;
}

/* 深色主题下的消息样式 */
.theme-dark .message-container {
    background-color: var(--bg-darker) !important;
    color: var(--text-light) !important;
}

.theme-dark .message-item {
    background-color: var(--card-bg) !important;
    color: var(--text-light) !important;
}

.theme-dark .message-payload {
    color: var(--text-light) !important;
}

/* 深色主题下的活动日志样式 */
.theme-dark .activity-log {
    background-color: var(--bg-darker) !important;
}

.theme-dark .activity-item {
    background-color: var(--card-bg) !important;
    color: var(--text-light) !important;
}

/* 深色主题下的模态框样式 */
.theme-dark .modal-content {
    background-color: var(--card-bg) !important;
    color: var(--text-light) !important;
}

.theme-dark .modal-header {
    border-bottom-color: var(--border-color) !important;
}

.theme-dark .modal-footer {
    border-top-color: var(--border-color) !important;
}

.theme-dark .modal-title {
    color: var(--text-light) !important;
}

/* 深色主题下的输入组样式 */
.theme-dark .input-group-text {
    background-color: var(--bg-darker) !important;
    border-color: var(--border-color) !important;
    color: var(--text-light) !important;
}

/* 深色主题下的分页样式 */
.theme-dark .page-link {
    background-color: var(--card-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-light) !important;
}

.theme-dark .page-link:hover {
    background-color: var(--bg-darker) !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
}

.theme-dark .page-item.active .page-link {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: var(--text-light) !important;
}

/* 深色主题下的警告框样式 */
.theme-dark .alert {
    border-color: var(--border-color) !important;
}

.theme-dark .alert-info {
    background-color: rgba(96, 165, 250, 0.1) !important;
    border-color: var(--info-color) !important;
    color: var(--info-color) !important;
}

.theme-dark .alert-warning {
    background-color: rgba(251, 191, 36, 0.1) !important;
    border-color: var(--warning-color) !important;
    color: var(--warning-color) !important;
}

.theme-dark .alert-success {
    background-color: rgba(52, 211, 153, 0.1) !important;
    border-color: var(--success-color) !important;
    color: var(--success-color) !important;
}

.theme-dark .alert-danger {
    background-color: rgba(248, 113, 113, 0.1) !important;
    border-color: var(--danger-color) !important;
    color: var(--danger-color) !important;
}

/* 深色主题下的表单帮助文本 */
.theme-dark .form-text {
    color: var(--text-muted) !important;
}

/* 浅色主题下的outline按钮样式 - 修复可见性问题 */
.theme-light .btn-outline-light,
body:not(.theme-dark) .btn-outline-light {
    color: var(--text-dark) !important;
    border-color: var(--text-muted) !important;
    background-color: transparent !important;
}

.theme-light .btn-outline-light:hover,
body:not(.theme-dark) .btn-outline-light:hover {
    background-color: var(--text-muted) !important;
    border-color: var(--text-muted) !important;
    color: var(--text-light) !important;
}

.theme-light .btn-outline-light:focus,
body:not(.theme-dark) .btn-outline-light:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25) !important;
}

/* 深色主题下的outline按钮样式 */
.theme-dark .btn-outline-light {
    color: var(--text-light) !important;
    border-color: var(--border-color) !important;
}

.theme-dark .btn-outline-light:hover {
    background-color: var(--border-color) !important;
    border-color: var(--border-color) !important;
    color: var(--text-dark) !important;
}

.theme-dark .btn-outline-light:focus {
    box-shadow: 0 0 0 0.2rem rgba(209, 213, 219, 0.25) !important;
}

/* 深色主题下的占位符文本 */
.theme-dark .form-control::placeholder {
    color: var(--text-muted) !important;
    opacity: 0.7;
}

.theme-dark textarea.form-control::placeholder {
    color: var(--text-muted) !important;
    opacity: 0.7;
}

/* 深色主题下的下拉选择框 */
.theme-dark .form-select option {
    background-color: var(--bg-darker) !important;
    color: var(--text-light) !important;
}

/* 浅色主题下的面包屑导航 */
.theme-light .breadcrumb,
body:not(.theme-dark) .breadcrumb {
    background-color: transparent !important;
}

.theme-light .breadcrumb-item a,
body:not(.theme-dark) .breadcrumb-item a {
    color: var(--primary-color) !important;
    text-decoration: none;
}

.theme-light .breadcrumb-item a:hover,
body:not(.theme-dark) .breadcrumb-item a:hover {
    color: var(--text-dark) !important;
    text-decoration: underline;
}

/* 深色主题下的面包屑导航 */
.theme-dark .breadcrumb {
    background-color: transparent !important;
}

.theme-dark .breadcrumb-item a {
    color: var(--text-muted) !important;
}

.theme-dark .breadcrumb-item a:hover {
    color: var(--primary-color) !important;
}

/* 浅色主题下的侧边栏切换按钮 */
.theme-light .sidebar-toggle,
body:not(.theme-dark) .sidebar-toggle {
    color: var(--text-dark) !important;
    border-color: var(--text-muted) !important;
}

.theme-light .sidebar-toggle:hover,
body:not(.theme-dark) .sidebar-toggle:hover {
    background-color: var(--text-muted) !important;
    color: var(--text-light) !important;
}

/* 深色主题下的侧边栏切换按钮 */
.theme-dark .sidebar-toggle {
    color: var(--text-light) !important;
    border-color: var(--border-color) !important;
}

.theme-dark .sidebar-toggle:hover {
    background-color: var(--border-color) !important;
    color: var(--text-dark) !important;
}

/* 浅色主题下的文本颜色修复 */
.theme-light .text-white,
body:not(.theme-dark) .text-white {
    color: var(--text-dark) !important;
}

.theme-light .text-light,
body:not(.theme-dark) .text-light {
    color: var(--text-muted) !important;
}

/* 浅色主题下的背景色+文本色组合修复 */
.theme-light .bg-primary.text-white,
body:not(.theme-dark) .bg-primary.text-white,
.theme-light .card-header.bg-primary.text-white,
body:not(.theme-dark) .card-header.bg-primary.text-white {
    color: var(--text-light) !important;
}

.theme-light .bg-success.text-white,
body:not(.theme-dark) .bg-success.text-white,
.theme-light .card-header.bg-success.text-white,
body:not(.theme-dark) .card-header.bg-success.text-white {
    color: var(--text-light) !important;
}

.theme-light .bg-danger.text-white,
body:not(.theme-dark) .bg-danger.text-white {
    color: var(--text-light) !important;
}

.theme-light .bg-warning.text-white,
body:not(.theme-dark) .bg-warning.text-white {
    color: var(--text-dark) !important;
}

.theme-light .bg-info.text-white,
body:not(.theme-dark) .bg-info.text-white {
    color: var(--text-light) !important;
}

.theme-light .bg-dark.text-white,
body:not(.theme-dark) .bg-dark.text-white {
    color: var(--text-light) !important;
}

/* 浅色主题下的表格样式修复 */
.theme-light .table,
body:not(.theme-dark) .table {
    color: var(--text-dark) !important;
}

.theme-light .table th,
body:not(.theme-dark) .table th {
    color: var(--text-dark) !important;
    border-color: var(--border-color) !important;
}

.theme-light .table td,
body:not(.theme-dark) .table td {
    color: var(--text-dark) !important;
    border-color: var(--border-color) !important;
}

/* 浅色主题下的输入框样式修复 */
.theme-light .form-control,
body:not(.theme-dark) .form-control {
    background-color: var(--bg-light) !important;
    border-color: var(--border-color) !important;
    color: var(--text-dark) !important;
}

.theme-light .form-control:focus,
body:not(.theme-dark) .form-control:focus {
    background-color: var(--bg-light) !important;
    border-color: var(--primary-color) !important;
    color: var(--text-dark) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* 浅色主题下的选择框样式修复 */
.theme-light .form-select,
body:not(.theme-dark) .form-select {
    background-color: var(--bg-light) !important;
    border-color: var(--border-color) !important;
    color: var(--text-dark) !important;
}

/* 深色主题下的Bootstrap文本颜色类 */
.theme-dark .text-primary {
    color: var(--primary-color) !important;
}

.theme-dark .text-secondary {
    color: var(--secondary-color) !important;
}

.theme-dark .text-success {
    color: var(--success-color) !important;
}

.theme-dark .text-danger {
    color: var(--danger-color) !important;
}

.theme-dark .text-warning {
    color: var(--warning-color) !important;
}

.theme-dark .text-info {
    color: var(--info-color) !important;
}

.theme-dark .text-white {
    color: var(--text-light) !important;
}

.theme-dark .text-light {
    color: var(--text-light) !important;
}

/* 深色主题下的代码块 */
.theme-dark code {
    background-color: var(--bg-darker) !important;
    color: var(--text-light) !important;
    border: 1px solid var(--border-color) !important;
}

.theme-dark .text-primary code {
    color: var(--primary-color) !important;
}

/* 深色主题下的徽章样式 */
.theme-dark .badge {
    color: var(--text-light) !important;
}

.theme-dark .bg-primary {
    background-color: var(--primary-color) !important;
}

.theme-dark .bg-secondary {
    background-color: var(--secondary-color) !important;
}

.theme-dark .bg-success {
    background-color: var(--success-color) !important;
}

.theme-dark .bg-danger {
    background-color: var(--danger-color) !important;
}

.theme-dark .bg-warning {
    background-color: var(--warning-color) !important;
    color: var(--text-dark) !important;
}

.theme-dark .bg-info {
    background-color: var(--info-color) !important;
}

/* 深色主题下的统计卡片 */
.theme-dark .stat-card {
    background-color: var(--card-bg) !important;
    border-color: var(--border-color) !important;
}

.theme-dark .stat-icon {
    opacity: 0.9;
}

.theme-dark .stat-value {
    color: var(--text-light) !important;
}

.theme-dark .stat-label {
    color: var(--text-muted) !important;
}

/* 深色主题下的图表样式 */
.theme-dark canvas {
    filter: brightness(1.1);
}

/* 深色主题下的小代码块 */
.theme-dark code[style*="font-size"] {
    background-color: var(--bg-darker) !important;
    color: var(--text-light) !important;
    border: 1px solid var(--border-color) !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
}

/* 深色主题下的表格悬停效果 */
.theme-dark .table-hover tbody tr:hover {
    background-color: rgba(96, 165, 250, 0.1) !important;
}

/* 深色主题下的列表组 */
.theme-dark .list-group-item {
    background-color: var(--card-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-light) !important;
}

.theme-dark .list-group-item:hover {
    background-color: var(--bg-darker) !important;
}

/* 深色主题下的加载动画 */
.theme-dark .loading {
    border-color: rgba(96, 165, 250, 0.3) !important;
    border-top-color: var(--primary-color) !important;
}

.theme-dark .spinner-border {
    color: var(--primary-color) !important;
}

/* 实时消息页面样式 */
.message-container {
    height: 500px;
    overflow-y: auto;
    background-color: var(--bg-darker);
    color: var(--text-dark);
    font-family: 'Courier New', monospace;
    font-size: 14px;
    padding: 15px;
}

.message-item {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-left: 3px solid var(--primary-color);
    background-color: var(--card-bg);
    border-radius: 4px;
}

.message-header {
    display: flex;
    justify-content-between;
    align-items: center;
    margin-bottom: 5px;
}

.message-topic {
    color: var(--warning-color);
    font-weight: bold;
}

.message-client {
    color: var(--success-color);
}

.message-time {
    color: var(--text-muted);
    font-size: 12px;
}

.message-payload {
    color: var(--text-dark);
    word-break: break-all;
}

/* 实时监控页面样式 */
.activity-log {
    height: 300px;
    overflow-y: auto;
    background-color: var(--bg-lighter);
    padding: 15px;
}

.activity-item {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-left: 3px solid var(--primary-color);
    background-color: var(--card-bg);
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-dark);
}

.activity-time {
    color: var(--text-muted);
    font-size: 12px;
    float: right;
}

.activity-type-connect {
    border-left-color: var(--success-color);
}

.activity-type-disconnect {
    border-left-color: var(--danger-color);
}

.activity-type-message {
    border-left-color: var(--primary-color);
}

.activity-type-subscribe {
    border-left-color: var(--warning-color);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-lighter);
    color: var(--text-dark);
    line-height: 1.6;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--bg-light) 0%, var(--card-bg) 100%);
    border-right: 1px solid var(--border-color);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.sidebar-header h3 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.sidebar-header .subtitle {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--text-dark);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: rgba(0, 123, 255, 0.1);
    border-left-color: var(--primary-color);
    color: var(--primary-color);
}

.nav-link.active {
    background-color: rgba(0, 123, 255, 0.15);
    border-left-color: var(--primary-color);
    color: var(--primary-color);
}

.nav-link i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

/* 主内容区域 */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    background-color: var(--bg-lighter);
}

.content-header {
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-lighter) 100%);
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.content-header h1 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.content-header .breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item {
    color: var(--text-muted);
}

.breadcrumb-item.active {
    color: var(--primary-color);
}

.content-body {
    padding: 2rem;
}

/* 卡片样式 */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    background: linear-gradient(135deg, var(--bg-darker) 0%, var(--card-bg) 100%);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
}

.card-body {
    padding: 1.5rem;
}

/* 统计卡片 */
.stat-card {
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--card-bg) 100%);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    color: var(--text-muted);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 表格样式 */
.table {
    color: var(--text-dark);
    background-color: var(--card-bg);
}

.table th {
    border-color: var(--border-color);
    background-color: var(--bg-lighter);
    color: var(--text-dark);
    font-weight: 600;
}

.table td {
    border-color: var(--border-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, var(--primary-color) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #1e7e34 100%);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%);
}

/* 表单样式 */
.form-control {
    background-color: var(--bg-light);
    border-color: var(--border-color);
    color: var(--text-dark);
}

.form-control:focus {
    background-color: var(--bg-light);
    border-color: var(--primary-color);
    color: var(--text-dark);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    color: var(--text-dark);
    font-weight: 500;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-online {
    background-color: var(--success-color);
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.status-offline {
    background-color: var(--danger-color);
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .content-header,
    .content-body {
        padding: 1rem;
    }
}

/* 工具提示 */
.tooltip-inner {
    background-color: var(--bg-darker);
    color: var(--text-light);
}

/* 模态框样式 */
.modal-content {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

.modal-header {
    border-bottom-color: var(--border-color);
}

.modal-footer {
    border-top-color: var(--border-color);
}

/* 进度条 */
.progress {
    background-color: var(--bg-darker);
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color) 0%, #0056b3 100%);
}

/* 徽章 */
.badge {
    font-weight: 500;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-darker);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}
