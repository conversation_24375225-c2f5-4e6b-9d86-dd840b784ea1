<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计信息 - MQTT管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义样式 -->
    <link href="css/common.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-server"></i> MQTT管理</h3>
            <div class="subtitle">服务端控制台</div>
        </div>
        
        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="clients.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>客户端管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="publish.html" class="nav-link">
                    <i class="fas fa-paper-plane"></i>
                    <span>消息发布</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="subscriptions.html" class="nav-link">
                    <i class="fas fa-rss"></i>
                    <span>订阅管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="messages.html" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    <span>消息管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-messages.html" class="nav-link">
                    <i class="fas fa-bolt"></i>
                    <span>实时消息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-monitor.html" class="nav-link">
                    <i class="fas fa-eye"></i>
                    <span>实时监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="stats.html" class="nav-link active">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计信息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <div class="content-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-chart-bar"></i> 统计信息</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html">首页</a></li>
                            <li class="breadcrumb-item active">统计信息</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-outline-light me-2" onclick="refreshStats()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <div class="form-check form-switch d-inline-block align-middle me-2">
                        <input class="form-check-input" type="checkbox" role="switch" id="autoRefreshSwitch">
                        <label class="form-check-label text-white" for="autoRefreshSwitch">自动刷新</label>
                    </div>
                    <button class="btn btn-outline-light me-2" onclick="exportStats()">
                        <i class="fas fa-download"></i> 导出
                    </button>
                    <button class="btn btn-outline-light d-md-none sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="content-body">
            <!-- 时间范围选择 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-end">
                        <div class="col-md-4">
                            <label for="timeRange" class="form-label">时间范围</label>
                            <select class="form-select" id="timeRange" onchange="changeTimeRange()">
                                <option value="1h">最近1小时</option>
                                <option value="6h">最近6小时</option>
                                <option value="24h" selected>最近24小时</option>
                                <option value="7d">最近7天</option>
                                <option value="30d">最近30天</option>
                            </select>
                        </div>
                        <div class="col-md-8">
                            <div class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <span>最后更新: <span id="lastUpdateTime">-</span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 核心指标 -->
            <div class="row mb-4">
                <div class="col-md-2 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-value text-primary" id="totalClients">0</div>
                        <div class="stat-label">总客户端</div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-success">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-value text-success" id="onlineClients">0</div>
                        <div class="stat-label">在线客户端</div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-info">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-value text-info" id="totalMessages">0</div>
                        <div class="stat-label">总消息数</div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-warning">
                            <i class="fas fa-rss"></i>
                        </div>
                        <div class="stat-value text-warning" id="totalSubscriptions">0</div>
                        <div class="stat-label">总订阅数</div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-danger">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-value text-danger" id="totalTopics">0</div>
                        <div class="stat-label">主题数</div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-secondary">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="stat-value text-secondary" id="totalBytes">0</div>
                        <div class="stat-label">数据量</div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-line"></i> 连接和消息趋势</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="trendChart" height="80"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-pie"></i> 协议分布</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="protocolChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- QoS和主题统计 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-bar"></i> QoS等级分布</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="qosChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-fire"></i> 热门主题 TOP 10</h5>
                        </div>
                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                            <div id="topTopicsList">
                                <div class="text-center py-3">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细统计表格 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-server"></i> 系统性能</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>CPU使用率:</strong></td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" id="cpuProgress" style="width: 0%">0%</div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>内存使用率:</strong></td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-info" id="memoryProgress" style="width: 0%">0%</div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>网络流量:</strong></td>
                                    <td id="networkTraffic">-</td>
                                </tr>
                                <tr>
                                    <td><strong>磁盘使用:</strong></td>
                                    <td id="diskUsage">-</td>
                                </tr>
                                <tr>
                                    <td><strong>运行时间:</strong></td>
                                    <td id="uptime">-</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-list"></i> 连接统计</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>总连接数:</strong></td>
                                    <td><span id="connectionStats_total">0</span></td>
                                </tr>
                                <tr>
                                    <td><strong>活跃连接:</strong></td>
                                    <td><span id="connectionStats_active">0</span></td>
                                </tr>
                                <tr>
                                    <td><strong>MQTT连接:</strong></td>
                                    <td><span id="connectionStats_mqtt">0</span></td>
                                </tr>
                                <tr>
                                    <td><strong>WebSocket连接:</strong></td>
                                    <td><span id="connectionStats_ws">0</span></td>
                                </tr>
                                <tr>
                                    <td><strong>SSL连接:</strong></td>
                                    <td><span id="connectionStats_ssl">0</span></td>
                                </tr>
                                <tr>
                                    <td><strong>平均连接时长:</strong></td>
                                    <td><span id="avgConnectionDuration">-</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/common.js"></script>
    <script>
        let trendChart, protocolChart, qosChart;
        let autoRefreshInterval = null;
        let startTime = new Date();

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            loadStatistics();
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('autoRefreshSwitch').addEventListener('change', function() {
                if (this.checked) {
                    if (!autoRefreshInterval) {
                        autoRefreshInterval = setInterval(loadStatistics, CONFIG.UI.CHART_REFRESH_INTERVAL || 10000);
                    }
                } else {
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                        autoRefreshInterval = null;
                    }
                }
            });
        }

        // 初始化图表
        function initializeCharts() {
            // 趋势图表
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            trendChart = new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '连接数',
                        data: [],
                        borderColor: CONFIG.THEME.PRIMARY_COLOR,
                        backgroundColor: CONFIG.THEME.PRIMARY_COLOR + '20',
                        tension: 0.4
                    }, {
                        label: '消息数/分钟',
                        data: [],
                        borderColor: CONFIG.THEME.SUCCESS_COLOR,
                        backgroundColor: CONFIG.THEME.SUCCESS_COLOR + '20',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: '#e9ecef' }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#6c757d' },
                            grid: { color: '#2d3748' }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            ticks: { color: '#6c757d' },
                            grid: { color: '#2d3748' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            ticks: { color: '#6c757d' },
                            grid: { drawOnChartArea: false }
                        }
                    }
                }
            });

            // 协议分布图表
            const protocolCtx = document.getElementById('protocolChart').getContext('2d');
            protocolChart = new Chart(protocolCtx, {
                type: 'doughnut',
                data: {
                    labels: ['MQTT', 'WebSocket', 'SSL'],
                    datasets: [{
                        data: [0, 0, 0],
                        backgroundColor: [
                            CONFIG.THEME.PRIMARY_COLOR,
                            CONFIG.THEME.SUCCESS_COLOR,
                            CONFIG.THEME.WARNING_COLOR
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: { color: '#e9ecef' }
                        }
                    }
                }
            });

            // QoS分布图表
            const qosCtx = document.getElementById('qosChart').getContext('2d');
            qosChart = new Chart(qosCtx, {
                type: 'bar',
                data: {
                    labels: ['QoS 0', 'QoS 1', 'QoS 2'],
                    datasets: [{
                        label: '消息数量',
                        data: [0, 0, 0],
                        backgroundColor: [
                            CONFIG.THEME.INFO_COLOR,
                            CONFIG.THEME.SUCCESS_COLOR,
                            CONFIG.THEME.WARNING_COLOR
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#6c757d' },
                            grid: { color: '#2d3748' }
                        },
                        y: {
                            ticks: { color: '#6c757d' },
                            grid: { color: '#2d3748' }
                        }
                    }
                }
            });
        }

        // 加载统计数据
        async function loadStatistics() {
            try {
                // 加载基础统计
                const stats = await mqttUI.apiCall('GET', '/stats');
                if (stats && stats.code === 1) {
                    updateBasicStats(stats.data);
                }

                // 加载客户端数据
                const clients = await mqttUI.apiCall('GET', '/clients', { _limit: 1000 });
                if (clients && clients.code === 1) {
                    updateClientStats(clients.data.list);
                    updateProtocolChart(clients.data.list);
                }

                // 更新趋势图表
                updateTrendChart();

                // 更新系统性能（模拟数据）
                updateSystemPerformance();

                // 更新最后更新时间
                document.getElementById('lastUpdateTime').textContent = new Date().toLocaleString('zh-CN');

            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 更新基础统计
        function updateBasicStats(data) {
            document.getElementById('totalClients').textContent = mqttUI.formatNumber(data.connections || 0);
            document.getElementById('onlineClients').textContent = mqttUI.formatNumber(data.connections || 0);
            document.getElementById('totalMessages').textContent = mqttUI.formatNumber(data.messages || 0);
            document.getElementById('totalSubscriptions').textContent = mqttUI.formatNumber(data.subscriptions || 0);
            document.getElementById('totalTopics').textContent = mqttUI.formatNumber(data.topics || 0);
            document.getElementById('totalBytes').textContent = mqttUI.formatBytes(data.bytes || 0);
        }

        // 更新客户端统计
        function updateClientStats(clients) {
            const onlineClients = clients.filter(c => c.connected);
            const mqttClients = clients.filter(c => c.protoName === 'MQTT');
            const wsClients = clients.filter(c => c.protoName === 'WebSocket');
            const sslClients = clients.filter(c => c.ssl === true);

            document.getElementById('connectionStats_total').textContent = mqttUI.formatNumber(clients.length);
            document.getElementById('connectionStats_active').textContent = mqttUI.formatNumber(onlineClients.length);
            document.getElementById('connectionStats_mqtt').textContent = mqttUI.formatNumber(mqttClients.length);
            document.getElementById('connectionStats_ws').textContent = mqttUI.formatNumber(wsClients.length);
            document.getElementById('connectionStats_ssl').textContent = mqttUI.formatNumber(sslClients.length);

            // 计算平均连接时长
            if (onlineClients.length > 0) {
                const avgDuration = onlineClients.reduce((sum, client) => {
                    return sum + (Date.now() - client.connectedAt);
                }, 0) / onlineClients.length;

                const hours = Math.floor(avgDuration / (1000 * 60 * 60));
                const minutes = Math.floor((avgDuration % (1000 * 60 * 60)) / (1000 * 60));
                document.getElementById('avgConnectionDuration').textContent = `${hours}小时${minutes}分钟`;
            } else {
                document.getElementById('avgConnectionDuration').textContent = '-';
            }

            // 更新热门主题
            updateTopTopics(clients);
        }

        // 更新协议分布图表
        function updateProtocolChart(clients) {
            const mqtt = clients.filter(c => c.protoName === 'MQTT').length;
            const websocket = clients.filter(c => c.protoName === 'WebSocket').length;
            const ssl = clients.filter(c => c.ssl === true).length;

            protocolChart.data.datasets[0].data = [mqtt, websocket, ssl];
            protocolChart.update();
        }

        // 更新趋势图表
        function updateTrendChart() {
            const now = new Date();
            const timeLabel = now.toLocaleTimeString();

            // 模拟数据
            const connections = Math.floor(Math.random() * 100) + 50;
            const messages = Math.floor(Math.random() * 500) + 100;

            trendChart.data.labels.push(timeLabel);
            trendChart.data.datasets[0].data.push(connections);
            trendChart.data.datasets[1].data.push(messages);

            // 保持数据点数量
            const maxDataPoints = 20;
            if (trendChart.data.labels.length > maxDataPoints) {
                trendChart.data.labels.shift();
                trendChart.data.datasets[0].data.shift();
                trendChart.data.datasets[1].data.shift();
            }

            trendChart.update('none');

            // 更新QoS图表（模拟数据）
            const qos0 = Math.floor(Math.random() * 1000) + 500;
            const qos1 = Math.floor(Math.random() * 500) + 200;
            const qos2 = Math.floor(Math.random() * 100) + 50;

            qosChart.data.datasets[0].data = [qos0, qos1, qos2];
            qosChart.update();
        }

        // 更新热门主题
        async function updateTopTopics(clients) {
            const topicCounts = {};

            // 模拟主题数据
            const sampleTopics = [
                'sensor/temperature/room1',
                'sensor/humidity/room1',
                'device/status/device1',
                'alert/warning/system',
                'data/telemetry/sensor1',
                'sensor/temperature/room2',
                'device/status/device2',
                'alert/info/system',
                'data/logs/application',
                'sensor/pressure/tank1'
            ];

            sampleTopics.forEach(topic => {
                topicCounts[topic] = Math.floor(Math.random() * 1000) + 100;
            });

            const sortedTopics = Object.entries(topicCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10);

            const container = document.getElementById('topTopicsList');
            container.innerHTML = sortedTopics.map(([topic, count], index) => `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 rounded"
                     style="background: var(--bg-darker);">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary me-2">${index + 1}</span>
                        <code style="font-size: 0.9rem;">${topic}</code>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-success">${mqttUI.formatNumber(count)}</span>
                        <div class="small text-muted">${((count / 10000) * 100).toFixed(1)}%</div>
                    </div>
                </div>
            `).join('');
        }

        // 更新系统性能
        function updateSystemPerformance() {
            document.getElementById('lastUpdateTime').textContent = new Date().toLocaleString('zh-CN');
            const uptimeSeconds = (new Date() - startTime) / 1000;
            document.getElementById('uptime').textContent = mqttUI.formatDuration(uptimeSeconds);
        }

        // 改变时间范围
        function changeTimeRange() {
            // 重新加载数据以反映新的时间范围
            loadStatistics();
        }

        // 刷新统计
        function refreshStats() {
            loadStatistics();
            mqttUI.showToast('统计数据已刷新', 'success');
        }

        // 导出统计数据
        function exportStats() {
            const statsData = {
                timestamp: new Date().toISOString(),
                basicStats: {
                    totalClients: document.getElementById('totalClients').textContent,
                    onlineClients: document.getElementById('onlineClients').textContent,
                    totalMessages: document.getElementById('totalMessages').textContent,
                    totalSubscriptions: document.getElementById('totalSubscriptions').textContent,
                    totalTopics: document.getElementById('totalTopics').textContent,
                    totalBytes: document.getElementById('totalBytes').textContent
                },
                systemPerformance: {
                    cpuUsage: document.getElementById('cpuProgress').textContent,
                    memoryUsage: document.getElementById('memoryProgress').textContent,
                    networkTraffic: document.getElementById('networkTraffic').textContent,
                    diskUsage: document.getElementById('diskUsage').textContent,
                    uptime: document.getElementById('uptime').textContent
                },
                connectionStats: {
                    total: document.getElementById('connectionStats_total').textContent,
                    active: document.getElementById('connectionStats_active').textContent,
                    mqtt: document.getElementById('connectionStats_mqtt').textContent,
                    websocket: document.getElementById('connectionStats_ws').textContent,
                    ssl: document.getElementById('connectionStats_ssl').textContent,
                    avgDuration: document.getElementById('avgConnectionDuration').textContent
                }
            };

            const blob = new Blob([JSON.stringify(statsData, null, 2)], {
                type: 'application/json'
            });

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mqtt_stats_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            mqttUI.showToast('统计数据导出成功', 'success');
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>

</body>
</html>
