<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时消息 - MQTT管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/common.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-server"></i> MQTT管理</h3>
            <div class="subtitle">服务端控制台</div>
        </div>
        
        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="clients.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>客户端管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="publish.html" class="nav-link">
                    <i class="fas fa-paper-plane"></i>
                    <span>消息发布</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="subscriptions.html" class="nav-link">
                    <i class="fas fa-rss"></i>
                    <span>订阅管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="messages.html" class="nav-link active">
                    <i class="fas fa-envelope"></i>
                    <span>消息管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-messages.html" class="nav-link">
                    <i class="fas fa-bolt"></i>
                    <span>实时消息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-monitor.html" class="nav-link">
                    <i class="fas fa-eye"></i>
                    <span>实时监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="stats.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计信息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <div class="content-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-comments"></i> 实时消息监控</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html">首页</a></li>
                            <li class="breadcrumb-item active">实时消息</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-outline-light me-2" onclick="toggleMonitoring()" id="monitorBtn">
                        <i class="fas fa-play"></i> 开始监控
                    </button>
                    <button class="btn btn-outline-light d-md-none sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="content-body">
            <!-- 监控控制面板 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog"></i> 监控设置</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="topicFilter" class="form-label">主题过滤</label>
                                <input type="text" class="form-control" id="topicFilter" 
                                       placeholder="例如: sensor/+/temperature" value="#">
                                <div class="form-text">支持通配符 + 和 #</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="qosFilter" class="form-label">QoS过滤</label>
                                <select class="form-select" id="qosFilter">
                                    <option value="">全部</option>
                                    <option value="0">QoS 0</option>
                                    <option value="1">QoS 1</option>
                                    <option value="2">QoS 2</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="maxMessages" class="form-label">最大消息数</label>
                                <input type="number" class="form-control" id="maxMessages" 
                                       value="100" min="10" max="1000">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label for="autoScroll" class="form-label">自动滚动</label>
                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="autoScroll" checked>
                                    <label class="form-check-label" for="autoScroll">启用</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">操作</label>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-secondary btn-sm" onclick="clearMessages()">
                                        <i class="fas fa-trash"></i> 清空
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="exportMessages()">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 消息统计 -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-primary">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-value text-primary" id="totalMessages">0</div>
                        <div class="stat-label">总消息数</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-success">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="stat-value text-success" id="messageRate">0</div>
                        <div class="stat-label">消息/秒</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-info">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-value text-info" id="uniqueTopics">0</div>
                        <div class="stat-label">唯一主题</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon text-warning">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="stat-value text-warning" id="totalBytes">0</div>
                        <div class="stat-label">总字节数</div>
                    </div>
                </div>
            </div>

            <!-- 消息列表 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> 消息流
                        <span class="badge bg-primary ms-2" id="messageCount">0</span>
                    </h5>
                    <div>
                        <span class="text-muted me-3">状态: </span>
                        <span class="badge" id="monitorStatus">已停止</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="messagesContainer" style="height: 600px; overflow-y: auto; background: var(--bg-darker);">
                        <div class="text-center py-5 text-muted">
                            <i class="fas fa-play-circle fa-3x mb-3"></i>
                            <div>点击"开始监控"开始接收消息</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 消息详情模态框 -->
            <div class="modal fade" id="messageDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title"><i class="fas fa-envelope-open"></i> 消息详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="messageDetailBody">
                            <!-- 动态内容 -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" onclick="copyMessageContent()">
                                <i class="fas fa-copy"></i> 复制内容
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/common.js"></script>
    <script>
        let isMonitoring = false;
        let messages = [];
        let messageStats = {
            total: 0,
            rate: 0,
            uniqueTopics: new Set(),
            totalBytes: 0
        };
        let lastMessageTime = Date.now();
        let messageRateInterval;
        let currentMessageDetail = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            updateStats();
            startMessageRateCalculation();
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 监听设置变化
            document.getElementById('topicFilter').addEventListener('change', applyMessageFilter);
            document.getElementById('qosFilter').addEventListener('change', applyMessageFilter);
        }

        // 开始消息速率计算
        function startMessageRateCalculation() {
            messageRateInterval = setInterval(() => {
                const now = Date.now();
                const timeDiff = (now - lastMessageTime) / 1000;

                if (timeDiff > 5) {
                    messageStats.rate = 0;
                } else {
                    // 计算最近5秒的消息速率
                    const recentMessages = messages.filter(msg =>
                        (now - msg.timestamp) < 5000
                    );
                    messageStats.rate = Math.round(recentMessages.length / 5);
                }

                updateStats();
            }, 1000);
        }

        // 切换监控状态
        function toggleMonitoring() {
            if (isMonitoring) {
                stopMonitoring();
            } else {
                startMonitoring();
            }
        }

        // 开始监控
        function startMonitoring() {
            isMonitoring = true;

            const monitorBtn = document.getElementById('monitorBtn');
            const statusBadge = document.getElementById('monitorStatus');

            monitorBtn.innerHTML = '<i class="fas fa-stop"></i> 停止监控';
            monitorBtn.className = 'btn btn-outline-danger me-2';
            statusBadge.textContent = '监控中';
            statusBadge.className = 'badge bg-success';

            // 模拟消息接收（实际项目中应该使用WebSocket）
            simulateMessageReceiving();

            mqttUI.showToast('消息监控已启动', 'success');
        }

        // 停止监控
        function stopMonitoring() {
            isMonitoring = false;

            const monitorBtn = document.getElementById('monitorBtn');
            const statusBadge = document.getElementById('monitorStatus');

            monitorBtn.innerHTML = '<i class="fas fa-play"></i> 开始监控';
            monitorBtn.className = 'btn btn-outline-light me-2';
            statusBadge.textContent = '已停止';
            statusBadge.className = 'badge bg-secondary';

            mqttUI.showToast('消息监控已停止', 'info');
        }

        // 模拟消息接收
        function simulateMessageReceiving() {
            if (!isMonitoring) return;

            // 模拟随机消息
            const topics = [
                'sensor/temperature/room1',
                'sensor/humidity/room1',
                'device/status/device1',
                'alert/warning/system',
                'data/telemetry/sensor1'
            ];

            const randomTopic = topics[Math.floor(Math.random() * topics.length)];
            const randomPayload = generateRandomPayload(randomTopic);

            const message = {
                id: Date.now() + Math.random(),
                topic: randomTopic,
                payload: randomPayload,
                qos: Math.floor(Math.random() * 3),
                retain: Math.random() > 0.8,
                timestamp: Date.now(),
                clientId: `client_${Math.floor(Math.random() * 100)}`,
                size: new Blob([randomPayload]).size
            };

            addMessage(message);

            // 随机间隔继续接收
            setTimeout(simulateMessageReceiving, Math.random() * 2000 + 500);
        }

        // 生成随机消息内容
        function generateRandomPayload(topic) {
            if (topic.includes('temperature')) {
                return JSON.stringify({
                    value: (Math.random() * 30 + 10).toFixed(2),
                    unit: '°C',
                    timestamp: Date.now()
                });
            } else if (topic.includes('humidity')) {
                return JSON.stringify({
                    value: (Math.random() * 60 + 30).toFixed(2),
                    unit: '%',
                    timestamp: Date.now()
                });
            } else if (topic.includes('status')) {
                return JSON.stringify({
                    status: Math.random() > 0.7 ? 'online' : 'offline',
                    battery: Math.floor(Math.random() * 100),
                    timestamp: Date.now()
                });
            } else if (topic.includes('alert')) {
                return JSON.stringify({
                    level: ['info', 'warning', 'error'][Math.floor(Math.random() * 3)],
                    message: 'System alert message',
                    code: Math.floor(Math.random() * 9000) + 1000,
                    timestamp: Date.now()
                });
            } else {
                return `Message data: ${Math.random().toString(36).substring(7)}`;
            }
        }

        // 添加消息
        function addMessage(message) {
            const topicFilter = document.getElementById('topicFilter').value;
            const qosFilter = document.getElementById('qosFilter').value;

            // 应用过滤器
            if (topicFilter && topicFilter !== '#' && !matchesTopic(message.topic, topicFilter)) {
                return;
            }

            if (qosFilter && message.qos.toString() !== qosFilter) {
                return;
            }

            messages.unshift(message);

            // 限制消息数量
            const maxMessages = parseInt(document.getElementById('maxMessages').value);
            if (messages.length > maxMessages) {
                messages = messages.slice(0, maxMessages);
            }

            // 更新统计
            messageStats.total++;
            messageStats.uniqueTopics.add(message.topic);
            messageStats.totalBytes += message.size;
            lastMessageTime = Date.now();

            renderMessages();
            updateStats();
        }

        // 主题匹配检查
        function matchesTopic(topic, filter) {
            if (filter === '#') return true;

            const topicParts = topic.split('/');
            const filterParts = filter.split('/');

            for (let i = 0; i < filterParts.length; i++) {
                if (filterParts[i] === '#') return true;
                if (filterParts[i] === '+') continue;
                if (filterParts[i] !== topicParts[i]) return false;
            }

            return topicParts.length === filterParts.length;
        }

        // 渲染消息列表
        function renderMessages() {
            const container = document.getElementById('messagesContainer');

            if (messages.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <div>暂无消息</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = messages.map(message => `
                <div class="message-item border-bottom p-3" onclick="showMessageDetail('${message.id}')">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-info me-2">QoS ${message.qos}</span>
                                ${message.retain ? '<span class="badge bg-warning me-2">保留</span>' : ''}
                                <code class="text-primary">${message.topic}</code>
                            </div>
                            <div class="message-payload text-truncate" style="max-width: 600px;">
                                ${formatPayload(message.payload)}
                            </div>
                            <div class="text-muted small mt-1">
                                <i class="fas fa-user me-1"></i>${message.clientId}
                                <i class="fas fa-clock ms-3 me-1"></i>${mqttUI.formatTime(message.timestamp)}
                                <i class="fas fa-database ms-3 me-1"></i>${mqttUI.formatBytes(message.size)}
                            </div>
                        </div>
                        <div class="text-end">
                            <button class="btn btn-sm btn-outline-info" onclick="event.stopPropagation(); showMessageDetail('${message.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            // 自动滚动
            if (document.getElementById('autoScroll').checked) {
                container.scrollTop = 0;
            }

            document.getElementById('messageCount').textContent = messages.length;
        }

        // 格式化消息内容
        function formatPayload(payload) {
            try {
                const parsed = JSON.parse(payload);
                return JSON.stringify(parsed, null, 2);
            } catch (e) {
                return payload;
            }
        }

        // 显示消息详情
        function showMessageDetail(messageId) {
            const message = messages.find(m => m.id.toString() === messageId.toString());
            if (!message) return;

            currentMessageDetail = message;

            document.getElementById('messageDetailBody').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-borderless table-sm">
                            <tr><td><strong>主题:</strong></td><td><code>${message.topic}</code></td></tr>
                            <tr><td><strong>QoS等级:</strong></td><td><span class="badge bg-info">QoS ${message.qos}</span></td></tr>
                            <tr><td><strong>保留消息:</strong></td><td>${message.retain ? '<span class="badge bg-warning">是</span>' : '<span class="badge bg-secondary">否</span>'}</td></tr>
                            <tr><td><strong>消息大小:</strong></td><td>${mqttUI.formatBytes(message.size)}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>发送信息</h6>
                        <table class="table table-borderless table-sm">
                            <tr><td><strong>客户端ID:</strong></td><td>${message.clientId}</td></tr>
                            <tr><td><strong>发送时间:</strong></td><td>${mqttUI.formatTime(message.timestamp)}</td></tr>
                            <tr><td><strong>消息ID:</strong></td><td>${message.id}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="mt-3">
                    <h6>消息内容</h6>
                    <pre class="bg-dark text-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"><code>${formatPayload(message.payload)}</code></pre>
                </div>
            `;

            new bootstrap.Modal(document.getElementById('messageDetailModal')).show();
        }

        // 复制消息内容
        function copyMessageContent() {
            if (!currentMessageDetail) return;

            navigator.clipboard.writeText(currentMessageDetail.payload).then(() => {
                mqttUI.showToast('消息内容已复制到剪贴板', 'success');
            }).catch(() => {
                mqttUI.showToast('复制失败', 'error');
            });
        }

        // 应用消息过滤器
        function applyMessageFilter() {
            renderMessages();
        }

        // 清空消息
        function clearMessages() {
            if (confirm('确定要清空所有消息吗？')) {
                messages = [];
                messageStats = {
                    total: 0,
                    rate: 0,
                    uniqueTopics: new Set(),
                    totalBytes: 0
                };
                renderMessages();
                updateStats();
                mqttUI.showToast('消息已清空', 'success');
            }
        }

        // 导出消息
        function exportMessages() {
            if (messages.length === 0) {
                mqttUI.showToast('没有消息可导出', 'warning');
                return;
            }

            const exportData = messages.map(msg => ({
                timestamp: new Date(msg.timestamp).toISOString(),
                topic: msg.topic,
                payload: msg.payload,
                qos: msg.qos,
                retain: msg.retain,
                clientId: msg.clientId,
                size: msg.size
            }));

            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mqtt_messages_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            mqttUI.showToast('消息导出成功', 'success');
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalMessages').textContent = mqttUI.formatNumber(messageStats.total);
            document.getElementById('messageRate').textContent = mqttUI.formatNumber(messageStats.rate);
            document.getElementById('uniqueTopics').textContent = mqttUI.formatNumber(messageStats.uniqueTopics.size);
            document.getElementById('totalBytes').textContent = mqttUI.formatBytes(messageStats.totalBytes);
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (messageRateInterval) {
                clearInterval(messageRateInterval);
            }
        });
    </script>

    <style>
        .message-item {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .message-item:hover {
            background-color: rgba(0, 123, 255, 0.1);
        }

        .message-payload {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: var(--text-dark);
        }

        .theme-dark .message-payload {
            color: var(--text-light);
        }
    </style>
</body>
</html>
