<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时消息 - MQTT管理界面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/common.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-server"></i> MQTT管理</h3>
            <div class="subtitle">服务端控制台</div>
        </div>

        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="clients.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>客户端管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="publish.html" class="nav-link">
                    <i class="fas fa-paper-plane"></i>
                    <span>消息发布</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="subscriptions.html" class="nav-link">
                    <i class="fas fa-rss"></i>
                    <span>订阅管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="messages.html" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    <span>消息管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-messages.html" class="nav-link active">
                    <i class="fas fa-bolt"></i>
                    <span>实时消息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="realtime-monitor.html" class="nav-link">
                    <i class="fas fa-eye"></i>
                    <span>实时监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="stats.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计信息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="settings.html" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
                <div class="content-header">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2"><i class="fas fa-bolt"></i> 实时消息</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearMessages()">
                                    <i class="fas fa-trash"></i> 清空
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleAutoScroll()">
                                    <i class="fas fa-arrows-alt-v"></i> <span id="autoScrollText">自动滚动</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-body">
                    <!-- 连接状态 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <div class="status-indicator" id="connectionStatus">
                                                <i class="fas fa-circle text-danger"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">WebSocket连接状态</h6>
                                            <small class="text-muted" id="connectionText">未连接</small>
                                        </div>
                                        <div class="ms-auto">
                                            <button class="btn btn-sm btn-primary" onclick="connectWebSocket()" id="connectBtn">
                                                <i class="fas fa-plug"></i> 连接
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 订阅控制 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-rss"></i> 订阅主题</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <input type="text" class="form-control" id="subscribeTopicInput"
                                                   placeholder="输入要订阅的主题，如: sensor/+/temperature" value="#">
                                        </div>
                                        <div class="col-md-4">
                                            <button class="btn btn-success" onclick="subscribeToTopic()" id="subscribeBtn" disabled>
                                                <i class="fas fa-plus"></i> 订阅
                                            </button>
                                            <button class="btn btn-warning ms-2" onclick="unsubscribeFromTopic()" id="unsubscribeBtn" disabled>
                                                <i class="fas fa-minus"></i> 取消订阅
                                            </button>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">当前订阅: <span id="currentSubscription">无</span></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 消息过滤 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-filter"></i> 消息过滤</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="filterTopic" class="form-label">主题过滤</label>
                                            <input type="text" class="form-control" id="filterTopic"
                                                   placeholder="输入主题关键词" onkeyup="applyMessageFilter()">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="filterClient" class="form-label">客户端过滤</label>
                                            <input type="text" class="form-control" id="filterClient"
                                                   placeholder="输入客户端ID" onkeyup="applyMessageFilter()">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="filterPayload" class="form-label">载荷过滤</label>
                                            <input type="text" class="form-control" id="filterPayload"
                                                   placeholder="输入载荷关键词" onkeyup="applyMessageFilter()">
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="showSystemMessages"
                                                       checked onchange="applyMessageFilter()">
                                                <label class="form-check-label" for="showSystemMessages">
                                                    显示系统消息
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <button class="btn btn-sm btn-outline-secondary" onclick="clearFilters()">
                                                <i class="fas fa-times"></i> 清除过滤
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 消息列表 -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list"></i> 实时消息</h5>
                            <div>
                                <small class="text-muted">消息数量: <span id="messageCount">0</span></small>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="message-container" id="messageContainer">
                                <div class="text-center py-4 text-muted">
                                    <i class="fas fa-info-circle"></i> 连接WebSocket并订阅主题以查看实时消息
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 通用JS -->
    <script src="js/config.js"></script>
    <script src="js/common.js"></script>



    <script>
        let websocket = null;
        let messageCount = 0;
        let autoScroll = true;
        let currentSubscription = null;
        let allMessages = [];
        let filteredMessages = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化UI
            mqttUI.initializeTheme();

            // 自动连接WebSocket
            connectWebSocket();
        });

        // 连接WebSocket
        function connectWebSocket() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                return;
            }

            const wsUrl = CONFIG.WEBSOCKET.URL;

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    updateConnectionStatus(true);
                    addSystemMessage('WebSocket连接已建立');
                };

                websocket.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    handleWebSocketMessage(message);
                };

                websocket.onclose = function(event) {
                    updateConnectionStatus(false);
                    addSystemMessage('WebSocket连接已关闭');
                };

                websocket.onerror = function(error) {
                    updateConnectionStatus(false);
                    addSystemMessage('WebSocket连接错误: ' + error);
                };

            } catch (error) {
                updateConnectionStatus(false);
                addSystemMessage('WebSocket连接失败: ' + error.message);
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            switch (message.type) {
                case 'connection':
                    addSystemMessage('连接成功，会话ID: ' + message.sessionId);
                    break;
                case 'mqtt_message':
                    addMqttMessage(message);
                    break;
                case 'subscribe_response':
                    addSystemMessage(message.message);
                    currentSubscription = message.topic;
                    updateSubscriptionStatus();
                    break;
                case 'unsubscribe_response':
                    addSystemMessage(message.message);
                    currentSubscription = null;
                    updateSubscriptionStatus();
                    break;
                case 'error':
                    addSystemMessage('错误: ' + message.message);
                    break;
            }
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            const textElement = document.getElementById('connectionText');
            const connectBtn = document.getElementById('connectBtn');
            const subscribeBtn = document.getElementById('subscribeBtn');
            const unsubscribeBtn = document.getElementById('unsubscribeBtn');

            if (connected) {
                statusElement.innerHTML = '<i class="fas fa-circle text-success"></i>';
                textElement.textContent = '已连接';
                connectBtn.innerHTML = '<i class="fas fa-unlink"></i> 断开';
                connectBtn.onclick = disconnectWebSocket;
                subscribeBtn.disabled = false;
                unsubscribeBtn.disabled = false;
            } else {
                statusElement.innerHTML = '<i class="fas fa-circle text-danger"></i>';
                textElement.textContent = '未连接';
                connectBtn.innerHTML = '<i class="fas fa-plug"></i> 连接';
                connectBtn.onclick = connectWebSocket;
                subscribeBtn.disabled = true;
                unsubscribeBtn.disabled = true;
            }
        }

        // 断开WebSocket
        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }

        // 订阅主题
        function subscribeToTopic() {
            const topic = document.getElementById('subscribeTopicInput').value.trim();
            if (!topic) {
                mqttUI.showToast('请输入主题', 'error');
                return;
            }

            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(JSON.stringify({
                    type: 'subscribe',
                    topic: topic
                }));
            }
        }

        // 取消订阅
        function unsubscribeFromTopic() {
            if (currentSubscription && websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(JSON.stringify({
                    type: 'unsubscribe',
                    topic: currentSubscription
                }));
            }
        }

        // 更新订阅状态
        function updateSubscriptionStatus() {
            document.getElementById('currentSubscription').textContent = currentSubscription || '无';
        }

        // 添加MQTT消息
        function addMqttMessage(message) {
            // 添加到消息数组
            const messageData = {
                ...message,
                type: 'mqtt',
                id: Date.now() + Math.random()
            };
            allMessages.push(messageData);

            // 限制消息数量
            const maxMessages = 1000;
            if (allMessages.length > maxMessages) {
                allMessages.shift();
            }

            // 应用过滤并重新渲染
            applyMessageFilter();
        }

        // 添加系统消息
        function addSystemMessage(text) {
            // 添加到消息数组
            const messageData = {
                type: 'system',
                message: text,
                timestamp: Date.now(),
                id: Date.now() + Math.random()
            };
            allMessages.push(messageData);

            // 应用过滤并重新渲染
            applyMessageFilter();
        }

        // 应用消息过滤
        function applyMessageFilter() {
            const topicFilter = document.getElementById('filterTopic').value.toLowerCase();
            const clientFilter = document.getElementById('filterClient').value.toLowerCase();
            const payloadFilter = document.getElementById('filterPayload').value.toLowerCase();
            const showSystemMessages = document.getElementById('showSystemMessages').checked;

            filteredMessages = allMessages.filter(msg => {
                // 系统消息过滤
                if (msg.type === 'system') {
                    return showSystemMessages &&
                           (!topicFilter || msg.message.toLowerCase().includes(topicFilter));
                }

                // MQTT消息过滤
                const topicMatch = !topicFilter || msg.topic.toLowerCase().includes(topicFilter);
                const clientMatch = !clientFilter || msg.clientId.toLowerCase().includes(clientFilter);
                const payloadMatch = !payloadFilter || msg.payload.toLowerCase().includes(payloadFilter);

                return topicMatch && clientMatch && payloadMatch;
            });

            renderMessages();
        }

        // 渲染消息
        function renderMessages() {
            const container = document.getElementById('messageContainer');
            container.innerHTML = '';

            if (filteredMessages.length === 0) {
                container.innerHTML = '<div class="text-center py-4 text-muted"><i class="fas fa-info-circle"></i> 没有符合条件的消息</div>';
                return;
            }

            filteredMessages.forEach(msg => {
                const messageElement = document.createElement('div');
                messageElement.className = 'message-item';

                if (msg.type === 'system') {
                    messageElement.style.borderLeftColor = '#6c757d';
                    messageElement.innerHTML = `
                        <div class="message-header">
                            <span style="color: #6c757d;">系统消息</span>
                            <span class="message-time">${new Date(msg.timestamp).toLocaleString()}</span>
                        </div>
                        <div class="message-payload">${msg.message}</div>
                    `;
                } else {
                    messageElement.innerHTML = `
                        <div class="message-header">
                            <span class="message-topic">${msg.topic}</span>
                            <span class="message-time">${new Date(msg.timestamp).toLocaleString()}</span>
                        </div>
                        <div class="message-client">客户端: ${msg.clientId}</div>
                        <div class="message-payload">${msg.payload}</div>
                    `;
                }

                container.appendChild(messageElement);
            });

            // 更新消息计数
            messageCount = allMessages.length;
            document.getElementById('messageCount').textContent = messageCount;

            if (autoScroll) {
                container.scrollTop = container.scrollHeight;
            }
        }

        // 清除过滤器
        function clearFilters() {
            document.getElementById('filterTopic').value = '';
            document.getElementById('filterClient').value = '';
            document.getElementById('filterPayload').value = '';
            document.getElementById('showSystemMessages').checked = true;
            applyMessageFilter();
        }

        // 清空消息
        function clearMessages() {
            allMessages = [];
            filteredMessages = [];
            messageCount = 0;
            document.getElementById('messageCount').textContent = '0';
            document.getElementById('messageContainer').innerHTML = '<div class="text-center py-4 text-muted"><i class="fas fa-info-circle"></i> 连接WebSocket并订阅主题以查看实时消息</div>';
        }

        // 切换自动滚动
        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            document.getElementById('autoScrollText').textContent = autoScroll ? '自动滚动' : '手动滚动';
        }
    </script>
</body>
</html>
