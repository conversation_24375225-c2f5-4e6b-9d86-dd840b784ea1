package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmPatrolstditemEntity;
import inks.service.std.eam.domain.pojo.DmPatrolstditemPojo;
import inks.service.std.eam.mapper.DmPatrolstditemMapper;
import inks.service.std.eam.service.DmPatrolstditemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 检查项目(DmPatrolstditem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 13:18:02
 */
@Service("dmPatrolstditemService")
public class DmPatrolstditemServiceImpl implements DmPatrolstditemService {
    @Resource
    private DmPatrolstditemMapper dmPatrolstditemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmPatrolstditemPojo getEntity(String key, String tid) {
        return this.dmPatrolstditemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolstditemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolstditemPojo> lst = dmPatrolstditemMapper.getPageList(queryParam);
            PageInfo<DmPatrolstditemPojo> pageInfo = new PageInfo<DmPatrolstditemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DmPatrolstditemPojo> getList(String Pid, String tid) {
        try {
            List<DmPatrolstditemPojo> lst = dmPatrolstditemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param dmPatrolstditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolstditemPojo insert(DmPatrolstditemPojo dmPatrolstditemPojo) {
        //初始化item的NULL
        DmPatrolstditemPojo itempojo = this.clearNull(dmPatrolstditemPojo);
        DmPatrolstditemEntity dmPatrolstditemEntity = new DmPatrolstditemEntity();
        BeanUtils.copyProperties(itempojo, dmPatrolstditemEntity);
        //生成雪花id
        dmPatrolstditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmPatrolstditemEntity.setRevision(1);  //乐观锁
        this.dmPatrolstditemMapper.insert(dmPatrolstditemEntity);
        return this.getEntity(dmPatrolstditemEntity.getId(), dmPatrolstditemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmPatrolstditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolstditemPojo update(DmPatrolstditemPojo dmPatrolstditemPojo) {
        DmPatrolstditemEntity dmPatrolstditemEntity = new DmPatrolstditemEntity();
        BeanUtils.copyProperties(dmPatrolstditemPojo, dmPatrolstditemEntity);
        this.dmPatrolstditemMapper.update(dmPatrolstditemEntity);
        return this.getEntity(dmPatrolstditemEntity.getId(), dmPatrolstditemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmPatrolstditemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param dmPatrolstditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolstditemPojo clearNull(DmPatrolstditemPojo dmPatrolstditemPojo) {
        //初始化NULL字段
        if (dmPatrolstditemPojo.getPid() == null) dmPatrolstditemPojo.setPid("");
        if (dmPatrolstditemPojo.getClausegroupid() == null) dmPatrolstditemPojo.setClausegroupid("");
        if (dmPatrolstditemPojo.getClausetype() == null) dmPatrolstditemPojo.setClausetype("");
        if (dmPatrolstditemPojo.getClause() == null) dmPatrolstditemPojo.setClause("");
        if (dmPatrolstditemPojo.getSeverity() == null) dmPatrolstditemPojo.setSeverity("");
        if (dmPatrolstditemPojo.getRequirement() == null) dmPatrolstditemPojo.setRequirement("");
        if (dmPatrolstditemPojo.getAcceptphoto() == null) dmPatrolstditemPojo.setAcceptphoto("");
        if (dmPatrolstditemPojo.getRejectphoto() == null) dmPatrolstditemPojo.setRejectphoto("");
        if (dmPatrolstditemPojo.getRownum() == null) dmPatrolstditemPojo.setRownum(0);
        if (dmPatrolstditemPojo.getRemark() == null) dmPatrolstditemPojo.setRemark("");
        if (dmPatrolstditemPojo.getCustom1() == null) dmPatrolstditemPojo.setCustom1("");
        if (dmPatrolstditemPojo.getCustom2() == null) dmPatrolstditemPojo.setCustom2("");
        if (dmPatrolstditemPojo.getCustom3() == null) dmPatrolstditemPojo.setCustom3("");
        if (dmPatrolstditemPojo.getCustom4() == null) dmPatrolstditemPojo.setCustom4("");
        if (dmPatrolstditemPojo.getCustom5() == null) dmPatrolstditemPojo.setCustom5("");
        if (dmPatrolstditemPojo.getCustom6() == null) dmPatrolstditemPojo.setCustom6("");
        if (dmPatrolstditemPojo.getCustom7() == null) dmPatrolstditemPojo.setCustom7("");
        if (dmPatrolstditemPojo.getCustom8() == null) dmPatrolstditemPojo.setCustom8("");
        if (dmPatrolstditemPojo.getCustom9() == null) dmPatrolstditemPojo.setCustom9("");
        if (dmPatrolstditemPojo.getCustom10() == null) dmPatrolstditemPojo.setCustom10("");
        if (dmPatrolstditemPojo.getTenantid() == null) dmPatrolstditemPojo.setTenantid("");
        if (dmPatrolstditemPojo.getRevision() == null) dmPatrolstditemPojo.setRevision(0);
        return dmPatrolstditemPojo;
    }
}
