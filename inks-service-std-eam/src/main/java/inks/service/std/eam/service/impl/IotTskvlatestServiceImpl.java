package inks.service.std.eam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.IotTskvlatestEntity;
import inks.service.std.eam.domain.pojo.IotTskvlatestPojo;
import inks.service.std.eam.mapper.IotTskvlatestMapper;
import inks.service.std.eam.service.IotTskvlatestService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 最新时间序列遥测数据快照表(IotTskvlatest)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17 10:44:02
 */
@Service("iotTskvlatestService")
public class IotTskvlatestServiceImpl implements IotTskvlatestService {
    @Resource
    private IotTskvlatestMapper iotTskvlatestMapper;

    @Override
    public IotTskvlatestPojo getEntity(String key, String tid) {
        return this.iotTskvlatestMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotTskvlatestPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotTskvlatestPojo> lst = iotTskvlatestMapper.getPageList(queryParam);
            PageInfo<IotTskvlatestPojo> pageInfo = new PageInfo<IotTskvlatestPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotTskvlatestPojo insert(IotTskvlatestPojo iotTskvlatestPojo) {
        //初始化NULL字段
        cleanNull(iotTskvlatestPojo);
        IotTskvlatestEntity iotTskvlatestEntity = new IotTskvlatestEntity(); 
        BeanUtils.copyProperties(iotTskvlatestPojo,iotTskvlatestEntity);
          //生成雪花id
          iotTskvlatestEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          this.iotTskvlatestMapper.insert(iotTskvlatestEntity);
        return this.getEntity(iotTskvlatestEntity.getId(),iotTskvlatestEntity.getTenantid());
    }


    @Override
    public IotTskvlatestPojo update(IotTskvlatestPojo iotTskvlatestPojo) {
        IotTskvlatestEntity iotTskvlatestEntity = new IotTskvlatestEntity(); 
        BeanUtils.copyProperties(iotTskvlatestPojo,iotTskvlatestEntity);
        this.iotTskvlatestMapper.update(iotTskvlatestEntity);
        return this.getEntity(iotTskvlatestEntity.getId(),iotTskvlatestEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotTskvlatestMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotTskvlatestPojo iotTskvlatestPojo) {
        if(iotTskvlatestPojo.getEntityid()==null) iotTskvlatestPojo.setEntityid("");
        if(iotTskvlatestPojo.getKeyid()==null) iotTskvlatestPojo.setKeyid(0);
        if(iotTskvlatestPojo.getKeyv()==null) iotTskvlatestPojo.setKeyv("");
        if(iotTskvlatestPojo.getBoolv()==null) iotTskvlatestPojo.setBoolv(0);
        if(iotTskvlatestPojo.getStrv()==null) iotTskvlatestPojo.setStrv("");
        if(iotTskvlatestPojo.getDblv()==null) iotTskvlatestPojo.setDblv(0D);
        if(iotTskvlatestPojo.getJsonv()==null) iotTskvlatestPojo.setJsonv("{}");
        if(iotTskvlatestPojo.getCreatedate()==null) iotTskvlatestPojo.setCreatedate(new Date());
        if(iotTskvlatestPojo.getCustom1()==null) iotTskvlatestPojo.setCustom1("");
        if(iotTskvlatestPojo.getCustom2()==null) iotTskvlatestPojo.setCustom2("");
        if(iotTskvlatestPojo.getCustom3()==null) iotTskvlatestPojo.setCustom3("");
        if(iotTskvlatestPojo.getCustom4()==null) iotTskvlatestPojo.setCustom4("");
        if(iotTskvlatestPojo.getCustom5()==null) iotTskvlatestPojo.setCustom5("");
        if(iotTskvlatestPojo.getCustom6()==null) iotTskvlatestPojo.setCustom6("");
        if(iotTskvlatestPojo.getCustom7()==null) iotTskvlatestPojo.setCustom7("");
        if(iotTskvlatestPojo.getCustom8()==null) iotTskvlatestPojo.setCustom8("");
        if(iotTskvlatestPojo.getCustom9()==null) iotTskvlatestPojo.setCustom9("");
        if(iotTskvlatestPojo.getCustom10()==null) iotTskvlatestPojo.setCustom10("");
        if(iotTskvlatestPojo.getTenantid()==null) iotTskvlatestPojo.setTenantid("");
        if(iotTskvlatestPojo.getTenantname()==null) iotTskvlatestPojo.setTenantname("");
   }

    @Override
    public int batchLatest(JSONObject mqttJSON) {
        String entityid = mqttJSON.getString("entityid");
        long ts = mqttJSON.getLongValue("ts");
        List<IotTskvlatestPojo> list = new ArrayList<>();
        Date now = new Date();

        // 遍历遥测键值（排除元数据字段）
        for (String key : mqttJSON.keySet()) {
            // 排除元数据字段
            if (Arrays.asList("entityid", "ts", "sn").contains(key)) continue;

            Object val = mqttJSON.get(key);
            IotTskvlatestPojo p = new IotTskvlatestPojo();
            p.setId(inksSnowflake.getSnowflake().nextIdStr());
            p.setEntityid(entityid);
            p.setKeyid(0);//TODO 根据字典来通过key确定keyid
            p.setKeyv(key);
            p.setTs(ts);
            p.setCreatedate(now);
            p.setTenantid("");
            // 值类型处理（与历史表一致）
            if (val instanceof Boolean) {
                p.setBoolv(((Boolean) val) ? 1 : 0);
            } else if (val instanceof Number) {
                if (val instanceof Integer || val instanceof Long) {
                    p.setLongv(((Number) val).longValue());
                } else {
                    p.setDblv(((Number) val).doubleValue());
                }
            } else {
                String s = String.valueOf(val);
                if (s.startsWith("{") || s.startsWith("[")) {
                    p.setJsonv(s);
                } else {
                    p.setStrv(s);
                }
            }
            list.add(p);
        }

        // 批量 Upsert
        int count = 0;
        if (!list.isEmpty()) {
            count = iotTskvlatestMapper.batchLatest(list);
        }
        return count;
    }

    @Override
    public List<IotTskvlatestPojo> getList(String entityid, String tenantid) {
        return this.iotTskvlatestMapper.getList(entityid,tenantid);
    }
}
