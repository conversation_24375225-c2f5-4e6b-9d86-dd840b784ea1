package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolborrowPojo;
import inks.service.std.eam.domain.pojo.DmToolborrowitemdetailPojo;
import inks.service.std.eam.domain.DmToolborrowEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具借还主表(DmToolborrow)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-13 09:51:56
 */
@Mapper
public interface DmToolborrowMapper {

    DmToolborrowPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolborrowitemdetailPojo> getPageList(QueryParam queryParam);

    List<DmToolborrowPojo> getPageTh(QueryParam queryParam);

    int insert(DmToolborrowEntity dmToolborrowEntity);

    int update(DmToolborrowEntity dmToolborrowEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(DmToolborrowPojo dmToolborrowPojo);

    List<String> getDelWsIds(DmToolborrowPojo dmToolborrowPojo);
}

