package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.NoRepeatSubmit;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.DmToolmaintenancePojo;
import inks.service.std.eam.domain.pojo.DmToolmaintenanceitemPojo;
import inks.service.std.eam.domain.pojo.DmToolmaintenanceitemdetailPojo;
import inks.service.std.eam.service.DmToolmaintenanceService;
import inks.service.std.eam.service.DmToolmaintenanceitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;


/**
 * 工装具保养记录表(Dm_ToolMaintenance)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:20
 */
//@RestController
//@RequestMapping("dmToolmaintenance")
public class DmToolmaintenanceController {

    @Resource
    private DmToolmaintenanceService dmToolmaintenanceService;
    @Resource
    private DmToolmaintenanceitemService dmToolmaintenanceitemService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;
    @Resource
    private SystemFeignService systemFeignService;

    private final static Logger logger = LoggerFactory.getLogger(DmToolmaintenanceController.class);
    

    @ApiOperation(value=" 获取工装具保养记录表详细信息", notes="获取工装具保养记录表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.List")
    public R<DmToolmaintenancePojo> getEntity(String key) {
      try {
           // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolmaintenanceService.getEntity(key, loginUser.getTenantid()));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.List")
    public R<PageInfo<DmToolmaintenanceitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Dm_ToolMaintenance.CreateDate");
             // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmToolmaintenanceService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value=" 获取工装具保养记录表详细信息", notes="获取工装具保养记录表详细信息", produces="application/json")
    @RequestMapping(value="/getBillEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.List")
    public R<DmToolmaintenancePojo> getBillEntity(String key) {
      try {
             // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolmaintenanceService.getBillEntity(key, loginUser.getTenantid()));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getBillList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.List")
    public R<PageInfo<DmToolmaintenancePojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
           if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Dm_ToolMaintenance.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmToolmaintenanceService.getBillList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageTh",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.List")
    public R<PageInfo<DmToolmaintenancePojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Dm_ToolMaintenance.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmToolmaintenanceService.getPageTh(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    private final String moduleCode = "D09M12B1";

    @ApiOperation(value=" 新增工装具保养记录表", notes="新增工装具保养记录表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.Add")
    @NoRepeatSubmit
    public R<DmToolmaintenancePojo> create(@RequestBody String json) {
        try {
       DmToolmaintenancePojo dmToolmaintenancePojo = JSONArray.parseObject(json,DmToolmaintenancePojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Dm_ToolMaintenance", "", tid);
            dmToolmaintenancePojo.setRefno(refno);
            dmToolmaintenancePojo.setCreateby(loginUser.getRealName());   // 创建者
            dmToolmaintenancePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            dmToolmaintenancePojo.setCreatedate(new Date());   // 创建时间
            dmToolmaintenancePojo.setLister(loginUser.getRealname());   // 制表
            dmToolmaintenancePojo.setListerid(loginUser.getUserid());    // 制表id            
            dmToolmaintenancePojo.setModifydate(new Date());   //修改时间
            dmToolmaintenancePojo.setTenantid(tid);   //租户id
            DmToolmaintenancePojo insertDB = this.dmToolmaintenanceService.insert(dmToolmaintenancePojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, tid);// 保存单据编码RefNoUtils
        return R.ok(insertDB);
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="修改工装具保养记录表", notes="修改工装具保养记录表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.Edit")
    public R<DmToolmaintenancePojo> update(@RequestBody String json) {
        try {
         DmToolmaintenancePojo dmToolmaintenancePojo = JSONArray.parseObject(json,DmToolmaintenancePojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolmaintenancePojo.setLister(loginUser.getRealname());   // 制表
            dmToolmaintenancePojo.setListerid(loginUser.getUserid());    // 制表id   
            dmToolmaintenancePojo.setModifydate(new Date());   //修改时间
            dmToolmaintenancePojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.dmToolmaintenanceService.update(dmToolmaintenancePojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除工装具保养记录表", notes="删除工装具保养记录表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.Delete")
    public R<Integer> delete(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
          this.dmToolmaintenanceService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1);
   }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value=" 新增工装具保养记录表Item", notes="新增工装具保养记录表Item", produces="application/json") 
    @RequestMapping(value="/createItem",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.Add")
    public R<DmToolmaintenanceitemPojo> createItem(@RequestBody String json) {
       try {
     DmToolmaintenanceitemPojo dmToolmaintenanceitemPojo = JSONArray.parseObject(json,DmToolmaintenanceitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolmaintenanceitemPojo.setTenantid(loginUser.getTenantid());
        return R.ok(this.dmToolmaintenanceitemService.insert(dmToolmaintenanceitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value=" 修改工装具保养记录表Item", notes="修改工装具保养记录表Item", produces="application/json") 
    @RequestMapping(value="/updateItem",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.Edit")
    public R<DmToolmaintenanceitemPojo> updateItem(@RequestBody String json) {
       try {
     DmToolmaintenanceitemPojo dmToolmaintenanceitemPojo = JSONArray.parseObject(json,DmToolmaintenanceitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolmaintenanceitemPojo.setTenantid(loginUser.getTenantid());
        return R.ok(this.dmToolmaintenanceitemService.update(dmToolmaintenanceitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }    

    @ApiOperation(value="删除工装具保养记录表Item", notes="删除工装具保养记录表Item", produces="application/json")   
    @RequestMapping(value="/deleteItem",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.Delete")
    public R<Integer> deleteItem(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolmaintenanceitemService.delete(key, loginUser.getTenantid()));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    


    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolMaintenance.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        DmToolmaintenancePojo dmToolmaintenancePojo = this.dmToolmaintenanceService.getBillEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(dmToolmaintenancePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content ;
        if (reportsPojo != null ) {
          content = reportsPojo.getRptdata();
        } else {
          throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
       if(reportsPojo.getPagerow()>0){
       int index=0;
      // 取行余数
      index =dmToolmaintenancePojo.getItem().size()%reportsPojo.getPagerow();
      if(index>0){
        // 补全空白行
        for(int i=0;i<reportsPojo.getPagerow()-index;i++){
            DmToolmaintenanceitemPojo dmToolmaintenanceitemPojo = new DmToolmaintenanceitemPojo();
            dmToolmaintenancePojo.getItem().add(dmToolmaintenanceitemPojo);
          }
      }
     }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(dmToolmaintenancePojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

