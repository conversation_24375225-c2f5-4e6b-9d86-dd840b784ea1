package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolusagePojo;
import inks.service.std.eam.domain.pojo.DmToolusageitemdetailPojo;
import inks.service.std.eam.domain.DmToolusageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具使用记录表(DmToolusage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:20
 */
@Mapper
public interface DmToolusageMapper {

    DmToolusagePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolusageitemdetailPojo> getPageList(QueryParam queryParam);

    List<DmToolusagePojo> getPageTh(QueryParam queryParam);

    int insert(DmToolusageEntity dmToolusageEntity);

    int update(DmToolusageEntity dmToolusageEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(DmToolusagePojo dmToolusagePojo);
}

