package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmRepairserviceEntity;
import inks.service.std.eam.domain.pojo.DmRepairservicePojo;
import inks.service.std.eam.mapper.DmRepairserviceMapper;
import inks.service.std.eam.service.DmRepairserviceService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 设备报修(DmRepairservice)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-06 14:55:26
 */
@Service("dmRepairserviceService")
public class DmRepairserviceServiceImpl implements DmRepairserviceService {
    @Resource
    private DmRepairserviceMapper dmRepairserviceMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmRepairservicePojo getEntity(String key, String tid) {
        return this.dmRepairserviceMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmRepairservicePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmRepairservicePojo> lst = dmRepairserviceMapper.getPageList(queryParam);
            PageInfo<DmRepairservicePojo> pageInfo = new PageInfo<DmRepairservicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmRepairservicePojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmRepairservicePojo insert(DmRepairservicePojo dmRepairservicePojo) {
        // 检查传入设备是否已经报修
        int i = this.dmRepairserviceMapper.checkDevice(dmRepairservicePojo.getDeviceid(), null, dmRepairservicePojo.getTenantid());
        if (i > 0) {
            throw new BaseBusinessException("该设备已经报修，请勿重复报修！");
        }
        //初始化NULL字段
        if (dmRepairservicePojo.getRefno() == null) dmRepairservicePojo.setRefno("");
        if (dmRepairservicePojo.getBilldate() == null) dmRepairservicePojo.setBilldate(new Date());
        if (dmRepairservicePojo.getBilltype() == null) dmRepairservicePojo.setBilltype("");
        if (dmRepairservicePojo.getBilltitle() == null) dmRepairservicePojo.setBilltitle("");
        if (dmRepairservicePojo.getDeviceid() == null) dmRepairservicePojo.setDeviceid("");
        if (dmRepairservicePojo.getOperator() == null) dmRepairservicePojo.setOperator("");
        if (dmRepairservicePojo.getRepairreason() == null) dmRepairservicePojo.setRepairreason("");
        if (dmRepairservicePojo.getBillresult() == null) dmRepairservicePojo.setBillresult("");
        if (dmRepairservicePojo.getRemark() == null) dmRepairservicePojo.setRemark("");
        if (dmRepairservicePojo.getCustom1() == null) dmRepairservicePojo.setCustom1("");
        if (dmRepairservicePojo.getCustom2() == null) dmRepairservicePojo.setCustom2("");
        if (dmRepairservicePojo.getCustom3() == null) dmRepairservicePojo.setCustom3("");
        if (dmRepairservicePojo.getCustom4() == null) dmRepairservicePojo.setCustom4("");
        if (dmRepairservicePojo.getCustom5() == null) dmRepairservicePojo.setCustom5("");
        if (dmRepairservicePojo.getCustom6() == null) dmRepairservicePojo.setCustom6("");
        if (dmRepairservicePojo.getCustom7() == null) dmRepairservicePojo.setCustom7("");
        if (dmRepairservicePojo.getCustom8() == null) dmRepairservicePojo.setCustom8("");
        if (dmRepairservicePojo.getLister() == null) dmRepairservicePojo.setLister("");
        if (dmRepairservicePojo.getListerid() == null) dmRepairservicePojo.setListerid("");
        if (dmRepairservicePojo.getCreateby() == null) dmRepairservicePojo.setCreateby("");
        if (dmRepairservicePojo.getCreatedate() == null) dmRepairservicePojo.setCreatedate(new Date());
        if (dmRepairservicePojo.getCreatebyid() == null) dmRepairservicePojo.setCreatebyid("");
        if (dmRepairservicePojo.getModifydate() == null) dmRepairservicePojo.setModifydate(new Date());
        if (dmRepairservicePojo.getAssessorid() == null) dmRepairservicePojo.setAssessorid("");
        if (dmRepairservicePojo.getAssessor() == null) dmRepairservicePojo.setAssessor("");
        if (dmRepairservicePojo.getAssessdate() == null) dmRepairservicePojo.setAssessdate(new Date());
        if (dmRepairservicePojo.getEnabledmark() == null) dmRepairservicePojo.setEnabledmark(0);
        if (dmRepairservicePojo.getDeletemark() == null) dmRepairservicePojo.setDeletemark(0);
        if (dmRepairservicePojo.getDeletelister() == null) dmRepairservicePojo.setDeletelister("");
        if (dmRepairservicePojo.getDeletedate() == null) dmRepairservicePojo.setDeletedate(new Date());
        if (dmRepairservicePojo.getTenantid() == null) dmRepairservicePojo.setTenantid("");
        if (dmRepairservicePojo.getRevision() == null) dmRepairservicePojo.setRevision(0);
        DmRepairserviceEntity dmRepairserviceEntity = new DmRepairserviceEntity();
        BeanUtils.copyProperties(dmRepairservicePojo, dmRepairserviceEntity);
        //生成雪花id
        dmRepairserviceEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmRepairserviceEntity.setRevision(1);  //乐观锁
        this.dmRepairserviceMapper.insert(dmRepairserviceEntity);
        // 同步设备状态为“报修”
        this.dmRepairserviceMapper.updateDeviceState(dmRepairserviceEntity.getDeviceid(), "报修", dmRepairserviceEntity.getTenantid());
        return this.getEntity(dmRepairserviceEntity.getId(), dmRepairserviceEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmRepairservicePojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmRepairservicePojo update(DmRepairservicePojo dmRepairservicePojo) {
        String tid = dmRepairservicePojo.getTenantid();
        // 检查传入设备是否已经报修
        int i = this.dmRepairserviceMapper.checkDevice(dmRepairservicePojo.getDeviceid(), dmRepairservicePojo.getId(), tid);
        if (i > 0) {
            throw new BaseBusinessException("该设备已经报修，请勿重复报修！");
        }
        // 同步设备状态为“报修”
        String usestate = "报修";
        // 如果完成,同步设备状态为“正常”
        if ("完成".equals(dmRepairservicePojo.getUsestate())) usestate = "正常";
        this.dmRepairserviceMapper.updateDeviceState(dmRepairservicePojo.getDeviceid(), usestate, tid);
        DmRepairserviceEntity dmRepairserviceEntity = new DmRepairserviceEntity();
        BeanUtils.copyProperties(dmRepairservicePojo, dmRepairserviceEntity);
        this.dmRepairserviceMapper.update(dmRepairserviceEntity);
        return this.getEntity(dmRepairserviceEntity.getId(), dmRepairserviceEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     *
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        DmRepairservicePojo dmRepairservicePojo = this.getEntity(key, tid);
        // 同步设备状态为“正常”
        this.dmRepairserviceMapper.updateDeviceState(dmRepairservicePojo.getDeviceid(), "正常", tid);
        return this.dmRepairserviceMapper.delete(key, tid);
    }

    /**
     * 审核数据
     *
     * @param dmRepairservicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmRepairservicePojo approval(DmRepairservicePojo dmRepairservicePojo) {
        //主表更改
        DmRepairserviceEntity dmRepairserviceEntity = new DmRepairserviceEntity();
        BeanUtils.copyProperties(dmRepairservicePojo, dmRepairserviceEntity);
        this.dmRepairserviceMapper.approval(dmRepairserviceEntity);
        //返回Bill实例
        return this.getEntity(dmRepairserviceEntity.getId(), dmRepairserviceEntity.getTenantid());
    }

}
