package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotRulenodestatePojo;
import com.github.pagehelper.PageInfo;

/**
 * 规则节点运行状态表(Iot_RuleNodeState)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:48
 */
public interface IotRulenodestateService {

    IotRulenodestatePojo getEntity(String key,String tid);

    PageInfo<IotRulenodestatePojo> getPageList(QueryParam queryParam);

    IotRulenodestatePojo insert(IotRulenodestatePojo iotRulenodestatePojo);

    IotRulenodestatePojo update(IotRulenodestatePojo iotRulenodestatepojo);

    int delete(String key,String tid);
}
