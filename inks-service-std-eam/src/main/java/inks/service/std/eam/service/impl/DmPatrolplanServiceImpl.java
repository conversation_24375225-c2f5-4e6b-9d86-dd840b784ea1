package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmPatrolplanEntity;
import inks.service.std.eam.domain.pojo.DmPatrolplanPojo;
import inks.service.std.eam.mapper.DmPatrolplanMapper;
import inks.service.std.eam.service.DmPatrolplanService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 巡检计划(DmPatrolplan)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 13:18:39
 */
@Service("dmPatrolplanService")
public class DmPatrolplanServiceImpl implements DmPatrolplanService {
    @Resource
    private DmPatrolplanMapper dmPatrolplanMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmPatrolplanPojo getEntity(String key, String tid) {
        return this.dmPatrolplanMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolplanPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolplanPojo> lst = dmPatrolplanMapper.getPageList(queryParam);
            PageInfo<DmPatrolplanPojo> pageInfo = new PageInfo<DmPatrolplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmPatrolplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolplanPojo insert(DmPatrolplanPojo dmPatrolplanPojo) {
        //初始化NULL字段
        if (dmPatrolplanPojo.getRefno() == null) dmPatrolplanPojo.setRefno("");
        if (dmPatrolplanPojo.getBilltype() == null) dmPatrolplanPojo.setBilltype("");
        if (dmPatrolplanPojo.getBilldate() == null) dmPatrolplanPojo.setBilldate(new Date());
        if (dmPatrolplanPojo.getBilltitle() == null) dmPatrolplanPojo.setBilltitle("");
        if (dmPatrolplanPojo.getPlangroupid() == null) dmPatrolplanPojo.setPlangroupid("");
        if (dmPatrolplanPojo.getPlancode() == null) dmPatrolplanPojo.setPlancode("");
        if (dmPatrolplanPojo.getPlanname() == null) dmPatrolplanPojo.setPlanname("");
        if (dmPatrolplanPojo.getPathid() == null) dmPatrolplanPojo.setPathid("");
        if (dmPatrolplanPojo.getPathcode() == null) dmPatrolplanPojo.setPathcode("");
        if (dmPatrolplanPojo.getPathname() == null) dmPatrolplanPojo.setPathname("");
        if (dmPatrolplanPojo.getPlanstart() == null) dmPatrolplanPojo.setPlanstart(new Date());
        if (dmPatrolplanPojo.getPlanend() == null) dmPatrolplanPojo.setPlanend(new Date());
        if (dmPatrolplanPojo.getReceiver() == null) dmPatrolplanPojo.setReceiver("");
        if (dmPatrolplanPojo.getRownum() == null) dmPatrolplanPojo.setRownum(0);
        if (dmPatrolplanPojo.getEnabledmark() == null) dmPatrolplanPojo.setEnabledmark(0);
        if (dmPatrolplanPojo.getFinishmark() == null) dmPatrolplanPojo.setFinishmark(0);
        if (dmPatrolplanPojo.getClosed() == null) dmPatrolplanPojo.setClosed(0);
        if (dmPatrolplanPojo.getOperator() == null) dmPatrolplanPojo.setOperator("");
        if (dmPatrolplanPojo.getRemark() == null) dmPatrolplanPojo.setRemark("");
        if (dmPatrolplanPojo.getStatecode() == null) dmPatrolplanPojo.setStatecode("");
        if (dmPatrolplanPojo.getStatedate() == null) dmPatrolplanPojo.setStatedate(new Date());
        if (dmPatrolplanPojo.getLister() == null) dmPatrolplanPojo.setLister("");
        if (dmPatrolplanPojo.getListerid() == null) dmPatrolplanPojo.setListerid("");
        if (dmPatrolplanPojo.getCreatedate() == null) dmPatrolplanPojo.setCreatedate(new Date());
        if (dmPatrolplanPojo.getCreateby() == null) dmPatrolplanPojo.setCreateby("");
        if (dmPatrolplanPojo.getCreatebyid() == null) dmPatrolplanPojo.setCreatebyid("");
        if (dmPatrolplanPojo.getModifydate() == null) dmPatrolplanPojo.setModifydate(new Date());
        if (dmPatrolplanPojo.getAssessor() == null) dmPatrolplanPojo.setAssessor("");
        if (dmPatrolplanPojo.getAssessorid() == null) dmPatrolplanPojo.setAssessorid("");
        if (dmPatrolplanPojo.getAssessdate() == null) dmPatrolplanPojo.setAssessdate(new Date());
        if (dmPatrolplanPojo.getDeletemark() == null) dmPatrolplanPojo.setDeletemark(0);
        if (dmPatrolplanPojo.getDeletelister() == null) dmPatrolplanPojo.setDeletelister("");
        if (dmPatrolplanPojo.getDeletedate() == null) dmPatrolplanPojo.setDeletedate(new Date());
        if (dmPatrolplanPojo.getCustom1() == null) dmPatrolplanPojo.setCustom1("");
        if (dmPatrolplanPojo.getCustom2() == null) dmPatrolplanPojo.setCustom2("");
        if (dmPatrolplanPojo.getCustom3() == null) dmPatrolplanPojo.setCustom3("");
        if (dmPatrolplanPojo.getCustom4() == null) dmPatrolplanPojo.setCustom4("");
        if (dmPatrolplanPojo.getCustom5() == null) dmPatrolplanPojo.setCustom5("");
        if (dmPatrolplanPojo.getCustom6() == null) dmPatrolplanPojo.setCustom6("");
        if (dmPatrolplanPojo.getCustom7() == null) dmPatrolplanPojo.setCustom7("");
        if (dmPatrolplanPojo.getCustom8() == null) dmPatrolplanPojo.setCustom8("");
        if (dmPatrolplanPojo.getCustom9() == null) dmPatrolplanPojo.setCustom9("");
        if (dmPatrolplanPojo.getCustom10() == null) dmPatrolplanPojo.setCustom10("");
        if (dmPatrolplanPojo.getTenantid() == null) dmPatrolplanPojo.setTenantid("");
        if (dmPatrolplanPojo.getRevision() == null) dmPatrolplanPojo.setRevision(0);
        DmPatrolplanEntity dmPatrolplanEntity = new DmPatrolplanEntity();
        BeanUtils.copyProperties(dmPatrolplanPojo, dmPatrolplanEntity);
        //生成雪花id
        dmPatrolplanEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmPatrolplanEntity.setRevision(1);  //乐观锁
        this.dmPatrolplanMapper.insert(dmPatrolplanEntity);
        return this.getEntity(dmPatrolplanEntity.getId(), dmPatrolplanEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmPatrolplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolplanPojo update(DmPatrolplanPojo dmPatrolplanPojo) {
        DmPatrolplanEntity dmPatrolplanEntity = new DmPatrolplanEntity();
        BeanUtils.copyProperties(dmPatrolplanPojo, dmPatrolplanEntity);
        this.dmPatrolplanMapper.update(dmPatrolplanEntity);
        return this.getEntity(dmPatrolplanEntity.getId(), dmPatrolplanEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmPatrolplanMapper.delete(key, tid);
    }

    /**
     * 审核数据
     *
     * @param dmPatrolplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmPatrolplanPojo approval(DmPatrolplanPojo dmPatrolplanPojo) {
        //主表更改
        DmPatrolplanEntity dmPatrolplanEntity = new DmPatrolplanEntity();
        BeanUtils.copyProperties(dmPatrolplanPojo, dmPatrolplanEntity);
        this.dmPatrolplanMapper.approval(dmPatrolplanEntity);
        //返回Bill实例
        return this.getEntity(dmPatrolplanEntity.getId(), dmPatrolplanEntity.getTenantid());
    }

}
