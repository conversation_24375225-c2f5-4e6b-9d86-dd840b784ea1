package inks.service.std.eam.mqtt.controller;

import inks.service.std.eam.mqtt.config.MqttTokenRedisInitializer;
import inks.service.std.eam.mqtt.service.DeviceTokenService;
import inks.service.std.eam.mqtt.service.MqttConnectionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 设备Token管理API
 * 提供Token的创建、查询、删除等功能
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/device-tokens")
public class DeviceTokenController {
    
    @Autowired
    private DeviceTokenService deviceTokenService;

    @Resource
    private MqttTokenRedisInitializer mqttTokenRedisInitializer;

    @Resource
    private MqttConnectionManager mqttConnectionManager;
    
    /**
     * 获取所有Token映射关系
     */
    @GetMapping
    public ResponseEntity<Map<String, String>> getAllTokens() {
        return ResponseEntity.ok(deviceTokenService.getAllTokensFromDatabase());
    }
    
    /**
     * 生成新Token
     */
    @PostMapping("/generate")
    public ResponseEntity<Map<String, String>> generateToken(
            @RequestParam String deviceId,
            @RequestParam String deviceType) {
        
        String token = deviceTokenService.generateDeviceToken(deviceId, deviceType);
        
        Map<String, String> response = new HashMap<>();
        response.put("token", token);
        response.put("deviceId", deviceId);
        response.put("deviceType", deviceType);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 添加自定义Token到Redis
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> addToken(
            @RequestParam String token,
            @RequestParam String deviceId) {
        boolean success = deviceTokenService.addTokenToRedis(token, deviceId);

        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        response.put("token", token);
        response.put("deviceId", deviceId);

        return ResponseEntity.ok(response);
    }
    
    /**
     * 从Redis删除Token
     */
    @DeleteMapping
    public ResponseEntity<Map<String, Object>> removeToken(@RequestParam String token) {
        boolean success = deviceTokenService.removeTokenFromRedis(token);

        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        response.put("token", token);

        return ResponseEntity.ok(response);
    }
    
    /**
     * 验证Token
     */
    @GetMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateToken(@RequestParam String token) {
        boolean isValid = deviceTokenService.isValidToken(token);
        
        Map<String, Object> response = new HashMap<>();
        response.put("valid", isValid);
        response.put("token", token);
        
        return ResponseEntity.ok(response);
    }

    /**
     * 手动刷新Redis缓存
     */
    @PostMapping("/refresh-redis")
    public ResponseEntity<Map<String, Object>> refreshRedisCache() {
        int syncCount = deviceTokenService.refreshRedisCache();

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Redis缓存刷新成功");
        response.put("syncCount", syncCount);

        return ResponseEntity.ok(response);
    }

    /**
     * Redis健康检查
     */
    @GetMapping("/redis-health")
    public ResponseEntity<Map<String, Object>> checkRedisHealth() {
        boolean isHealthy = mqttTokenRedisInitializer.performRedisHealthCheck();

        Map<String, Object> response = new HashMap<>();
        response.put("healthy", isHealthy);
        response.put("message", isHealthy ? "Redis连接正常" : "Redis连接异常");

        return ResponseEntity.ok(response);
    }

    /**
     * 数据一致性检查
     */
    @GetMapping("/consistency-check")
    public ResponseEntity<Map<String, Object>> checkDataConsistency() {
        Map<String, Object> result = mqttTokenRedisInitializer.performDataConsistencyCheck();

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", result);

        return ResponseEntity.ok(response);
    }

    /**
     * 修复数据不一致
     */
    @PostMapping("/repair-inconsistency")
    public ResponseEntity<Map<String, Object>> repairDataInconsistency() {
        int repairCount = mqttTokenRedisInitializer.repairDataInconsistency();

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "数据不一致修复完成");
        response.put("repairCount", repairCount);

        return ResponseEntity.ok(response);
    }

    /**
     * 主动断开设备MQTT连接（通过token）
     */
    @PostMapping("/disconnect-by-token")
    public ResponseEntity<Map<String, Object>> disconnectByToken(
            @RequestParam String token,
            @RequestParam(defaultValue = "管理员主动断开") String reason) {

        boolean success = mqttConnectionManager.disconnectByToken(token, reason);

        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        response.put("token", token);
        response.put("reason", reason);
        response.put("message", success ? "连接断开成功" : "连接断开失败或连接不存在");

        return ResponseEntity.ok(response);
    }

    /**
     * 主动断开设备MQTT连接（通过设备ID）
     */
    @PostMapping("/disconnect-by-device")
    public ResponseEntity<Map<String, Object>> disconnectByDeviceId(
            @RequestParam String deviceId,
            @RequestParam(defaultValue = "管理员主动断开") String reason) {

        boolean success = mqttConnectionManager.disconnectByDeviceId(deviceId, reason);

        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        response.put("deviceId", deviceId);
        response.put("reason", reason);
        response.put("message", success ? "连接断开成功" : "连接断开失败或连接不存在");

        return ResponseEntity.ok(response);
    }

    /**
     * 获取MQTT连接状态
     */
    @GetMapping("/connection-status")
    public ResponseEntity<Map<String, Object>> getConnectionStatus() {
        int connectionCount = mqttConnectionManager.getConnectionCount();

        Map<String, Object> response = new HashMap<>();
        response.put("connectionCount", connectionCount);
        response.put("connectedTokens", mqttConnectionManager.getAllConnectedTokens());

        return ResponseEntity.ok(response);
    }

    /**
     * 检查设备是否在线
     */
    @GetMapping("/device-online")
    public ResponseEntity<Map<String, Object>> checkDeviceOnline(@RequestParam String deviceId) {
        boolean isOnline = mqttConnectionManager.isDeviceOnline(deviceId);

        Map<String, Object> response = new HashMap<>();
        response.put("deviceId", deviceId);
        response.put("online", isOnline);
        response.put("message", isOnline ? "设备在线" : "设备离线");

        return ResponseEntity.ok(response);
    }

    /**
     * 检查token是否在线
     */
    @GetMapping("/token-online")
    public ResponseEntity<Map<String, Object>> checkTokenOnline(@RequestParam String token) {
        boolean isOnline = mqttConnectionManager.isTokenOnline(token);

        Map<String, Object> response = new HashMap<>();
        response.put("token", token);
        response.put("online", isOnline);
        response.put("message", isOnline ? "Token在线" : "Token离线");

        return ResponseEntity.ok(response);
    }

    /**
     * 清理无效连接
     */
    @PostMapping("/cleanup-connections")
    public ResponseEntity<Map<String, Object>> cleanupConnections() {
        mqttConnectionManager.cleanupInvalidConnections();

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "无效连接清理完成");

        return ResponseEntity.ok(response);
    }

}
