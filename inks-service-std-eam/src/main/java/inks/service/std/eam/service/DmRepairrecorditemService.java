package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmRepairrecorditemPojo;

import java.util.List;

/**
 * 维修配件(DmRepairrecorditem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-06 14:49:33
 */
public interface DmRepairrecorditemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmRepairrecorditemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmRepairrecorditemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmRepairrecorditemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param dmRepairrecorditemPojo 实例对象
     * @return 实例对象
     */
    DmRepairrecorditemPojo insert(DmRepairrecorditemPojo dmRepairrecorditemPojo);

    /**
     * 修改数据
     *
     * @param dmRepairrecorditempojo 实例对象
     * @return 实例对象
     */
    DmRepairrecorditemPojo update(DmRepairrecorditemPojo dmRepairrecorditempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param dmRepairrecorditempojo 实例对象
     * @return 实例对象
     */
    DmRepairrecorditemPojo clearNull(DmRepairrecorditemPojo dmRepairrecorditempojo);
}
