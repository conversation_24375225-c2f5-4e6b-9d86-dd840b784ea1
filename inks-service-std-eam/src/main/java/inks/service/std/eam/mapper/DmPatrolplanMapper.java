package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmPatrolplanEntity;
import inks.service.std.eam.domain.pojo.DmPatrolplanPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡检计划(DmPatrolplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 13:08:46
 */
@Mapper
public interface DmPatrolplanMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolplanPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmPatrolplanPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param dmPatrolplanEntity 实例对象
     * @return 影响行数
     */
    int insert(DmPatrolplanEntity dmPatrolplanEntity);


    /**
     * 修改数据
     *
     * @param dmPatrolplanEntity 实例对象
     * @return 影响行数
     */
    int update(DmPatrolplanEntity dmPatrolplanEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改数据
     *
     * @param dmPatrolplanEntity 实例对象
     * @return 影响行数
     */
    int approval(DmPatrolplanEntity dmPatrolplanEntity);
}

