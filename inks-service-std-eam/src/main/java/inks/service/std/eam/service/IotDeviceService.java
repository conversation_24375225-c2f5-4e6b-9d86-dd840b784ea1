package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotDevicePojo;
import inks.service.std.eam.domain.vo.DeviceTokenVO;

import java.util.List;

/**
 * 设备基本信息表(Iot_Device)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:12
 */
public interface IotDeviceService {

    IotDevicePojo getEntity(String key,String tid);

    PageInfo<IotDevicePojo> getPageList(QueryParam queryParam);

    IotDevicePojo insert(IotDevicePojo iotDevicePojo);

    IotDevicePojo update(IotDevicePojo iotDevicepojo);

    int delete(String key,String tid);

    IotDevicePojo getEntitybySn(String sn);

    /**
     * 通过token查询设备信息
     * @param token 设备token
     * @return 设备信息，如果不存在返回null
     */
    IotDevicePojo getEntityByToken(String token);

    /**
     * 获取所有有token的设备信息
     * @return 设备token信息列表
     */
    List<DeviceTokenVO> getAllDevicesWithToken();
}
