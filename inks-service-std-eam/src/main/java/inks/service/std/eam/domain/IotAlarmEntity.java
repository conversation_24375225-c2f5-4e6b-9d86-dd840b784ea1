package inks.service.std.eam.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 告警信息记录表(IotAlarm)实体类
 *
 * <AUTHOR>
 * @since 2025-04-18 17:10:14
 */
public class IotAlarmEntity implements Serializable {
    private static final long serialVersionUID = 706938848570239816L;
     // 告警ID
    private String id;
     // 确认时间戳
    private Long ackts;
     // 清除时间戳
    private Long clearts;
     // 附加信息
    private String additionalinfo;
     // 结束时间戳
    private Long endts;
     // 触发实体ID
    private String originatorid;
     // 触发实体类型（整数枚举）
    private Integer originatortype;
     // 是否传播告警
    private Integer propagate;
     // 严重程度（如CRITICAL、MAJOR）
    private String severity;
     // 开始时间戳
    private Long startts;
     // 分配时间戳
    private Long assignts;
     // 分配给的用户ID
    private String assigneeid;
     // 客户ID
    private String customerid;
     // 传播关系类型
    private String propagaterelationtypes;
     // 告警类型
    private String type;
     // 是否传播给所有者
    private Integer propagatetoowner;
     // 是否传播给租户
    private Integer propagatetotenant;
     // 是否已确认
    private Integer acknowledged;
     // 是否已清除
    private Integer cleared;
     // 备注
    private String remark;
     // 顺序
    private Integer rownum;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

// 告警ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 确认时间戳
    public Long getAckts() {
        return ackts;
    }
    
    public void setAckts(Long ackts) {
        this.ackts = ackts;
    }
        
// 清除时间戳
    public Long getClearts() {
        return clearts;
    }
    
    public void setClearts(Long clearts) {
        this.clearts = clearts;
    }
        
// 附加信息
    public String getAdditionalinfo() {
        return additionalinfo;
    }
    
    public void setAdditionalinfo(String additionalinfo) {
        this.additionalinfo = additionalinfo;
    }
        
// 结束时间戳
    public Long getEndts() {
        return endts;
    }
    
    public void setEndts(Long endts) {
        this.endts = endts;
    }
        
// 触发实体ID
    public String getOriginatorid() {
        return originatorid;
    }
    
    public void setOriginatorid(String originatorid) {
        this.originatorid = originatorid;
    }
        
// 触发实体类型（整数枚举）
    public Integer getOriginatortype() {
        return originatortype;
    }
    
    public void setOriginatortype(Integer originatortype) {
        this.originatortype = originatortype;
    }
        
// 是否传播告警
    public Integer getPropagate() {
        return propagate;
    }
    
    public void setPropagate(Integer propagate) {
        this.propagate = propagate;
    }
        
// 严重程度（如CRITICAL、MAJOR）
    public String getSeverity() {
        return severity;
    }
    
    public void setSeverity(String severity) {
        this.severity = severity;
    }
        
// 开始时间戳
    public Long getStartts() {
        return startts;
    }
    
    public void setStartts(Long startts) {
        this.startts = startts;
    }
        
// 分配时间戳
    public Long getAssignts() {
        return assignts;
    }
    
    public void setAssignts(Long assignts) {
        this.assignts = assignts;
    }
        
// 分配给的用户ID
    public String getAssigneeid() {
        return assigneeid;
    }
    
    public void setAssigneeid(String assigneeid) {
        this.assigneeid = assigneeid;
    }
        
// 客户ID
    public String getCustomerid() {
        return customerid;
    }
    
    public void setCustomerid(String customerid) {
        this.customerid = customerid;
    }
        
// 传播关系类型
    public String getPropagaterelationtypes() {
        return propagaterelationtypes;
    }
    
    public void setPropagaterelationtypes(String propagaterelationtypes) {
        this.propagaterelationtypes = propagaterelationtypes;
    }
        
// 告警类型
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
        
// 是否传播给所有者
    public Integer getPropagatetoowner() {
        return propagatetoowner;
    }
    
    public void setPropagatetoowner(Integer propagatetoowner) {
        this.propagatetoowner = propagatetoowner;
    }
        
// 是否传播给租户
    public Integer getPropagatetotenant() {
        return propagatetotenant;
    }
    
    public void setPropagatetotenant(Integer propagatetotenant) {
        this.propagatetotenant = propagatetotenant;
    }
        
// 是否已确认
    public Integer getAcknowledged() {
        return acknowledged;
    }
    
    public void setAcknowledged(Integer acknowledged) {
        this.acknowledged = acknowledged;
    }
        
// 是否已清除
    public Integer getCleared() {
        return cleared;
    }
    
    public void setCleared(Integer cleared) {
        this.cleared = cleared;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 顺序
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

