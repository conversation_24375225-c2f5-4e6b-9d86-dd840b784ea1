package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolborrowitemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 工装具借还子表-工装具(DmToolborrowitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-13 09:52:08
 */
public interface DmToolborrowitemService {

    DmToolborrowitemPojo getEntity(String key,String tid);

    PageInfo<DmToolborrowitemPojo> getPageList(QueryParam queryParam);

    List<DmToolborrowitemPojo> getList(String Pid,String tid);  

    DmToolborrowitemPojo insert(DmToolborrowitemPojo dmToolborrowitemPojo);

    DmToolborrowitemPojo update(DmToolborrowitemPojo dmToolborrowitempojo);

    int delete(String key,String tid);

    DmToolborrowitemPojo clearNull(DmToolborrowitemPojo dmToolborrowitempojo);
}
