package inks.service.std.eam.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

//AppCtxUtils 是一个 轻量、安全、灵活 的工具类，适合大多数Spring Bean获取场景。如果需要更复杂的功能（如AOP代理或环境配置），
// 可以在此基础上扩展，但建议优先使用Spring的依赖注入而非静态工具类。

//优先选择 @Resource 字段注入 ：这是最符合 Spring 设计原则的方式。
//其次选择 AppCtxUtils ：适用于无法直接注入的场景（如非 Spring 管理的类）。
@Component
public class AppCtxUtils implements ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(AppCtxUtils.class);
    private static volatile ApplicationContext appCtx; // 使用volatile确保可见性

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (appCtx == null) {
            appCtx = applicationContext;
            log.info("ApplicationContext 已注入");
        }
    }

    /**
     * 获取ApplicationContext实例
     *
     * @return ApplicationContext
     */
    public static ApplicationContext getApplicationContext() {
        return appCtx;
    }

    /**
     * 通过Bean名称获取实例
     *
     * @param beanName Bean名称
     * @return Bean实例
     */
    @SuppressWarnings("unchecked")
    public static <T> T getBean(String beanName) {
        Objects.requireNonNull(beanName, "Bean名称不能为空");
        if (appCtx == null) {
            throw new IllegalStateException("ApplicationContext尚未初始化，请确保Spring容器已启动");
        }
        return (T) appCtx.getBean(beanName);
    }

    /**
     * 通过Bean类型获取实例（默认返回第一个匹配的Bean）
     *
     * @param clazz Bean类型
     * @param <T>   泛型类型
     * @return Bean实例
     */
    public static <T> T getBean(Class<T> clazz) {
        Objects.requireNonNull(clazz, "Bean类型不能为空");
        if (appCtx == null) {
            throw new IllegalStateException("ApplicationContext尚未初始化，请确保Spring容器已启动");
        }
        return appCtx.getBean(clazz);
    }

    /**
     * 通过Bean名称和类型获取实例（避免类型转换异常）
     *
     * @param beanName Bean名称
     * @param clazz    Bean类型
     * @param <T>      泛型类型
     * @return Bean实例
     */
    public static <T> T getBean(String beanName, Class<T> clazz) {
        Objects.requireNonNull(beanName, "Bean名称不能为空");
        Objects.requireNonNull(clazz, "Bean类型不能为空");
        if (appCtx == null) {
            throw new IllegalStateException("ApplicationContext尚未初始化，请确保Spring容器已启动");
        }
        return appCtx.getBean(beanName, clazz);
    }

    /**
     * 检查Bean是否存在
     *
     * @param beanName Bean名称
     * @return 是否存在
     */
    public static boolean containsBean(String beanName) {
        return appCtx != null && appCtx.containsBean(beanName);
    }
}