package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmToolborrowEntity;
import inks.service.std.eam.domain.DmToolborrowitemEntity;
import inks.service.std.eam.domain.DmToolborrowwsEntity;
import inks.service.std.eam.domain.pojo.DmToolborrowPojo;
import inks.service.std.eam.domain.pojo.DmToolborrowitemPojo;
import inks.service.std.eam.domain.pojo.DmToolborrowitemdetailPojo;
import inks.service.std.eam.domain.pojo.DmToolborrowwsPojo;
import inks.service.std.eam.mapper.DmToolborrowMapper;
import inks.service.std.eam.mapper.DmToolborrowitemMapper;
import inks.service.std.eam.mapper.DmToolborrowwsMapper;
import inks.service.std.eam.service.DmToolborrowService;
import inks.service.std.eam.service.DmToolborrowitemService;
import inks.service.std.eam.service.DmToolborrowwsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 工装具借还主表(DmToolborrow)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-13 09:51:56
 */
@Service("dmToolborrowService")
public class DmToolborrowServiceImpl implements DmToolborrowService {
    @Resource
    private DmToolborrowMapper dmToolborrowMapper;
    
    @Resource
    private DmToolborrowitemMapper dmToolborrowitemMapper;
    @Resource
    private DmToolborrowwsMapper  dmToolborrowwsMapper;

    @Resource
    private DmToolborrowitemService dmToolborrowitemService;
    @Resource
    private DmToolborrowwsService dmToolborrowwsService;

    @Override
    public DmToolborrowPojo getEntity(String key, String tid) {
        return this.dmToolborrowMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<DmToolborrowitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolborrowitemdetailPojo> lst = dmToolborrowMapper.getPageList(queryParam);
            PageInfo<DmToolborrowitemdetailPojo> pageInfo = new PageInfo<DmToolborrowitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public DmToolborrowPojo getBillEntity(String key, String tid) {
       try {
           //读取主表
           DmToolborrowPojo dmToolborrowPojo = this.dmToolborrowMapper.getEntity(key,tid);
           //读取子表
           dmToolborrowPojo.setItem(dmToolborrowitemMapper.getList(dmToolborrowPojo.getId(),tid));
           //ws子表
           dmToolborrowPojo.setWs(dmToolborrowwsMapper.getList(dmToolborrowPojo.getId(),tid));
           return dmToolborrowPojo;
       }catch (Exception e){
          throw new BaseBusinessException(e.getMessage());
       } 
    }


    @Override
    public PageInfo<DmToolborrowPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolborrowPojo> lst = dmToolborrowMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(DmToolborrowPojo item : lst){
                item.setItem(dmToolborrowitemMapper.getList(item.getId(), tid));
            }
            PageInfo<DmToolborrowPojo> pageInfo = new PageInfo<DmToolborrowPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<DmToolborrowPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolborrowPojo> lst = dmToolborrowMapper.getPageTh(queryParam);
            PageInfo<DmToolborrowPojo> pageInfo = new PageInfo<DmToolborrowPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public DmToolborrowPojo insert(DmToolborrowPojo dmToolborrowPojo) {
    String tid = dmToolborrowPojo.getTenantid();
        //初始化NULL字段
        cleanNull(dmToolborrowPojo);
         //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmToolborrowEntity dmToolborrowEntity = new DmToolborrowEntity(); 
        BeanUtils.copyProperties(dmToolborrowPojo,dmToolborrowEntity);
      
        //设置id和新建日期
        dmToolborrowEntity.setId(id);
        dmToolborrowEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmToolborrowMapper.insert(dmToolborrowEntity);
        //Item子表处理
        List<DmToolborrowitemPojo> lst = dmToolborrowPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(DmToolborrowitemPojo item : lst){
               //初始化item的NULL
               DmToolborrowitemPojo itemPojo =this.dmToolborrowitemService.clearNull(item);
               DmToolborrowitemEntity dmToolborrowitemEntity = new DmToolborrowitemEntity(); 
               BeanUtils.copyProperties(itemPojo,dmToolborrowitemEntity);
               //设置id和Pid
               dmToolborrowitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               dmToolborrowitemEntity.setPid(id);
               dmToolborrowitemEntity.setTenantid(tid);
               dmToolborrowitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.dmToolborrowitemMapper.insert(dmToolborrowitemEntity);
            }
            
        }
        ////ws子表
        List<DmToolborrowwsPojo> ws = dmToolborrowPojo.getWs();
        if (ws != null){
            //循环每个ws子表
            for(DmToolborrowwsPojo item : ws){
               //初始化item的NULL
               DmToolborrowwsPojo itemPojo =this.dmToolborrowwsService.clearNull(item);
               DmToolborrowwsEntity dmToolborrowwsEntity = new DmToolborrowwsEntity();
               BeanUtils.copyProperties(itemPojo,dmToolborrowwsEntity);
               //设置id和Pid
               dmToolborrowwsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               dmToolborrowwsEntity.setPid(id);
               dmToolborrowwsEntity.setTenantid(tid);
               dmToolborrowwsEntity.setRevision(1);  //乐观锁
               //插入子表
               this.dmToolborrowwsMapper.insert(dmToolborrowwsEntity);
            }

        }
        //返回Bill实例
        return this.getBillEntity(dmToolborrowEntity.getId(),tid);
    }


    @Override
    @Transactional
    public DmToolborrowPojo update(DmToolborrowPojo dmToolborrowPojo) {
        String tid = dmToolborrowPojo.getTenantid();
        //主表更改
        DmToolborrowEntity dmToolborrowEntity = new DmToolborrowEntity(); 
        BeanUtils.copyProperties(dmToolborrowPojo,dmToolborrowEntity);
        this.dmToolborrowMapper.update(dmToolborrowEntity);
        //Item子表处理
        if (dmToolborrowPojo.getItem() != null) {
        List<DmToolborrowitemPojo> lst = dmToolborrowPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =dmToolborrowMapper.getDelItemIds(dmToolborrowPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for (String delId : lstDelIds) {
             this.dmToolborrowitemMapper.delete(delId, tid);
            }
        }
        if (lst != null){
            //循环每个item子表
            for(DmToolborrowitemPojo item : lst){
               DmToolborrowitemEntity dmToolborrowitemEntity = new DmToolborrowitemEntity(); 
               if ("".equals(item.getId()) || item.getId() == null){
                //初始化item的NULL
               DmToolborrowitemPojo itemPojo =this.dmToolborrowitemService.clearNull(item);
               BeanUtils.copyProperties(itemPojo,dmToolborrowitemEntity);
               //设置id和Pid
               dmToolborrowitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               dmToolborrowitemEntity.setPid(dmToolborrowEntity.getId());  // 主表 id
               dmToolborrowitemEntity.setTenantid(tid);   // 租户id
               dmToolborrowitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.dmToolborrowitemMapper.insert(dmToolborrowitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(item,dmToolborrowitemEntity);       
                dmToolborrowitemEntity.setTenantid(tid);        
               this.dmToolborrowitemMapper.update(dmToolborrowitemEntity);
               }
            }
        } 
        }
        //ws子表
        if (dmToolborrowPojo.getWs() != null) {
        List<DmToolborrowwsPojo> lst = dmToolborrowPojo.getWs();
        //获取被删除的Item
         List<String> lstDelIds =dmToolborrowMapper.getDelWsIds(dmToolborrowPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for (String delId : lstDelIds) {
             this.dmToolborrowwsMapper.delete(delId, tid);
            }
        }
        if (lst != null){
            //循环每个item子表
            for(DmToolborrowwsPojo item : lst){
               DmToolborrowwsEntity dmToolborrowwsEntity = new DmToolborrowwsEntity();
               if ("".equals(item.getId()) || item.getId() == null){
                //初始化item的NULL
               DmToolborrowwsPojo itemPojo =this.dmToolborrowwsService.clearNull(item);
               BeanUtils.copyProperties(itemPojo,dmToolborrowwsEntity);
               //设置id和Pid
               dmToolborrowwsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               dmToolborrowwsEntity.setPid(dmToolborrowEntity.getId());  // 主表 id
               dmToolborrowwsEntity.setTenantid(tid);   // 租户id
               dmToolborrowwsEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.dmToolborrowwsMapper.insert(dmToolborrowwsEntity);
               }
               else
               {
               BeanUtils.copyProperties(item,dmToolborrowwsEntity);
                dmToolborrowwsEntity.setTenantid(tid);
               this.dmToolborrowwsMapper.update(dmToolborrowwsEntity);
               }
            }
        }
        }
        //返回Bill实例
        return this.getBillEntity(dmToolborrowEntity.getId(),tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
       DmToolborrowPojo dmToolborrowPojo =  this.getBillEntity(key,tid);
        //Item子表处理
        List<DmToolborrowitemPojo> lst = dmToolborrowPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(DmToolborrowitemPojo item : lst){
              this.dmToolborrowitemMapper.delete(item.getId(),tid);
            }
        }
        //ws子表
        List<DmToolborrowwsPojo> lstws = dmToolborrowPojo.getWs();
        if (lstws != null){
            //循环每个删除item子表
            for(DmToolborrowwsPojo item : lstws){
              this.dmToolborrowwsMapper.delete(item.getId(),tid);
            }
        }
        return this.dmToolborrowMapper.delete(key,tid) ;
    }
    

    

    private static void cleanNull(DmToolborrowPojo dmToolborrowPojo) {
        if(dmToolborrowPojo.getRefno()==null) dmToolborrowPojo.setRefno("");
        if(dmToolborrowPojo.getBilldate()==null) dmToolborrowPojo.setBilldate(new Date());
        if(dmToolborrowPojo.getBilltype()==null) dmToolborrowPojo.setBilltype("");
        if(dmToolborrowPojo.getBilltitle()==null) dmToolborrowPojo.setBilltitle("");
        if(dmToolborrowPojo.getStatus()==null) dmToolborrowPojo.setStatus(0);
        if(dmToolborrowPojo.getBorrowerid()==null) dmToolborrowPojo.setBorrowerid("");
        if(dmToolborrowPojo.getBorrowername()==null) dmToolborrowPojo.setBorrowername("");
        if(dmToolborrowPojo.getDeptid()==null) dmToolborrowPojo.setDeptid("");
        //if(dmToolborrowPojo.getExpreturndate()==null) dmToolborrowPojo.setExpreturndate(new Date());
        //if(dmToolborrowPojo.getActreturndate()==null) dmToolborrowPojo.setActreturndate(new Date());
        if(dmToolborrowPojo.getItemcount()==null) dmToolborrowPojo.setItemcount(0);
        if(dmToolborrowPojo.getFinishcount()==null) dmToolborrowPojo.setFinishcount(0);
        if(dmToolborrowPojo.getOverduedays()==null) dmToolborrowPojo.setOverduedays(0);
        if(dmToolborrowPojo.getSummary()==null) dmToolborrowPojo.setSummary("");
        if(dmToolborrowPojo.getCreateby()==null) dmToolborrowPojo.setCreateby("");
        if(dmToolborrowPojo.getCreatebyid()==null) dmToolborrowPojo.setCreatebyid("");
        if(dmToolborrowPojo.getCreatedate()==null) dmToolborrowPojo.setCreatedate(new Date());
        if(dmToolborrowPojo.getLister()==null) dmToolborrowPojo.setLister("");
        if(dmToolborrowPojo.getListerid()==null) dmToolborrowPojo.setListerid("");
        if(dmToolborrowPojo.getModifydate()==null) dmToolborrowPojo.setModifydate(new Date());
        if(dmToolborrowPojo.getCustom1()==null) dmToolborrowPojo.setCustom1("");
        if(dmToolborrowPojo.getCustom2()==null) dmToolborrowPojo.setCustom2("");
        if(dmToolborrowPojo.getCustom3()==null) dmToolborrowPojo.setCustom3("");
        if(dmToolborrowPojo.getCustom4()==null) dmToolborrowPojo.setCustom4("");
        if(dmToolborrowPojo.getCustom5()==null) dmToolborrowPojo.setCustom5("");
        if(dmToolborrowPojo.getCustom6()==null) dmToolborrowPojo.setCustom6("");
        if(dmToolborrowPojo.getCustom7()==null) dmToolborrowPojo.setCustom7("");
        if(dmToolborrowPojo.getCustom8()==null) dmToolborrowPojo.setCustom8("");
        if(dmToolborrowPojo.getCustom9()==null) dmToolborrowPojo.setCustom9("");
        if(dmToolborrowPojo.getCustom10()==null) dmToolborrowPojo.setCustom10("");
        if(dmToolborrowPojo.getTenantid()==null) dmToolborrowPojo.setTenantid("");
        if(dmToolborrowPojo.getTenantname()==null) dmToolborrowPojo.setTenantname("");
        if(dmToolborrowPojo.getRevision()==null) dmToolborrowPojo.setRevision(0);
   }

}
