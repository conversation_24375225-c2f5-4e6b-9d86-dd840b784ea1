package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolreturnPojo;
import inks.service.std.eam.domain.pojo.DmToolreturnitemdetailPojo;
import inks.service.std.eam.domain.DmToolreturnEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具归还主表(DmToolreturn)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-19 10:16:36
 */
@Mapper
public interface DmToolreturnMapper {

    DmToolreturnPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolreturnitemdetailPojo> getPageList(QueryParam queryParam);

    List<DmToolreturnPojo> getPageTh(QueryParam queryParam);

    int insert(DmToolreturnEntity dmToolreturnEntity);

    int update(DmToolreturnEntity dmToolreturnEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(DmToolreturnPojo dmToolreturnPojo);
}

