package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmUpkeepplanEntity;
import inks.service.std.eam.domain.DmUpkeepplanitemEntity;
import inks.service.std.eam.domain.pojo.DmUpkeepplanPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepplanitemPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepplanitemdetailPojo;
import inks.service.std.eam.mapper.DmUpkeepflowitemMapper;
import inks.service.std.eam.mapper.DmUpkeepplanMapper;
import inks.service.std.eam.mapper.DmUpkeepplanitemMapper;
import inks.service.std.eam.service.DmUpkeepplanService;
import inks.service.std.eam.service.DmUpkeepplanitemService;
import inks.service.std.eam.service.DmUpkeeprecordService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * 保养计划(DmUpkeepplan)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-08 14:23:18
 */
@Service("dmUpkeepplanService")
public class DmUpkeepplanServiceImpl implements DmUpkeepplanService {
    @Resource
    private DmUpkeepplanMapper dmUpkeepplanMapper;

    @Resource
    private DmUpkeepplanitemMapper dmUpkeepplanitemMapper;
    @Resource
    private DmUpkeepflowitemMapper dmUpkeepflowitemMapper;
    @Resource
    private DmUpkeeprecordService dmUpkeeprecordService;
    /**
     * 服务对象Item
     */
    @Resource
    private DmUpkeepplanitemService dmUpkeepplanitemService;

    /**
     * @return DayOfWeek[]
     * @Description String转DayOfWeek[]  传入的字符串格式为"1,4"，输出的为DayOfWeek[]数组“MONDAY,THURSDAY”
     * <AUTHOR>
     * @param[1] daysOfWeekString
     * @time 2023/6/8 14:03
     */
    private static DayOfWeek[] stringToDayOfWeek(String daysOfWeekString) {
        String[] daysOfWeekArray = daysOfWeekString.split(",");
        DayOfWeek[] daysOfWeek = new DayOfWeek[daysOfWeekArray.length];
        for (int i = 0; i < daysOfWeekArray.length; i++) {
            int dayIndex = Integer.parseInt(daysOfWeekArray[i]);
            DayOfWeek dayOfWeek = DayOfWeek.of(dayIndex);
            daysOfWeek[i] = dayOfWeek;
        }
        return daysOfWeek;
    }

    /**
     * @return int[]
     * @Description String转int[]  传入的字符串格式为每月几号"1,11,22"，输出的为int[]数组
     * <AUTHOR>
     * @param[1] str
     * @time 2023/6/8 14:04
     */
    public static int[] StringToIntArray(String str) {
        String[] strArray = str.split(",");
        int[] intArray = new int[strArray.length];
        for (int i = 0; i < strArray.length; i++) {
            intArray[i] = Integer.parseInt(strArray[i]);
        }
        return intArray;
    }

    /**
     * @return List<LocalDate>
     * @Description 根据起始日期、结束日期、周期类型和周期值返回日期列表(每周周3周4,每月几号,每隔几天,每年几月几号)
     * <AUTHOR>
     * @param[1] startDate 开始日期
     * @param[2] endDate 结束日期
     * @param[3] cycleType  周期类型
     * @param[4] cycleValue  Object类型,可以是DayOfWeek[]、int[]、Integer
     * @time 2023/6/8 13:05
     */
    public static List<LocalDate> getDatesInRange(LocalDate startDate, LocalDate endDate, String cycleType, String cycleValue) {
        List<LocalDate> dates = new ArrayList<>();
        switch (cycleType) {
            case "WEEKLY":  //每周几,可以是多个
                DayOfWeek[] daysOfWeek = stringToDayOfWeek(cycleValue);
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    DayOfWeek dayOfWeek = date.getDayOfWeek();
                    for (DayOfWeek targetDayOfWeek : daysOfWeek) {
                        if (dayOfWeek == targetDayOfWeek) {
                            dates.add(date);
                            break;
                        }
                    }
                }
                break;
            case "MONTHLY":  //每月几号,可以是多个
                int[] daysOfMonth = StringToIntArray(cycleValue);
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    int dayOfMonth = date.getDayOfMonth();
                    for (int targetDayOfMonth : daysOfMonth) {
                        if (dayOfMonth == targetDayOfMonth) {
                            dates.add(date);
                            break;
                        }
                    }

                }
                break;
            case "DAY":   //每隔几天,只能是一个
                // 将 String 类型的 cycleValue 转换为 int 类型
                int intervalDays = Integer.parseInt(cycleValue);
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(intervalDays)) {
                    dates.add(date);
                }
                break;
            case "YEARLY": // 每年几月几号,可以是多个 7.28,8.11,9.3
                String[] datesOfYear = cycleValue.split(",");
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    int month = date.getMonthValue();
                    int dayOfMonth = date.getDayOfMonth();
                    for (String targetDate : datesOfYear) {
                        String[] parts = targetDate.split("\\.");
                        int targetMonth = Integer.parseInt(parts[0]);
                        int targetDay = Integer.parseInt(parts[1]);
                        if (month == targetMonth && dayOfMonth == targetDay) {
                            dates.add(date);
                            break;
                        }
                    }
                }
                break;
            default:
                System.out.println("无效的周期类型");
        }
        return dates;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmUpkeepplanPojo getEntity(String key, String tid) {
        return this.dmUpkeepplanMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeepplanitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeepplanitemdetailPojo> lst = dmUpkeepplanMapper.getPageList(queryParam);
            PageInfo<DmUpkeepplanitemdetailPojo> pageInfo = new PageInfo<DmUpkeepplanitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmUpkeepplanPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            DmUpkeepplanPojo dmUpkeepplanPojo = this.dmUpkeepplanMapper.getEntity(key, tid);
            //读取子表
            dmUpkeepplanPojo.setItem(dmUpkeepplanitemMapper.getList(dmUpkeepplanPojo.getId(), dmUpkeepplanPojo.getTenantid()));
            // 读取planid关联的保养方案的子表List
            dmUpkeepplanPojo.setFlowitem(dmUpkeepflowitemMapper.getList(dmUpkeepplanPojo.getFlowid(), dmUpkeepplanPojo.getTenantid()));

            return dmUpkeepplanPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeepplanPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeepplanPojo> lst = dmUpkeepplanMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(dmUpkeepplanitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<DmUpkeepplanPojo> pageInfo = new PageInfo<DmUpkeepplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeepplanPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeepplanPojo> lst = dmUpkeepplanMapper.getPageTh(queryParam);
            PageInfo<DmUpkeepplanPojo> pageInfo = new PageInfo<DmUpkeepplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmUpkeepplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmUpkeepplanPojo insert(DmUpkeepplanPojo dmUpkeepplanPojo) {
//初始化NULL字段
        if (dmUpkeepplanPojo.getRefno() == null) dmUpkeepplanPojo.setRefno("");
        if (dmUpkeepplanPojo.getBilldate() == null) dmUpkeepplanPojo.setBilldate(new Date());
        if (dmUpkeepplanPojo.getBilltype() == null) dmUpkeepplanPojo.setBilltype("");
        if (dmUpkeepplanPojo.getBilltitle() == null) dmUpkeepplanPojo.setBilltitle("");
        if (dmUpkeepplanPojo.getStartdate() == null) dmUpkeepplanPojo.setStartdate(new Date());
        if (dmUpkeepplanPojo.getEnddate() == null) dmUpkeepplanPojo.setEnddate(new Date());
        if (dmUpkeepplanPojo.getCycletype() == null) dmUpkeepplanPojo.setCycletype("");
        if (dmUpkeepplanPojo.getCyclevalue() == null) dmUpkeepplanPojo.setCyclevalue("");
        if (dmUpkeepplanPojo.getCyclenotice() == null) dmUpkeepplanPojo.setCyclenotice(0);
        if (dmUpkeepplanPojo.getNoticestart() == null) dmUpkeepplanPojo.setNoticestart(new Date());
        if (dmUpkeepplanPojo.getNoticeend() == null) dmUpkeepplanPojo.setNoticeend(new Date());
        if (dmUpkeepplanPojo.getFlowid() == null) dmUpkeepplanPojo.setFlowid("");
        if (dmUpkeepplanPojo.getOperator() == null) dmUpkeepplanPojo.setOperator("");
        if (dmUpkeepplanPojo.getItemcount() == null) dmUpkeepplanPojo.setItemcount(dmUpkeepplanPojo.getItem().size());
        if (dmUpkeepplanPojo.getFinishcount() == null) dmUpkeepplanPojo.setFinishcount(0);
        if (dmUpkeepplanPojo.getSummary() == null) dmUpkeepplanPojo.setSummary("");
        if (dmUpkeepplanPojo.getCustom1() == null) dmUpkeepplanPojo.setCustom1("");
        if (dmUpkeepplanPojo.getCustom2() == null) dmUpkeepplanPojo.setCustom2("");
        if (dmUpkeepplanPojo.getCustom3() == null) dmUpkeepplanPojo.setCustom3("");
        if (dmUpkeepplanPojo.getCustom4() == null) dmUpkeepplanPojo.setCustom4("");
        if (dmUpkeepplanPojo.getCustom5() == null) dmUpkeepplanPojo.setCustom5("");
        if (dmUpkeepplanPojo.getCustom6() == null) dmUpkeepplanPojo.setCustom6("");
        if (dmUpkeepplanPojo.getCustom7() == null) dmUpkeepplanPojo.setCustom7("");
        if (dmUpkeepplanPojo.getCustom8() == null) dmUpkeepplanPojo.setCustom8("");
        if (dmUpkeepplanPojo.getLister() == null) dmUpkeepplanPojo.setLister("");
        if (dmUpkeepplanPojo.getListerid() == null) dmUpkeepplanPojo.setListerid("");
        if (dmUpkeepplanPojo.getCreatedate() == null) dmUpkeepplanPojo.setCreatedate(new Date());
        if (dmUpkeepplanPojo.getCreatebyid() == null) dmUpkeepplanPojo.setCreatebyid("");
        if (dmUpkeepplanPojo.getCreateby() == null) dmUpkeepplanPojo.setCreateby("");
        if (dmUpkeepplanPojo.getModifydate() == null) dmUpkeepplanPojo.setModifydate(new Date());
        if (dmUpkeepplanPojo.getAssessorid() == null) dmUpkeepplanPojo.setAssessorid("");
        if (dmUpkeepplanPojo.getAssessor() == null) dmUpkeepplanPojo.setAssessor("");
        if (dmUpkeepplanPojo.getAssessdate() == null) dmUpkeepplanPojo.setAssessdate(new Date());
        if (dmUpkeepplanPojo.getEnabledmark() == null) dmUpkeepplanPojo.setEnabledmark(0);
        if (dmUpkeepplanPojo.getDeletemark() == null) dmUpkeepplanPojo.setDeletemark(0);
        if (dmUpkeepplanPojo.getDeletelister() == null) dmUpkeepplanPojo.setDeletelister("");
        if (dmUpkeepplanPojo.getDeletedate() == null) dmUpkeepplanPojo.setDeletedate(new Date());
        if (dmUpkeepplanPojo.getTenantid() == null) dmUpkeepplanPojo.setTenantid("");
        if (dmUpkeepplanPojo.getRevision() == null) dmUpkeepplanPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmUpkeepplanEntity dmUpkeepplanEntity = new DmUpkeepplanEntity();
        BeanUtils.copyProperties(dmUpkeepplanPojo, dmUpkeepplanEntity);

        //设置id和新建日期
        dmUpkeepplanEntity.setId(id);
        dmUpkeepplanEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmUpkeepplanMapper.insert(dmUpkeepplanEntity);
        //Item子表处理
        List<DmUpkeepplanitemPojo> lst = dmUpkeepplanPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                DmUpkeepplanitemPojo itemPojo = this.dmUpkeepplanitemService.clearNull(lst.get(i));
                DmUpkeepplanitemEntity dmUpkeepplanitemEntity = new DmUpkeepplanitemEntity();
                BeanUtils.copyProperties(itemPojo, dmUpkeepplanitemEntity);
                //设置id和Pid
                dmUpkeepplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmUpkeepplanitemEntity.setPid(id);
                dmUpkeepplanitemEntity.setTenantid(dmUpkeepplanPojo.getTenantid());
                dmUpkeepplanitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmUpkeepplanitemMapper.insert(dmUpkeepplanitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmUpkeepplanEntity.getId(), dmUpkeepplanEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmUpkeepplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmUpkeepplanPojo update(DmUpkeepplanPojo dmUpkeepplanPojo) {
        //主表更改
        DmUpkeepplanEntity dmUpkeepplanEntity = new DmUpkeepplanEntity();
        //更新款数
        dmUpkeepplanPojo.setItemcount(dmUpkeepplanPojo.getItem().size());
        BeanUtils.copyProperties(dmUpkeepplanPojo, dmUpkeepplanEntity);
        this.dmUpkeepplanMapper.update(dmUpkeepplanEntity);
        if (dmUpkeepplanPojo.getItem() != null) {
            //Item子表处理
            List<DmUpkeepplanitemPojo> lst = dmUpkeepplanPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = dmUpkeepplanMapper.getDelItemIds(dmUpkeepplanPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.dmUpkeepplanitemMapper.delete(lstDelIds.get(i), dmUpkeepplanEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    DmUpkeepplanitemEntity dmUpkeepplanitemEntity = new DmUpkeepplanitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        DmUpkeepplanitemPojo itemPojo = this.dmUpkeepplanitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, dmUpkeepplanitemEntity);
                        //设置id和Pid
                        dmUpkeepplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        dmUpkeepplanitemEntity.setPid(dmUpkeepplanEntity.getId());  // 主表 id
                        dmUpkeepplanitemEntity.setTenantid(dmUpkeepplanPojo.getTenantid());   // 租户id
                        dmUpkeepplanitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.dmUpkeepplanitemMapper.insert(dmUpkeepplanitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), dmUpkeepplanitemEntity);
                        dmUpkeepplanitemEntity.setTenantid(dmUpkeepplanPojo.getTenantid());
                        this.dmUpkeepplanitemMapper.update(dmUpkeepplanitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmUpkeepplanEntity.getId(), dmUpkeepplanEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        DmUpkeepplanPojo dmUpkeepplanPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<DmUpkeepplanitemPojo> lst = dmUpkeepplanPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.dmUpkeepplanitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.dmUpkeepplanMapper.delete(key, tid);
    }

    /**
     * 审核数据
     *
     * @param dmUpkeepplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmUpkeepplanPojo approval(DmUpkeepplanPojo dmUpkeepplanPojo) {
        //主表更改
        DmUpkeepplanEntity dmUpkeepplanEntity = new DmUpkeepplanEntity();
        BeanUtils.copyProperties(dmUpkeepplanPojo, dmUpkeepplanEntity);
        this.dmUpkeepplanMapper.approval(dmUpkeepplanEntity);
        //返回Bill实例
        return this.getBillEntity(dmUpkeepplanEntity.getId(), dmUpkeepplanEntity.getTenantid());
    }

    @Override
    public List<DmUpkeepplanPojo> getAllList(String tid) {
        return this.dmUpkeepplanMapper.getAllList(tid);
    }

    // 不传日期则默认获取所有的保养计划,传入日期范围则获取该日期范围的保养计划 start和end要么同时为null，要么同时不为null
    @Override
    public List<Map<String, String>> getALLPlan(String tid, LocalDate start, LocalDate end) {
        List<Map<String, String>> allPlanList = new ArrayList<>();
        try {
            List<DmUpkeepplanPojo> dmUpkeepplanList = this.getAllList(tid);
            for (DmUpkeepplanPojo dmUpkeepplanPojo : dmUpkeepplanList) {

                // 不传日期则默认获取所有的保养计划,传入日期范围则获取该日期范围的保养计划
                List<LocalDate> datesInRange = null;
                if (start == null) {
                    // Date转LocalDate
                    LocalDate localDateStart = dmUpkeepplanPojo.getStartdate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate localDateEnd = dmUpkeepplanPojo.getEnddate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    datesInRange = getDatesInRange(localDateStart, localDateEnd, dmUpkeepplanPojo.getCycletype(), dmUpkeepplanPojo.getCyclevalue());
                } else {
                    datesInRange = getDatesInRange(start, end, dmUpkeepplanPojo.getCycletype(), dmUpkeepplanPojo.getCyclevalue());
                }

                for (LocalDate date : datesInRange) {
                    Map<String, String> plan = new HashMap<>();
                    plan.put("plandate", date.toString());
                    plan.put("planname", dmUpkeepplanPojo.getBilltitle());
                    plan.put("planid", dmUpkeepplanPojo.getId());
                    plan.put("itemcount", String.valueOf(dmUpkeepplanPojo.getItemcount()));
                    // 获取保养实施单完成的finishcount(累加)
                    int finishCount = dmUpkeeprecordService.getSumFinishCount(dmUpkeepplanPojo.getId(), date.toString(), tid);
                    plan.put("finishcount", String.valueOf(finishCount));
                    // 20230907 多返回个cyclenotice(提取几天检查提醒)
                    plan.put("cyclenotice", String.valueOf(dmUpkeepplanPojo.getCyclenotice()));
                    allPlanList.add(plan);
                }
            }
            // planList根据日期排序 较早的日期排在前面
            allPlanList.sort((o1, o2) -> {
                String date1 = o1.get("plandate");
                String date2 = o2.get("plandate");
                return date1.compareTo(date2);
            });
            return allPlanList;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<Map<String, String>> getALLPlanByMonth(Integer year, Integer month, String tid) {
        List<Map<String, String>> allPlanList = new ArrayList<>();
        try {
            List<DmUpkeepplanPojo> dmUpkeepplanList = this.getAllList(tid);
            for (DmUpkeepplanPojo dmUpkeepplanPojo : dmUpkeepplanList) {
                // Date转LocalDate
                // 获取该年该月的第一天和最后一天
                LocalDate monthStart = LocalDate.of(year, month, 1);
                LocalDate monthEnd = monthStart.with(TemporalAdjusters.lastDayOfMonth());
                // 校验该月的第一天和最后一天是否在保养计划的开始时间和结束时间之间
                LocalDate planStartDate = dmUpkeepplanPojo.getStartdate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate planEndDate = dmUpkeepplanPojo.getEnddate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                // 如果monthStart到monthEnd完全不在planStartDate到planEndDate之间，则跳过该保养计划
                if (monthStart.isAfter(planEndDate) || monthEnd.isBefore(planStartDate)) {
                    continue;
                }
                // 否则monthStart和monthEnd分别截取planStartDate和planEndDate的较大值和较小值
                if (monthStart.isBefore(planStartDate)) monthStart = planStartDate;
                if (monthEnd.isAfter(planEndDate)) monthEnd = planEndDate;
                List<LocalDate> datesInRange = getDatesInRange(monthStart, monthEnd, dmUpkeepplanPojo.getCycletype(), dmUpkeepplanPojo.getCyclevalue());
                for (LocalDate date : datesInRange) {
                    Map<String, String> plan = new HashMap<>();
                    plan.put("plandate", date.toString());
                    plan.put("planname", dmUpkeepplanPojo.getBilltitle());
                    plan.put("planid", dmUpkeepplanPojo.getId());
                    plan.put("itemcount", String.valueOf(dmUpkeepplanPojo.getItemcount()));
                    // 获取保养实施单完成的finishcount(累加)
                    int finishCount = dmUpkeeprecordService.getSumFinishCount(dmUpkeepplanPojo.getId(), date.toString(), tid);
                    plan.put("finishcount", String.valueOf(finishCount));
                    allPlanList.add(plan);
                }
            }
            // planList根据日期排序 较早的日期排在前面
            allPlanList.sort((o1, o2) -> {
                String date1 = o1.get("plandate");
                String date2 = o2.get("plandate");
                return date1.compareTo(date2);
            });
            return allPlanList;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public List<Map<String, String>> getALLPlanToday(String tid) {
        List<Map<String, String>> allPlanList = new ArrayList<>();
        try {
            List<DmUpkeepplanPojo> dmUpkeepplanList = this.getAllList(tid);
            LocalDate today = LocalDate.now(); // 获取今天的日期
            for (DmUpkeepplanPojo dmUpkeepplanPojo : dmUpkeepplanList) {
                // Date转LocalDate
                LocalDate localDateStart = dmUpkeepplanPojo.getStartdate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate localDateEnd = dmUpkeepplanPojo.getEnddate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                List<LocalDate> datesInRange = getDatesInRange(localDateStart, localDateEnd, dmUpkeepplanPojo.getCycletype(), dmUpkeepplanPojo.getCyclevalue());
                for (LocalDate date : datesInRange) {
                    // 仅需要添加今天的计划
                    if (date.isEqual(today)) {
                        Map<String, String> plan = new HashMap<>();
                        plan.put("plandate", date.toString());
                        plan.put("planname", dmUpkeepplanPojo.getBilltitle());
                        plan.put("planid", dmUpkeepplanPojo.getId());
                        plan.put("itemcount", String.valueOf(dmUpkeepplanPojo.getItemcount()));
                        // 获取保养实施单完成的finishcount(累加)
                        int finishCount = dmUpkeeprecordService.getSumFinishCount(dmUpkeepplanPojo.getId(), date.toString(), tid);
                        plan.put("finishcount", String.valueOf(finishCount));
                        allPlanList.add(plan);
                    }
                }
            }
//            // 都是今天的计划 所以不用排序  planList根据日期排序 较早的日期排在前面
//            allPlanList.sort((o1, o2) -> {
//                String date1 = o1.get("plandate");
//                String date2 = o2.get("plandate");
//                return date1.compareTo(date2);
//            });
            return allPlanList;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<String> getAllDevId(String planid, String tid) {
        return dmUpkeepplanMapper.getAllDevId(planid, tid);
    }
}
