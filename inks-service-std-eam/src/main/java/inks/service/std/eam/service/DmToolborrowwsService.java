package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolborrowwsPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 工装具借还子表-加工单(DmToolborrowws)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-13 09:52:08
 */
public interface DmToolborrowwsService {

    DmToolborrowwsPojo getEntity(String key,String tid);

    PageInfo<DmToolborrowwsPojo> getPageList(QueryParam queryParam);

    List<DmToolborrowwsPojo> getList(String Pid,String tid);  

    DmToolborrowwsPojo insert(DmToolborrowwsPojo dmToolborrowwsPojo);

    DmToolborrowwsPojo update(DmToolborrowwsPojo dmToolborrowwspojo);

    int delete(String key,String tid);

    DmToolborrowwsPojo clearNull(DmToolborrowwsPojo dmToolborrowwspojo);
}
