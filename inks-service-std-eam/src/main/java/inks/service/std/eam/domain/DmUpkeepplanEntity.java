package inks.service.std.eam.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 保养计划(DmUpkeepplan)实体类
 *
 * <AUTHOR>
 * @since 2023-06-08 14:23:16
 */
public class DmUpkeepplanEntity implements Serializable {
    private static final long serialVersionUID = 694396685157253661L;
    // 计划id
    private String id;
    // 计划编码
    private String refno;
    // 单据日期
    private Date billdate;
    // 类型(计划分类)
    private String billtype;
    // 单据主题(计划名称)
    private String billtitle;
    // 计划周期
    private Date startdate;
    // 计划周期
    private Date enddate;
    // 保养频率(每N天,每周,每月)
    private String cycletype;
    // 保养频率(每2天,每周周2周5,每月2号20号)
    private String cyclevalue;
    // 每隔几分钟提醒一次
    private Integer cyclenotice;
    // 提醒开始时间
    private Date noticestart;
    // 提醒结束时间
    private Date noticeend;
    // 保养方案id
    private String flowid;
    // 操作员
    private String operator;
    // 子表设备数
    private Integer itemcount;
    // 完成数
    private Integer finishcount;
    // 简述
    private String summary;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 新建日期
    private Date createdate;
    // 创建者id
    private String createbyid;
    // 创建者
    private String createby;
    // 修改日期
    private Date modifydate;
    // 审核员id
    private String assessorid;
    // 审核员
    private String assessor;
    // 审核日期
    private Date assessdate;
    // 有效标识
    private Integer enabledmark;
    // 删除标识
    private Integer deletemark;
    // 删除人员
    private String deletelister;
    // 删除日期
    private Date deletedate;
    // 租户id
    private String tenantid;
    // 乐观锁
    private Integer revision;

    // 计划id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 计划编码
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 单据日期
    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }

    // 类型(计划分类)
    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }

    // 单据主题(计划名称)
    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }

    // 计划周期
    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }

    // 计划周期
    public Date getEnddate() {
        return enddate;
    }

    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }

    // 保养频率(每N天,每周,每月)
    public String getCycletype() {
        return cycletype;
    }

    public void setCycletype(String cycletype) {
        this.cycletype = cycletype;
    }

    // 保养频率(每2天,每周周2周5,每月2号20号)
    public String getCyclevalue() {
        return cyclevalue;
    }

    public void setCyclevalue(String cyclevalue) {
        this.cyclevalue = cyclevalue;
    }

    // 每隔几分钟提醒一次
    public Integer getCyclenotice() {
        return cyclenotice;
    }

    public void setCyclenotice(Integer cyclenotice) {
        this.cyclenotice = cyclenotice;
    }

    // 提醒开始时间
    public Date getNoticestart() {
        return noticestart;
    }

    public void setNoticestart(Date noticestart) {
        this.noticestart = noticestart;
    }

    // 提醒结束时间
    public Date getNoticeend() {
        return noticeend;
    }

    public void setNoticeend(Date noticeend) {
        this.noticeend = noticeend;
    }

    // 保养方案id
    public String getFlowid() {
        return flowid;
    }

    public void setFlowid(String flowid) {
        this.flowid = flowid;
    }

    // 操作员
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    // 子表设备数
    public Integer getItemcount() {
        return itemcount;
    }

    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }

    // 完成数
    public Integer getFinishcount() {
        return finishcount;
    }

    public void setFinishcount(Integer finishcount) {
        this.finishcount = finishcount;
    }

    // 简述
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 审核员id
    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }

    // 审核员
    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }

    // 审核日期
    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }

    // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 删除标识
    public Integer getDeletemark() {
        return deletemark;
    }

    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }

    // 删除人员
    public String getDeletelister() {
        return deletelister;
    }

    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }

    // 删除日期
    public Date getDeletedate() {
        return deletedate;
    }

    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

