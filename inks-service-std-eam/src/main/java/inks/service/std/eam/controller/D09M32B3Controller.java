package inks.service.std.eam.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 规则节点运行状态表(Iot_RuleNodeState)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:48
 */
@RestController
@RequestMapping("D09M32B3")
@Api(tags = "D09M32B3:规则节点运行状态表")
public class D09M32B3Controller extends IotRulenodestateController {


}
