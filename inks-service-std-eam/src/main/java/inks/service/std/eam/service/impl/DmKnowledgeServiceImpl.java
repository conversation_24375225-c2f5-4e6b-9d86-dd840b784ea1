package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmKnowledgeEntity;
import inks.service.std.eam.domain.pojo.DmKnowledgePojo;
import inks.service.std.eam.mapper.DmKnowledgeMapper;
import inks.service.std.eam.service.DmKnowledgeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 设备知识库(DmKnowledge)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 11:22:51
 */
@Service("dmKnowledgeService")
public class DmKnowledgeServiceImpl implements DmKnowledgeService {
    @Resource
    private DmKnowledgeMapper dmKnowledgeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmKnowledgePojo getEntity(String key, String tid) {
        return this.dmKnowledgeMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmKnowledgePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmKnowledgePojo> lst = dmKnowledgeMapper.getPageList(queryParam);
            PageInfo<DmKnowledgePojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmKnowledgePojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmKnowledgePojo insert(DmKnowledgePojo dmKnowledgePojo) {
        //初始化NULL字段
        if (dmKnowledgePojo.getId() == null) dmKnowledgePojo.setId("");
        if (dmKnowledgePojo.getRefno() == null) dmKnowledgePojo.setRefno("");
        if (dmKnowledgePojo.getDeviceid() == null) dmKnowledgePojo.setDeviceid("");
        if (dmKnowledgePojo.getDevicename() == null) dmKnowledgePojo.setDevicename("");
        if (dmKnowledgePojo.getBilltype() == null) dmKnowledgePojo.setBilltype("");
        if (dmKnowledgePojo.getBilltitle() == null) dmKnowledgePojo.setBilltitle("");
        if (dmKnowledgePojo.getOperator() == null) dmKnowledgePojo.setOperator("");
        if (dmKnowledgePojo.getBilldate() == null) dmKnowledgePojo.setBilldate(new Date());
        if (dmKnowledgePojo.getReason() == null) dmKnowledgePojo.setReason("");
        if (dmKnowledgePojo.getDetails() == null) dmKnowledgePojo.setDetails("");
        if (dmKnowledgePojo.getRemark() == null) dmKnowledgePojo.setRemark("");
        if (dmKnowledgePojo.getVideourl() == null) dmKnowledgePojo.setVideourl("");
        if (dmKnowledgePojo.getCustom1() == null) dmKnowledgePojo.setCustom1("");
        if (dmKnowledgePojo.getCustom2() == null) dmKnowledgePojo.setCustom2("");
        if (dmKnowledgePojo.getCustom3() == null) dmKnowledgePojo.setCustom3("");
        if (dmKnowledgePojo.getCustom4() == null) dmKnowledgePojo.setCustom4("");
        if (dmKnowledgePojo.getCustom5() == null) dmKnowledgePojo.setCustom5("");
        if (dmKnowledgePojo.getCustom6() == null) dmKnowledgePojo.setCustom6("");
        if (dmKnowledgePojo.getCustom7() == null) dmKnowledgePojo.setCustom7("");
        if (dmKnowledgePojo.getCustom8() == null) dmKnowledgePojo.setCustom8("");
        if (dmKnowledgePojo.getLister() == null) dmKnowledgePojo.setLister("");
        if (dmKnowledgePojo.getListerid() == null) dmKnowledgePojo.setListerid("");
        if (dmKnowledgePojo.getCreateby() == null) dmKnowledgePojo.setCreateby("");
        if (dmKnowledgePojo.getCreatebyid() == null) dmKnowledgePojo.setCreatebyid("");
        if (dmKnowledgePojo.getCreatedate() == null) dmKnowledgePojo.setCreatedate(new Date());
        if (dmKnowledgePojo.getModifydate() == null) dmKnowledgePojo.setModifydate(new Date());
        if (dmKnowledgePojo.getEnabledmark() == null) dmKnowledgePojo.setEnabledmark(0);
        if (dmKnowledgePojo.getDeletemark() == null) dmKnowledgePojo.setDeletemark(0);
        if (dmKnowledgePojo.getDeletelister() == null) dmKnowledgePojo.setDeletelister("");
        if (dmKnowledgePojo.getDeletedate() == null) dmKnowledgePojo.setDeletedate(new Date());
        if (dmKnowledgePojo.getTenantid() == null) dmKnowledgePojo.setTenantid("");
        if (dmKnowledgePojo.getRevision() == null) dmKnowledgePojo.setRevision(0);
        DmKnowledgeEntity dmKnowledgeEntity = new DmKnowledgeEntity();
        BeanUtils.copyProperties(dmKnowledgePojo, dmKnowledgeEntity);
        //生成雪花id
        dmKnowledgeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmKnowledgeEntity.setRevision(1);  //乐观锁
        this.dmKnowledgeMapper.insert(dmKnowledgeEntity);
        return this.getEntity(dmKnowledgeEntity.getId(), dmKnowledgeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmKnowledgePojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmKnowledgePojo update(DmKnowledgePojo dmKnowledgePojo) {
        DmKnowledgeEntity dmKnowledgeEntity = new DmKnowledgeEntity();
        BeanUtils.copyProperties(dmKnowledgePojo, dmKnowledgeEntity);
        this.dmKnowledgeMapper.update(dmKnowledgeEntity);
        return this.getEntity(dmKnowledgeEntity.getId(), dmKnowledgeEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmKnowledgeMapper.delete(key, tid);
    }


}
