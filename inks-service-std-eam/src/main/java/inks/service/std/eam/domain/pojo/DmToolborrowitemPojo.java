package inks.service.std.eam.domain.pojo;

import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 工装具借用子表-工装具(DmToolborrowitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-06-19 10:17:40
 */
public class DmToolborrowitemPojo implements Serializable {
    private static final long serialVersionUID = -34764155998603241L;
     // id
  @Excel(name = "id")    
  private String id;
     // 借用主表id
  @Excel(name = "借用主表id")
  private String pid;
     // 工装具id
  @Excel(name = "工装具id")    
  private String toolid;
     // 借用数量
  @Excel(name = "借用数量")
  private Integer borrowqty;
     // 累计归还数量
  @Excel(name = "累计归还数量")
  private Integer returnedqty;
     // 行状态 (1:待归还, 2:已归还)
  @Excel(name = "行状态 (1:待归还, 2:已归还)")    
  private Integer status;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 自定义6
  @Excel(name = "自定义6")    
  private String custom6;
     // 自定义7
  @Excel(name = "自定义7")    
  private String custom7;
     // 自定义8
  @Excel(name = "自定义8")    
  private String custom8;
     // 自定义9
  @Excel(name = "自定义9")    
  private String custom9;
     // 自定义10
  @Excel(name = "自定义10")    
  private String custom10;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;

  //Dm_ToolInfo.ToolName,
    //               Dm_ToolInfo.ToolCode

   private String toolname;
   private String toolcode;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public String getToolname() {
        return toolname;
    }

    public void setToolname(String toolname) {
        this.toolname = toolname;
    }

    public String getToolcode() {
        return toolcode;
    }

    public void setToolcode(String toolcode) {
        this.toolcode = toolcode;
    }

    // 主表id
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 工装具id
    public String getToolid() {
        return toolid;
    }
    
    public void setToolid(String toolid) {
        this.toolid = toolid;
    }

   // 借用数量
    public Integer getBorrowqty() {
        return borrowqty;
    }
    
    public void setBorrowqty(Integer borrowqty) {
        this.borrowqty = borrowqty;
    }
        
   // 累计归还数量
    public Integer getReturnedqty() {
        return returnedqty;
    }
    
    public void setReturnedqty(Integer returnedqty) {
        this.returnedqty = returnedqty;
    }
        
   // 行状态 (1:待归还, 2:已归还)
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }

   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

