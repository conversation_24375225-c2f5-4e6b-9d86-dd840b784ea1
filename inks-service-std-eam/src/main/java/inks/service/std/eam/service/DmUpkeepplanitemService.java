package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmUpkeepplanitemPojo;

import java.util.List;

/**
 * 设备清单(DmUpkeepplanitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-06 14:36:10
 */
public interface DmUpkeepplanitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeepplanitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeepplanitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmUpkeepplanitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param dmUpkeepplanitemPojo 实例对象
     * @return 实例对象
     */
    DmUpkeepplanitemPojo insert(DmUpkeepplanitemPojo dmUpkeepplanitemPojo);

    /**
     * 修改数据
     *
     * @param dmUpkeepplanitempojo 实例对象
     * @return 实例对象
     */
    DmUpkeepplanitemPojo update(DmUpkeepplanitemPojo dmUpkeepplanitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param dmUpkeepplanitempojo 实例对象
     * @return 实例对象
     */
    DmUpkeepplanitemPojo clearNull(DmUpkeepplanitemPojo dmUpkeepplanitempojo);
}
