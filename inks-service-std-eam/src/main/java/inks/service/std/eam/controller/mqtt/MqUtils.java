package inks.service.std.eam.controller.mqtt;

import inks.service.std.eam.iotrule.A_IotMqttService;
import inks.service.std.eam.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * MQTT中获取Bean
 * <p>
 * 这是一个单例工具类，用于在全局范围内提供对服务的访问。
 * 采用Spring管理的单例模式实现，确保线程安全和资源的有效管理。
 * <p>
 * 设计说明：
 * 1. 单例模式：通过静态变量和私有构造函数确保全局唯一实例
 * 2. Spring集成：使用@Component注解将类交给Spring容器管理
 * 3. 延迟初始化：实例在Spring容器启动时才完成初始化
 * 4. 线程安全：由Spring容器保证实例的线程安全性
 * <p>
 * 使用示例：
 * IotDeviceService deviceService = MqUtils.getInstance().getIotDeviceService();
 */
@Component
public class MqUtils {

    private static final Logger log = LoggerFactory.getLogger(MqUtils.class);

    /**
     * 静态实例，用于存储当前类的唯一实例
     * volatile关键字确保多线程环境下的可见性
     */
    private static volatile MqUtils instance;

    /**
     * 服务实例
     * 使用@Resource注解进行依赖注入
     */
    @Resource
    private A_IotMqttService aIotMqttService;
    @Resource
    private IotDeviceprofileService iotDeviceprofileService;

    @Resource
    private IotDeviceService iotDeviceService;

    @Resource
    private IotRulechainService iotRulechainService;

    @Resource
    private IotTskvService iotTskvService;

    @Resource
    private IotTskvlatestService iotTskvlatestService;

    /**
     * 获取设备配置服务
     *
     * @return 设备配置服务实例
     */
    public IotDeviceprofileService getIotDeviceprofileService() {
        return iotDeviceprofileService;
    }

    /**
     * 获取设备服务
     *
     * @return 设备服务实例
     */
    public IotDeviceService getIotDeviceService() {
        return iotDeviceService;
    }

    /**
     * 获取规则链服务
     *
     * @return 规则链服务实例
     */
    public IotRulechainService getIotRulechainService() {
        return iotRulechainService;
    }

    /**
     * 获取TSKV服务
     *
     * @return TSKV服务实例
     */
    public IotTskvService getIotTskvService() {
        return iotTskvService;
    }

    /**
     * 获取TSKV最新服务
     *
     * @return TSKV最新服务实例
     */
    public IotTskvlatestService getIotTskvlatestService() {
        return iotTskvlatestService;
    }

    /**
     * 私有构造函数
     * 防止外部直接创建实例，确保单例模式
     */
    private MqUtils() {
    }

    /**
     * 获取MqUtils的单例实例
     *
     * @return MqUtils实例
     * @throws IllegalStateException 如果实例未初始化
     */
    public static MqUtils getInstance() {
        if (instance == null) {
            throw new IllegalStateException("MqUtils尚未初始化，请确保Spring容器已启动");
        }
        return instance;
    }

    /**
     * 初始化方法
     * 在Spring容器启动时自动执行
     * 使用@PostConstruct注解确保在依赖注入完成后执行
     */
    @PostConstruct
    public void init() {
        log.info("MqUtils初始化完成");
        MqUtils.instance = this;
    }
}