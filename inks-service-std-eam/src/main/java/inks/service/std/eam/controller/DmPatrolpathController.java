package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.DmPatrolpathPojo;
import inks.service.std.eam.domain.pojo.DmPatrolpathitemPojo;
import inks.service.std.eam.domain.pojo.DmPatrolpathitemdetailPojo;
import inks.service.std.eam.service.DmPatrolpathService;
import inks.service.std.eam.service.DmPatrolpathitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 巡检路线(DmPatrolpath)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-10 12:57:50
 */
public class DmPatrolpathController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(DmPatrolpathController.class);
    /**
     * 服务对象
     */
    @Resource
    private DmPatrolpathService dmPatrolpathService;
    /**
     * 服务对象Item
     */
    @Resource
    private DmPatrolpathitemService dmPatrolpathitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取巡检路线详细信息", notes = "获取巡检路线详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.List")
    public R<DmPatrolpathPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmPatrolpathService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.List")
    public R<PageInfo<DmPatrolpathitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_PatrolPath.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.dmPatrolpathService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取巡检路线详细信息", notes = "获取巡检路线详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.List")
    public R<DmPatrolpathPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmPatrolpathService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.List")
    public R<PageInfo<DmPatrolpathPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_PatrolPath.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.dmPatrolpathService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.List")
    public R<PageInfo<DmPatrolpathPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_PatrolPath.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.dmPatrolpathService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增巡检路线", notes = "新增巡检路线", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.Add")
    public R<DmPatrolpathPojo> create(@RequestBody String json) {
        try {
            DmPatrolpathPojo dmPatrolpathPojo = JSONArray.parseObject(json, DmPatrolpathPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmPatrolpathPojo.setCreateby(loginUser.getRealName());   // 创建者
            dmPatrolpathPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            dmPatrolpathPojo.setCreatedate(new Date());   // 创建时间
            dmPatrolpathPojo.setLister(loginUser.getRealname());   // 制表
            dmPatrolpathPojo.setListerid(loginUser.getUserid());    // 制表id            
            dmPatrolpathPojo.setModifydate(new Date());   //修改时间
            dmPatrolpathPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.dmPatrolpathService.insert(dmPatrolpathPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改巡检路线", notes = "修改巡检路线", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.Edit")
    public R<DmPatrolpathPojo> update(@RequestBody String json) {
        try {
            DmPatrolpathPojo dmPatrolpathPojo = JSONArray.parseObject(json, DmPatrolpathPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmPatrolpathPojo.setLister(loginUser.getRealname());   // 制表
            dmPatrolpathPojo.setListerid(loginUser.getUserid());    // 制表id   
            dmPatrolpathPojo.setModifydate(new Date());   //修改时间
            dmPatrolpathPojo.setAssessor(""); //审核员
            dmPatrolpathPojo.setAssessorid(""); //审核员
            dmPatrolpathPojo.setAssessdate(new Date()); //审核时间
            dmPatrolpathPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.dmPatrolpathService.update(dmPatrolpathPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除巡检路线", notes = "删除巡检路线", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmPatrolpathService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增巡检路线Item", notes = "新增巡检路线Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.Add")
    public R<DmPatrolpathitemPojo> createItem(@RequestBody String json) {
        try {
            DmPatrolpathitemPojo dmPatrolpathitemPojo = JSONArray.parseObject(json, DmPatrolpathitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmPatrolpathitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.dmPatrolpathitemService.insert(dmPatrolpathitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除巡检路线Item", notes = "删除巡检路线Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmPatrolpathitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核巡检路线", notes = "审核巡检路线", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.Approval")
    public R<DmPatrolpathPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            DmPatrolpathPojo dmPatrolpathPojo = this.dmPatrolpathService.getEntity(key, loginUser.getTenantid());
            if (dmPatrolpathPojo.getAssessor().equals("")) {
                dmPatrolpathPojo.setAssessor(loginUser.getRealname()); //审核员
                dmPatrolpathPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                dmPatrolpathPojo.setAssessor(""); //审核员
                dmPatrolpathPojo.setAssessorid(""); //审核员
            }
            dmPatrolpathPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.dmPatrolpathService.approval(dmPatrolpathPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_PatrolPath.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        DmPatrolpathPojo dmPatrolpathPojo = this.dmPatrolpathService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(dmPatrolpathPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = dmPatrolpathPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    DmPatrolpathitemPojo dmPatrolpathitemPojo = new DmPatrolpathitemPojo();
                    dmPatrolpathPojo.getItem().add(dmPatrolpathitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(dmPatrolpathPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

