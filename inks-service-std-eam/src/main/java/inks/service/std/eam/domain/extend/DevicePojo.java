package inks.service.std.eam.domain.extend;

import java.io.Serializable;

/**
 * 设备实体类 用于各个Item继承设备的以下字段
 *
 * <AUTHOR>
 * @since 2023-06-06 12:59:48
 */
public class DevicePojo implements Serializable {
    private static final long serialVersionUID = 142217117475453922L;
    //id
    private String devid;
    // 编码
    private String devcode;
    // 名称
    private String devname;
    // 规格
    private String devspec;
    // 单位
    private String devunit;
    // 状态usestate
    private String usestate;

    public String getDevid() {
        return devid;
    }

    public void setDevid(String devid) {
        this.devid = devid;
    }

    public String getDevcode() {
        return devcode;
    }

    public void setDevcode(String devcode) {
        this.devcode = devcode;
    }

    public String getDevname() {
        return devname;
    }

    public void setDevname(String devname) {
        this.devname = devname;
    }

    public String getDevspec() {
        return devspec;
    }

    public void setDevspec(String devspec) {
        this.devspec = devspec;
    }

    public String getDevunit() {
        return devunit;
    }

    public void setDevunit(String devunit) {
        this.devunit = devunit;
    }

    public String getUsestate() {
        return usestate;
    }

    public void setUsestate(String usestate) {
        this.usestate = usestate;
    }
}

