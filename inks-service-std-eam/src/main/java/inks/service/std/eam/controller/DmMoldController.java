package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmMoldPojo;
import inks.service.std.eam.service.DmMoldService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 模具管理(Dm_Mold)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-13 14:12:19
 */
//@RestController
//@RequestMapping("dmMold")
public class DmMoldController {

    @Resource
    private DmMoldService dmMoldService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(DmMoldController.class);


    @ApiOperation(value=" 获取模具管理详细信息", notes="获取模具管理详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Mold.List")
    public R<DmMoldPojo> getEntity(String key) {
    try {
           // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmMoldService.getEntity(key, loginUser.getTenantid()));
         }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Mold.List")
    public R<PageInfo<DmMoldPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Dm_Mold.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmMoldService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增模具管理", notes="新增模具管理", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Mold.Add")
    public R<DmMoldPojo> create(@RequestBody String json) {
       try {
       DmMoldPojo dmMoldPojo = JSONArray.parseObject(json,DmMoldPojo.class);       
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmMoldPojo.setCreateby(loginUser.getRealName());   // 创建者
            dmMoldPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            dmMoldPojo.setCreatedate(new Date());   // 创建时间
            dmMoldPojo.setLister(loginUser.getRealname());   // 制表
            dmMoldPojo.setListerid(loginUser.getUserid());    // 制表id  
            dmMoldPojo.setModifydate(new Date());   //修改时间
            dmMoldPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.dmMoldService.insert(dmMoldPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改模具管理", notes="修改模具管理", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Mold.Edit")
    public R<DmMoldPojo> update(@RequestBody String json) {
       try {
         DmMoldPojo dmMoldPojo = JSONArray.parseObject(json,DmMoldPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmMoldPojo.setLister(loginUser.getRealname());   // 制表
            dmMoldPojo.setListerid(loginUser.getUserid());    // 制表id  
            dmMoldPojo.setTenantid(loginUser.getTenantid());   //租户id
            dmMoldPojo.setModifydate(new Date());   //修改时间
//            dmMoldPojo.setAssessor(""); // 审核员
//            dmMoldPojo.setAssessorid(""); // 审核员id
//            dmMoldPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.dmMoldService.update(dmMoldPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除模具管理", notes="删除模具管理", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Mold.Delete")
    public R<Integer> delete(String key) {
    try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmMoldService.delete(key, loginUser.getTenantid()));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Mold.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        DmMoldPojo dmMoldPojo = this.dmMoldService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(dmMoldPojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

