package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmSpareinventoryEntity;
import inks.service.std.eam.domain.pojo.DmSpareinventoryPojo;
import inks.service.std.eam.mapper.DmSpareinventoryMapper;
import inks.service.std.eam.service.DmSpareinventoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 备件库存(DmSpareinventory)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 10:10:17
 */
@Service("dmSpareinventoryService")
public class DmSpareinventoryServiceImpl implements DmSpareinventoryService {
    @Resource
    private DmSpareinventoryMapper dmSpareinventoryMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmSpareinventoryPojo getEntity(String key, String tid) {
        return this.dmSpareinventoryMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmSpareinventoryPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmSpareinventoryPojo> lst = dmSpareinventoryMapper.getPageList(queryParam);
            PageInfo<DmSpareinventoryPojo> pageInfo = new PageInfo<DmSpareinventoryPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmSpareinventoryPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmSpareinventoryPojo insert(DmSpareinventoryPojo dmSpareinventoryPojo) {
        //初始化NULL字段
        if (dmSpareinventoryPojo.getStoreid() == null) dmSpareinventoryPojo.setStoreid("");
        if (dmSpareinventoryPojo.getSpareid() == null) dmSpareinventoryPojo.setSpareid("");
        if (dmSpareinventoryPojo.getQuantity() == null) dmSpareinventoryPojo.setQuantity(0D);
        if (dmSpareinventoryPojo.getAmount() == null) dmSpareinventoryPojo.setAmount(0D);
        if (dmSpareinventoryPojo.getLocation() == null) dmSpareinventoryPojo.setLocation("");
        if (dmSpareinventoryPojo.getBatchno() == null) dmSpareinventoryPojo.setBatchno("");
        if (dmSpareinventoryPojo.getExpirydate() == null) dmSpareinventoryPojo.setExpirydate(new Date());
        if (dmSpareinventoryPojo.getEnduid() == null) dmSpareinventoryPojo.setEnduid("");
        if (dmSpareinventoryPojo.getEndinuid() == null) dmSpareinventoryPojo.setEndinuid("");
        if (dmSpareinventoryPojo.getEndindate() == null) dmSpareinventoryPojo.setEndindate(new Date());
        if (dmSpareinventoryPojo.getEndoutuid() == null) dmSpareinventoryPojo.setEndoutuid("");
        if (dmSpareinventoryPojo.getEndoutdate() == null) dmSpareinventoryPojo.setEndoutdate(new Date());
        if (dmSpareinventoryPojo.getCustom1() == null) dmSpareinventoryPojo.setCustom1("");
        if (dmSpareinventoryPojo.getCustom2() == null) dmSpareinventoryPojo.setCustom2("");
        if (dmSpareinventoryPojo.getCustom3() == null) dmSpareinventoryPojo.setCustom3("");
        if (dmSpareinventoryPojo.getCustom4() == null) dmSpareinventoryPojo.setCustom4("");
        if (dmSpareinventoryPojo.getCustom5() == null) dmSpareinventoryPojo.setCustom5("");
        if (dmSpareinventoryPojo.getCustom6() == null) dmSpareinventoryPojo.setCustom6("");
        if (dmSpareinventoryPojo.getCustom7() == null) dmSpareinventoryPojo.setCustom7("");
        if (dmSpareinventoryPojo.getCustom8() == null) dmSpareinventoryPojo.setCustom8("");
        if (dmSpareinventoryPojo.getLister() == null) dmSpareinventoryPojo.setLister("");
        if (dmSpareinventoryPojo.getListerid() == null) dmSpareinventoryPojo.setListerid("");
        if (dmSpareinventoryPojo.getCreateby() == null) dmSpareinventoryPojo.setCreateby("");
        if (dmSpareinventoryPojo.getCreatebyid() == null) dmSpareinventoryPojo.setCreatebyid("");
        if (dmSpareinventoryPojo.getCreatedate() == null) dmSpareinventoryPojo.setCreatedate(new Date());
        if (dmSpareinventoryPojo.getModifydate() == null) dmSpareinventoryPojo.setModifydate(new Date());
        if (dmSpareinventoryPojo.getTenantid() == null) dmSpareinventoryPojo.setTenantid("");
        if (dmSpareinventoryPojo.getRevision() == null) dmSpareinventoryPojo.setRevision(0);
        DmSpareinventoryEntity dmSpareinventoryEntity = new DmSpareinventoryEntity();
        BeanUtils.copyProperties(dmSpareinventoryPojo, dmSpareinventoryEntity);
        //生成雪花id
        dmSpareinventoryEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmSpareinventoryEntity.setRevision(1);  //乐观锁
        this.dmSpareinventoryMapper.insert(dmSpareinventoryEntity);
        return this.getEntity(dmSpareinventoryEntity.getId(), dmSpareinventoryEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmSpareinventoryPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmSpareinventoryPojo update(DmSpareinventoryPojo dmSpareinventoryPojo) {
        DmSpareinventoryEntity dmSpareinventoryEntity = new DmSpareinventoryEntity();
        BeanUtils.copyProperties(dmSpareinventoryPojo, dmSpareinventoryEntity);
        this.dmSpareinventoryMapper.update(dmSpareinventoryEntity);
        return this.getEntity(dmSpareinventoryEntity.getId(), dmSpareinventoryEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmSpareinventoryMapper.delete(key, tid);
    }

    @Override
    public DmSpareinventoryPojo getEntityBySpareid(String spareid, String location, String batchno, String tid) {
        return this.dmSpareinventoryMapper.getEntityBySpareid(spareid, location, batchno, tid);
    }
}
