package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmPatrolpathitemEntity;
import inks.service.std.eam.domain.pojo.DmPatrolpathitemPojo;
import inks.service.std.eam.mapper.DmPatrolpathitemMapper;
import inks.service.std.eam.service.DmPatrolpathitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 路线巡检点(DmPatrolpathitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 13:18:00
 */
@Service("dmPatrolpathitemService")
public class DmPatrolpathitemServiceImpl implements DmPatrolpathitemService {
    @Resource
    private DmPatrolpathitemMapper dmPatrolpathitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmPatrolpathitemPojo getEntity(String key, String tid) {
        return this.dmPatrolpathitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolpathitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolpathitemPojo> lst = dmPatrolpathitemMapper.getPageList(queryParam);
            PageInfo<DmPatrolpathitemPojo> pageInfo = new PageInfo<DmPatrolpathitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DmPatrolpathitemPojo> getList(String Pid, String tid) {
        try {
            List<DmPatrolpathitemPojo> lst = dmPatrolpathitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param dmPatrolpathitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolpathitemPojo insert(DmPatrolpathitemPojo dmPatrolpathitemPojo) {
        //初始化item的NULL
        DmPatrolpathitemPojo itempojo = this.clearNull(dmPatrolpathitemPojo);
        DmPatrolpathitemEntity dmPatrolpathitemEntity = new DmPatrolpathitemEntity();
        BeanUtils.copyProperties(itempojo, dmPatrolpathitemEntity);
        //生成雪花id
        dmPatrolpathitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmPatrolpathitemEntity.setRevision(1);  //乐观锁
        this.dmPatrolpathitemMapper.insert(dmPatrolpathitemEntity);
        return this.getEntity(dmPatrolpathitemEntity.getId(), dmPatrolpathitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmPatrolpathitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolpathitemPojo update(DmPatrolpathitemPojo dmPatrolpathitemPojo) {
        DmPatrolpathitemEntity dmPatrolpathitemEntity = new DmPatrolpathitemEntity();
        BeanUtils.copyProperties(dmPatrolpathitemPojo, dmPatrolpathitemEntity);
        this.dmPatrolpathitemMapper.update(dmPatrolpathitemEntity);
        return this.getEntity(dmPatrolpathitemEntity.getId(), dmPatrolpathitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmPatrolpathitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param dmPatrolpathitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolpathitemPojo clearNull(DmPatrolpathitemPojo dmPatrolpathitemPojo) {
        //初始化NULL字段
        if (dmPatrolpathitemPojo.getPid() == null) dmPatrolpathitemPojo.setPid("");
        if (dmPatrolpathitemPojo.getPointid() == null) dmPatrolpathitemPojo.setPointid("");
        if (dmPatrolpathitemPojo.getPointcode() == null) dmPatrolpathitemPojo.setPointcode("");
        if (dmPatrolpathitemPojo.getPointname() == null) dmPatrolpathitemPojo.setPointname("");
        if (dmPatrolpathitemPojo.getStdid() == null) dmPatrolpathitemPojo.setStdid("");
        if (dmPatrolpathitemPojo.getStdcode() == null) dmPatrolpathitemPojo.setStdcode("");
        if (dmPatrolpathitemPojo.getStdname() == null) dmPatrolpathitemPojo.setStdname("");
        if (dmPatrolpathitemPojo.getRownum() == null) dmPatrolpathitemPojo.setRownum(0);
        if (dmPatrolpathitemPojo.getRemark() == null) dmPatrolpathitemPojo.setRemark("");
        if (dmPatrolpathitemPojo.getCustom1() == null) dmPatrolpathitemPojo.setCustom1("");
        if (dmPatrolpathitemPojo.getCustom2() == null) dmPatrolpathitemPojo.setCustom2("");
        if (dmPatrolpathitemPojo.getCustom3() == null) dmPatrolpathitemPojo.setCustom3("");
        if (dmPatrolpathitemPojo.getCustom4() == null) dmPatrolpathitemPojo.setCustom4("");
        if (dmPatrolpathitemPojo.getCustom5() == null) dmPatrolpathitemPojo.setCustom5("");
        if (dmPatrolpathitemPojo.getCustom6() == null) dmPatrolpathitemPojo.setCustom6("");
        if (dmPatrolpathitemPojo.getCustom7() == null) dmPatrolpathitemPojo.setCustom7("");
        if (dmPatrolpathitemPojo.getCustom8() == null) dmPatrolpathitemPojo.setCustom8("");
        if (dmPatrolpathitemPojo.getCustom9() == null) dmPatrolpathitemPojo.setCustom9("");
        if (dmPatrolpathitemPojo.getCustom10() == null) dmPatrolpathitemPojo.setCustom10("");
        if (dmPatrolpathitemPojo.getTenantid() == null) dmPatrolpathitemPojo.setTenantid("");
        if (dmPatrolpathitemPojo.getRevision() == null) dmPatrolpathitemPojo.setRevision(0);
        return dmPatrolpathitemPojo;
    }
}
