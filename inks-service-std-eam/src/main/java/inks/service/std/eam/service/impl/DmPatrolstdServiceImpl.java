package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmPatrolstdEntity;
import inks.service.std.eam.domain.DmPatrolstditemEntity;
import inks.service.std.eam.domain.pojo.DmPatrolstdPojo;
import inks.service.std.eam.domain.pojo.DmPatrolstditemPojo;
import inks.service.std.eam.domain.pojo.DmPatrolstditemdetailPojo;
import inks.service.std.eam.mapper.DmPatrolstdMapper;
import inks.service.std.eam.mapper.DmPatrolstditemMapper;
import inks.service.std.eam.service.DmPatrolstdService;
import inks.service.std.eam.service.DmPatrolstditemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 检查标准(DmPatrolstd)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 13:17:11
 */
@Service("dmPatrolstdService")
public class DmPatrolstdServiceImpl implements DmPatrolstdService {
    @Resource
    private DmPatrolstdMapper dmPatrolstdMapper;

    @Resource
    private DmPatrolstditemMapper dmPatrolstditemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private DmPatrolstditemService dmPatrolstditemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmPatrolstdPojo getEntity(String key, String tid) {
        return this.dmPatrolstdMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolstditemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolstditemdetailPojo> lst = dmPatrolstdMapper.getPageList(queryParam);
            PageInfo<DmPatrolstditemdetailPojo> pageInfo = new PageInfo<DmPatrolstditemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmPatrolstdPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            DmPatrolstdPojo dmPatrolstdPojo = this.dmPatrolstdMapper.getEntity(key, tid);
            //读取子表
            dmPatrolstdPojo.setItem(dmPatrolstditemMapper.getList(dmPatrolstdPojo.getId(), dmPatrolstdPojo.getTenantid()));
            return dmPatrolstdPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolstdPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolstdPojo> lst = dmPatrolstdMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(dmPatrolstditemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<DmPatrolstdPojo> pageInfo = new PageInfo<DmPatrolstdPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolstdPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolstdPojo> lst = dmPatrolstdMapper.getPageTh(queryParam);
            PageInfo<DmPatrolstdPojo> pageInfo = new PageInfo<DmPatrolstdPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmPatrolstdPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmPatrolstdPojo insert(DmPatrolstdPojo dmPatrolstdPojo) {
//初始化NULL字段
        if (dmPatrolstdPojo.getStdgroupid() == null) dmPatrolstdPojo.setStdgroupid("");
        if (dmPatrolstdPojo.getStdcode() == null) dmPatrolstdPojo.setStdcode("");
        if (dmPatrolstdPojo.getStdname() == null) dmPatrolstdPojo.setStdname("");
        if (dmPatrolstdPojo.getOperator() == null) dmPatrolstdPojo.setOperator("");
        if (dmPatrolstdPojo.getSummary() == null) dmPatrolstdPojo.setSummary("");
        if (dmPatrolstdPojo.getListerid() == null) dmPatrolstdPojo.setListerid("");
        if (dmPatrolstdPojo.getLister() == null) dmPatrolstdPojo.setLister("");
        if (dmPatrolstdPojo.getCreatebyid() == null) dmPatrolstdPojo.setCreatebyid("");
        if (dmPatrolstdPojo.getCreatedate() == null) dmPatrolstdPojo.setCreatedate(new Date());
        if (dmPatrolstdPojo.getCreateby() == null) dmPatrolstdPojo.setCreateby("");
        if (dmPatrolstdPojo.getModifydate() == null) dmPatrolstdPojo.setModifydate(new Date());
        if (dmPatrolstdPojo.getAssessor() == null) dmPatrolstdPojo.setAssessor("");
        if (dmPatrolstdPojo.getAssessorid() == null) dmPatrolstdPojo.setAssessorid("");
        if (dmPatrolstdPojo.getAssessdate() == null) dmPatrolstdPojo.setAssessdate(new Date());
        if (dmPatrolstdPojo.getEnabledmark() == null) dmPatrolstdPojo.setEnabledmark(0);
        if (dmPatrolstdPojo.getDeletemark() == null) dmPatrolstdPojo.setDeletemark(0);
        if (dmPatrolstdPojo.getDeletelister() == null) dmPatrolstdPojo.setDeletelister("");
        if (dmPatrolstdPojo.getDeletedate() == null) dmPatrolstdPojo.setDeletedate(new Date());
        if (dmPatrolstdPojo.getCustom1() == null) dmPatrolstdPojo.setCustom1("");
        if (dmPatrolstdPojo.getCustom2() == null) dmPatrolstdPojo.setCustom2("");
        if (dmPatrolstdPojo.getCustom3() == null) dmPatrolstdPojo.setCustom3("");
        if (dmPatrolstdPojo.getCustom4() == null) dmPatrolstdPojo.setCustom4("");
        if (dmPatrolstdPojo.getCustom5() == null) dmPatrolstdPojo.setCustom5("");
        if (dmPatrolstdPojo.getCustom6() == null) dmPatrolstdPojo.setCustom6("");
        if (dmPatrolstdPojo.getCustom7() == null) dmPatrolstdPojo.setCustom7("");
        if (dmPatrolstdPojo.getCustom8() == null) dmPatrolstdPojo.setCustom8("");
        if (dmPatrolstdPojo.getCustom9() == null) dmPatrolstdPojo.setCustom9("");
        if (dmPatrolstdPojo.getCustom10() == null) dmPatrolstdPojo.setCustom10("");
        if (dmPatrolstdPojo.getTenantid() == null) dmPatrolstdPojo.setTenantid("");
        if (dmPatrolstdPojo.getRevision() == null) dmPatrolstdPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmPatrolstdEntity dmPatrolstdEntity = new DmPatrolstdEntity();
        BeanUtils.copyProperties(dmPatrolstdPojo, dmPatrolstdEntity);

        //设置id和新建日期
        dmPatrolstdEntity.setId(id);
        dmPatrolstdEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmPatrolstdMapper.insert(dmPatrolstdEntity);
        //Item子表处理
        List<DmPatrolstditemPojo> lst = dmPatrolstdPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                DmPatrolstditemPojo itemPojo = this.dmPatrolstditemService.clearNull(lst.get(i));
                DmPatrolstditemEntity dmPatrolstditemEntity = new DmPatrolstditemEntity();
                BeanUtils.copyProperties(itemPojo, dmPatrolstditemEntity);
                //设置id和Pid
                dmPatrolstditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmPatrolstditemEntity.setPid(id);
                dmPatrolstditemEntity.setTenantid(dmPatrolstdPojo.getTenantid());
                dmPatrolstditemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmPatrolstditemMapper.insert(dmPatrolstditemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmPatrolstdEntity.getId(), dmPatrolstdEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmPatrolstdPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmPatrolstdPojo update(DmPatrolstdPojo dmPatrolstdPojo) {
        //主表更改
        DmPatrolstdEntity dmPatrolstdEntity = new DmPatrolstdEntity();
        BeanUtils.copyProperties(dmPatrolstdPojo, dmPatrolstdEntity);
        this.dmPatrolstdMapper.update(dmPatrolstdEntity);
        if (dmPatrolstdPojo.getItem() != null) {
            //Item子表处理
            List<DmPatrolstditemPojo> lst = dmPatrolstdPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = dmPatrolstdMapper.getDelItemIds(dmPatrolstdPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.dmPatrolstditemMapper.delete(lstDelIds.get(i), dmPatrolstdEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    DmPatrolstditemEntity dmPatrolstditemEntity = new DmPatrolstditemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        DmPatrolstditemPojo itemPojo = this.dmPatrolstditemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, dmPatrolstditemEntity);
                        //设置id和Pid
                        dmPatrolstditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        dmPatrolstditemEntity.setPid(dmPatrolstdEntity.getId());  // 主表 id
                        dmPatrolstditemEntity.setTenantid(dmPatrolstdPojo.getTenantid());   // 租户id
                        dmPatrolstditemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.dmPatrolstditemMapper.insert(dmPatrolstditemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), dmPatrolstditemEntity);
                        dmPatrolstditemEntity.setTenantid(dmPatrolstdPojo.getTenantid());
                        this.dmPatrolstditemMapper.update(dmPatrolstditemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmPatrolstdEntity.getId(), dmPatrolstdEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        DmPatrolstdPojo dmPatrolstdPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<DmPatrolstditemPojo> lst = dmPatrolstdPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.dmPatrolstditemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.dmPatrolstdMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param dmPatrolstdPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmPatrolstdPojo approval(DmPatrolstdPojo dmPatrolstdPojo) {
        //主表更改
        DmPatrolstdEntity dmPatrolstdEntity = new DmPatrolstdEntity();
        BeanUtils.copyProperties(dmPatrolstdPojo, dmPatrolstdEntity);
        this.dmPatrolstdMapper.approval(dmPatrolstdEntity);
        //返回Bill实例
        return this.getBillEntity(dmPatrolstdEntity.getId(), dmPatrolstdEntity.getTenantid());
    }

}
