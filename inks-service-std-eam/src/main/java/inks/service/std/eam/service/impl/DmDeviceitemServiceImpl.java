package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmDeviceitemEntity;
import inks.service.std.eam.domain.pojo.DmDeviceitemPojo;
import inks.service.std.eam.mapper.DmDeviceitemMapper;
import inks.service.std.eam.service.DmDeviceitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备参数(DmDeviceitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-06 12:59:33
 */
@Service("dmDeviceitemService")
public class DmDeviceitemServiceImpl implements DmDeviceitemService {
    @Resource
    private DmDeviceitemMapper dmDeviceitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmDeviceitemPojo getEntity(String key, String tid) {
        return this.dmDeviceitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmDeviceitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmDeviceitemPojo> lst = dmDeviceitemMapper.getPageList(queryParam);
            PageInfo<DmDeviceitemPojo> pageInfo = new PageInfo<DmDeviceitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DmDeviceitemPojo> getList(String Pid, String tid) {
        try {
            List<DmDeviceitemPojo> lst = dmDeviceitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param dmDeviceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmDeviceitemPojo insert(DmDeviceitemPojo dmDeviceitemPojo) {
        //初始化item的NULL
        DmDeviceitemPojo itempojo = this.clearNull(dmDeviceitemPojo);
        DmDeviceitemEntity dmDeviceitemEntity = new DmDeviceitemEntity();
        BeanUtils.copyProperties(itempojo, dmDeviceitemEntity);
        //生成雪花id
        dmDeviceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmDeviceitemEntity.setRevision(1);  //乐观锁
        this.dmDeviceitemMapper.insert(dmDeviceitemEntity);
        return this.getEntity(dmDeviceitemEntity.getId(), dmDeviceitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmDeviceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmDeviceitemPojo update(DmDeviceitemPojo dmDeviceitemPojo) {
        DmDeviceitemEntity dmDeviceitemEntity = new DmDeviceitemEntity();
        BeanUtils.copyProperties(dmDeviceitemPojo, dmDeviceitemEntity);
        this.dmDeviceitemMapper.update(dmDeviceitemEntity);
        return this.getEntity(dmDeviceitemEntity.getId(), dmDeviceitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmDeviceitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param dmDeviceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmDeviceitemPojo clearNull(DmDeviceitemPojo dmDeviceitemPojo) {
        //初始化NULL字段
        if (dmDeviceitemPojo.getPid() == null) dmDeviceitemPojo.setPid("");
        if (dmDeviceitemPojo.getItemcode() == null) dmDeviceitemPojo.setItemcode("");
        if (dmDeviceitemPojo.getItemname() == null) dmDeviceitemPojo.setItemname("");
        if (dmDeviceitemPojo.getItemspec() == null) dmDeviceitemPojo.setItemspec("");
        if (dmDeviceitemPojo.getInspectmark() == null) dmDeviceitemPojo.setInspectmark(0);
        if (dmDeviceitemPojo.getRownum() == null) dmDeviceitemPojo.setRownum(0);
        if (dmDeviceitemPojo.getRemark() == null) dmDeviceitemPojo.setRemark("");
        if (dmDeviceitemPojo.getCustom1() == null) dmDeviceitemPojo.setCustom1("");
        if (dmDeviceitemPojo.getCustom2() == null) dmDeviceitemPojo.setCustom2("");
        if (dmDeviceitemPojo.getCustom3() == null) dmDeviceitemPojo.setCustom3("");
        if (dmDeviceitemPojo.getCustom4() == null) dmDeviceitemPojo.setCustom4("");
        if (dmDeviceitemPojo.getCustom5() == null) dmDeviceitemPojo.setCustom5("");
        if (dmDeviceitemPojo.getCustom6() == null) dmDeviceitemPojo.setCustom6("");
        if (dmDeviceitemPojo.getCustom7() == null) dmDeviceitemPojo.setCustom7("");
        if (dmDeviceitemPojo.getCustom8() == null) dmDeviceitemPojo.setCustom8("");
        if (dmDeviceitemPojo.getTenantid() == null) dmDeviceitemPojo.setTenantid("");
        if (dmDeviceitemPojo.getRevision() == null) dmDeviceitemPojo.setRevision(0);
        return dmDeviceitemPojo;
    }
}
