package inks.service.std.eam.iotrule.alarm;

import java.util.*;

public class AlarmService {
    private Map<String, List<AlarmRule>> deviceRules = new HashMap<>();
    private Map<String, List<Alarm>> activeAlarms = new HashMap<>();
    
    // 注册设备的告警规则
    public void registerDeviceRule(String deviceId, AlarmRule rule) {
        deviceRules.computeIfAbsent(deviceId, k -> new ArrayList<>()).add(rule);
    }
    
    // 处理遥测数据
    public List<Alarm> processDeviceTelemetry(String deviceId, Map<String, Object> telemetry) {
        List<Alarm> newAlarms = new ArrayList<>();
        
        // 获取设备的告警规则
        List<AlarmRule> rules = deviceRules.getOrDefault(deviceId, Collections.emptyList());
        
        // 评估每条规则
        for (AlarmRule rule : rules) {
            // 简单规则直接评估
            if (rule.getType().equals("SIMPLE")) {
                if (rule.evaluate(telemetry)) {
                    Alarm alarm = new Alarm(rule.getName(), deviceId, "WARNING", rule);
                    alarm.setDetails("触发值: " + telemetry.toString());
                    activeAlarms.computeIfAbsent(deviceId, k -> new ArrayList<>()).add(alarm);
                    newAlarms.add(alarm);
                }
            }
            // 持续型和重复型规则需要更复杂的状态管理，此处简化处理
        }
        
        return newAlarms;
    }
    
    // 获取设备当前活动告警
    public List<Alarm> getActiveAlarms(String deviceId) {
        return activeAlarms.getOrDefault(deviceId, Collections.emptyList());
    }
    
    // 清除告警
    public void clearAlarm(String deviceId, String alarmId) {
        List<Alarm> alarms = activeAlarms.getOrDefault(deviceId, Collections.emptyList());
        for (Alarm alarm : alarms) {
            if (alarm.getId().equals(alarmId)) {
                alarm.clear();
                break;
            }
        }
    }
    
    // 确认告警
    public void acknowledgeAlarm(String deviceId, String alarmId) {
        List<Alarm> alarms = activeAlarms.getOrDefault(deviceId, Collections.emptyList());
        for (Alarm alarm : alarms) {
            if (alarm.getId().equals(alarmId)) {
                alarm.acknowledge();
                break;
            }
        }
    }
}

