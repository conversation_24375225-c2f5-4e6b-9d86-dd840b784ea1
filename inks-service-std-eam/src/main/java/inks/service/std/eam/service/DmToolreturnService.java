package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolreturnPojo;
import inks.service.std.eam.domain.pojo.DmToolreturnitemdetailPojo;
import com.github.pagehelper.PageInfo;

/**
 * 工装具归还主表(DmToolreturn)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-19 10:16:37
 */
public interface DmToolreturnService {

    DmToolreturnPojo getEntity(String key,String tid);

    PageInfo<DmToolreturnitemdetailPojo> getPageList(QueryParam queryParam);

    DmToolreturnPojo getBillEntity(String key,String tid);

    PageInfo<DmToolreturnPojo> getBillList(QueryParam queryParam);

    PageInfo<DmToolreturnPojo> getPageTh(QueryParam queryParam);

    DmToolreturnPojo insert(DmToolreturnPojo dmToolreturnPojo);

    DmToolreturnPojo update(DmToolreturnPojo dmToolreturnpojo);

    int delete(String key,String tid);

}
