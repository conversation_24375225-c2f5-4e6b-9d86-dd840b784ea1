package inks.service.std.eam.controller;

import inks.service.std.eam.utils.PrintColor;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.apache.commons.lang3.StringUtils.*;

@RestController
public class ems_MailController {
    @Resource
    private JavaMailSender javaMailSender;

    @Value("${spring.mail.username}")
    private String fromEmail;
    @Value("${spring.mail.toEmail}")
    private String toEmail;

    //将【需要提醒保养计划】的登List<Map<String, Object>>转换为html格式,便于预览
    public static String listMapToHtmlDevPlan(List<Map<String, String>> unFinishDevPlanList) {
        // 生成每个admin用户登录情况的HTML表格
        StringBuilder tableContent = new StringBuilder();
        tableContent.append("<table style='border-collapse: collapse; width: 100%;'>");
        tableContent.append("<tr style='background-color: #f2f2f2;'>" +
                "<th style='padding: 8px; text-align: left;'>保养计划时间</th>" +
                "<th style='padding: 8px; text-align: left;'>保养计划名称</th>" +
                "<th style='padding: 8px; text-align: left;'>未完成设备</th>");
        boolean isAlternateRow = false;
        for (Map<String, String> unFinishDevPlan : unFinishDevPlanList) {
            tableContent.append("<tr style='background-color: ").append(isAlternateRow ? "#ffffff;" : "#f9f9f9;").append("'>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(unFinishDevPlan.get("plandate")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(unFinishDevPlan.get("planname")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(unFinishDevPlan.get("unfinishdevnames")).append("</td>");
            tableContent.append("</tr>");
            isAlternateRow = !isAlternateRow; // 切换交替行的背景颜色
        }
        tableContent.append("</table>");
        //今日日期字符串 2023-09-08
        String todayString = java.time.LocalDate.now().toString();

        // 构造完整的邮件内容
        String content =
                "<h2>" + todayString + " 设备保养计划提醒</h2>" +
                        "<div style='margin-top: 10px;'>" +
                        tableContent +
                        "</div>";
        return content;
    }

    /**
     * @Description 原始简陋版发邮件
     * <AUTHOR>
     * @param[1] to 收件人邮箱
     * @param[2] subject 主题
     * @param[3] content 内容
     * @time 2023/5/24 22:08
     */
    public void sendEmail(String to, String subject, String content) throws MessagingException {
        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom("<EMAIL>");
        helper.setTo(to);
        helper.setSubject(subject);
        //String content111 = "<h1>一号html</h1><h3>3号html</h3>";
        helper.setText(content, true); // true表示使用HTML格式
        javaMailSender.send(message);
    }

    /**
     * @param emailCC 包含多个邮箱地址的字符串，使用逗号分隔(第一个邮箱才是收件人!之后为抄送人)  格式为: 收件人邮箱,抄送人邮箱1,抄送人邮箱2
     * @param subject 主题
     * @param content 内容
     * @throws MessagingException 发送邮件时可能抛出的异常
     * @Description 发邮件 附加抄送人
     */
    public void sendEmailCC(String emailCC, String subject, String content) throws MessagingException {
        String[] emailAddresses = emailCC.split(",");
        if (emailAddresses.length < 1) {
            throw new IllegalArgumentException("邮箱地址不能为空");
        }

        String to = emailAddresses[0].trim(); // 第一个邮箱地址设置为收件人
        // 设置抄送人（之后的邮箱都为抄送人）
        InternetAddress[] ccAddresses = new InternetAddress[emailAddresses.length - 1];
        for (int i = 1; i < emailAddresses.length; i++) {
            ccAddresses[i - 1] = new InternetAddress(emailAddresses[i].trim());
        }

        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom(fromEmail);
        helper.setTo(to);
        helper.setSubject(subject);
        helper.setText(content, true); // true表示使用HTML格式
        // 添加抄送人
        if (StringUtils.isNotBlank(ccAddresses.toString())) {
            PrintColor.zi("cc:" + ccAddresses.toString());
            helper.setCc(ccAddresses);
        }
        javaMailSender.send(message);
    }

    /**
     * @param toEmail  收件人邮箱
     * @param ccEmails 抄送人邮箱，多个邮箱使用逗号分隔
     * @param subject  主题
     * @param content  内容
     * @throws MessagingException 发送邮件时可能抛出的异常
     * @Description 发邮件 附加抄送人
     */
    public void sendEmailCC(String toEmail, String ccEmails, String subject, String content) throws MessagingException {
        try {
            if (isBlank(toEmail)) throw new IllegalArgumentException("收件人邮箱不能为空");

            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(toEmail);
            helper.setSubject(subject);
            helper.setText(content, true); // true表示使用HTML格式
            // 添加抄送人
            if (isNotBlank(ccEmails)) {
                String[] ccEmailArray = ccEmails.split(",");
                InternetAddress[] ccAddresses = Arrays.stream(ccEmailArray)
                        .map(String::trim)
                        .map(email -> {
                            try {
                                return new InternetAddress(email);
                            } catch (AddressException e) {   //抄送人邮件地址不合法时可以跳过 列如：100@@qq.com,**********可以发送给**********
                                e.printStackTrace();
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .toArray(InternetAddress[]::new);
                if (ccAddresses.length > 0) {
                    helper.setCc(ccAddresses);
                }
            }
            javaMailSender.send(message);
        } catch (MailException e) {
            e.printStackTrace();
            throw new MessagingException("发送邮件失败");
        }
    }

    @ApiOperation(value = " sendEmailTest", notes = "", produces = "application/json")
    @RequestMapping(value = "/sendEmailTest", method = RequestMethod.POST)
    public String sendEmailTest() throws MessagingException {
//        LoginUser loginUser = tokenService.getLoginUser();
        String emailString = "<EMAIL>,<EMAIL>,<EMAIL>";
        // 获取系统参数里的[收件人&抄送人字符串]
//        String emails = wkWipnoteMapper.getEmails("system.email.people", loginUser.getTenantid());
        String[] emailAddresses = emailString.split(",");
        if (emailAddresses.length < 1) {
            throw new IllegalArgumentException("邮箱地址不能为空");
        }

        String to = emailAddresses[0].trim(); // 第一个邮箱地址设置为收件人
        // 设置抄送人（之后的邮箱都为抄送人）
        InternetAddress[] ccAddresses = new InternetAddress[emailAddresses.length - 1];
        for (int i = 1; i < emailAddresses.length; i++) {
            ccAddresses[i - 1] = new InternetAddress(emailAddresses[i].trim());
        }

        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom("<EMAIL>");
        helper.setTo(to);
        helper.setSubject("subject");
        helper.setText("content", true); // true表示使用HTML格式


        // 添加抄送人
        if (StringUtils.isNotBlank(ccAddresses.toString())) {
            PrintColor.zi("cc:" + ccAddresses.toString());
            helper.setCc(ccAddresses);
        }
        javaMailSender.send(message);
        return "success";
    }


}
