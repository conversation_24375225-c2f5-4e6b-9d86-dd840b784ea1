package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmPatrolrecPojo;
import inks.service.std.eam.domain.pojo.DmPatrolrecitemdetailPojo;

/**
 * 巡检记录(DmPatrolrec)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 13:09:30
 */
public interface DmPatrolrecService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolrecPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolrecitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolrecPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolrecPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolrecPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmPatrolrecPojo 实例对象
     * @return 实例对象
     */
    DmPatrolrecPojo insert(DmPatrolrecPojo dmPatrolrecPojo);

    /**
     * 修改数据
     *
     * @param dmPatrolrecpojo 实例对象
     * @return 实例对象
     */
    DmPatrolrecPojo update(DmPatrolrecPojo dmPatrolrecpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param dmPatrolrecPojo 实例对象
     * @return 实例对象
     */
    DmPatrolrecPojo approval(DmPatrolrecPojo dmPatrolrecPojo);
}
