package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmSpareaccessitemPojo;

import java.util.List;

/**
 * 出入库项目(DmSpareaccessitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 10:09:41
 */
public interface DmSpareaccessitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmSpareaccessitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmSpareaccessitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmSpareaccessitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param dmSpareaccessitemPojo 实例对象
     * @return 实例对象
     */
    DmSpareaccessitemPojo insert(DmSpareaccessitemPojo dmSpareaccessitemPojo);

    /**
     * 修改数据
     *
     * @param dmSpareaccessitempojo 实例对象
     * @return 实例对象
     */
    DmSpareaccessitemPojo update(DmSpareaccessitemPojo dmSpareaccessitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param dmSpareaccessitempojo 实例对象
     * @return 实例对象
     */
    DmSpareaccessitemPojo clearNull(DmSpareaccessitemPojo dmSpareaccessitempojo);
}
