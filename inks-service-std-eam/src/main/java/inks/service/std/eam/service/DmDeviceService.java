package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmDevicePojo;
import inks.service.std.eam.domain.pojo.DmDeviceitemdetailPojo;

/**
 * 设备台账(DmDevice)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-06 12:59:49
 */
public interface DmDeviceService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmDevicePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmDeviceitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmDevicePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmDevicePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmDevicePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmDevicePojo 实例对象
     * @return 实例对象
     */
    DmDevicePojo insert(DmDevicePojo dmDevicePojo);

    /**
     * 修改数据
     *
     * @param dmDevicepojo 实例对象
     * @return 实例对象
     */
    DmDevicePojo update(DmDevicePojo dmDevicepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    String getMaxDevCode(String tenantid);
}
