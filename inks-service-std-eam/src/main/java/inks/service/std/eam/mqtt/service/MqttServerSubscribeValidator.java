package inks.service.std.eam.mqtt.service;

import org.dromara.mica.mqtt.codec.MqttQoS;
import org.dromara.mica.mqtt.core.server.auth.IMqttServerSubscribeValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.tio.core.ChannelContext;

/**
 * MQTT服务器订阅校验器
 *
 * <AUTHOR>
 */
@Service
public class MqttServerSubscribeValidator implements IMqttServerSubscribeValidator {

    @Autowired
    private MqttStatsService mqttStatsService;

    @Override
    public boolean isValid(ChannelContext context, String clientId, String userName, MqttQoS mqttQoS) {
        // 订阅权限控制
        System.out.println("MQTT Subscribe Validation - ClientId: " + clientId + ", QoS: " + mqttQoS);

        // 这里可以实现更复杂的权限控制逻辑
        // 例如：某些用户只能使用特定的QoS级别

        // 示例：限制QoS级别
        if (mqttQoS == MqttQoS.QOS2 && !"admin".equals(userName)) {
            System.out.println("QoS 2 subscribe denied for non-admin client: " + clientId);
            return false;
        }

        // 示例：管理员拥有所有权限
        if ("admin".equals(userName)) {
            return true;
        }

        // 默认允许普通订阅
        return true;
    }

}
