package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolborrowitemPojo;
import inks.service.std.eam.domain.DmToolborrowitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具借还子表-工装具(DmToolborrowitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-13 09:52:08
 */
 @Mapper
public interface DmToolborrowitemMapper {

    DmToolborrowitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolborrowitemPojo> getPageList(QueryParam queryParam);

    List<DmToolborrowitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(DmToolborrowitemEntity dmToolborrowitemEntity);

    int update(DmToolborrowitemEntity dmToolborrowitemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

}

