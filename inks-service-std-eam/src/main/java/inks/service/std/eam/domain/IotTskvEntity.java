package inks.service.std.eam.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 时间序列遥测数据表(IotTskv)实体类
 *
 * <AUTHOR>
 * @since 2025-04-25 14:44:54
 */
public class IotTskvEntity implements Serializable {
    private static final long serialVersionUID = 112598587113138059L;
     // 记录ID
    private String id;
     // 实体ID
    private String entityid;
     // 键ID（对应key_dictionary.key_id）
    private Integer keyid;
     // 键
    private String keyv;
     // 时间戳
    private Long ts;
     // 布尔值
    private Integer boolv;
     // 字符串值
    private String strv;
     // 长整型值
    private Long longv;
     // 双精度浮点值
    private Double dblv;
     // JSON格式数据
    private String jsonv;
     // 日期
    private Date createdate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;

// 记录ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 实体ID
    public String getEntityid() {
        return entityid;
    }
    
    public void setEntityid(String entityid) {
        this.entityid = entityid;
    }
        
// 键ID（对应key_dictionary.key_id）
    public Integer getKeyid() {
        return keyid;
    }
    
    public void setKeyid(Integer keyid) {
        this.keyid = keyid;
    }
        
// 键
    public String getKeyv() {
        return keyv;
    }
    
    public void setKeyv(String keyv) {
        this.keyv = keyv;
    }
        
// 时间戳
    public Long getTs() {
        return ts;
    }
    
    public void setTs(Long ts) {
        this.ts = ts;
    }
        
// 布尔值
    public Integer getBoolv() {
        return boolv;
    }
    
    public void setBoolv(Integer boolv) {
        this.boolv = boolv;
    }
        
// 字符串值
    public String getStrv() {
        return strv;
    }
    
    public void setStrv(String strv) {
        this.strv = strv;
    }
        
// 长整型值
    public Long getLongv() {
        return longv;
    }
    
    public void setLongv(Long longv) {
        this.longv = longv;
    }
        
// 双精度浮点值
    public Double getDblv() {
        return dblv;
    }
    
    public void setDblv(Double dblv) {
        this.dblv = dblv;
    }
        
// JSON格式数据
    public String getJsonv() {
        return jsonv;
    }
    
    public void setJsonv(String jsonv) {
        this.jsonv = jsonv;
    }
        
// 日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        

}

