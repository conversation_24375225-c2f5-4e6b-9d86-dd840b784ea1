package inks.service.std.eam.config.old;

import com.alibaba.fastjson.JSONArray;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class JsonPathUtilTest {

    private final String json = "{\n" +
            "  \"msg\": {\n" +
            "    \"info\": {\n" +
            "      \"code\": \"ds01\",\n" +
            "      \"date\": \"2024-06-08 13:42:00\",\n" +
            "      \"content\": [\n" +
            "        {\n" +
            "          \"key\": \"js\",\n" +
            "          \"value\": 677\n" +
            "        },\n" +
            "        {\n" +
            "          \"key\": \"java\",\n" +
            "          \"value\": 123\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    \"msgtype\": \"info\",\n" +
            "    \"Trigger\": 0,\n" +
            "    \"sn\": \"2873406DCF72\"\n" +
            "  },\n" +
            "  \"modulecode\": \"saiot\"\n" +
            "}";

    @Test
    public void testSimplePath() {
        System.out.println("code: " + JsonPathUtil.getByJsonPath(json, "msg.info.code"));
        System.out.println("key[1]: " + JsonPathUtil.getByJsonPath(json, "msg.info.content[1].key"));
        System.out.println("value[1]: " + JsonPathUtil.getByJsonPath(json, "msg.info.content[1].value"));

        assertEquals("ds01", JsonPathUtil.getByJsonPath(json, "msg.info.code"));
        assertEquals("java", JsonPathUtil.getByJsonPath(json, "msg.info.content[1].key"));
        assertEquals(123, JsonPathUtil.getByJsonPath(json, "msg.info.content[1].value"));
    }

    @Test
    public void testArrayAll() {
        Object result = JsonPathUtil.getByJsonPath(json, "msg.info.content[].key");
        System.out.println("content[].key: " + result);
        assertEquals(JSONArray.class, result.getClass());
        JSONArray keys = (JSONArray) result;
        assertEquals(2, keys.size());
        assertEquals("js", keys.get(0));
        assertEquals("java", keys.get(1));
    }

    @Test
    public void testInvalidPath() {
        Object val1 = JsonPathUtil.getByJsonPath(json, "msg.info.notexist");
        Object val2 = JsonPathUtil.getByJsonPath(json, "msg.info.content[5].key");
        System.out.println("invalid 1: " + val1);
        System.out.println("invalid 2: " + val2);

        assertNull(val1);
        assertNull(val2);
    }

    @Test
    public void testNestedPath() {
        String nestedJson = "{ \"a\": { \"b\": [ { \"c\": [1, 2] } ] } }";
        Object value = JsonPathUtil.getByJsonPath(nestedJson, "a.b[0].c[1]");
        System.out.println("nested: " + value);
        assertEquals(2, value);
    }

    @Test
    public void testEmptyArray() {
        String emptyJson = "{ \"items\": [] }";
        Object firstItem = JsonPathUtil.getByJsonPath(emptyJson, "items[0]");
        Object array = JsonPathUtil.getByJsonPath(emptyJson, "items[]");
        System.out.println("items[0]: " + firstItem);
        System.out.println("items[]: " + array);
        assertNull(firstItem);
        assertEquals(JSONArray.class, array.getClass());
    }
}
