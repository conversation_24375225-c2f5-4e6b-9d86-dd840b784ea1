package inks.service.std.eam.service;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotAttributekvPojo;
import com.github.pagehelper.PageInfo;

/**
 * 实体属性存储表(Iot_AttributeKv)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-07 10:06:38
 */
public interface IotAttributekvService {

    IotAttributekvPojo getEntity(String key,String tid);

    PageInfo<IotAttributekvPojo> getPageList(QueryParam queryParam);

    IotAttributekvPojo insert(IotAttributekvPojo iotAttributekvPojo);

    IotAttributekvPojo update(IotAttributekvPojo iotAttributekvpojo);

    int delete(String key,String tid);

    int batchLatest(JSONObject mqttJSON);
}
