package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotTskvPojo;
import inks.service.std.eam.domain.IotTskvEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 时间序列遥测数据表(Iot_TsKv)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:52
 */
@Mapper
public interface IotTskvMapper {

    IotTskvPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotTskvPojo> getPageList(QueryParam queryParam);

    int insert(IotTskvEntity iotTskvEntity);

    int update(IotTskvEntity iotTskvEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    int batchInsert(List<IotTskvPojo> list);
}

