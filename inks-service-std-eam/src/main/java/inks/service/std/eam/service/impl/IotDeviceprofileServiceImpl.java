package inks.service.std.eam.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.eam.domain.pojo.IotDeviceprofilePojo;
import inks.service.std.eam.domain.IotDeviceprofileEntity;
import inks.service.std.eam.mapper.IotDeviceprofileMapper;
import inks.service.std.eam.service.IotDeviceprofileService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 设备配置模板表(IotDeviceprofile)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:26
 */
@Service("iotDeviceprofileService")
public class IotDeviceprofileServiceImpl implements IotDeviceprofileService {
    @Resource
    private IotDeviceprofileMapper iotDeviceprofileMapper;

    @Override
    public IotDeviceprofilePojo getEntity(String key, String tid) {
        return this.iotDeviceprofileMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotDeviceprofilePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotDeviceprofilePojo> lst = iotDeviceprofileMapper.getPageList(queryParam);
            PageInfo<IotDeviceprofilePojo> pageInfo = new PageInfo<IotDeviceprofilePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotDeviceprofilePojo insert(IotDeviceprofilePojo iotDeviceprofilePojo) {
        //初始化NULL字段
        cleanNull(iotDeviceprofilePojo);
        IotDeviceprofileEntity iotDeviceprofileEntity = new IotDeviceprofileEntity(); 
        BeanUtils.copyProperties(iotDeviceprofilePojo,iotDeviceprofileEntity);
          //生成雪花id
          iotDeviceprofileEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotDeviceprofileEntity.setRevision(1);  //乐观锁
          this.iotDeviceprofileMapper.insert(iotDeviceprofileEntity);
        return this.getEntity(iotDeviceprofileEntity.getId(),iotDeviceprofileEntity.getTenantid());
    }


    @Override
    public IotDeviceprofilePojo update(IotDeviceprofilePojo iotDeviceprofilePojo) {
        IotDeviceprofileEntity iotDeviceprofileEntity = new IotDeviceprofileEntity(); 
        BeanUtils.copyProperties(iotDeviceprofilePojo,iotDeviceprofileEntity);
        this.iotDeviceprofileMapper.update(iotDeviceprofileEntity);
        return this.getEntity(iotDeviceprofileEntity.getId(),iotDeviceprofileEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotDeviceprofileMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotDeviceprofilePojo iotDeviceprofilePojo) {
        if(iotDeviceprofilePojo.getProfname()==null) iotDeviceprofilePojo.setProfname("");
        if(iotDeviceprofilePojo.getProftype()==null) iotDeviceprofilePojo.setProftype("");
        if(iotDeviceprofilePojo.getImage()==null) iotDeviceprofilePojo.setImage("");
        if(iotDeviceprofilePojo.getTransporttype()==null) iotDeviceprofilePojo.setTransporttype("");
        if(iotDeviceprofilePojo.getProvisiontype()==null) iotDeviceprofilePojo.setProvisiontype("");
        if(iotDeviceprofilePojo.getProfiledata()==null) iotDeviceprofilePojo.setProfiledata("{}");
        if(iotDeviceprofilePojo.getDescription()==null) iotDeviceprofilePojo.setDescription("");
        if(iotDeviceprofilePojo.getIsdefault()==null) iotDeviceprofilePojo.setIsdefault(0);
        if(iotDeviceprofilePojo.getFirmwareid()==null) iotDeviceprofilePojo.setFirmwareid("");
        if(iotDeviceprofilePojo.getSoftwareid()==null) iotDeviceprofilePojo.setSoftwareid("");
        if(iotDeviceprofilePojo.getDefaultrulechainid()==null) iotDeviceprofilePojo.setDefaultrulechainid("");
        if(iotDeviceprofilePojo.getDefaultdashboardid()==null) iotDeviceprofilePojo.setDefaultdashboardid("");
        if(iotDeviceprofilePojo.getDefaultqueuename()==null) iotDeviceprofilePojo.setDefaultqueuename("");
        if(iotDeviceprofilePojo.getProvisiondevicekey()==null) iotDeviceprofilePojo.setProvisiondevicekey("");
        if(iotDeviceprofilePojo.getDefaultedgerulechainid()==null) iotDeviceprofilePojo.setDefaultedgerulechainid("");
        if(iotDeviceprofilePojo.getExternalid()==null) iotDeviceprofilePojo.setExternalid("");
        if(iotDeviceprofilePojo.getRemark()==null) iotDeviceprofilePojo.setRemark("");
        if(iotDeviceprofilePojo.getRownum()==null) iotDeviceprofilePojo.setRownum(0);
        if(iotDeviceprofilePojo.getCreateby()==null) iotDeviceprofilePojo.setCreateby("");
        if(iotDeviceprofilePojo.getCreatebyid()==null) iotDeviceprofilePojo.setCreatebyid("");
        if(iotDeviceprofilePojo.getCreatedate()==null) iotDeviceprofilePojo.setCreatedate(new Date());
        if(iotDeviceprofilePojo.getLister()==null) iotDeviceprofilePojo.setLister("");
        if(iotDeviceprofilePojo.getListerid()==null) iotDeviceprofilePojo.setListerid("");
        if(iotDeviceprofilePojo.getModifydate()==null) iotDeviceprofilePojo.setModifydate(new Date());
        if(iotDeviceprofilePojo.getCustom1()==null) iotDeviceprofilePojo.setCustom1("");
        if(iotDeviceprofilePojo.getCustom2()==null) iotDeviceprofilePojo.setCustom2("");
        if(iotDeviceprofilePojo.getCustom3()==null) iotDeviceprofilePojo.setCustom3("");
        if(iotDeviceprofilePojo.getCustom4()==null) iotDeviceprofilePojo.setCustom4("");
        if(iotDeviceprofilePojo.getCustom5()==null) iotDeviceprofilePojo.setCustom5("");
        if(iotDeviceprofilePojo.getCustom6()==null) iotDeviceprofilePojo.setCustom6("");
        if(iotDeviceprofilePojo.getCustom7()==null) iotDeviceprofilePojo.setCustom7("");
        if(iotDeviceprofilePojo.getCustom8()==null) iotDeviceprofilePojo.setCustom8("");
        if(iotDeviceprofilePojo.getCustom9()==null) iotDeviceprofilePojo.setCustom9("");
        if(iotDeviceprofilePojo.getCustom10()==null) iotDeviceprofilePojo.setCustom10("");
        if(iotDeviceprofilePojo.getTenantid()==null) iotDeviceprofilePojo.setTenantid("");
        if(iotDeviceprofilePojo.getTenantname()==null) iotDeviceprofilePojo.setTenantname("");
        if(iotDeviceprofilePojo.getRevision()==null) iotDeviceprofilePojo.setRevision(0);
   }

}
