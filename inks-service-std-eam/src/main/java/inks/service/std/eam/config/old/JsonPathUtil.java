package inks.service.std.eam.config.old;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class JsonPathUtil {

    /**
     * 支持 JSON Path 表达式获取值，支持：
     * - 点分隔属性访问（msg.info.code）
     * - 数组索引访问（msg.info.content[0].key）
     * - 全数组访问（msg.info.content[].key）
     *
     * @param jsonStr JSON 字符串
     * @param path    路径表达式
     * @return 对应的值（Object 类型）
     */
    public static Object getByJsonPath(String jsonStr, String path) {
        return getByJsonPathInternal(JSON.parse(jsonStr), path);
    }

    private static Object getByJsonPathInternal(Object current, String path) {
        if (current == null || path == null || path.isEmpty()) {
            return null;
        }

        String[] parts = path.split("\\.", 2); // 每次处理一个部分
        String part = parts[0];
        String remainingPath = parts.length > 1 ? parts[1] : null;

        if (part.contains("[")) {
            return handleArrayAccess(current, part, remainingPath);
        } else {
            return handleSimpleProperty(current, part, remainingPath);
        }
    }

    /**
     * 处理数组访问（如 content[0] 或 content[]）
     */
    private static Object handleArrayAccess(Object current, String part, String remainingPath) {
        int leftBracket = part.indexOf('[');
        int rightBracket = part.indexOf(']');
        String key = part.substring(0, leftBracket);
        String indexStr = part.substring(leftBracket + 1, rightBracket);

        if (current instanceof JSONObject) {
            Object arrayObj = ((JSONObject) current).get(key);
            if (arrayObj instanceof JSONArray) {
                JSONArray array = (JSONArray) arrayObj;
                if (indexStr.isEmpty()) { // 全数组访问（content[]）
                    if (remainingPath == null) {
                        return array;
                    } else {
                        JSONArray result = new JSONArray();
                        for (int i = 0; i < array.size(); i++) {
                            Object elem = array.get(i);
                            result.add(getByJsonPathInternal(elem, remainingPath));
                        }
                        return result;
                    }
                } else { // 单个索引访问（content[0]）
                    try {
                        int index = Integer.parseInt(indexStr);
                        if (index >= 0 && index < array.size()) {
                            return getByJsonPathInternal(array.get(index), remainingPath);
                        }
                    } catch (NumberFormatException e) {
                        // 索引格式错误
                    }
                }
            }
        }
        return null;
    }

    /**
     * 处理普通属性访问（如 msg.info）
     */
    private static Object handleSimpleProperty(Object current, String part, String remainingPath) {
        if (current instanceof JSONObject) {
            Object value = ((JSONObject) current).get(part);
            if (remainingPath == null) {
                return value;
            } else {
                return getByJsonPathInternal(value, remainingPath);
            }
        }
        return null;
    }
}