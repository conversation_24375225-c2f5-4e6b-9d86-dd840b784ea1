package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmToolinspectEntity;
import inks.service.std.eam.domain.DmToolinspectitemEntity;
import inks.service.std.eam.domain.pojo.DmToolinspectPojo;
import inks.service.std.eam.domain.pojo.DmToolinspectitemPojo;
import inks.service.std.eam.domain.pojo.DmToolinspectitemdetailPojo;
import inks.service.std.eam.mapper.DmToolinspectMapper;
import inks.service.std.eam.mapper.DmToolinspectitemMapper;
import inks.service.std.eam.service.DmToolinspectService;
import inks.service.std.eam.service.DmToolinspectitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 工装具校验主表(DmToolinspect)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-19 10:17:23
 */
@Service("dmToolinspectService")
public class DmToolinspectServiceImpl implements DmToolinspectService {
    @Resource
    private DmToolinspectMapper dmToolinspectMapper;
    
    @Resource
    private DmToolinspectitemMapper dmToolinspectitemMapper;
    

    @Resource
    private DmToolinspectitemService dmToolinspectitemService;

    @Override
    public DmToolinspectPojo getEntity(String key, String tid) {
        return this.dmToolinspectMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<DmToolinspectitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolinspectitemdetailPojo> lst = dmToolinspectMapper.getPageList(queryParam);
            PageInfo<DmToolinspectitemdetailPojo> pageInfo = new PageInfo<DmToolinspectitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public DmToolinspectPojo getBillEntity(String key, String tid) {
       try {
           //读取主表
           DmToolinspectPojo dmToolinspectPojo = this.dmToolinspectMapper.getEntity(key,tid);
           //读取子表
           dmToolinspectPojo.setItem(dmToolinspectitemMapper.getList(dmToolinspectPojo.getId(),tid));
           return dmToolinspectPojo;
       }catch (Exception e){
          throw new BaseBusinessException(e.getMessage());
       } 
    }


    @Override
    public PageInfo<DmToolinspectPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolinspectPojo> lst = dmToolinspectMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(DmToolinspectPojo item : lst){
                item.setItem(dmToolinspectitemMapper.getList(item.getId(), tid));
            }
            PageInfo<DmToolinspectPojo> pageInfo = new PageInfo<DmToolinspectPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<DmToolinspectPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolinspectPojo> lst = dmToolinspectMapper.getPageTh(queryParam);
            PageInfo<DmToolinspectPojo> pageInfo = new PageInfo<DmToolinspectPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public DmToolinspectPojo insert(DmToolinspectPojo dmToolinspectPojo) {
    String tid = dmToolinspectPojo.getTenantid();
        //初始化NULL字段
        cleanNull(dmToolinspectPojo);
         //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmToolinspectEntity dmToolinspectEntity = new DmToolinspectEntity(); 
        BeanUtils.copyProperties(dmToolinspectPojo,dmToolinspectEntity);
      
        //设置id和新建日期
        dmToolinspectEntity.setId(id);
        dmToolinspectEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmToolinspectMapper.insert(dmToolinspectEntity);
        //Item子表处理
        List<DmToolinspectitemPojo> lst = dmToolinspectPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(DmToolinspectitemPojo item : lst){
               //初始化item的NULL
               DmToolinspectitemPojo itemPojo =this.dmToolinspectitemService.clearNull(item);
               DmToolinspectitemEntity dmToolinspectitemEntity = new DmToolinspectitemEntity(); 
               BeanUtils.copyProperties(itemPojo,dmToolinspectitemEntity);
               //设置id和Pid
               dmToolinspectitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               dmToolinspectitemEntity.setPid(id);
               dmToolinspectitemEntity.setTenantid(tid);
               dmToolinspectitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.dmToolinspectitemMapper.insert(dmToolinspectitemEntity);
            }
            
        } 
        //返回Bill实例
        return this.getBillEntity(dmToolinspectEntity.getId(),tid);
    }


    @Override
    @Transactional
    public DmToolinspectPojo update(DmToolinspectPojo dmToolinspectPojo) {
        String tid = dmToolinspectPojo.getTenantid();
        //主表更改
        DmToolinspectEntity dmToolinspectEntity = new DmToolinspectEntity(); 
        BeanUtils.copyProperties(dmToolinspectPojo,dmToolinspectEntity);
        this.dmToolinspectMapper.update(dmToolinspectEntity);
        if (dmToolinspectPojo.getItem() != null) {
        //Item子表处理
        List<DmToolinspectitemPojo> lst = dmToolinspectPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =dmToolinspectMapper.getDelItemIds(dmToolinspectPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for (String delId : lstDelIds) {
             this.dmToolinspectitemMapper.delete(delId, tid);
            }
        }
        if (lst != null){
            //循环每个item子表
            for(DmToolinspectitemPojo item : lst){
               DmToolinspectitemEntity dmToolinspectitemEntity = new DmToolinspectitemEntity(); 
               if ("".equals(item.getId()) || item.getId() == null){
                //初始化item的NULL
               DmToolinspectitemPojo itemPojo =this.dmToolinspectitemService.clearNull(item);
               BeanUtils.copyProperties(itemPojo,dmToolinspectitemEntity);
               //设置id和Pid
               dmToolinspectitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               dmToolinspectitemEntity.setPid(dmToolinspectEntity.getId());  // 主表 id
               dmToolinspectitemEntity.setTenantid(tid);   // 租户id
               dmToolinspectitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.dmToolinspectitemMapper.insert(dmToolinspectitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(item,dmToolinspectitemEntity);       
                dmToolinspectitemEntity.setTenantid(tid);        
               this.dmToolinspectitemMapper.update(dmToolinspectitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(dmToolinspectEntity.getId(),tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
       DmToolinspectPojo dmToolinspectPojo =  this.getBillEntity(key,tid);
        //Item子表处理
        List<DmToolinspectitemPojo> lst = dmToolinspectPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(DmToolinspectitemPojo item : lst){
              this.dmToolinspectitemMapper.delete(item.getId(),tid);
            }
        }        
        return this.dmToolinspectMapper.delete(key,tid) ;
    }
    

    

    private static void cleanNull(DmToolinspectPojo dmToolinspectPojo) {
        if(dmToolinspectPojo.getRefno()==null) dmToolinspectPojo.setRefno("");
        if(dmToolinspectPojo.getBilldate()==null) dmToolinspectPojo.setBilldate(new Date());
        if(dmToolinspectPojo.getBilltype()==null) dmToolinspectPojo.setBilltype("");
        if(dmToolinspectPojo.getBilltitle()==null) dmToolinspectPojo.setBilltitle("");
        if(dmToolinspectPojo.getInspecttype()==null) dmToolinspectPojo.setInspecttype(0);
        if(dmToolinspectPojo.getOperatorid()==null) dmToolinspectPojo.setOperatorid("");
        if(dmToolinspectPojo.getOperator()==null) dmToolinspectPojo.setOperator("");
        if(dmToolinspectPojo.getItemcount()==null) dmToolinspectPojo.setItemcount(0);
        if(dmToolinspectPojo.getFinishcount()==null) dmToolinspectPojo.setFinishcount(0);
        if(dmToolinspectPojo.getAgency()==null) dmToolinspectPojo.setAgency("");
        if(dmToolinspectPojo.getSentdate()==null) dmToolinspectPojo.setSentdate(new Date());
        if(dmToolinspectPojo.getSummary()==null) dmToolinspectPojo.setSummary("");
        if(dmToolinspectPojo.getCreateby()==null) dmToolinspectPojo.setCreateby("");
        if(dmToolinspectPojo.getCreatebyid()==null) dmToolinspectPojo.setCreatebyid("");
        if(dmToolinspectPojo.getCreatedate()==null) dmToolinspectPojo.setCreatedate(new Date());
        if(dmToolinspectPojo.getLister()==null) dmToolinspectPojo.setLister("");
        if(dmToolinspectPojo.getListerid()==null) dmToolinspectPojo.setListerid("");
        if(dmToolinspectPojo.getModifydate()==null) dmToolinspectPojo.setModifydate(new Date());
        if(dmToolinspectPojo.getCustom1()==null) dmToolinspectPojo.setCustom1("");
        if(dmToolinspectPojo.getCustom2()==null) dmToolinspectPojo.setCustom2("");
        if(dmToolinspectPojo.getCustom3()==null) dmToolinspectPojo.setCustom3("");
        if(dmToolinspectPojo.getCustom4()==null) dmToolinspectPojo.setCustom4("");
        if(dmToolinspectPojo.getCustom5()==null) dmToolinspectPojo.setCustom5("");
        if(dmToolinspectPojo.getCustom6()==null) dmToolinspectPojo.setCustom6("");
        if(dmToolinspectPojo.getCustom7()==null) dmToolinspectPojo.setCustom7("");
        if(dmToolinspectPojo.getCustom8()==null) dmToolinspectPojo.setCustom8("");
        if(dmToolinspectPojo.getCustom9()==null) dmToolinspectPojo.setCustom9("");
        if(dmToolinspectPojo.getCustom10()==null) dmToolinspectPojo.setCustom10("");
        if(dmToolinspectPojo.getTenantid()==null) dmToolinspectPojo.setTenantid("");
        if(dmToolinspectPojo.getTenantname()==null) dmToolinspectPojo.setTenantname("");
        if(dmToolinspectPojo.getRevision()==null) dmToolinspectPojo.setRevision(0);
   }

}
