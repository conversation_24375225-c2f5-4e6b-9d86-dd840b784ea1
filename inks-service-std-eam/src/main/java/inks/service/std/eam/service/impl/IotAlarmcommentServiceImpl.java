package inks.service.std.eam.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.eam.domain.pojo.IotAlarmcommentPojo;
import inks.service.std.eam.domain.IotAlarmcommentEntity;
import inks.service.std.eam.mapper.IotAlarmcommentMapper;
import inks.service.std.eam.service.IotAlarmcommentService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
import com.alibaba.fastjson.JSONObject;
/**
 * 告警评论表(IotAlarmcomment)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24 15:27:08
 */
@Service("iotAlarmcommentService")
public class IotAlarmcommentServiceImpl implements IotAlarmcommentService {
    @Resource
    private IotAlarmcommentMapper iotAlarmcommentMapper;

    @Override
    public IotAlarmcommentPojo getEntity(String key, String tid) {
        return this.iotAlarmcommentMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotAlarmcommentPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotAlarmcommentPojo> lst = iotAlarmcommentMapper.getPageList(queryParam);
            PageInfo<IotAlarmcommentPojo> pageInfo = new PageInfo<IotAlarmcommentPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotAlarmcommentPojo insert(IotAlarmcommentPojo iotAlarmcommentPojo) {
        //初始化NULL字段
        cleanNull(iotAlarmcommentPojo);
        IotAlarmcommentEntity iotAlarmcommentEntity = new IotAlarmcommentEntity(); 
        BeanUtils.copyProperties(iotAlarmcommentPojo,iotAlarmcommentEntity);
          //生成雪花id
          iotAlarmcommentEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotAlarmcommentEntity.setRevision(1);  //乐观锁
          this.iotAlarmcommentMapper.insert(iotAlarmcommentEntity);
        return this.getEntity(iotAlarmcommentEntity.getId(),iotAlarmcommentEntity.getTenantid());
    }


    @Override
    public IotAlarmcommentPojo update(IotAlarmcommentPojo iotAlarmcommentPojo) {
        IotAlarmcommentEntity iotAlarmcommentEntity = new IotAlarmcommentEntity(); 
        BeanUtils.copyProperties(iotAlarmcommentPojo,iotAlarmcommentEntity);
        this.iotAlarmcommentMapper.update(iotAlarmcommentEntity);
        return this.getEntity(iotAlarmcommentEntity.getId(),iotAlarmcommentEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotAlarmcommentMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotAlarmcommentPojo iotAlarmcommentPojo) {
        if(iotAlarmcommentPojo.getAlarmid()==null) iotAlarmcommentPojo.setAlarmid("");
        if(iotAlarmcommentPojo.getUserid()==null) iotAlarmcommentPojo.setUserid("");
        if(iotAlarmcommentPojo.getType()==null) iotAlarmcommentPojo.setType("");
        if(iotAlarmcommentPojo.getComment()==null) iotAlarmcommentPojo.setComment("");
        if(iotAlarmcommentPojo.getRemark()==null) iotAlarmcommentPojo.setRemark("");
        if(iotAlarmcommentPojo.getRownum()==null) iotAlarmcommentPojo.setRownum(0);
        if(iotAlarmcommentPojo.getCreateby()==null) iotAlarmcommentPojo.setCreateby("");
        if(iotAlarmcommentPojo.getCreatebyid()==null) iotAlarmcommentPojo.setCreatebyid("");
        if(iotAlarmcommentPojo.getCreatedate()==null) iotAlarmcommentPojo.setCreatedate(new Date());
        if(iotAlarmcommentPojo.getLister()==null) iotAlarmcommentPojo.setLister("");
        if(iotAlarmcommentPojo.getListerid()==null) iotAlarmcommentPojo.setListerid("");
        if(iotAlarmcommentPojo.getModifydate()==null) iotAlarmcommentPojo.setModifydate(new Date());
        if(iotAlarmcommentPojo.getCustom1()==null) iotAlarmcommentPojo.setCustom1("");
        if(iotAlarmcommentPojo.getCustom2()==null) iotAlarmcommentPojo.setCustom2("");
        if(iotAlarmcommentPojo.getCustom3()==null) iotAlarmcommentPojo.setCustom3("");
        if(iotAlarmcommentPojo.getCustom4()==null) iotAlarmcommentPojo.setCustom4("");
        if(iotAlarmcommentPojo.getCustom5()==null) iotAlarmcommentPojo.setCustom5("");
        if(iotAlarmcommentPojo.getCustom6()==null) iotAlarmcommentPojo.setCustom6("");
        if(iotAlarmcommentPojo.getCustom7()==null) iotAlarmcommentPojo.setCustom7("");
        if(iotAlarmcommentPojo.getCustom8()==null) iotAlarmcommentPojo.setCustom8("");
        if(iotAlarmcommentPojo.getCustom9()==null) iotAlarmcommentPojo.setCustom9("");
        if(iotAlarmcommentPojo.getCustom10()==null) iotAlarmcommentPojo.setCustom10("");
        if(iotAlarmcommentPojo.getTenantid()==null) iotAlarmcommentPojo.setTenantid("");
        if(iotAlarmcommentPojo.getTenantname()==null) iotAlarmcommentPojo.setTenantname("");
        if(iotAlarmcommentPojo.getRevision()==null) iotAlarmcommentPojo.setRevision(0);
   }

    @Override
    public List<IotAlarmcommentPojo> getCommentsByAlarmId(String alarmId, String tenantId) {
        return this.iotAlarmcommentMapper.getCommentsByAlarmId(alarmId, tenantId);
    }

    @Override
    public IotAlarmcommentPojo addUserComment(String alarmId, String userId, String userName, String commentText, String tenantId) {
        // 创建用户评论JSON格式
        JSONObject commentJson = new JSONObject();
        commentJson.put("text", commentText);

        // 创建评论对象
        IotAlarmcommentPojo comment = new IotAlarmcommentPojo();
        comment.setAlarmid(alarmId);
        comment.setUserid(userId);
        comment.setType("1"); // 用户评论
        comment.setComment(commentJson.toJSONString());
        comment.setCreateby(userName);
        comment.setCreatebyid(userId);
        comment.setCreatedate(new Date());
        comment.setLister(userName);
        comment.setListerid(userId);
        comment.setModifydate(new Date());
        comment.setTenantid(tenantId);

        return this.insert(comment);
    }

}
