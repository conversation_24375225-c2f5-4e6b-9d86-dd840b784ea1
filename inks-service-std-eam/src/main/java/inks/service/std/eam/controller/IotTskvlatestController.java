package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotTskvlatestPojo;
import inks.service.std.eam.service.IotTskvlatestService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 最新时间序列遥测数据快照表(Iot_TsKvLatest)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:56
 */
//@RestController
//@RequestMapping("iotTskvlatest")
public class IotTskvlatestController {

    @Resource
    private IotTskvlatestService iotTskvlatestService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(IotTskvlatestController.class);


    @ApiOperation(value=" 获取最新时间序列遥测数据快照表详细信息", notes="获取最新时间序列遥测数据快照表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_TsKvLatest.List")
    public R<IotTskvlatestPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotTskvlatestService.getEntity(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_TsKvLatest.List")
    public R<PageInfo<IotTskvlatestPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Iot_TsKvLatest.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.iotTskvlatestService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增最新时间序列遥测数据快照表", notes="新增最新时间序列遥测数据快照表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_TsKvLatest.Add")
    public R<IotTskvlatestPojo> create(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
       IotTskvlatestPojo iotTskvlatestPojo = JSONArray.parseObject(json,IotTskvlatestPojo.class);       
            iotTskvlatestPojo.setCreatedate(new Date());   // 创建时间
            iotTskvlatestPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.iotTskvlatestService.insert(iotTskvlatestPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改最新时间序列遥测数据快照表", notes="修改最新时间序列遥测数据快照表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_TsKvLatest.Edit")
    public R<IotTskvlatestPojo> update(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
         IotTskvlatestPojo iotTskvlatestPojo = JSONArray.parseObject(json,IotTskvlatestPojo.class);
            iotTskvlatestPojo.setTenantid(loginUser.getTenantid());   //租户id
//            iotTskvlatestPojo.setAssessor(""); // 审核员
//            iotTskvlatestPojo.setAssessorid(""); // 审核员id
//            iotTskvlatestPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.iotTskvlatestService.update(iotTskvlatestPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除最新时间序列遥测数据快照表", notes="删除最新时间序列遥测数据快照表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_TsKvLatest.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotTskvlatestService.delete(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_TsKvLatest.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        IotTskvlatestPojo iotTskvlatestPojo = this.iotTskvlatestService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(iotTskvlatestPojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

