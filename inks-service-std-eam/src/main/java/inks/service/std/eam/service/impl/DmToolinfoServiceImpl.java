package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmToolinfoEntity;
import inks.service.std.eam.domain.pojo.DmToolinfoPojo;
import inks.service.std.eam.mapper.DmToolinfoMapper;
import inks.service.std.eam.service.DmToolinfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 工装具基础信息(DmToolinfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-09 16:30:37
 */
@Service("dmToolinfoService")
public class DmToolinfoServiceImpl implements DmToolinfoService {
    @Resource
    private DmToolinfoMapper dmToolinfoMapper;

    @Override
    public DmToolinfoPojo getEntity(String key, String tid) {
        return this.dmToolinfoMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<DmToolinfoPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolinfoPojo> lst = dmToolinfoMapper.getPageList(queryParam);
            PageInfo<DmToolinfoPojo> pageInfo = new PageInfo<DmToolinfoPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public DmToolinfoPojo insert(DmToolinfoPojo dmToolinfoPojo) {
        //初始化NULL字段
        cleanNull(dmToolinfoPojo);
        DmToolinfoEntity dmToolinfoEntity = new DmToolinfoEntity(); 
        BeanUtils.copyProperties(dmToolinfoPojo,dmToolinfoEntity);
          //生成雪花id
          dmToolinfoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          dmToolinfoEntity.setRevision(1);  //乐观锁
          this.dmToolinfoMapper.insert(dmToolinfoEntity);
        return this.getEntity(dmToolinfoEntity.getId(),dmToolinfoEntity.getTenantid());
    }


    @Override
    public DmToolinfoPojo update(DmToolinfoPojo dmToolinfoPojo) {
        DmToolinfoEntity dmToolinfoEntity = new DmToolinfoEntity(); 
        BeanUtils.copyProperties(dmToolinfoPojo,dmToolinfoEntity);
        this.dmToolinfoMapper.update(dmToolinfoEntity);
        return this.getEntity(dmToolinfoEntity.getId(),dmToolinfoEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.dmToolinfoMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(DmToolinfoPojo dmToolinfoPojo) {
        if(dmToolinfoPojo.getToolcode()==null) dmToolinfoPojo.setToolcode("");
        if(dmToolinfoPojo.getToolname()==null) dmToolinfoPojo.setToolname("");
        if(dmToolinfoPojo.getCategoryid()==null) dmToolinfoPojo.setCategoryid("");
        //if(dmToolinfoPojo.getActivatedate()==null) dmToolinfoPojo.setActivatedate(new Date());
        //if(dmToolinfoPojo.getDiscarddate()==null) dmToolinfoPojo.setDiscarddate(new Date());
        if(dmToolinfoPojo.getStorelocation()==null) dmToolinfoPojo.setStorelocation("");
        if(dmToolinfoPojo.getStatus()==null) dmToolinfoPojo.setStatus(0);
        //if(dmToolinfoPojo.getLastmaintdate()==null) dmToolinfoPojo.setLastmaintdate(new Date());
        if(dmToolinfoPojo.getMaintusedqty()==null) dmToolinfoPojo.setMaintusedqty(0);
        if(dmToolinfoPojo.getMaintwarnqty()==null) dmToolinfoPojo.setMaintwarnqty(0);
        if(dmToolinfoPojo.getMaintlimitqty()==null) dmToolinfoPojo.setMaintlimitqty(0);
        if(dmToolinfoPojo.getMaintcount()==null) dmToolinfoPojo.setMaintcount(0);
        if(dmToolinfoPojo.getScrapusedqty()==null) dmToolinfoPojo.setScrapusedqty(0);
        if(dmToolinfoPojo.getScrapwarnqty()==null) dmToolinfoPojo.setScrapwarnqty(0);
        if(dmToolinfoPojo.getScraplimitqty()==null) dmToolinfoPojo.setScraplimitqty(0);
        //if(dmToolinfoPojo.getLastinspdate()==null) dmToolinfoPojo.setLastinspdate(new Date());
        if(dmToolinfoPojo.getInspstatus()==null) dmToolinfoPojo.setInspstatus(0);
        if(dmToolinfoPojo.getInsptype()==null) dmToolinfoPojo.setInsptype(0);
        if(dmToolinfoPojo.getEnabledmark()==null) dmToolinfoPojo.setEnabledmark(0);
        if(dmToolinfoPojo.getPicture()==null) dmToolinfoPojo.setPicture("");
        if(dmToolinfoPojo.getSpec()==null) dmToolinfoPojo.setSpec("");
        if(dmToolinfoPojo.getBrand()==null) dmToolinfoPojo.setBrand("");
        if(dmToolinfoPojo.getGroupid()==null) dmToolinfoPojo.setGroupid("");
        if(dmToolinfoPojo.getPrice()==null) dmToolinfoPojo.setPrice(0D);
        if(dmToolinfoPojo.getRownum()==null) dmToolinfoPojo.setRownum(0);
        if(dmToolinfoPojo.getRemark()==null) dmToolinfoPojo.setRemark("");
        if(dmToolinfoPojo.getCreateby()==null) dmToolinfoPojo.setCreateby("");
        if(dmToolinfoPojo.getCreatebyid()==null) dmToolinfoPojo.setCreatebyid("");
        if(dmToolinfoPojo.getCreatedate()==null) dmToolinfoPojo.setCreatedate(new Date());
        if(dmToolinfoPojo.getLister()==null) dmToolinfoPojo.setLister("");
        if(dmToolinfoPojo.getListerid()==null) dmToolinfoPojo.setListerid("");
        if(dmToolinfoPojo.getModifydate()==null) dmToolinfoPojo.setModifydate(new Date());
        if(dmToolinfoPojo.getCustom1()==null) dmToolinfoPojo.setCustom1("");
        if(dmToolinfoPojo.getCustom2()==null) dmToolinfoPojo.setCustom2("");
        if(dmToolinfoPojo.getCustom3()==null) dmToolinfoPojo.setCustom3("");
        if(dmToolinfoPojo.getCustom4()==null) dmToolinfoPojo.setCustom4("");
        if(dmToolinfoPojo.getCustom5()==null) dmToolinfoPojo.setCustom5("");
        if(dmToolinfoPojo.getCustom6()==null) dmToolinfoPojo.setCustom6("");
        if(dmToolinfoPojo.getCustom7()==null) dmToolinfoPojo.setCustom7("");
        if(dmToolinfoPojo.getCustom8()==null) dmToolinfoPojo.setCustom8("");
        if(dmToolinfoPojo.getCustom9()==null) dmToolinfoPojo.setCustom9("");
        if(dmToolinfoPojo.getCustom10()==null) dmToolinfoPojo.setCustom10("");
        if(dmToolinfoPojo.getDeptid()==null) dmToolinfoPojo.setDeptid("");
        if(dmToolinfoPojo.getTenantid()==null) dmToolinfoPojo.setTenantid("");
        if(dmToolinfoPojo.getTenantname()==null) dmToolinfoPojo.setTenantname("");
        if(dmToolinfoPojo.getRevision()==null) dmToolinfoPojo.setRevision(0);
   }

}
