package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmUpkeepflowEntity;
import inks.service.std.eam.domain.DmUpkeepflowitemEntity;
import inks.service.std.eam.domain.pojo.DmUpkeepflowPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepflowitemPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepflowitemdetailPojo;
import inks.service.std.eam.mapper.DmUpkeepflowMapper;
import inks.service.std.eam.mapper.DmUpkeepflowitemMapper;
import inks.service.std.eam.service.DmUpkeepflowService;
import inks.service.std.eam.service.DmUpkeepflowitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 保养方案(DmUpkeepflow)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-06 14:35:37
 */
@Service("dmUpkeepflowService")
public class DmUpkeepflowServiceImpl implements DmUpkeepflowService {
    @Resource
    private DmUpkeepflowMapper dmUpkeepflowMapper;

    @Resource
    private DmUpkeepflowitemMapper dmUpkeepflowitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private DmUpkeepflowitemService dmUpkeepflowitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmUpkeepflowPojo getEntity(String key, String tid) {
        return this.dmUpkeepflowMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeepflowitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeepflowitemdetailPojo> lst = dmUpkeepflowMapper.getPageList(queryParam);
            PageInfo<DmUpkeepflowitemdetailPojo> pageInfo = new PageInfo<DmUpkeepflowitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmUpkeepflowPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            DmUpkeepflowPojo dmUpkeepflowPojo = this.dmUpkeepflowMapper.getEntity(key, tid);
            //读取子表
            dmUpkeepflowPojo.setItem(dmUpkeepflowitemMapper.getList(dmUpkeepflowPojo.getId(), dmUpkeepflowPojo.getTenantid()));
            return dmUpkeepflowPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeepflowPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeepflowPojo> lst = dmUpkeepflowMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(dmUpkeepflowitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<DmUpkeepflowPojo> pageInfo = new PageInfo<DmUpkeepflowPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeepflowPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeepflowPojo> lst = dmUpkeepflowMapper.getPageTh(queryParam);
            PageInfo<DmUpkeepflowPojo> pageInfo = new PageInfo<DmUpkeepflowPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmUpkeepflowPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmUpkeepflowPojo insert(DmUpkeepflowPojo dmUpkeepflowPojo) {
//初始化NULL字段
        if (dmUpkeepflowPojo.getRefno() == null) dmUpkeepflowPojo.setRefno("");
        if (dmUpkeepflowPojo.getBilldate() == null) dmUpkeepflowPojo.setBilldate(new Date());
        if (dmUpkeepflowPojo.getBilltype() == null) dmUpkeepflowPojo.setBilltype("");
        if (dmUpkeepflowPojo.getBilltitle() == null) dmUpkeepflowPojo.setBilltitle("");
        if (dmUpkeepflowPojo.getOperator() == null) dmUpkeepflowPojo.setOperator("");
        if (dmUpkeepflowPojo.getSummary() == null) dmUpkeepflowPojo.setSummary("");
        if (dmUpkeepflowPojo.getCustom1() == null) dmUpkeepflowPojo.setCustom1("");
        if (dmUpkeepflowPojo.getCustom2() == null) dmUpkeepflowPojo.setCustom2("");
        if (dmUpkeepflowPojo.getCustom3() == null) dmUpkeepflowPojo.setCustom3("");
        if (dmUpkeepflowPojo.getCustom4() == null) dmUpkeepflowPojo.setCustom4("");
        if (dmUpkeepflowPojo.getCustom5() == null) dmUpkeepflowPojo.setCustom5("");
        if (dmUpkeepflowPojo.getCustom6() == null) dmUpkeepflowPojo.setCustom6("");
        if (dmUpkeepflowPojo.getCustom7() == null) dmUpkeepflowPojo.setCustom7("");
        if (dmUpkeepflowPojo.getCustom8() == null) dmUpkeepflowPojo.setCustom8("");
        if (dmUpkeepflowPojo.getLister() == null) dmUpkeepflowPojo.setLister("");
        if (dmUpkeepflowPojo.getListerid() == null) dmUpkeepflowPojo.setListerid("");
        if (dmUpkeepflowPojo.getCreatedate() == null) dmUpkeepflowPojo.setCreatedate(new Date());
        if (dmUpkeepflowPojo.getCreateby() == null) dmUpkeepflowPojo.setCreateby("");
        if (dmUpkeepflowPojo.getCreatebyid() == null) dmUpkeepflowPojo.setCreatebyid("");
        if (dmUpkeepflowPojo.getModifydate() == null) dmUpkeepflowPojo.setModifydate(new Date());
        if (dmUpkeepflowPojo.getAssessorid() == null) dmUpkeepflowPojo.setAssessorid("");
        if (dmUpkeepflowPojo.getAssessor() == null) dmUpkeepflowPojo.setAssessor("");
        if (dmUpkeepflowPojo.getAssessdate() == null) dmUpkeepflowPojo.setAssessdate(new Date());
        if (dmUpkeepflowPojo.getEnabledmark() == null) dmUpkeepflowPojo.setEnabledmark(0);
        if (dmUpkeepflowPojo.getDeletemark() == null) dmUpkeepflowPojo.setDeletemark(0);
        if (dmUpkeepflowPojo.getDeletelister() == null) dmUpkeepflowPojo.setDeletelister("");
        if (dmUpkeepflowPojo.getDeletedate() == null) dmUpkeepflowPojo.setDeletedate(new Date());
        if (dmUpkeepflowPojo.getTenantid() == null) dmUpkeepflowPojo.setTenantid("");
        if (dmUpkeepflowPojo.getRevision() == null) dmUpkeepflowPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmUpkeepflowEntity dmUpkeepflowEntity = new DmUpkeepflowEntity();
        BeanUtils.copyProperties(dmUpkeepflowPojo, dmUpkeepflowEntity);

        //设置id和新建日期
        dmUpkeepflowEntity.setId(id);
        dmUpkeepflowEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmUpkeepflowMapper.insert(dmUpkeepflowEntity);
        //Item子表处理
        List<DmUpkeepflowitemPojo> lst = dmUpkeepflowPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                DmUpkeepflowitemPojo itemPojo = this.dmUpkeepflowitemService.clearNull(lst.get(i));
                DmUpkeepflowitemEntity dmUpkeepflowitemEntity = new DmUpkeepflowitemEntity();
                BeanUtils.copyProperties(itemPojo, dmUpkeepflowitemEntity);
                //设置id和Pid
                dmUpkeepflowitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmUpkeepflowitemEntity.setPid(id);
                dmUpkeepflowitemEntity.setTenantid(dmUpkeepflowPojo.getTenantid());
                dmUpkeepflowitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmUpkeepflowitemMapper.insert(dmUpkeepflowitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmUpkeepflowEntity.getId(), dmUpkeepflowEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmUpkeepflowPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmUpkeepflowPojo update(DmUpkeepflowPojo dmUpkeepflowPojo) {
        //主表更改
        DmUpkeepflowEntity dmUpkeepflowEntity = new DmUpkeepflowEntity();
        BeanUtils.copyProperties(dmUpkeepflowPojo, dmUpkeepflowEntity);
        this.dmUpkeepflowMapper.update(dmUpkeepflowEntity);
        if (dmUpkeepflowPojo.getItem() != null) {
            //Item子表处理
            List<DmUpkeepflowitemPojo> lst = dmUpkeepflowPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = dmUpkeepflowMapper.getDelItemIds(dmUpkeepflowPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.dmUpkeepflowitemMapper.delete(lstDelIds.get(i), dmUpkeepflowEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    DmUpkeepflowitemEntity dmUpkeepflowitemEntity = new DmUpkeepflowitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        DmUpkeepflowitemPojo itemPojo = this.dmUpkeepflowitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, dmUpkeepflowitemEntity);
                        //设置id和Pid
                        dmUpkeepflowitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        dmUpkeepflowitemEntity.setPid(dmUpkeepflowEntity.getId());  // 主表 id
                        dmUpkeepflowitemEntity.setTenantid(dmUpkeepflowPojo.getTenantid());   // 租户id
                        dmUpkeepflowitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.dmUpkeepflowitemMapper.insert(dmUpkeepflowitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), dmUpkeepflowitemEntity);
                        dmUpkeepflowitemEntity.setTenantid(dmUpkeepflowPojo.getTenantid());
                        this.dmUpkeepflowitemMapper.update(dmUpkeepflowitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmUpkeepflowEntity.getId(), dmUpkeepflowEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        DmUpkeepflowPojo dmUpkeepflowPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<DmUpkeepflowitemPojo> lst = dmUpkeepflowPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.dmUpkeepflowitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.dmUpkeepflowMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param dmUpkeepflowPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmUpkeepflowPojo approval(DmUpkeepflowPojo dmUpkeepflowPojo) {
        //主表更改
        DmUpkeepflowEntity dmUpkeepflowEntity = new DmUpkeepflowEntity();
        BeanUtils.copyProperties(dmUpkeepflowPojo, dmUpkeepflowEntity);
        this.dmUpkeepflowMapper.approval(dmUpkeepflowEntity);
        //返回Bill实例
        return this.getBillEntity(dmUpkeepflowEntity.getId(), dmUpkeepflowEntity.getTenantid());
    }

}
