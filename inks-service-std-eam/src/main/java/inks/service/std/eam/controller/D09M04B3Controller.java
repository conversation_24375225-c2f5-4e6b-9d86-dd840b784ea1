package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.DmUpkeeprecordPojo;
import inks.service.std.eam.service.DmUpkeeprecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 保养实施(Dm_UpkeepRecord)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-06 14:35:40
 */
@RestController
@RequestMapping("D09M04B3")
@Api(tags = "D09M04B3:保养实施")
public class D09M04B3Controller extends DmUpkeeprecordController {
    @Resource
    private DmUpkeeprecordService dmUpkeeprecordService;

    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取【日期+计划id下】保养实施单集合", notes = "获取保养实施详细信息", produces = "application/json")
    @RequestMapping(value = "/getListByPlanidAndDate", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepRecord.List")
    public R<List<DmUpkeeprecordPojo>> getListByPlanidAndDate(String planid, String plandate) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmUpkeeprecordService.getListByPlanidAndDate(planid, plandate, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
