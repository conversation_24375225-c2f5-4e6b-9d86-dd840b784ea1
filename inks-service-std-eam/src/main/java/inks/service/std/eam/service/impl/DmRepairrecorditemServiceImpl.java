package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmRepairrecorditemEntity;
import inks.service.std.eam.domain.pojo.DmRepairrecorditemPojo;
import inks.service.std.eam.mapper.DmRepairrecorditemMapper;
import inks.service.std.eam.service.DmRepairrecorditemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 维修配件(DmRepairrecorditem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-06 14:49:34
 */
@Service("dmRepairrecorditemService")
public class DmRepairrecorditemServiceImpl implements DmRepairrecorditemService {
    @Resource
    private DmRepairrecorditemMapper dmRepairrecorditemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmRepairrecorditemPojo getEntity(String key, String tid) {
        return this.dmRepairrecorditemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmRepairrecorditemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmRepairrecorditemPojo> lst = dmRepairrecorditemMapper.getPageList(queryParam);
            PageInfo<DmRepairrecorditemPojo> pageInfo = new PageInfo<DmRepairrecorditemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DmRepairrecorditemPojo> getList(String Pid, String tid) {
        try {
            List<DmRepairrecorditemPojo> lst = dmRepairrecorditemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param dmRepairrecorditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmRepairrecorditemPojo insert(DmRepairrecorditemPojo dmRepairrecorditemPojo) {
        //初始化item的NULL
        DmRepairrecorditemPojo itempojo = this.clearNull(dmRepairrecorditemPojo);
        DmRepairrecorditemEntity dmRepairrecorditemEntity = new DmRepairrecorditemEntity();
        BeanUtils.copyProperties(itempojo, dmRepairrecorditemEntity);
        //生成雪花id
        dmRepairrecorditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmRepairrecorditemEntity.setRevision(1);  //乐观锁
        this.dmRepairrecorditemMapper.insert(dmRepairrecorditemEntity);
        return this.getEntity(dmRepairrecorditemEntity.getId(), dmRepairrecorditemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmRepairrecorditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmRepairrecorditemPojo update(DmRepairrecorditemPojo dmRepairrecorditemPojo) {
        DmRepairrecorditemEntity dmRepairrecorditemEntity = new DmRepairrecorditemEntity();
        BeanUtils.copyProperties(dmRepairrecorditemPojo, dmRepairrecorditemEntity);
        this.dmRepairrecorditemMapper.update(dmRepairrecorditemEntity);
        return this.getEntity(dmRepairrecorditemEntity.getId(), dmRepairrecorditemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmRepairrecorditemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param dmRepairrecorditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmRepairrecorditemPojo clearNull(DmRepairrecorditemPojo dmRepairrecorditemPojo) {
        //初始化NULL字段
        if (dmRepairrecorditemPojo.getPid() == null) dmRepairrecorditemPojo.setPid("");
        if (dmRepairrecorditemPojo.getSpareid() == null) dmRepairrecorditemPojo.setSpareid("");
        if (dmRepairrecorditemPojo.getPlanqty() == null) dmRepairrecorditemPojo.setPlanqty(0D);
        if (dmRepairrecorditemPojo.getQuantity() == null) dmRepairrecorditemPojo.setQuantity(0D);
        if (dmRepairrecorditemPojo.getPrice() == null) dmRepairrecorditemPojo.setPrice(0D);
        if (dmRepairrecorditemPojo.getAmount() == null) dmRepairrecorditemPojo.setAmount(0D);
        if (dmRepairrecorditemPojo.getRemark() == null) dmRepairrecorditemPojo.setRemark("");
        if (dmRepairrecorditemPojo.getRownum() == null) dmRepairrecorditemPojo.setRownum(0);
        if (dmRepairrecorditemPojo.getStoreid() == null) dmRepairrecorditemPojo.setStoreid("");
        if (dmRepairrecorditemPojo.getLocation() == null) dmRepairrecorditemPojo.setLocation("");
        if (dmRepairrecorditemPojo.getBatchno() == null) dmRepairrecorditemPojo.setBatchno("");
        if (dmRepairrecorditemPojo.getExpirydate() == null) dmRepairrecorditemPojo.setExpirydate(new Date());
        if (dmRepairrecorditemPojo.getTaxprice() == null) dmRepairrecorditemPojo.setTaxprice(0D);
        if (dmRepairrecorditemPojo.getTaxamount() == null) dmRepairrecorditemPojo.setTaxamount(0D);
        if (dmRepairrecorditemPojo.getItemtaxrate() == null) dmRepairrecorditemPojo.setItemtaxrate(0D);
        if (dmRepairrecorditemPojo.getCustom1() == null) dmRepairrecorditemPojo.setCustom1("");
        if (dmRepairrecorditemPojo.getCustom2() == null) dmRepairrecorditemPojo.setCustom2("");
        if (dmRepairrecorditemPojo.getCustom3() == null) dmRepairrecorditemPojo.setCustom3("");
        if (dmRepairrecorditemPojo.getCustom4() == null) dmRepairrecorditemPojo.setCustom4("");
        if (dmRepairrecorditemPojo.getCustom5() == null) dmRepairrecorditemPojo.setCustom5("");
        if (dmRepairrecorditemPojo.getCustom6() == null) dmRepairrecorditemPojo.setCustom6("");
        if (dmRepairrecorditemPojo.getCustom7() == null) dmRepairrecorditemPojo.setCustom7("");
        if (dmRepairrecorditemPojo.getCustom8() == null) dmRepairrecorditemPojo.setCustom8("");
        if (dmRepairrecorditemPojo.getTenantid() == null) dmRepairrecorditemPojo.setTenantid("");
        if (dmRepairrecorditemPojo.getRevision() == null) dmRepairrecorditemPojo.setRevision(0);
        return dmRepairrecorditemPojo;
    }
}
