package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmPatrolstditemPojo;

import java.util.List;

/**
 * 检查项目(DmPatrolstditem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 13:10:47
 */
public interface DmPatrolstditemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolstditemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolstditemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmPatrolstditemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param dmPatrolstditemPojo 实例对象
     * @return 实例对象
     */
    DmPatrolstditemPojo insert(DmPatrolstditemPojo dmPatrolstditemPojo);

    /**
     * 修改数据
     *
     * @param dmPatrolstditempojo 实例对象
     * @return 实例对象
     */
    DmPatrolstditemPojo update(DmPatrolstditemPojo dmPatrolstditempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param dmPatrolstditempojo 实例对象
     * @return 实例对象
     */
    DmPatrolstditemPojo clearNull(DmPatrolstditemPojo dmPatrolstditempojo);
}
