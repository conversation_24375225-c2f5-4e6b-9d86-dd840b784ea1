package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotAlarmPojo;
import com.github.pagehelper.PageInfo;

/**
 * 警报(Iot_Alarm)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-14 13:01:05
 */
public interface IotAlarmService {

    IotAlarmPojo getEntity(String key,String tid);

    PageInfo<IotAlarmPojo> getPageList(QueryParam queryParam);

    IotAlarmPojo insert(IotAlarmPojo iotAlarmPojo);

    IotAlarmPojo update(IotAlarmPojo iotAlarmpojo);

    int delete(String key,String tid);

    /**
     * 确认警报
     * @param alarmId 警报ID
     * @param userId 确认用户ID
     * @param userName 确认用户名
     * @param tenantId 租户ID
     * @return 更新后的警报信息
     */
    IotAlarmPojo acknowledgeAlarm(String alarmId, String userId, String userName, String tenantId);

    /**
     * 清除警报
     * @param alarmId 警报ID
     * @param userId 清除用户ID
     * @param userName 清除用户名
     * @param tenantId 租户ID
     * @return 更新后的警报信息
     */
    IotAlarmPojo clearAlarm(String alarmId, String userId, String userName, String tenantId);

    /**
     * 分配警报
     * @param alarmId 警报ID
     * @param assigneeId 被分配用户ID
     * @param assigneeName 被分配用户名
     * @param operatorId 操作用户ID
     * @param operatorName 操作用户名
     * @param tenantId 租户ID
     * @return 更新后的警报信息
     */
    IotAlarmPojo assignAlarm(String alarmId, String assigneeId, String assigneeName, String operatorId, String operatorName, String tenantId);

    /**
     * 取消确认警报
     * @param alarmId 警报ID
     * @param userId 操作用户ID
     * @param userName 操作用户名
     * @param tenantId 租户ID
     * @return 更新后的警报信息
     */
    IotAlarmPojo unacknowledgeAlarm(String alarmId, String userId, String userName, String tenantId);
}
