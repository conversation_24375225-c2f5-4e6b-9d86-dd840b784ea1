package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolcategoryPojo;
import com.github.pagehelper.PageInfo;

/**
 * 工装具类别表(Dm_ToolCategory)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-09 14:05:55
 */
public interface DmToolcategoryService {

    DmToolcategoryPojo getEntity(String key,String tid);

    PageInfo<DmToolcategoryPojo> getPageList(QueryParam queryParam);

    DmToolcategoryPojo insert(DmToolcategoryPojo dmToolcategoryPojo);

    DmToolcategoryPojo update(DmToolcategoryPojo dmToolcategorypojo);

    int delete(String key,String tid);
}
