package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmPatrolpathEntity;
import inks.service.std.eam.domain.DmPatrolpathitemEntity;
import inks.service.std.eam.domain.pojo.DmPatrolpathPojo;
import inks.service.std.eam.domain.pojo.DmPatrolpathitemPojo;
import inks.service.std.eam.domain.pojo.DmPatrolpathitemdetailPojo;
import inks.service.std.eam.mapper.DmPatrolpathMapper;
import inks.service.std.eam.mapper.DmPatrolpathitemMapper;
import inks.service.std.eam.service.DmPatrolpathService;
import inks.service.std.eam.service.DmPatrolpathitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 巡检路线(DmPatrolpath)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 13:17:12
 */
@Service("dmPatrolpathService")
public class DmPatrolpathServiceImpl implements DmPatrolpathService {
    @Resource
    private DmPatrolpathMapper dmPatrolpathMapper;

    @Resource
    private DmPatrolpathitemMapper dmPatrolpathitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private DmPatrolpathitemService dmPatrolpathitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmPatrolpathPojo getEntity(String key, String tid) {
        return this.dmPatrolpathMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolpathitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolpathitemdetailPojo> lst = dmPatrolpathMapper.getPageList(queryParam);
            PageInfo<DmPatrolpathitemdetailPojo> pageInfo = new PageInfo<DmPatrolpathitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmPatrolpathPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            DmPatrolpathPojo dmPatrolpathPojo = this.dmPatrolpathMapper.getEntity(key, tid);
            //读取子表
            dmPatrolpathPojo.setItem(dmPatrolpathitemMapper.getList(dmPatrolpathPojo.getId(), dmPatrolpathPojo.getTenantid()));
            return dmPatrolpathPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolpathPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolpathPojo> lst = dmPatrolpathMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(dmPatrolpathitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<DmPatrolpathPojo> pageInfo = new PageInfo<DmPatrolpathPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolpathPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolpathPojo> lst = dmPatrolpathMapper.getPageTh(queryParam);
            PageInfo<DmPatrolpathPojo> pageInfo = new PageInfo<DmPatrolpathPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmPatrolpathPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmPatrolpathPojo insert(DmPatrolpathPojo dmPatrolpathPojo) {
//初始化NULL字段
        if (dmPatrolpathPojo.getPathgroupid() == null) dmPatrolpathPojo.setPathgroupid("");
        if (dmPatrolpathPojo.getPathcode() == null) dmPatrolpathPojo.setPathcode("");
        if (dmPatrolpathPojo.getPathname() == null) dmPatrolpathPojo.setPathname("");
        if (dmPatrolpathPojo.getOperator() == null) dmPatrolpathPojo.setOperator("");
        if (dmPatrolpathPojo.getSummary() == null) dmPatrolpathPojo.setSummary("");
        if (dmPatrolpathPojo.getLister() == null) dmPatrolpathPojo.setLister("");
        if (dmPatrolpathPojo.getListerid() == null) dmPatrolpathPojo.setListerid("");
        if (dmPatrolpathPojo.getCreateby() == null) dmPatrolpathPojo.setCreateby("");
        if (dmPatrolpathPojo.getCreatebyid() == null) dmPatrolpathPojo.setCreatebyid("");
        if (dmPatrolpathPojo.getCreatedate() == null) dmPatrolpathPojo.setCreatedate(new Date());
        if (dmPatrolpathPojo.getModifydate() == null) dmPatrolpathPojo.setModifydate(new Date());
        if (dmPatrolpathPojo.getAssessor() == null) dmPatrolpathPojo.setAssessor("");
        if (dmPatrolpathPojo.getAssessorid() == null) dmPatrolpathPojo.setAssessorid("");
        if (dmPatrolpathPojo.getAssessdate() == null) dmPatrolpathPojo.setAssessdate(new Date());
        if (dmPatrolpathPojo.getEnabledmark() == null) dmPatrolpathPojo.setEnabledmark(0);
        if (dmPatrolpathPojo.getDeletemark() == null) dmPatrolpathPojo.setDeletemark(0);
        if (dmPatrolpathPojo.getDeletelister() == null) dmPatrolpathPojo.setDeletelister("");
        if (dmPatrolpathPojo.getDeletedate() == null) dmPatrolpathPojo.setDeletedate(new Date());
        if (dmPatrolpathPojo.getEnduid() == null) dmPatrolpathPojo.setEnduid("");
        if (dmPatrolpathPojo.getEnddate() == null) dmPatrolpathPojo.setEnddate(new Date());
        if (dmPatrolpathPojo.getCustom1() == null) dmPatrolpathPojo.setCustom1("");
        if (dmPatrolpathPojo.getCustom2() == null) dmPatrolpathPojo.setCustom2("");
        if (dmPatrolpathPojo.getCustom3() == null) dmPatrolpathPojo.setCustom3("");
        if (dmPatrolpathPojo.getCustom4() == null) dmPatrolpathPojo.setCustom4("");
        if (dmPatrolpathPojo.getCustom5() == null) dmPatrolpathPojo.setCustom5("");
        if (dmPatrolpathPojo.getCustom6() == null) dmPatrolpathPojo.setCustom6("");
        if (dmPatrolpathPojo.getCustom7() == null) dmPatrolpathPojo.setCustom7("");
        if (dmPatrolpathPojo.getCustom8() == null) dmPatrolpathPojo.setCustom8("");
        if (dmPatrolpathPojo.getCustom9() == null) dmPatrolpathPojo.setCustom9("");
        if (dmPatrolpathPojo.getCustom10() == null) dmPatrolpathPojo.setCustom10("");
        if (dmPatrolpathPojo.getTenantid() == null) dmPatrolpathPojo.setTenantid("");
        if (dmPatrolpathPojo.getRevision() == null) dmPatrolpathPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmPatrolpathEntity dmPatrolpathEntity = new DmPatrolpathEntity();
        BeanUtils.copyProperties(dmPatrolpathPojo, dmPatrolpathEntity);

        //设置id和新建日期
        dmPatrolpathEntity.setId(id);
        dmPatrolpathEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmPatrolpathMapper.insert(dmPatrolpathEntity);
        //Item子表处理
        List<DmPatrolpathitemPojo> lst = dmPatrolpathPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                DmPatrolpathitemPojo itemPojo = this.dmPatrolpathitemService.clearNull(lst.get(i));
                DmPatrolpathitemEntity dmPatrolpathitemEntity = new DmPatrolpathitemEntity();
                BeanUtils.copyProperties(itemPojo, dmPatrolpathitemEntity);
                //设置id和Pid
                dmPatrolpathitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmPatrolpathitemEntity.setPid(id);
                dmPatrolpathitemEntity.setTenantid(dmPatrolpathPojo.getTenantid());
                dmPatrolpathitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmPatrolpathitemMapper.insert(dmPatrolpathitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmPatrolpathEntity.getId(), dmPatrolpathEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmPatrolpathPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmPatrolpathPojo update(DmPatrolpathPojo dmPatrolpathPojo) {
        //主表更改
        DmPatrolpathEntity dmPatrolpathEntity = new DmPatrolpathEntity();
        BeanUtils.copyProperties(dmPatrolpathPojo, dmPatrolpathEntity);
        this.dmPatrolpathMapper.update(dmPatrolpathEntity);
        if (dmPatrolpathPojo.getItem() != null) {
            //Item子表处理
            List<DmPatrolpathitemPojo> lst = dmPatrolpathPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = dmPatrolpathMapper.getDelItemIds(dmPatrolpathPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.dmPatrolpathitemMapper.delete(lstDelIds.get(i), dmPatrolpathEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    DmPatrolpathitemEntity dmPatrolpathitemEntity = new DmPatrolpathitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        DmPatrolpathitemPojo itemPojo = this.dmPatrolpathitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, dmPatrolpathitemEntity);
                        //设置id和Pid
                        dmPatrolpathitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        dmPatrolpathitemEntity.setPid(dmPatrolpathEntity.getId());  // 主表 id
                        dmPatrolpathitemEntity.setTenantid(dmPatrolpathPojo.getTenantid());   // 租户id
                        dmPatrolpathitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.dmPatrolpathitemMapper.insert(dmPatrolpathitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), dmPatrolpathitemEntity);
                        dmPatrolpathitemEntity.setTenantid(dmPatrolpathPojo.getTenantid());
                        this.dmPatrolpathitemMapper.update(dmPatrolpathitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmPatrolpathEntity.getId(), dmPatrolpathEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        DmPatrolpathPojo dmPatrolpathPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<DmPatrolpathitemPojo> lst = dmPatrolpathPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.dmPatrolpathitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.dmPatrolpathMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param dmPatrolpathPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmPatrolpathPojo approval(DmPatrolpathPojo dmPatrolpathPojo) {
        //主表更改
        DmPatrolpathEntity dmPatrolpathEntity = new DmPatrolpathEntity();
        BeanUtils.copyProperties(dmPatrolpathPojo, dmPatrolpathEntity);
        this.dmPatrolpathMapper.approval(dmPatrolpathEntity);
        //返回Bill实例
        return this.getBillEntity(dmPatrolpathEntity.getId(), dmPatrolpathEntity.getTenantid());
    }

}
