package inks.service.std.eam.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 维修配件(DmRepairrecorditem)Pojo
 *
 * <AUTHOR>
 * @since 2023-06-06 14:49:33
 */
public class DmRepairrecorditemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = 859321205447606733L;
    // ID
    @Excel(name = "ID")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 备件id
    @Excel(name = "备件id")
    private String spareid;
    // 预估数量
    @Excel(name = "预估数量")
    private Double planqty;
    // 实用数量
    @Excel(name = "实用数量")
    private Double quantity;
    // 单价
    @Excel(name = "单价")
    private Double price;
    // 金额
    @Excel(name = "金额")
    private Double amount;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 仓库ID
    @Excel(name = "仓库ID")
    private String storeid;
    // 库位编码
    @Excel(name = "库位编码")
    private String location;
    // 批号
    @Excel(name = "批号")
    private String batchno;
    // 限用日期
    @Excel(name = "限用日期")
    private Date expirydate;
    // 含税单价
    @Excel(name = "含税单价")
    private Double taxprice;
    // 含税金额
    @Excel(name = "含税金额")
    private Double taxamount;
    // 记录税率
    @Excel(name = "记录税率")
    private Double itemtaxrate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    //RepairRefNo
    @Excel(name = "RepairRefNo")
    private String repairrefno;
    // 报修时间
    @Excel(name = "报修时间")
    private Date repairdate;
    // 备件编码
    private String sparecode;
    // 备件name Guid
    private String sparename;
    // 备件规格
    private String sparespec;
    // 备件单位
    private String spareunit;
    // 编码
    private String devcode;
    // 名称
    private String devname;
    // 规格
    private String devspec;
    // 单位
    private String devunit;

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 备件id
    public String getSpareid() {
        return spareid;
    }

    public void setSpareid(String spareid) {
        this.spareid = spareid;
    }

    // 预估数量
    public Double getPlanqty() {
        return planqty;
    }

    public void setPlanqty(Double planqty) {
        this.planqty = planqty;
    }

    // 实用数量
    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public String getRepairrefno() {
        return repairrefno;
    }

    public void setRepairrefno(String repairrefno) {
        this.repairrefno = repairrefno;
    }

    public String getSparecode() {
        return sparecode;
    }

    public void setSparecode(String sparecode) {
        this.sparecode = sparecode;
    }

    public String getSparename() {
        return sparename;
    }

    public void setSparename(String sparename) {
        this.sparename = sparename;
    }

    public String getSparespec() {
        return sparespec;
    }

    public void setSparespec(String sparespec) {
        this.sparespec = sparespec;
    }

    public String getSpareunit() {
        return spareunit;
    }

    public void setSpareunit(String spareunit) {
        this.spareunit = spareunit;
    }

    public String getDevcode() {
        return devcode;
    }

    public void setDevcode(String devcode) {
        this.devcode = devcode;
    }

    public String getDevname() {
        return devname;
    }

    public void setDevname(String devname) {
        this.devname = devname;
    }

    public String getDevspec() {
        return devspec;
    }

    public void setDevspec(String devspec) {
        this.devspec = devspec;
    }

    public String getDevunit() {
        return devunit;
    }

    public void setDevunit(String devunit) {
        this.devunit = devunit;
    }

    // 单价
    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    // 金额
    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    public Date getRepairdate() {
        return repairdate;
    }

    public void setRepairdate(Date repairdate) {
        this.repairdate = repairdate;
    }

    // 仓库ID
    public String getStoreid() {
        return storeid;
    }

    public void setStoreid(String storeid) {
        this.storeid = storeid;
    }

    // 库位编码
    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    // 批号
    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    // 限用日期
    public Date getExpirydate() {
        return expirydate;
    }

    public void setExpirydate(Date expirydate) {
        this.expirydate = expirydate;
    }

    // 含税单价
    public Double getTaxprice() {
        return taxprice;
    }

    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }

    // 含税金额
    public Double getTaxamount() {
        return taxamount;
    }

    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }

    // 记录税率
    public Double getItemtaxrate() {
        return itemtaxrate;
    }

    public void setItemtaxrate(Double itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

