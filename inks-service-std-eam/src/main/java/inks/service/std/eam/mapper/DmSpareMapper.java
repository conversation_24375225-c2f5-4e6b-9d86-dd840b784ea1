package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmSpareEntity;
import inks.service.std.eam.domain.pojo.DmSparePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备件信息(DmSpare)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-07 16:26:36
 */
@Mapper
public interface DmSpareMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmSparePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmSparePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param dmSpareEntity 实例对象
     * @return 影响行数
     */
    int insert(DmSpareEntity dmSpareEntity);


    /**
     * 修改数据
     *
     * @param dmSpareEntity 实例对象
     * @return 影响行数
     */
    int update(DmSpareEntity dmSpareEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    int updateIvQuantity(@Param("quantity") Double quantity, @Param("spareid") String spareid, @Param("tid") String tid);
}

