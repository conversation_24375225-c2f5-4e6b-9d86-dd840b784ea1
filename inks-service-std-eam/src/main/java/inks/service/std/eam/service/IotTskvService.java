package inks.service.std.eam.service;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotTskvPojo;
import com.github.pagehelper.PageInfo;

/**
 * 时间序列遥测数据表(Iot_TsKv)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:54
 */
public interface IotTskvService {

    IotTskvPojo getEntity(String key,String tid);

    PageInfo<IotTskvPojo> getPageList(QueryParam queryParam);

    IotTskvPojo insert(IotTskvPojo iotTskvPojo);

    IotTskvPojo update(IotTskvPojo iotTskvpojo);

    int delete(String key,String tid);

    int batchInsert(JSONObject mqttJSON);
}
