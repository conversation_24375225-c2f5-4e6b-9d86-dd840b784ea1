package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.DmUpkeepflowPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepflowitemPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepflowitemdetailPojo;
import inks.service.std.eam.service.DmUpkeepflowService;
import inks.service.std.eam.service.DmUpkeepflowitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 保养方案(DmUpkeepflow)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-06 14:35:33
 */
public class DmUpkeepflowController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(DmUpkeepflowController.class);
    /**
     * 服务对象
     */
    @Resource
    private DmUpkeepflowService dmUpkeepflowService;
    /**
     * 服务对象Item
     */
    @Resource
    private DmUpkeepflowitemService dmUpkeepflowitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取保养方案详细信息", notes = "获取保养方案详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.List")
    public R<DmUpkeepflowPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmUpkeepflowService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.List")
    public R<PageInfo<DmUpkeepflowitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_UpkeepFlow.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.dmUpkeepflowService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取保养方案详细信息", notes = "获取保养方案详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.List")
    public R<DmUpkeepflowPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmUpkeepflowService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.List")
    public R<PageInfo<DmUpkeepflowPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_UpkeepFlow.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.dmUpkeepflowService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.List")
    public R<PageInfo<DmUpkeepflowPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_UpkeepFlow.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.dmUpkeepflowService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增保养方案", notes = "新增保养方案", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.Add")
    public R<DmUpkeepflowPojo> create(@RequestBody String json) {
        try {
            DmUpkeepflowPojo dmUpkeepflowPojo = JSONArray.parseObject(json, DmUpkeepflowPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            R r = systemFeignService.getBillCode("DxxMxxB1", loginUser.getToken());
            if (r.getCode() == 200)
                dmUpkeepflowPojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            dmUpkeepflowPojo.setCreateby(loginUser.getRealName());   // 创建者
            dmUpkeepflowPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            dmUpkeepflowPojo.setCreatedate(new Date());   // 创建时间
            dmUpkeepflowPojo.setLister(loginUser.getRealname());   // 制表
            dmUpkeepflowPojo.setListerid(loginUser.getUserid());    // 制表id            
            dmUpkeepflowPojo.setModifydate(new Date());   //修改时间
            dmUpkeepflowPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.dmUpkeepflowService.insert(dmUpkeepflowPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改保养方案", notes = "修改保养方案", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.Edit")
    public R<DmUpkeepflowPojo> update(@RequestBody String json) {
        try {
            DmUpkeepflowPojo dmUpkeepflowPojo = JSONArray.parseObject(json, DmUpkeepflowPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmUpkeepflowPojo.setLister(loginUser.getRealname());   // 制表
            dmUpkeepflowPojo.setListerid(loginUser.getUserid());    // 制表id   
            dmUpkeepflowPojo.setModifydate(new Date());   //修改时间
            dmUpkeepflowPojo.setAssessor(""); //审核员
            dmUpkeepflowPojo.setAssessorid(""); //审核员
            dmUpkeepflowPojo.setAssessdate(new Date()); //审核时间
            dmUpkeepflowPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.dmUpkeepflowService.update(dmUpkeepflowPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除保养方案", notes = "删除保养方案", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmUpkeepflowService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增保养方案Item", notes = "新增保养方案Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.Add")
    public R<DmUpkeepflowitemPojo> createItem(@RequestBody String json) {
        try {
            DmUpkeepflowitemPojo dmUpkeepflowitemPojo = JSONArray.parseObject(json, DmUpkeepflowitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmUpkeepflowitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.dmUpkeepflowitemService.insert(dmUpkeepflowitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除保养方案Item", notes = "删除保养方案Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmUpkeepflowitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核保养方案", notes = "审核保养方案", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.Approval")
    public R<DmUpkeepflowPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            DmUpkeepflowPojo dmUpkeepflowPojo = this.dmUpkeepflowService.getEntity(key, loginUser.getTenantid());
            if (dmUpkeepflowPojo.getAssessor().equals("")) {
                dmUpkeepflowPojo.setAssessor(loginUser.getRealname()); //审核员
                dmUpkeepflowPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                dmUpkeepflowPojo.setAssessor(""); //审核员
                dmUpkeepflowPojo.setAssessorid(""); //审核员
            }
            dmUpkeepflowPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.dmUpkeepflowService.approval(dmUpkeepflowPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepFlow.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        DmUpkeepflowPojo dmUpkeepflowPojo = this.dmUpkeepflowService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(dmUpkeepflowPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = dmUpkeepflowPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    DmUpkeepflowitemPojo dmUpkeepflowitemPojo = new DmUpkeepflowitemPojo();
                    dmUpkeepflowPojo.getItem().add(dmUpkeepflowitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(dmUpkeepflowPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

