package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotRulenodestatePojo;
import inks.service.std.eam.domain.IotRulenodestateEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 规则节点运行状态表(Iot_RuleNodeState)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:48
 */
@Mapper
public interface IotRulenodestateMapper {

    IotRulenodestatePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotRulenodestatePojo> getPageList(QueryParam queryParam);

    int insert(IotRulenodestateEntity iotRulenodestateEntity);

    int update(IotRulenodestateEntity iotRulenodestateEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

