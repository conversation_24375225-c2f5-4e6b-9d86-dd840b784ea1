package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.IotAlarmcommentPojo;
import inks.service.std.eam.service.IotAlarmcommentService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 告警评论表(Iot_AlarmComment)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-24 15:27:08
 */
//@RestController
//@RequestMapping("iotAlarmcomment")
public class IotAlarmcommentController {

    @Resource
    private IotAlarmcommentService iotAlarmcommentService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(IotAlarmcommentController.class);


    @ApiOperation(value = " 获取告警评论表详细信息", notes = "获取告警评论表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_AlarmComment.List")
    public R<IotAlarmcommentPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.iotAlarmcommentService.getEntity(key, loginUser.getTenantid()));
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_AlarmComment.List")
    public R<PageInfo<IotAlarmcommentPojo>> getPageList(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Iot_AlarmComment.CreateDate");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        queryParam.setTenantid(loginUser.getTenantid());
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.iotAlarmcommentService.getPageList(queryParam));
    }


    @ApiOperation(value = " 新增告警评论表", notes = "新增告警评论表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_AlarmComment.Add")
    public R<IotAlarmcommentPojo> create(@RequestBody String json) {
        IotAlarmcommentPojo iotAlarmcommentPojo = JSONArray.parseObject(json, IotAlarmcommentPojo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        iotAlarmcommentPojo.setCreateby(loginUser.getRealName());   // 创建者
        iotAlarmcommentPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        iotAlarmcommentPojo.setCreatedate(new Date());   // 创建时间
        iotAlarmcommentPojo.setLister(loginUser.getRealname());   // 制表
        iotAlarmcommentPojo.setListerid(loginUser.getUserid());    // 制表id
        iotAlarmcommentPojo.setModifydate(new Date());   //修改时间
        iotAlarmcommentPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.iotAlarmcommentService.insert(iotAlarmcommentPojo));
    }


    @ApiOperation(value = "修改告警评论表", notes = "修改告警评论表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_AlarmComment.Edit")
    public R<IotAlarmcommentPojo> update(@RequestBody String json) {
        IotAlarmcommentPojo iotAlarmcommentPojo = JSONArray.parseObject(json, IotAlarmcommentPojo.class);
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        iotAlarmcommentPojo.setLister(loginUser.getRealname());   // 制表
        iotAlarmcommentPojo.setListerid(loginUser.getUserid());    // 制表id
        iotAlarmcommentPojo.setTenantid(loginUser.getTenantid());   //租户id
        iotAlarmcommentPojo.setModifydate(new Date());   //修改时间
//            iotAlarmcommentPojo.setAssessor(""); // 审核员
//            iotAlarmcommentPojo.setAssessorid(""); // 审核员id
//            iotAlarmcommentPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.iotAlarmcommentService.update(iotAlarmcommentPojo));
    }


    @ApiOperation(value = "删除告警评论表", notes = "删除告警评论表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_AlarmComment.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.iotAlarmcommentService.delete(key, loginUser.getTenantid()));
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_AlarmComment.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        IotAlarmcommentPojo iotAlarmcommentPojo = this.iotAlarmcommentService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(iotAlarmcommentPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


}

