package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.exception.WarnException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmToolinfoEntity;
import inks.service.std.eam.domain.DmToolusageEntity;
import inks.service.std.eam.domain.DmToolusageitemEntity;
import inks.service.std.eam.domain.pojo.DmToolinfoPojo;
import inks.service.std.eam.domain.pojo.DmToolusagePojo;
import inks.service.std.eam.domain.pojo.DmToolusageitemPojo;
import inks.service.std.eam.domain.pojo.DmToolusageitemdetailPojo;
import inks.service.std.eam.mapper.DmToolinfoMapper;
import inks.service.std.eam.mapper.DmToolusageMapper;
import inks.service.std.eam.mapper.DmToolusageitemMapper;
import inks.service.std.eam.service.DmToolusageService;
import inks.service.std.eam.service.DmToolusageitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 工装具使用记录表(DmToolusage)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:20
 */
@Service("dmToolusageService")
public class DmToolusageServiceImpl implements DmToolusageService {
    @Resource
    private DmToolusageMapper dmToolusageMapper;
    @Resource
    private DmToolinfoMapper dmToolinfoMapper;
    @Resource
    private DmToolusageitemMapper dmToolusageitemMapper;


    @Resource
    private DmToolusageitemService dmToolusageitemService;

    @Override
    public DmToolusagePojo getEntity(String key, String tid) {
        return this.dmToolusageMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<DmToolusageitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolusageitemdetailPojo> lst = dmToolusageMapper.getPageList(queryParam);
            PageInfo<DmToolusageitemdetailPojo> pageInfo = new PageInfo<DmToolusageitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public DmToolusagePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            DmToolusagePojo dmToolusagePojo = this.dmToolusageMapper.getEntity(key, tid);
            //读取子表
            dmToolusagePojo.setItem(dmToolusageitemMapper.getList(dmToolusagePojo.getId(), tid));
            return dmToolusagePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<DmToolusagePojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolusagePojo> lst = dmToolusageMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (DmToolusagePojo item : lst) {
                item.setItem(dmToolusageitemMapper.getList(item.getId(), tid));
            }
            PageInfo<DmToolusagePojo> pageInfo = new PageInfo<DmToolusagePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<DmToolusagePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolusagePojo> lst = dmToolusageMapper.getPageTh(queryParam);
            PageInfo<DmToolusagePojo> pageInfo = new PageInfo<DmToolusagePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public DmToolusagePojo insert(DmToolusagePojo dmToolusagePojo, Integer warn) {
        String tid = dmToolusagePojo.getTenantid();
        //初始化NULL字段
        cleanNull(dmToolusagePojo);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmToolusageEntity dmToolusageEntity = new DmToolusageEntity();
        BeanUtils.copyProperties(dmToolusagePojo, dmToolusageEntity);

        //设置id和新建日期
        dmToolusageEntity.setId(id);
        dmToolusageEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmToolusageMapper.insert(dmToolusageEntity);
        //Item子表处理
        List<DmToolusageitemPojo> lst = dmToolusagePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (DmToolusageitemPojo item : lst) {
                //初始化item的NULL
                DmToolusageitemPojo itemPojo = this.dmToolusageitemService.clearNull(item);
                DmToolusageitemEntity dmToolusageitemEntity = new DmToolusageitemEntity();
                BeanUtils.copyProperties(itemPojo, dmToolusageitemEntity);
                //设置id和Pid
                dmToolusageitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmToolusageitemEntity.setPid(id);
                dmToolusageitemEntity.setTenantid(tid);
                dmToolusageitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmToolusageitemMapper.insert(dmToolusageitemEntity);
                // 检查是否超过保养预警量、限制量
                DmToolinfoPojo toolDB = this.dmToolinfoMapper.getEntity(item.getToolid(), tid);
                // 该工装加上本次累计保养使用数量、累计报废使用数量
                int sumMaintUsedQty = toolDB.getMaintusedqty() + item.getQuantity();
                int sumScrapUsedQty = toolDB.getScrapusedqty() + item.getQuantity();
                if (warn == 1 && sumScrapUsedQty > toolDB.getScrapwarnqty()) {
                    throw new WarnException("工装具[" + toolDB.getToolname() + "]使用量" + sumScrapUsedQty + "已超过报废预警值" + toolDB.getScrapwarnqty() + "，请及时报废！");
                }
                if (sumScrapUsedQty > toolDB.getScraplimitqty()) {
                    throw new BaseBusinessException("工装具[" + toolDB.getToolname() + "]使用量" + sumScrapUsedQty + "已超过报废限制值" + toolDB.getScraplimitqty() + "，请及时报废！");
                }
                if (warn == 1 && sumMaintUsedQty > toolDB.getMaintwarnqty()) {
                    throw new WarnException("工装具[" + toolDB.getToolname() + "]使用量" + sumMaintUsedQty + "已超过保养预警值" + toolDB.getMaintwarnqty() + "，请及时保养！");
                }
                if (sumMaintUsedQty > toolDB.getMaintlimitqty()) {
                    throw new BaseBusinessException("工装具[" + toolDB.getToolname() + "]使用量" + sumMaintUsedQty + "已超过保养限制值" + toolDB.getMaintlimitqty() + "，请及时保养！");
                }
                // 同步工装具保养使用量、报废使用量
                DmToolinfoEntity dmToolinfoEntity = new DmToolinfoEntity();
                dmToolinfoEntity.setId(item.getToolid());
                dmToolinfoEntity.setMaintusedqty(sumMaintUsedQty);
                dmToolinfoEntity.setScrapusedqty(sumScrapUsedQty);
                dmToolinfoEntity.setStatus(2);// 当前状态(1:在库, 2:使用中, 3:保养中, 4:已报废)
                dmToolinfoEntity.setTenantid(dmToolusagePojo.getTenantid());
                dmToolinfoMapper.update(dmToolinfoEntity);
            }

        }
        //返回Bill实例
        return this.getBillEntity(dmToolusageEntity.getId(), tid);
    }


    @Override
    @Transactional
    public DmToolusagePojo update(DmToolusagePojo dmToolusagePojo, Integer warn) {
        String tid = dmToolusagePojo.getTenantid();
        //主表更改
        DmToolusageEntity dmToolusageEntity = new DmToolusageEntity();
        BeanUtils.copyProperties(dmToolusagePojo, dmToolusageEntity);
        this.dmToolusageMapper.update(dmToolusageEntity);
        if (dmToolusagePojo.getItem() != null) {
            //Item子表处理
            List<DmToolusageitemPojo> lst = dmToolusagePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = dmToolusageMapper.getDelItemIds(dmToolusagePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String delId : lstDelIds) {
                    DmToolusageitemPojo itemOrg = dmToolusageitemMapper.getEntity(delId, tid);
                    DmToolinfoPojo toolDB = this.dmToolinfoMapper.getEntity(itemOrg.getToolid(), tid);
                    this.dmToolusageitemMapper.delete(delId, tid);
                    // 同步工装具保养使用量
                    DmToolinfoEntity dmToolinfoEntity = new DmToolinfoEntity();
                    dmToolinfoEntity.setId(toolDB.getId());
                    dmToolinfoEntity.setMaintusedqty(toolDB.getMaintusedqty() - itemOrg.getQuantity());
                    dmToolinfoEntity.setScrapusedqty(toolDB.getScrapusedqty() - itemOrg.getQuantity());
                    //dmToolinfoEntity.setStatus(2);// 当前状态(1:在库, 2:使用中, 3:保养中, 4:已报废)
                    dmToolinfoEntity.setTenantid(dmToolusagePojo.getTenantid());
                    dmToolinfoMapper.update(dmToolinfoEntity);
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (DmToolusageitemPojo item : lst) {
                    DmToolusageitemPojo toolusageitemOrg = dmToolusageitemMapper.getEntity(item.getId(), tid);
                    DmToolusageitemEntity dmToolusageitemEntity = new DmToolusageitemEntity();
                    if ("".equals(item.getId()) || item.getId() == null) {
                        //初始化item的NULL
                        DmToolusageitemPojo itemPojo = this.dmToolusageitemService.clearNull(item);
                        BeanUtils.copyProperties(itemPojo, dmToolusageitemEntity);
                        //设置id和Pid
                        dmToolusageitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        dmToolusageitemEntity.setPid(dmToolusageEntity.getId());  // 主表 id
                        dmToolusageitemEntity.setTenantid(tid);   // 租户id
                        dmToolusageitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.dmToolusageitemMapper.insert(dmToolusageitemEntity);
                    } else {
                        BeanUtils.copyProperties(item, dmToolusageitemEntity);
                        dmToolusageitemEntity.setTenantid(tid);
                        this.dmToolusageitemMapper.update(dmToolusageitemEntity);
                    }
                    // 检查是否超过保养预警量、限制量
                    DmToolinfoPojo toolDB = this.dmToolinfoMapper.getEntity(item.getToolid(), tid);
                    // 该工装加上本次累计使用数量(减去原数量即为本次修改量)、累计报废使用数量
                    int sumMaintUsedQty = toolDB.getMaintusedqty() + item.getQuantity() - toolusageitemOrg.getQuantity();
                    int sumScrapUsedQty = toolDB.getScrapusedqty() + item.getQuantity() - toolusageitemOrg.getQuantity();
                    if (warn == 1 && sumScrapUsedQty > toolDB.getScrapwarnqty()) {
                        throw new WarnException("工装具[" + toolDB.getToolname() + "]使用量" + sumScrapUsedQty + "已超过报废预警值" + toolDB.getScrapwarnqty() + "，请及时报废！");
                    }
                    if (sumScrapUsedQty > toolDB.getScraplimitqty()) {
                        throw new BaseBusinessException("工装具[" + toolDB.getToolname() + "]使用量" + sumScrapUsedQty + "已超过报废限制值" + toolDB.getScraplimitqty() + "，请及时报废！");
                    }
                    if (warn == 1 && sumMaintUsedQty > toolDB.getMaintwarnqty()) {
                        throw new WarnException("工装具[" + toolDB.getToolname() + "]使用量" + sumMaintUsedQty + "已超过保养预警值 " + toolDB.getMaintwarnqty() + "，请及时保养！");
                    }
                    if (sumMaintUsedQty > toolDB.getMaintlimitqty()) {
                        throw new BaseBusinessException("工装具[" + toolDB.getToolname() + "]使用量" + sumMaintUsedQty + "已超过保养限制值 " + toolDB.getMaintlimitqty() + "，请及时保养！");
                    }
                    // 同步工装具保养使用量
                    DmToolinfoEntity dmToolinfoEntity = new DmToolinfoEntity();
                    dmToolinfoEntity.setId(item.getToolid());
                    dmToolinfoEntity.setMaintusedqty(sumMaintUsedQty);
                    dmToolinfoEntity.setScrapusedqty(sumScrapUsedQty);
                    dmToolinfoEntity.setStatus(2);// 当前状态(1:在库, 2:使用中, 3:保养中, 4:已报废)
                    dmToolinfoEntity.setTenantid(dmToolusagePojo.getTenantid());
                    dmToolinfoMapper.update(dmToolinfoEntity);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmToolusageEntity.getId(), tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
        DmToolusagePojo dmToolusagePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<DmToolusageitemPojo> lst = dmToolusagePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (DmToolusageitemPojo item : lst) {
                DmToolusageitemPojo toolusageitemDB = dmToolusageitemMapper.getEntity(item.getId(), tid);
                DmToolinfoPojo toolDB = this.dmToolinfoMapper.getEntity(toolusageitemDB.getToolid(), tid);
                this.dmToolusageitemMapper.delete(item.getId(), tid);
                // 同步工装具保养使用量
                DmToolinfoEntity dmToolinfoEntity = new DmToolinfoEntity();
                dmToolinfoEntity.setId(toolDB.getId());
                dmToolinfoEntity.setMaintusedqty(toolDB.getMaintusedqty() - toolusageitemDB.getQuantity());
                dmToolinfoEntity.setScrapusedqty(toolDB.getScrapusedqty() - toolusageitemDB.getQuantity());
                //dmToolinfoEntity.setStatus(2);// 当前状态(1:在库, 2:使用中, 3:保养中, 4:已报废)
                dmToolinfoEntity.setTenantid(dmToolusagePojo.getTenantid());
                dmToolinfoMapper.update(dmToolinfoEntity);
            }
        }
        return this.dmToolusageMapper.delete(key, tid);
    }


    private static void cleanNull(DmToolusagePojo dmToolusagePojo) {
        if (dmToolusagePojo.getRefno() == null) dmToolusagePojo.setRefno("");
        if (dmToolusagePojo.getBilldate() == null) dmToolusagePojo.setBilldate(new Date());
        if (dmToolusagePojo.getBilltype() == null) dmToolusagePojo.setBilltype("");
        if (dmToolusagePojo.getBilltitle() == null) dmToolusagePojo.setBilltitle("");
        if (dmToolusagePojo.getOperatorid() == null) dmToolusagePojo.setOperatorid("");
        if (dmToolusagePojo.getOperator() == null) dmToolusagePojo.setOperator("");
        if (dmToolusagePojo.getItemcount() == null) dmToolusagePojo.setItemcount(0);
        if (dmToolusagePojo.getFinishcount() == null) dmToolusagePojo.setFinishcount(0);
        if (dmToolusagePojo.getSummary() == null) dmToolusagePojo.setSummary("");
        if (dmToolusagePojo.getRownum() == null) dmToolusagePojo.setRownum(0);
        if (dmToolusagePojo.getCreateby() == null) dmToolusagePojo.setCreateby("");
        if (dmToolusagePojo.getCreatebyid() == null) dmToolusagePojo.setCreatebyid("");
        if (dmToolusagePojo.getCreatedate() == null) dmToolusagePojo.setCreatedate(new Date());
        if (dmToolusagePojo.getLister() == null) dmToolusagePojo.setLister("");
        if (dmToolusagePojo.getListerid() == null) dmToolusagePojo.setListerid("");
        if (dmToolusagePojo.getModifydate() == null) dmToolusagePojo.setModifydate(new Date());
        if (dmToolusagePojo.getCustom1() == null) dmToolusagePojo.setCustom1("");
        if (dmToolusagePojo.getCustom2() == null) dmToolusagePojo.setCustom2("");
        if (dmToolusagePojo.getCustom3() == null) dmToolusagePojo.setCustom3("");
        if (dmToolusagePojo.getCustom4() == null) dmToolusagePojo.setCustom4("");
        if (dmToolusagePojo.getCustom5() == null) dmToolusagePojo.setCustom5("");
        if (dmToolusagePojo.getCustom6() == null) dmToolusagePojo.setCustom6("");
        if (dmToolusagePojo.getCustom7() == null) dmToolusagePojo.setCustom7("");
        if (dmToolusagePojo.getCustom8() == null) dmToolusagePojo.setCustom8("");
        if (dmToolusagePojo.getCustom9() == null) dmToolusagePojo.setCustom9("");
        if (dmToolusagePojo.getCustom10() == null) dmToolusagePojo.setCustom10("");
        if (dmToolusagePojo.getDeptid() == null) dmToolusagePojo.setDeptid("");
        if (dmToolusagePojo.getTenantid() == null) dmToolusagePojo.setTenantid("");
        if (dmToolusagePojo.getTenantname() == null) dmToolusagePojo.setTenantname("");
        if (dmToolusagePojo.getRevision() == null) dmToolusagePojo.setRevision(0);
    }

}
