package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmRepairservicePojo;

/**
 * 设备报修(DmRepairservice)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-06 14:55:26
 */
public interface DmRepairserviceService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmRepairservicePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmRepairservicePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmRepairservicePojo 实例对象
     * @return 实例对象
     */
    DmRepairservicePojo insert(DmRepairservicePojo dmRepairservicePojo);

    /**
     * 修改数据
     *
     * @param dmRepairservicepojo 实例对象
     * @return 实例对象
     */
    DmRepairservicePojo update(DmRepairservicePojo dmRepairservicepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param dmRepairservicePojo 实例对象
     * @return 实例对象
     */
    DmRepairservicePojo approval(DmRepairservicePojo dmRepairservicePojo);
}
