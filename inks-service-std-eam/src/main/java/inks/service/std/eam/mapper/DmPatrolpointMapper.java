package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmPatrolpointEntity;
import inks.service.std.eam.domain.pojo.DmPatrolpointPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡检点(DmPatrolpoint)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 13:09:01
 */
@Mapper
public interface DmPatrolpointMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolpointPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmPatrolpointPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param dmPatrolpointEntity 实例对象
     * @return 影响行数
     */
    int insert(DmPatrolpointEntity dmPatrolpointEntity);


    /**
     * 修改数据
     *
     * @param dmPatrolpointEntity 实例对象
     * @return 影响行数
     */
    int update(DmPatrolpointEntity dmPatrolpointEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

}

