package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolmaintenanceitemPojo;
import inks.service.std.eam.domain.DmToolmaintenanceitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具保养记录子表(DmToolmaintenanceitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:35
 */
 @Mapper
public interface DmToolmaintenanceitemMapper {

    DmToolmaintenanceitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolmaintenanceitemPojo> getPageList(QueryParam queryParam);

    List<DmToolmaintenanceitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(DmToolmaintenanceitemEntity dmToolmaintenanceitemEntity);

    int update(DmToolmaintenanceitemEntity dmToolmaintenanceitemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    DmToolmaintenanceitemPojo getLeastEntity(String toolid, String tid);
}

