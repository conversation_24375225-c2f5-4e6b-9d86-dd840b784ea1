package inks.service.std.eam.mqtt.service;

import inks.common.redis.service.RedisService;
import inks.service.std.eam.domain.vo.DeviceTokenVO;
import inks.service.std.eam.mqtt.auth.TokenBasedMqttAuthHandler;
import inks.service.std.eam.mqtt.constants.MqttConstants;
import inks.service.std.eam.service.IotDeviceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备Token管理服务
 * 用于管理MQTT设备的访问Token
 * 支持Redis存储和数据库同步
 */
@Service
public class DeviceTokenService {

    private static final Logger logger = LoggerFactory.getLogger(DeviceTokenService.class);

    @Resource
    private RedisService redisService;

    @Resource
    private IotDeviceService iotDeviceService;

    @Resource
    private TokenBasedMqttAuthHandler tokenBasedMqttAuthHandler;

    /**
     * 应用启动时初始化Redis数据
     */
    @PostConstruct
    public void initializeRedisFromDatabase() {
        logger.info("开始从数据库初始化Redis token数据...");
        try {
            List<DeviceTokenVO> devices = iotDeviceService.getAllDevicesWithToken();
            int successCount = 0;
            for (DeviceTokenVO device : devices) {
                if (device.getToken() != null && !device.getToken().trim().isEmpty()) {
                    tokenBasedMqttAuthHandler.addTokenToRedis(device.getToken(), device.getId());
                    successCount++;
                }
            }
            logger.info("Redis token数据初始化完成，成功同步{}个设备token", successCount);
        } catch (Exception e) {
            logger.error("Redis token数据初始化失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 生成新的设备Token
     * @param deviceId 设备ID
     * @param deviceType 设备类型（如：sensor, iphone, android等）
     * @return 生成的Token
     */
    public String generateDeviceToken(String deviceId, String deviceType) {
        // 生成格式：deviceType_deviceId_randomUUID
        String token = String.format("%s%s%s%s%s",
                                   deviceType,
                                   MqttConstants.TOKEN_SEPARATOR,
                                   deviceId,
                                   MqttConstants.TOKEN_SEPARATOR,
                                   UUID.randomUUID().toString().replace("-", "").substring(0, MqttConstants.TOKEN_RANDOM_LENGTH));

        // 添加到Redis
        tokenBasedMqttAuthHandler.addTokenToRedis(token, deviceId);

        logger.info("为设备生成新Token - DeviceId: {}, DeviceType: {}, Token: {}",
                   deviceId, deviceType, token);

        return token;
    }
    
    /**
     * 添加Token到Redis
     * @param token 设备token
     * @param deviceId 设备ID
     * @return 是否添加成功
     */
    public boolean addTokenToRedis(String token, String deviceId) {
        if (token == null || token.trim().isEmpty() || deviceId == null || deviceId.trim().isEmpty()) {
            logger.warn("尝试添加空Token或设备ID");
            return false;
        }

        try {
            tokenBasedMqttAuthHandler.addTokenToRedis(token.trim(), deviceId.trim());
            logger.info("添加Token到Redis成功: token={}, deviceId={}", token, deviceId);
            return true;
        } catch (Exception e) {
            logger.error("添加Token到Redis失败: token={}, deviceId={}, error={}",
                        token, deviceId, e.getMessage());
            return false;
        }
    }

    /**
     * 从Redis移除Token
     * @param token 要移除的Token
     * @return 是否移除成功
     */
    public boolean removeTokenFromRedis(String token) {
        if (token == null || token.trim().isEmpty()) {
            logger.warn("尝试移除空Token");
            return false;
        }

        try {
            tokenBasedMqttAuthHandler.removeTokenFromRedis(token.trim());
            logger.info("从Redis移除Token成功: {}", token);
            return true;
        } catch (Exception e) {
            logger.error("从Redis移除Token失败: token={}, error={}", token, e.getMessage());
            return false;
        }
    }

    /**
     * 通过token获取设备ID
     * @param token 设备token
     * @return 设备ID，如果不存在返回null
     */
    public String getDeviceIdByToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }

        return tokenBasedMqttAuthHandler.getDeviceIdByToken(token.trim());
    }


    
    /**
     * 获取所有设备Token映射关系（从数据库）
     * @return Token到设备ID的映射Map
     */
    public Map<String, String> getAllTokensFromDatabase() {
        try {
            List<DeviceTokenVO> devices = iotDeviceService.getAllDevicesWithToken();
            return devices.stream()
                    .filter(device -> device.getToken() != null && !device.getToken().trim().isEmpty())
                    .collect(Collectors.toMap(
                            DeviceTokenVO::getToken,
                            DeviceTokenVO::getId,
                            (existing, replacement) -> existing // 如果有重复key，保留第一个
                    ));
        } catch (Exception e) {
            logger.error("从数据库获取Token映射失败: {}", e.getMessage());
            return new HashMap<>();
        }
    }



    /**
     * 验证Token是否有效（从Redis检查）
     * @param token 要验证的Token
     * @return 是否有效
     */
    public boolean isValidToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        // 优先从Redis检查
        String deviceId = getDeviceIdByToken(token);
        if (deviceId != null) {
            return true;
        }

        // 如果Redis中没有找到，返回false
        return false;
    }
    
    /**
     * 手动刷新Redis缓存（从数据库重新同步）
     * @return 同步成功的token数量
     */
    public int refreshRedisCache() {
        logger.info("开始手动刷新Redis token缓存...");
        try {
            List<DeviceTokenVO> devices = iotDeviceService.getAllDevicesWithToken();
            int successCount = 0;
            for (DeviceTokenVO device : devices) {
                if (device.getToken() != null && !device.getToken().trim().isEmpty()) {
                    tokenBasedMqttAuthHandler.addTokenToRedis(device.getToken(), device.getId());
                    successCount++;
                }
            }
            logger.info("Redis token缓存刷新完成，成功同步{}个设备token", successCount);
            return successCount;
        } catch (Exception e) {
            logger.error("Redis token缓存刷新失败: {}", e.getMessage(), e);
            return 0;
        }
    }


}
