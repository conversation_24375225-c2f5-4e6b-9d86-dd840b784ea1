package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmRepairrecordPojo;
import inks.service.std.eam.domain.pojo.DmRepairrecorditemdetailPojo;

/**
 * 维护记录(DmRepairrecord)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-06 14:54:55
 */
public interface DmRepairrecordService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmRepairrecordPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmRepairrecorditemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmRepairrecordPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmRepairrecordPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmRepairrecordPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmRepairrecordPojo 实例对象
     * @return 实例对象
     */
    DmRepairrecordPojo insert(DmRepairrecordPojo dmRepairrecordPojo);

    /**
     * 修改数据
     *
     * @param dmRepairrecordpojo 实例对象
     * @return 实例对象
     */
    DmRepairrecordPojo update(DmRepairrecordPojo dmRepairrecordpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

}
