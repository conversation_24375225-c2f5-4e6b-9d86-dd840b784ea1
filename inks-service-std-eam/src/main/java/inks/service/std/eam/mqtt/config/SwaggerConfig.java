package inks.service.std.eam.mqtt.config;

import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.service.ApiKey;
import springfox.documentation.service.SecurityReference;
import springfox.documentation.service.SecurityScheme;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.*;

/**
 * Swagger 配置类，用于生成 RESTful API 文档
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig {
    @Bean
    public Docket api(ApplicationContext context) {
        // 动态获取所有的 Controller
        Set<String> controllerPackages = getControllerPackages(context);

        // 打印所有扫描到的包路径
        System.out.println("Swagger 扫描的 Controller 包路径：" + controllerPackages);

        // 构建 Docket
        Docket docket = new Docket(DocumentationType.SWAGGER_2)
                .select()
                .paths(PathSelectors.any()) // 匹配所有路径
                .apis(handler -> {
                    // 遍历所有包路径，动态判断是否匹配
                    for (String basePackage : controllerPackages) {
                        if (handler.declaringClass().getPackage().getName().startsWith(basePackage)) {
                            return true;
                        }
                    }
                    return false;
                })
                .build()
                .apiInfo(new ApiInfoBuilder()
                        .title("Dynamic API Documentation")
                        .description("Generated by SwaggerConfig")
                        .version("1.0.0")
                        .build())
                // 注册 SecuritySchemes，用于支持 Authorization 头
                .securitySchemes(Collections.singletonList(apiAuth()))
                // 注册 SecurityContexts，用于将 SecurityScheme 应用到 API 路径
                .securityContexts(Collections.singletonList(securityContext()));
        return docket;
    }

    /**
     * 动态获取所有 Controller 的包路径
     */
    private Set<String> getControllerPackages(ApplicationContext context) {
        Set<String> packages = new HashSet<>();

        // 获取标注了 @RestController 的类
        Map<String, Object> restControllers = context.getBeansWithAnnotation(RestController.class);
        restControllers.values().forEach(bean ->
                packages.add(bean.getClass().getPackage().getName()));

        // 获取标注了 @Controller 的类
        Map<String, Object> controllers = context.getBeansWithAnnotation(Controller.class);
        controllers.values().forEach(bean ->
                packages.add(bean.getClass().getPackage().getName()));

        return packages;
    }


    /**
     * 定义用于 Authorization 的 ApiKey
     *
     * @return ApiKey 对象
     */
    private SecurityScheme apiAuth() {
        return new ApiKey("Authorization", "Authorization", "header");
    }

    /**
     * 配置 SecurityContext，将 ApiKey 与请求路径关联
     *
     * @return SecurityContext 对象
     */
    private SecurityContext securityContext() {
        return SecurityContext.builder()
                // 配置默认的 SecurityReference
                .securityReferences(defaultAuth())
                // 将 ApiKey 作用于所有路径
                .forPaths(PathSelectors.any())
                .build();
    }

    /**
     * 配置默认的 SecurityReference
     *
     * @return SecurityReference 列表
     */
    private List<SecurityReference> defaultAuth() {
        // 定义全局授权范围
        SecurityReference securityReference = new SecurityReference(
                "Authorization",
                new springfox.documentation.service.AuthorizationScope[]{
                        // 授权范围为全局
                        new springfox.documentation.service.AuthorizationScope("global", "accessEverything")
                }
        );
        return Collections.singletonList(securityReference);
    }
}
