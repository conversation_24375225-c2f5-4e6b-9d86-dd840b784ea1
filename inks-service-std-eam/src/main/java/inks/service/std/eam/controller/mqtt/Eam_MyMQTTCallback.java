//package inks.service.std.eam.controller.mqtt;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONException;
//import inks.service.std.eam.iotrule.A_IotMqttService;
//import inks.service.std.eam.utils.PrintColor;
//import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
//import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
//import org.eclipse.paho.client.mqttv3.MqttException;
//import org.eclipse.paho.client.mqttv3.MqttMessage;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import java.util.concurrent.atomic.AtomicBoolean;
//
///**
// * MQTT监听回调类
// */
//@Component
//public class Eam_MyMQTTCallback implements MqttCallbackExtended {
//
//    private static final Logger log = LoggerFactory.getLogger(Eam_MyMQTTCallback.class);
//    //private static boolean firstMessagePrinted = false; 首次打印
//    private final AtomicBoolean isReconnecting = new AtomicBoolean(false);
//    //手动注入
//    private final Eam_MqttConfiguration eamMqttConfiguration = Eam_SpringUtilsMQTT.getBean(Eam_MqttConfiguration.class);
//    private final A_IotMqttService aIotMqttService = Eam_SpringUtilsMQTT.getBean(A_IotMqttService.class);
//
//    private final Eam_MyMQTTClient eamMyMQTTClient;
//
//    public Eam_MyMQTTCallback(Eam_MyMQTTClient eamMyMQTTClient) {
//        this.eamMyMQTTClient = eamMyMQTTClient;
//    }
//
//    /**
//     * 丢失连接，可在这里做重连
//     * 只会调用一次
//     *
//     * @param throwable
//     */
//    @Override
//    public void connectionLost(Throwable throwable) {
//        log.error("mqtt connectionLost 连接断开，5S之后尝试重连: {}", throwable.getMessage());
//
//        // 使用AtomicBoolean确保同一时间只有一个线程在执行重连
//        if (!isReconnecting.compareAndSet(false, true)) {
//            log.warn("已有重连进程正在执行，不再重复启动");
//            return;
//        }
//
//        try {
//            long reconnectTimes = 1;
//            final int MAX_RECONNECT_ATTEMPTS = 10;
//
//            while (reconnectTimes <= MAX_RECONNECT_ATTEMPTS) {
//                try {
//                    if (Eam_MyMQTTClient.getClient().isConnected()) {
//                        log.warn("mqtt reconnect success end 重新连接 重新订阅成功");
//                        return;
//                    }
//
//                    reconnectTimes += 1;
//                    log.warn("mqtt reconnect times = {} try again... mqtt重新连接时间 {}", reconnectTimes, reconnectTimes);
//                    Eam_MyMQTTClient.getClient().reconnect();
//
//                    // 成功重连后返回
//                    if (Eam_MyMQTTClient.getClient().isConnected()) {
//                        log.warn("mqtt reconnect success end 重新连接 重新订阅成功");
//                        return;
//                    }
//
//                } catch (MqttException e) {
//                    log.error("mqtt断连异常: {}", e.getMessage());
//                    // 如果是"已在进行连接"错误，等待更长时间
//                    if (e.getMessage().contains("已在进行连接")) {
//                        log.warn("连接正在进行中，等待更长时间...");
//                        Thread.sleep(10000); // 等待10秒
//                    }
//                }
//
//                // 普通等待周期
//                Thread.sleep(5000);
//            }
//
//            log.error("MQTT重连失败，达到最大重试次数 {}", MAX_RECONNECT_ATTEMPTS);
//
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//            log.error("MQTT重连被中断", e);
//        } finally {
//            // 重置重连标志
//            isReconnecting.set(false);
//        }
//    }
//
//    // 接收到MQTT消息
//    @Override
//    public void messageArrived(String topic, MqttMessage mqttMsg) {
//        try {
//            String msg = new String(mqttMsg.getPayload());
//            PrintColor.jin("messageArrived方法： 【接收消息topic】 : " + topic + " 【接收消息内容】 : " + msg);
//
//            try {
//                // 判断是否为JSON格式数据
//                if (isValidJson(msg)) {
//                    //Todo 业务逻辑
//                    try {
//                        if (topic.startsWith("v1/dev")) {
//                            //  接收MQTT消息：通过规则引擎处理信息、存入遥测/属性、报警...
//                            aIotMqttService.mqttToRule(topic, msg);
//                        }
//                    } catch (Exception e) {
//                        log.error("执行ruleToKv逻辑异常", e);
//                    }
//                } else {
//                    //Todo 业务逻辑
//                }
//
//
//            } catch (JSONException e) {
//                log.error("JSON Format Parsing Exception : {}", msg);
//            }
//        } catch (Exception e) {
//            // 捕获所有异常，确保消息处理的异常不会导致MQTT线程终止
//            log.error("处理MQTT消息时发生未预期异常", e);
//        }
//    }
//
//
//    public static void main(String[] args) {
//        //s时间戳
//        System.out.println(System.currentTimeMillis());
//    }
//
//    /**
//     * 连接成功后的回调 可以在这个方法执行 订阅主题  生成Bean的 MqttConfiguration方法中订阅主题 出现bug
//     * 重新连接后  主题也需要再次订阅  将重新订阅主题放在连接成功后的回调 比较合理
//     *
//     * @param reconnect
//     * @param serverURI
//     */
//    @Override
//    public void connectComplete(boolean reconnect, String serverURI) {
//        log.info("MQTT 连接成功，连接方式：{}", reconnect ? "重连" : "直连");
//        try {
//            //订阅主题 配合yml配置文件:需要订阅几个主题就订阅几个
//            // 解析订阅主题 enableTopic=1,2,3 表示只开启主题1,2,3
//            String[] topics = eamMqttConfiguration.enableTopic.split(",");
//            // 逐个订阅主题
//            for (String topic : topics) {
//                try {
//                    switch (topic) {
//                        case "1":
//                            eamMyMQTTClient.subscribe(eamMqttConfiguration.topic1, 1);
//                            PrintColor.jin("【订阅】主题1：" + eamMqttConfiguration.topic1);
//                            break;
//                        case "2":
//                            eamMyMQTTClient.subscribe(eamMqttConfiguration.topic2, 1);
//                            PrintColor.jin("【订阅】主题2：" + eamMqttConfiguration.topic2);
//                            break;
//                        case "3":
//                            eamMyMQTTClient.subscribe(eamMqttConfiguration.topic3, 1);
//                            PrintColor.jin("【订阅】主题3：" + eamMqttConfiguration.topic3);
//                            break;
//                        case "4":
//                            eamMyMQTTClient.subscribe(eamMqttConfiguration.topic4, 1);
//                            PrintColor.jin("【订阅】主题4：" + eamMqttConfiguration.topic4);
//                            break;
//                        default:
//                            PrintColor.jin("未知的主题：" + topic);
//                    }
//                } catch (Exception e) {
//                    log.error("订阅主题{}失败", topic, e);
//                }
//            }
//        } catch (Exception e) {
//            log.error("连接完成后订阅主题失败", e);
//        }
//    }
//
//    /**
//     * publish后，配送完成后回调的方法
//     *
//     * @param iMqttDeliveryToken
//     */
//    @Override
//    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
//        log.info("==========deliveryComplete={}==========", iMqttDeliveryToken.isComplete());
//    }
//
//    // 编写一个方法来验证JSON字符串的有效性
//    private boolean isValidJson(String jsonStr) {
//        try {
//            JSON.parse(jsonStr);
//            return true;
//        } catch (JSONException e) {
//            return false;
//        }
//    }
//}