package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.NoRepeatSubmit;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.DmToolreturnPojo;
import inks.service.std.eam.domain.pojo.DmToolreturnitemPojo;
import inks.service.std.eam.domain.pojo.DmToolreturnitemdetailPojo;
import inks.service.std.eam.service.DmToolreturnService;
import inks.service.std.eam.service.DmToolreturnitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;


/**
 * 工装具归还主表(Dm_ToolReturn)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-19 10:16:35
 */
//@RestController
//@RequestMapping("dmToolreturn")
public class DmToolreturnController {

    @Resource
    private DmToolreturnService dmToolreturnService;
    @Resource
    private DmToolreturnitemService dmToolreturnitemService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;
    @Resource
    private SystemFeignService systemFeignService;

    private final static Logger logger = LoggerFactory.getLogger(DmToolreturnController.class);
    

    @ApiOperation(value=" 获取工装具归还主表详细信息", notes="获取工装具归还主表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.List")
    public R<DmToolreturnPojo> getEntity(String key) {
      try {
           // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolreturnService.getEntity(key, loginUser.getTenantid()));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.List")
    public R<PageInfo<DmToolreturnitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Dm_ToolReturn.CreateDate");
             // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmToolreturnService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value=" 获取工装具归还主表详细信息", notes="获取工装具归还主表详细信息", produces="application/json")
    @RequestMapping(value="/getBillEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.List")
    public R<DmToolreturnPojo> getBillEntity(String key) {
      try {
             // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolreturnService.getBillEntity(key, loginUser.getTenantid()));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getBillList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.List")
    public R<PageInfo<DmToolreturnPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
           if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Dm_ToolReturn.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmToolreturnService.getBillList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageTh",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.List")
    public R<PageInfo<DmToolreturnPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Dm_ToolReturn.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmToolreturnService.getPageTh(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    private final String moduleCode = "DxxMxxB1";

    @ApiOperation(value=" 新增工装具归还主表", notes="新增工装具归还主表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.Add")
    @NoRepeatSubmit
    public R<DmToolreturnPojo> create(@RequestBody String json) {
        try {
       DmToolreturnPojo dmToolreturnPojo = JSONArray.parseObject(json,DmToolreturnPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            // 生成单据编码RefNoUtils
            String refno = RefNoUtils.generateRefNo(moduleCode, "Dm_ToolReturn", "", tid);
            dmToolreturnPojo.setRefno(refno);
            dmToolreturnPojo.setCreateby(loginUser.getRealName());   // 创建者
            dmToolreturnPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            dmToolreturnPojo.setCreatedate(new Date());   // 创建时间
            dmToolreturnPojo.setLister(loginUser.getRealname());   // 制表
            dmToolreturnPojo.setListerid(loginUser.getUserid());    // 制表id            
            dmToolreturnPojo.setModifydate(new Date());   //修改时间
            dmToolreturnPojo.setTenantid(tid);   //租户id
            DmToolreturnPojo insertDB = this.dmToolreturnService.insert(dmToolreturnPojo);
            RefNoUtils.saveRedisRefNo(refno, moduleCode, tid);// 保存单据编码RefNoUtils
        return R.ok(insertDB);
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="修改工装具归还主表", notes="修改工装具归还主表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.Edit")
    public R<DmToolreturnPojo> update(@RequestBody String json) {
        try {
         DmToolreturnPojo dmToolreturnPojo = JSONArray.parseObject(json,DmToolreturnPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolreturnPojo.setLister(loginUser.getRealname());   // 制表
            dmToolreturnPojo.setListerid(loginUser.getUserid());    // 制表id   
            dmToolreturnPojo.setModifydate(new Date());   //修改时间
            dmToolreturnPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.dmToolreturnService.update(dmToolreturnPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除工装具归还主表", notes="删除工装具归还主表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.Delete")
    public R<Integer> delete(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
          this.dmToolreturnService.delete(key, loginUser.getTenantid());
            RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
            return R.ok(1);
   }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value=" 新增工装具归还主表Item", notes="新增工装具归还主表Item", produces="application/json") 
    @RequestMapping(value="/createItem",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.Add")
    public R<DmToolreturnitemPojo> createItem(@RequestBody String json) {
       try {
     DmToolreturnitemPojo dmToolreturnitemPojo = JSONArray.parseObject(json,DmToolreturnitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolreturnitemPojo.setTenantid(loginUser.getTenantid());
        return R.ok(this.dmToolreturnitemService.insert(dmToolreturnitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value=" 修改工装具归还主表Item", notes="修改工装具归还主表Item", produces="application/json") 
    @RequestMapping(value="/updateItem",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.Edit")
    public R<DmToolreturnitemPojo> updateItem(@RequestBody String json) {
       try {
     DmToolreturnitemPojo dmToolreturnitemPojo = JSONArray.parseObject(json,DmToolreturnitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolreturnitemPojo.setTenantid(loginUser.getTenantid());
        return R.ok(this.dmToolreturnitemService.update(dmToolreturnitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }    

    @ApiOperation(value="删除工装具归还主表Item", notes="删除工装具归还主表Item", produces="application/json")   
    @RequestMapping(value="/deleteItem",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.Delete")
    public R<Integer> deleteItem(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolreturnitemService.delete(key, loginUser.getTenantid()));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    


    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolReturn.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        DmToolreturnPojo dmToolreturnPojo = this.dmToolreturnService.getBillEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(dmToolreturnPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content ;
        if (reportsPojo != null ) {
          content = reportsPojo.getRptdata();
        } else {
          throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
       if(reportsPojo.getPagerow()>0){
       int index=0;
      // 取行余数
      index =dmToolreturnPojo.getItem().size()%reportsPojo.getPagerow();
      if(index>0){
        // 补全空白行
        for(int i=0;i<reportsPojo.getPagerow()-index;i++){
            DmToolreturnitemPojo dmToolreturnitemPojo = new DmToolreturnitemPojo();
            dmToolreturnPojo.getItem().add(dmToolreturnitemPojo);
          }
      }
     }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(dmToolreturnPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

