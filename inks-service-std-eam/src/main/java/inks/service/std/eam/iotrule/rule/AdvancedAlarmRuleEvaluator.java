package inks.service.std.eam.iotrule.rule;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

// --- 内部类定义 ---

/**
 * 存储时间戳和对应值的简单类
 */
class TimeValuePair implements Serializable {
    // 实现 Serializable 以便潜在的缓存或序列化需求
    private static final long serialVersionUID = 1L; // 版本号
    private final long timestamp;
    private final Object value;

    public TimeValuePair(long timestamp, Object value) {
        this.timestamp = timestamp;
        this.value = value;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public Object getValue() {
        return value;
    }

    @Override
    public String toString() {
        return "TimeValuePair{" +
                "timestamp=" + timestamp +
                ", value=" + value +
                '}';
    }
}

/**
 * 告警评估结果类
 * 包含告警是否触发、规则信息、遥测数据、详细条件评估结果等。
 */
@JsonInclude(JsonInclude.Include.NON_NULL) // JSON序列化时忽略null字段
class AlarmEvaluationResult implements Serializable {
    private static final long serialVersionUID = 1L;
    private String ruleId;       // 规则ID
    private String ruleName;     // 规则名称
    private String ruleType;     // 规则类型 (顶层可能是复合类型)
    private String severity;     // 告警级别
    private String deviceId;     // 设备ID
    private long timestamp;      // 数据时间戳
    private boolean triggered;   // 是否触发告警
    private boolean cleared;     // 是否满足清除条件
    private boolean debounced;   // 是否因去抖动未触发
    private String debounceInfo; // 去抖动信息
    private boolean suppressed;  // 是否被抑制 (频率或重复)
    private String suppressionReason; // 抑制原因
    private String errorMessage; // 评估过程中的错误信息
    private Map<String, Object> telemetryData; // 触发评估的遥测数据
    private List<ConditionResult> conditionResults = new ArrayList<>(); // 触发条件的评估结果列表
    private List<ConditionResult> clearConditionResults; // 清除条件的评估结果列表 (如果评估了)

    // Getters and Setters...
    public String getRuleId() { return ruleId; }
    public void setRuleId(String ruleId) { this.ruleId = ruleId; }
    public String getRuleName() { return ruleName; }
    public void setRuleName(String ruleName) { this.ruleName = ruleName; }
    public String getRuleType() { return ruleType; }
    public void setRuleType(String ruleType) { this.ruleType = ruleType; }
    public String getSeverity() { return severity; }
    public void setSeverity(String severity) { this.severity = severity; }
    public String getDeviceId() { return deviceId; }
    public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    public boolean isTriggered() { return triggered; }
    public void setTriggered(boolean triggered) { this.triggered = triggered; }
    public boolean isCleared() { return cleared; }
    public void setCleared(boolean cleared) { this.cleared = cleared; }
    public boolean isDebounced() { return debounced; }
    public void setDebounced(boolean debounced) { this.debounced = debounced; }
    public String getDebounceInfo() { return debounceInfo; }
    public void setDebounceInfo(String debounceInfo) { this.debounceInfo = debounceInfo; }
    public boolean isSuppressed() { return suppressed; }
    public void setSuppressed(boolean suppressed) { this.suppressed = suppressed; }
    public String getSuppressionReason() { return suppressionReason; }
    public void setSuppressionReason(String suppressionReason) { this.suppressionReason = suppressionReason; }
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    public Map<String, Object> getTelemetryData() { return telemetryData; }
    public void setTelemetryData(Map<String, Object> telemetryData) { this.telemetryData = telemetryData; }
    public List<ConditionResult> getConditionResults() { return conditionResults; }
    public void setConditionResults(List<ConditionResult> conditionResults) { this.conditionResults = conditionResults; }
    public List<ConditionResult> getClearConditionResults() { return clearConditionResults; }
    public void setClearConditionResults(List<ConditionResult> clearConditionResults) { this.clearConditionResults = clearConditionResults; }
}

/**
 * 单个条件的评估结果
 * 包含条件的类型、参数、实际值、期望值以及评估结果。
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
class ConditionResult implements Serializable {
    private static final long serialVersionUID = 1L;
    private String type;        // 条件类型 (SIMPLE, AND, OR, TIME_WINDOW, etc.)
    private String key;         // 关联的遥测数据键 (对于SIMPLE, TIME_WINDOW等)
    private String operation;   // 操作符 (GT, EQ, CONTAINS, MATCHES, etc.)
    private String value;       // 条件中定义的期望值/阈值
    private String actualValue; // 评估时使用的实际值
    private boolean result;     // 当前条件的评估结果 (true/false)
    private String errorMessage;// 评估此条件时的错误信息
    private List<ConditionResult> subConditions; // 子条件结果 (用于 AND/OR/NOT/DURATION)

    // --- 特定条件类型的附加信息 ---
    // TimeWindow specific
    private String aggregation; // 聚合函数 (AVG, SUM, MIN, MAX, COUNT)
    private String timeWindow;  // 时间窗口大小 (e.g., "60s")
    private Integer sampleCount;// 时间窗口内样本数量
    // ChangeRate specific
    private String unit;        // 变化率单位 (PERCENTAGE, ABSOLUTE)
    private String initialValue;// 变化率计算的初始值
    private String finalValue;  // 变化率计算的最终值
    // Pattern specific
    private String pattern;     // 正则表达式模式
    // Script specific
    private String script;      // 执行的脚本内容 (暂未实现)
    // ThresholdBoundary specific
    private String lowerBound;  // 下边界值
    private String upperBound;  // 上边界值
    private Boolean includeLower;// 是否包含下边界
    private Boolean includeUpper;// 是否包含上边界
    // Duration specific
    private String duration;    // 要求的持续时间 (e.g., "5s")
    private Boolean durationMet;// 持续时间要求是否已满足
    // TimeOfDay specific
    private String startTime;   // 开始时间 (e.g., "09:00:00")
    private String endTime;     // 结束时间 (e.g., "17:00:00")
    private List<String> daysOfWeek; // 应用的星期几列表
    private String currentTime; // 评估时的当前时间
    private String currentDay;  // 评估时的当前星期几

    // Getters and Setters...
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public String getKey() { return key; }
    public void setKey(String key) { this.key = key; }
    public String getOperation() { return operation; }
    public void setOperation(String operation) { this.operation = operation; }
    public String getValue() { return value; }
    public void setValue(String value) { this.value = value; }
    public String getActualValue() { return actualValue; }
    public void setActualValue(String actualValue) { this.actualValue = actualValue; }
    public boolean isResult() { return result; }
    public void setResult(boolean result) { this.result = result; }
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    public List<ConditionResult> getSubConditions() { return subConditions; }
    public void setSubConditions(List<ConditionResult> subConditions) { this.subConditions = subConditions; }
    public String getAggregation() { return aggregation; }
    public void setAggregation(String aggregation) { this.aggregation = aggregation; }
    public String getTimeWindow() { return timeWindow; }
    public void setTimeWindow(String timeWindow) { this.timeWindow = timeWindow; }
    public Integer getSampleCount() { return sampleCount; }
    public void setSampleCount(Integer sampleCount) { this.sampleCount = sampleCount; }
    public String getUnit() { return unit; }
    public void setUnit(String unit) { this.unit = unit; }
    public String getInitialValue() { return initialValue; }
    public void setInitialValue(String initialValue) { this.initialValue = initialValue; }
    public String getFinalValue() { return finalValue; }
    public void setFinalValue(String finalValue) { this.finalValue = finalValue; }
    public String getPattern() { return pattern; }
    public void setPattern(String pattern) { this.pattern = pattern; }
    public String getScript() { return script; }
    public void setScript(String script) { this.script = script; }
    public String getLowerBound() { return lowerBound; }
    public void setLowerBound(String lowerBound) { this.lowerBound = lowerBound; }
    public String getUpperBound() { return upperBound; }
    public void setUpperBound(String upperBound) { this.upperBound = upperBound; }
    public Boolean getIncludeLower() { return includeLower; }
    public void setIncludeLower(Boolean includeLower) { this.includeLower = includeLower; }
    public Boolean getIncludeUpper() { return includeUpper; }
    public void setIncludeUpper(Boolean includeUpper) { this.includeUpper = includeUpper; }
    public String getDuration() { return duration; }
    public void setDuration(String duration) { this.duration = duration; }
    public Boolean getDurationMet() { return durationMet; }
    public void setDurationMet(Boolean durationMet) { this.durationMet = durationMet; }
    public String getStartTime() { return startTime; }
    public void setStartTime(String startTime) { this.startTime = startTime; }
    public String getEndTime() { return endTime; }
    public void setEndTime(String endTime) { this.endTime = endTime; }
    public List<String> getDaysOfWeek() { return daysOfWeek; }
    public void setDaysOfWeek(List<String> daysOfWeek) { this.daysOfWeek = daysOfWeek; }
    public String getCurrentTime() { return currentTime; }
    public void setCurrentTime(String currentTime) { this.currentTime = currentTime; }
    public String getCurrentDay() { return currentDay; }
    public void setCurrentDay(String currentDay) { this.currentDay = currentDay; }
}

/**
 * 评估上下文类
 * 封装评估单个条件时可能需要的上下文信息。
 */
class EvaluationContext implements Serializable {
    private static final long serialVersionUID = 1L;
    private final String deviceId;             // 当前设备ID
    private final long timestamp;              // 当前数据时间戳
    private final Map<String, Object> currentTelemetry; // 当前遥测数据点

    // 可以添加其他上下文信息，例如设备元数据、关联设备状态等
    // private final Map<String, Object> deviceMetadata;

    public EvaluationContext(String deviceId, long timestamp, Map<String, Object> currentTelemetry) {
        this.deviceId = deviceId;
        this.timestamp = timestamp;
        this.currentTelemetry = currentTelemetry;
    }

    public String getDeviceId() { return deviceId; }
    public long getTimestamp() { return timestamp; }
    public Map<String, Object> getCurrentTelemetry() { return currentTelemetry; }
}

/**
 * 告警状态类
 * 存储特定设备上特定规则的当前告警状态。
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
class AlarmState implements Serializable {
    private static final long serialVersionUID = 1L;
    private boolean active = false;          // 告警是否激活
    private long startTime;                // 告警首次触发时间
    private long lastUpdateTime;           // 告警状态最后更新时间 (可能因持续触发而更新)
    private long clearTime;                // 告警清除时间 (如果已清除)
    private int occurrenceCount = 0;       // 告警触发次数 (从激活开始计数)
    private boolean acknowledged = false;    // 告警是否已被确认
    private long acknowledgeTime;          // 告警确认时间
    // 可以存储触发时的遥测快照或其他信息
    private Map<String, Object> triggeringTelemetry; // 首次触发告警时的遥测数据快照

    // Getters and Setters...
    public boolean isActive() { return active; }
    public void setActive(boolean active) { this.active = active; }
    public long getStartTime() { return startTime; }
    public void setStartTime(long startTime) { this.startTime = startTime; }
    public long getLastUpdateTime() { return lastUpdateTime; }
    public void setLastUpdateTime(long lastUpdateTime) { this.lastUpdateTime = lastUpdateTime; }
    public long getClearTime() { return clearTime; }
    public void setClearTime(long clearTime) { this.clearTime = clearTime; }
    public int getOccurrenceCount() { return occurrenceCount; }
    public void setOccurrenceCount(int occurrenceCount) { this.occurrenceCount = occurrenceCount; }
    public boolean isAcknowledged() { return acknowledged; }
    public void setAcknowledged(boolean acknowledged) { this.acknowledged = acknowledged; }
    public long getAcknowledgeTime() { return acknowledgeTime; }
    public void setAcknowledgeTime(long acknowledgeTime) { this.acknowledgeTime = acknowledgeTime; }
    public Map<String, Object> getTriggeringTelemetry() { return triggeringTelemetry; }
    public void setTriggeringTelemetry(Map<String, Object> triggeringTelemetry) { this.triggeringTelemetry = triggeringTelemetry; }
}

/**
 * 频率计数器类
 * 用于在指定时间窗口内对事件进行计数 (用于去抖动和频率抑制)。
 */
class FrequencyCounter implements Serializable {
    private static final long serialVersionUID = 1L;
    private final long timeWindowMs;          // 计数的时间窗口（毫秒）
    private final Deque<Long> timestamps = new ArrayDeque<>(); // 存储事件发生的时间戳队列

    public FrequencyCounter(long timeWindowMs) {
        this.timeWindowMs = timeWindowMs;
    }

    /**
     * 增加计数，并记录当前时间戳
     */
    public synchronized void increment() {
        long now = System.currentTimeMillis();
        timestamps.addLast(now);
        cleanup(now); // 清理旧的时间戳
    }

    /**
     * 获取当前有效时间窗口内的计数值
     */
    public synchronized int getCount() {
        cleanup(System.currentTimeMillis()); // 先清理再获取
        return timestamps.size();
    }

    /**
     * 清理掉早于 (currentTime - timeWindowMs) 的时间戳
     * @param currentTime 当前时间
     */
    public synchronized void cleanup(long currentTime) {
        long cutoff = currentTime - timeWindowMs;
        // 从队列头部移除过期的时间戳
        while (!timestamps.isEmpty() && timestamps.peekFirst() < cutoff) {
            timestamps.pollFirst();
        }
    }
}

/**
 * 内部类，用于 DURATION 条件的状态跟踪
 */
class DurationState implements Serializable {
    private static final long serialVersionUID = 1L;
    private boolean conditionMet = false; // 内部条件上次是否满足
    private long firstMetTimestamp = -1;  // 内部条件首次满足的时间戳
    private long lastUpdateTime;          // 此状态的最后更新时间 (用于缓存清理)

    public boolean isConditionMet() { return conditionMet; }
    public void setConditionMet(boolean conditionMet) { this.conditionMet = conditionMet; }
    public long getFirstMetTimestamp() { return firstMetTimestamp; }
    public void setFirstMetTimestamp(long firstMetTimestamp) { this.firstMetTimestamp = firstMetTimestamp; }
    public long getLastUpdateTime() { return lastUpdateTime; }
    public void setLastUpdateTime(long lastUpdateTime) { this.lastUpdateTime = lastUpdateTime; }
}

// --- 主要评估器类 ---

/**
 * 增强版告警规则评估器
 * 支持多种条件类型、时间窗口、去抖动、抑制、清除条件等。
 */
public class AdvancedAlarmRuleEvaluator {

    // 存储设备历史数据的缓存 <DeviceId, <DataKey, List<TimeValuePair>>>
    private static final Map<String, Map<String, List<TimeValuePair>>> deviceDataCache = new ConcurrentHashMap<>();

    // 存储当前激活的告警 <DeviceId, <RuleId, AlarmState>>
    private static final Map<String, Map<String, AlarmState>> activeAlarms = new ConcurrentHashMap<>();

    // 存储条件模板 <TemplateId, JsonNode>
    private static final Map<String, JsonNode> conditionTemplates = new ConcurrentHashMap<>();

    // 缓存过期时间（毫秒），默认24小时，用于历史数据点清理
    private static final long CACHE_EXPIRY_MS = 24 * 60 * 60 * 1000;

    // 调度器用于定期任务 (如缓存清理)
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    // 告警维度计数器 <CounterKey, FrequencyCounter> (用于去抖动和频率抑制)
    // CounterKey 通常是 "deviceId:ruleId:type" (e.g., "dev1:rule1:debounce")
    private static final Map<String, FrequencyCounter> alarmFrequencyCounters = new ConcurrentHashMap<>();

    // 存储持续条件状态 <DeviceId, <StateKey, DurationState>> (用于DURATION条件)
    // StateKey 通常是 "ruleId:duration" (因为 DURATION 是规则级别的)
    private static final Map<String, Map<String, DurationState>> durationStates = new ConcurrentHashMap<>();
    /**
     * [测试辅助方法] 清理指定设备的缓存数据。
     * 警告：仅用于测试目的，以隔离测试状态。
     * @param deviceId 要清理缓存的设备ID
     */
    // @VisibleForTesting // 如果使用 Guava
    public static void clearDeviceCacheForTesting(String deviceId) {
        deviceDataCache.remove(deviceId);
        // 可能还需要清理与该设备相关的其他状态，例如：
        activeAlarms.remove(deviceId);
        durationStates.remove(deviceId);
        // 清理与该设备相关的计数器 (需要迭代或更精细的键设计)
        alarmFrequencyCounters.keySet().removeIf(key -> key.startsWith(deviceId + ":"));
    }

    // 静态初始化块，启动定时清理任务
    static {
        // 每小时执行一次清理任务
        scheduler.scheduleAtFixedRate(AdvancedAlarmRuleEvaluator::cleanupCache, 1, 1, TimeUnit.HOURS);
    }

    /**
     * 注册条件模板
     * @param templateId 模板ID
     * @param templateJson 模板JSON字符串
     */
    public static void registerConditionTemplate(String templateId, String templateJson) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode templateNode = mapper.readTree(templateJson);
            conditionTemplates.put(templateId, templateNode);
        } catch (IOException e) {
            // 在实际应用中，应该使用日志记录错误
            System.err.println("解析模板JSON出错: " + templateId + ", Error: " + e.getMessage());
            // 可以选择抛出运行时异常或者记录错误后继续
            // throw new RuntimeException("解析模板JSON出错: " + e.getMessage(), e);
        }
    }

    /**
     * 根据JSON格式的规则定义和遥测数据判断是否触发告警 (主入口方法)
     *
     * @param telemetryJson JSON格式的遥测数据
     * @param ruleJson JSON格式的规则定义
     * @return 告警评估结果 {@link AlarmEvaluationResult}
     */
    public static AlarmEvaluationResult evaluateRule(String telemetryJson, String ruleJson) {
        ObjectMapper mapper = new ObjectMapper(); // 建议将 ObjectMapper 作为成员变量或静态变量以提高性能
        try {
            // 解析规则和遥测数据
            JsonNode ruleNode = mapper.readTree(ruleJson);
            // 使用 Fastjson 解析遥测数据可能更符合用户习惯，但这里保持 Jackson 一致性
            // Map<String, Object> telemetry = com.alibaba.fastjson.JSON.parseObject(telemetryJson, Map.class);
            @SuppressWarnings("unchecked") // Jackson 解析到通用 Map 时需要类型转换或抑制警告
            Map<String, Object> telemetry = mapper.readValue(telemetryJson, Map.class);

            return evaluateRule(telemetry, ruleNode);
        } catch (IOException e) {
            // 发生解析错误，返回包含错误信息的评估结果
            AlarmEvaluationResult errorResult = new AlarmEvaluationResult();
            errorResult.setTriggered(false);
            errorResult.setErrorMessage("解析规则或遥测JSON出错: " + e.getMessage());
            // 可以在这里添加更多上下文信息，如尝试解析的 ruleId (如果可能)
            return errorResult;
        }
    }

    /**
     * 根据已解析的规则定义和遥测数据判断是否触发告警 (核心评估逻辑)
     *
     * @param telemetry 遥测数据Map
     * @param ruleNode 规则JsonNode
     * @return 告警评估结果 {@link AlarmEvaluationResult}
     */
    public static AlarmEvaluationResult evaluateRule(Map<String, Object> telemetry, JsonNode ruleNode) {
        AlarmEvaluationResult result = new AlarmEvaluationResult();
        String ruleId = null; // 先声明 ruleId

        try {
            // 1. 初始化评估结果对象
            ruleId = ruleNode.path("id").asText("rule-" + UUID.randomUUID().toString().substring(0, 8)); // 提供默认ID
            result.setRuleId(ruleId);
            result.setRuleName(ruleNode.path("name").asText("未命名规则"));
            // ruleType 可能在解析条件时确定，这里可以先设个默认值或留空
            result.setRuleType(ruleNode.path("condition").path("type").asText("UNKNOWN"));
            result.setSeverity(ruleNode.path("severity").asText("WARNING")); // 默认告警级别
            result.setTelemetryData(telemetry);

            // 2. 获取设备ID和时间戳
            String deviceId = telemetry.getOrDefault("deviceId",
                    telemetry.getOrDefault("sn", "unknown")).toString();
            if ("unknown".equals(deviceId) && telemetry.containsKey("entityId")) {
                deviceId = telemetry.get("entityId").toString(); // 兼容 entityId
            }
            result.setDeviceId(deviceId);

            long timestamp;
            // 尝试从不同字段获取时间戳，提供灵活性
            if (telemetry.containsKey("timestamp") && telemetry.get("timestamp") instanceof Number) {
                timestamp = ((Number) telemetry.get("timestamp")).longValue();
            } else if (telemetry.containsKey("ts") && telemetry.get("ts") instanceof Number) {
                timestamp = ((Number) telemetry.get("ts")).longValue();
            } else {
                timestamp = System.currentTimeMillis(); // 回退到当前系统时间
            }
            result.setTimestamp(timestamp);

            // 3. 更新设备历史数据缓存
            updateDeviceDataCache(deviceId, telemetry, timestamp);

            // 4. 解析和评估触发条件
            JsonNode conditionNode = ruleNode.path("condition");

            // 4.1 处理条件模板
            if (conditionNode.has("templateId")) {
                String templateId = conditionNode.path("templateId").asText();
                JsonNode templateNode = conditionTemplates.get(templateId);
                if (templateNode != null) {
                    // 合并模板和当前条件定义 (当前定义可覆盖模板参数)
                    conditionNode = mergeTemplate(templateNode, conditionNode);
                    result.setRuleType(conditionNode.path("type").asText("UNKNOWN")); // 更新可能的类型
                } else {
                    result.setErrorMessage("找不到条件模板: " + templateId);
                    result.setTriggered(false);
                    return result; // 模板不存在，无法继续评估
                }
            }

            // 4.2 创建评估上下文并评估条件
            EvaluationContext context = new EvaluationContext(deviceId, timestamp, telemetry);
            // 注意：将 ruleId 传递给 evaluateCondition
            boolean triggered = evaluateCondition(conditionNode, context, result.getConditionResults(), ruleId);
            result.setTriggered(triggered); // 设置初步触发状态

            // --- 后处理：去抖动、抑制、状态管理、清除条件 ---

            // 5. 应用去抖动 (Debounce)
            if (ruleNode.has("debounce")) {
                JsonNode debounceNode = ruleNode.path("debounce");
                int requiredCount = debounceNode.path("count").asInt(1); // 默认至少1次
                long windowSeconds = debounceNode.path("timeWindow").asLong(60); // 默认60秒窗口
                long windowMs = windowSeconds * 1000;

                String debounceKey = deviceId + ":" + ruleId + ":debounce"; // 唯一的去抖动计数器键
                FrequencyCounter counter = alarmFrequencyCounters.computeIfAbsent(
                        debounceKey, k -> new FrequencyCounter(windowMs));

                if (triggered) { // 只有当基础条件满足时才考虑去抖动计数
                    counter.increment(); // 增加计数
                    int currentCount = counter.getCount();
                    if (currentCount < requiredCount) {
                        // 未达到要求的触发次数，取消本次触发
                        result.setTriggered(false);
                        result.setDebounced(true);
                        result.setDebounceInfo(String.format("计数 %d/%d (窗口 %ds)", currentCount, requiredCount, windowSeconds));
                    }
                    // else: 达到次数，保持 triggered = true
                }
                // 注意：如果 'triggered' 为 false，当前实现不会重置计数器，允许其自然过期。
                // 如果需要每次不满足条件时都重置，可以在这里添加 alarmFrequencyCounters.remove(debounceKey);
            }

            // 6. 检查频率抑制 (仅当告警尝试触发时检查)
            if (result.isTriggered() && ruleNode.has("frequencySuppression")) {
                JsonNode freqNode = ruleNode.path("frequencySuppression");
                int maxCount = freqNode.path("maxCount").asInt(5); // 默认窗口内最多5次
                long timeWindowSeconds = freqNode.path("timeWindow").asLong(300); // 默认5分钟窗口
                long timeWindowMs = timeWindowSeconds * 1000;

                String freqKey = deviceId + ":" + ruleId + ":freq"; // 唯一的频率抑制计数器键
                FrequencyCounter counter = alarmFrequencyCounters.computeIfAbsent(
                        freqKey, k -> new FrequencyCounter(timeWindowMs));
                counter.increment(); // 每次评估触发都计数

                int currentCount = counter.getCount();
                if (currentCount > maxCount) {
                    // 超过频率限制，抑制本次触发
                    result.setTriggered(false);
                    result.setSuppressed(true);
                    result.setSuppressionReason(String.format("频率超限: %d次/%ds (最多%d次)",
                            currentCount, timeWindowSeconds, maxCount));
                }
            }

            // 7. 检查重复抑制 (仅当告警尝试触发时检查)
            String repeatSuppression = ruleNode.path("repeatSuppression").asText("NONE"); // NONE 或 SUPPRESS_UNTIL_CLEAR
            if (result.isTriggered() && "SUPPRESS_UNTIL_CLEAR".equals(repeatSuppression)) {
                AlarmState existingAlarmState = getAlarmState(deviceId, ruleId);
                // 如果告警已激活且未被确认，则抑制重复触发
                if (existingAlarmState != null && existingAlarmState.isActive() && !existingAlarmState.isAcknowledged()) {
                    result.setTriggered(false); // 抑制触发
                    result.setSuppressed(true);
                    result.setSuppressionReason("告警已激活，抑制重复触发 (等待清除或确认)");
                }
            }

            // 8. 处理告警状态 和 评估清除条件
            AlarmState alarmState = getAlarmState(deviceId, ruleId);
            boolean previouslyActive = alarmState != null && alarmState.isActive();

            // 8.1 评估清除条件 (仅当告警之前是激活状态时才有意义)
            boolean cleared = false;
            JsonNode clearConditionNode = ruleNode.path("clearCondition");
            if (!clearConditionNode.isMissingNode() && previouslyActive) {
                EvaluationContext clearContext = new EvaluationContext(deviceId, timestamp, telemetry);
                List<ConditionResult> clearResults = new ArrayList<>();
                // 注意：将 ruleId 传递给 evaluateCondition
                cleared = evaluateCondition(clearConditionNode, clearContext, clearResults, ruleId);
                result.setClearConditionResults(clearResults); // 记录清除条件的评估详情
                result.setCleared(cleared); // 设置最终的清除状态
            } else {
                // 如果告警之前未激活，或者没有定义清除条件，则不能被清除
                result.setCleared(false);
            }


            // 8.2 更新告警状态机 (State Machine Logic)
            if (result.isTriggered()) {
                // --- 状态转移: (*) -> Active ---
                // 告警被触发 (满足触发条件且未被抑制/去抖动)
                alarmState = getOrCreateAlarmState(deviceId, ruleId);
                if (!alarmState.isActive()) {
                    // 从 非激活 -> 激活 (首次触发或清除后再次触发)
                    alarmState.setActive(true);
                    alarmState.setStartTime(result.getTimestamp());
                    alarmState.setLastUpdateTime(result.getTimestamp());
                    alarmState.setOccurrenceCount(1);
                    alarmState.setAcknowledged(false); // 新告警总是未确认
                    alarmState.setAcknowledgeTime(0);
                    alarmState.setClearTime(0);         // 重置清除时间
                    alarmState.setTriggeringTelemetry(deepCopyMap(telemetry)); // 记录触发时的遥测数据快照
                } else {
                    // 从 激活 -> 激活 (持续触发)
                    alarmState.setLastUpdateTime(result.getTimestamp());
                    alarmState.setOccurrenceCount(alarmState.getOccurrenceCount() + 1);
                    // 确认状态不变
                }
            } else if (cleared && previouslyActive) {
                // --- 状态转移: Active -> Inactive (Cleared) ---
                // 告警被清除 (由清除条件触发)
                if (alarmState != null) { // 理论上 alarmState 不会是 null
                    alarmState.setActive(false);
                    alarmState.setClearTime(result.getTimestamp());
                    // 清除时，重置相关计数器和状态，防止旧状态影响下次触发
                    alarmFrequencyCounters.remove(deviceId + ":" + ruleId + ":debounce");
                    alarmFrequencyCounters.remove(deviceId + ":" + ruleId + ":freq");
                    durationStates.computeIfAbsent(deviceId, k -> new ConcurrentHashMap<>()).remove(ruleId + ":duration"); // 清理持续状态
                }
            } else if (!triggered && !previouslyActive) {
                // --- 状态转移: Inactive -> Inactive ---
                // 未触发，且之前也未激活 -> 无状态变化
            } else if (!triggered && previouslyActive && !cleared) {
                // --- 状态转移: Active -> Active ---
                // 未触发(或被抑制/去抖动)，但之前已激活，且未满足清除条件 -> 状态保持激活
                if(alarmState != null) {
                    // 可以选择更新 lastUpdateTime，表示规则仍在评估此活动告警
                    // alarmState.setLastUpdateTime(result.getTimestamp());
                }
            }

            return result; // 返回最终的评估结果

        } catch (Exception e) {
            // 捕获评估过程中任何未预料的异常
            System.err.printf("评估规则 '%s' 出错: %s%n", ruleId != null ? ruleId : "未知", e.getMessage());
            // e.printStackTrace(); // 打印堆栈信息以便调试

            AlarmEvaluationResult errorResult = new AlarmEvaluationResult();
            errorResult.setTriggered(false);
            errorResult.setErrorMessage("评估规则时发生内部错误: " + e.getMessage());
            // 填充已知信息
            errorResult.setRuleId(ruleId != null ? ruleId : "未知");
            if (telemetry != null) {
                errorResult.setDeviceId(telemetry.getOrDefault("deviceId", telemetry.getOrDefault("sn", "unknown")).toString());
                result.setTelemetryData(telemetry);
            }
            return errorResult;
        }
    }

    /**
     * 合并模板和实例条件。实例中的字段会覆盖模板中的同名字段。
     * @param templateNode 模板 JsonNode
     * @param instanceNode 实例条件 JsonNode (包含 templateId 及可能覆盖的字段)
     * @return 合并后的 JsonNode
     */
    private static JsonNode mergeTemplate(JsonNode templateNode, JsonNode instanceNode) {
        // 创建模板的可变副本
        ObjectNode mergedNode = templateNode.deepCopy();
        // 遍历实例节点中的所有字段
        Iterator<Map.Entry<String, JsonNode>> fields = instanceNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            // 不合并 "templateId" 字段本身，其他字段直接覆盖或添加到合并后的节点
            if (!field.getKey().equals("templateId")) {
                mergedNode.set(field.getKey(), field.getValue());
            }
        }
        return mergedNode;
    }

    /**
     * 获取告警状态 (线程安全)
     * @param deviceId 设备ID
     * @param ruleId 规则ID
     * @return 告警状态对象，如果不存在则返回 null
     */
    private static AlarmState getAlarmState(String deviceId, String ruleId) {
        Map<String, AlarmState> deviceAlarms = activeAlarms.get(deviceId);
        if (deviceAlarms == null) {
            return null;
        }
        return deviceAlarms.get(ruleId);
    }

    /**
     * 获取或创建告警状态 (线程安全)
     * @param deviceId 设备ID
     * @param ruleId 规则ID
     * @return 现有或新创建的告警状态对象
     */
    private static AlarmState getOrCreateAlarmState(String deviceId, String ruleId) {
        // 使用 ConcurrentHashMap 的 computeIfAbsent 保证原子性和线程安全
        Map<String, AlarmState> deviceAlarms = activeAlarms.computeIfAbsent(deviceId, k -> new ConcurrentHashMap<>());
        // 同样使用 computeIfAbsent 创建 AlarmState
        return deviceAlarms.computeIfAbsent(ruleId, k -> new AlarmState());
    }

    /**
     * 更新设备数据缓存 (线程安全)
     * @param deviceId 设备ID
     * @param telemetry 遥测数据Map
     * @param timestamp 数据时间戳
     */
    private static void updateDeviceDataCache(String deviceId, Map<String, Object> telemetry, long timestamp) {
        // 获取设备对应的缓存，如果不存在则创建 (线程安全)
        Map<String, List<TimeValuePair>> deviceCache = deviceDataCache.computeIfAbsent(deviceId, k -> new ConcurrentHashMap<>());

        for (Map.Entry<String, Object> entry : telemetry.entrySet()) {
            String key = entry.getKey();
            // 跳过一些常见的元数据字段，避免缓存不必要的键
            if (key.equals("deviceId") || key.equals("sn") || key.equals("ts") ||
                    key.equals("timestamp") || key.equals("entityId") || key.equals("model") ||
                    key.equals("type") || key.equals("_id")) { // 添加常见的MongoDB ID字段
                continue;
            }

            Object value = entry.getValue();
            // 可以选择是否缓存 null 值，这里选择跳过 null 值
            if (value == null) continue;

            // 获取或创建该数据键对应的历史值列表 (线程安全)
            // 使用 Collections.synchronizedList 包装 ArrayList 提供基本的线程安全
            List<TimeValuePair> timeValueList = deviceCache.computeIfAbsent(key, k -> Collections.synchronizedList(new ArrayList<>()));

            // 对列表的操作需要同步块保证原子性 (添加 + 排序 + 裁剪)
            synchronized (timeValueList) {
                timeValueList.add(new TimeValuePair(timestamp, value));

                // 按时间戳降序排序 (最新的数据在列表前面)
                timeValueList.sort(Comparator.comparingLong(TimeValuePair::getTimestamp).reversed());

                // 限制列表大小，例如最多保留最近的100个历史值
                int maxSize = 100;
                if (timeValueList.size() > maxSize) {
                    // subList 返回的是视图，需要创建新的 List 来存储裁剪后的结果
                    List<TimeValuePair> trimmedList = new ArrayList<>(timeValueList.subList(0, maxSize));
                    // 清空旧列表并添加回裁剪后的元素 (或者直接用新列表替换，但computeIfAbsent返回的是旧引用)
                    timeValueList.clear();
                    timeValueList.addAll(trimmedList);
                    // 或者: deviceCache.put(key, Collections.synchronizedList(trimmedList)); // 如果不依赖 computeIfAbsent 的引用
                }
            }
        }
    }


    /**
     * 定期清理过期缓存和状态 (由调度器调用)
     */
    private static synchronized void cleanupCache() {
        // 使用 synchronized 关键字确保清理操作的原子性，防止并发问题
        long currentTime = System.currentTimeMillis();
        long dataCutoffTime = currentTime - CACHE_EXPIRY_MS; // 历史数据点的过期时间点
        long alarmStateCutoffTime = currentTime - CACHE_EXPIRY_MS; // 已清除告警状态的保留时间点 (例如24小时)
        long durationStateCutoffTime = currentTime - TimeUnit.HOURS.toMillis(6); // DURATION 状态的保留时间点 (例如6小时)

        // 1. 清理设备历史数据缓存 (deviceDataCache)
        deviceDataCache.forEach((deviceId, keyDataMap) -> {
            keyDataMap.forEach((key, dataList) -> {
                // 对列表操作加锁
                synchronized (dataList) {
                    // 移除时间戳早于 cutoffTime 的数据点
                    dataList.removeIf(pair -> pair.getTimestamp() < dataCutoffTime);
                }
            });
            // 移除数据列表为空的数据键条目
            keyDataMap.entrySet().removeIf(entry -> {
                synchronized (entry.getValue()) {
                    return entry.getValue().isEmpty();
                }
            });
        });
        // 移除没有任何数据键的设备条目
        deviceDataCache.entrySet().removeIf(entry -> entry.getValue().isEmpty());


        // 2. 清理告警频率计数器 (alarmFrequencyCounters)
        alarmFrequencyCounters.entrySet().removeIf(entry -> {
            FrequencyCounter counter = entry.getValue();
            counter.cleanup(currentTime); // 清理计数器内部过期的时间戳
            // 如果清理后计数为0，则可以从缓存中移除该计数器
            return counter.getCount() == 0;
        });


        // 3. 清理过期的非活动告警状态 (activeAlarms)
        activeAlarms.forEach((deviceId, alarmMap) -> {
            alarmMap.entrySet().removeIf(entry -> {
                AlarmState state = entry.getValue();
                // 移除那些已清除 (inactive) 且清除时间早于 alarmStateCutoffTime 的告警状态
                return !state.isActive() && state.getClearTime() > 0 &&
                        state.getClearTime() < alarmStateCutoffTime;
            });
        });
        // 移除没有任何告警状态的设备条目
        activeAlarms.entrySet().removeIf(entry -> entry.getValue().isEmpty());

        // 4. 清理过期的 DURATION 状态 (durationStates)
        durationStates.forEach((deviceId, stateMap) -> {
            // 移除最后更新时间早于 durationStateCutoffTime 的状态条目
            stateMap.entrySet().removeIf(entry -> entry.getValue().getLastUpdateTime() < durationStateCutoffTime);
        });
        // 移除没有任何 DURATION 状态的设备条目
        durationStates.entrySet().removeIf(entry -> entry.getValue().isEmpty());

        // System.out.println("Cache cleanup completed at " + Instant.now()); // 日志记录清理完成
    }

    /**
     * 告警确认 (外部调用接口)
     * @param deviceId 设备ID
     * @param ruleId   规则ID
     * @return 如果成功确认返回 true，否则返回 false (例如告警不存在或未激活)
     */
    public static boolean acknowledgeAlarm(String deviceId, String ruleId) {
        AlarmState state = getAlarmState(deviceId, ruleId);
        // 只能确认处于激活状态且尚未被确认的告警
        if (state != null && state.isActive() && !state.isAcknowledged()) {
            state.setAcknowledged(true);
            state.setAcknowledgeTime(System.currentTimeMillis());
            // 确认告警后，可能需要重置抑制状态？(取决于业务需求)
            // 如果 repeatSuppression 是 SUPPRESS_UNTIL_CLEAR，确认本身不清除抑制
            return true;
        }
        return false;
    }

    /**
     * 手动清除告警 (外部调用接口)
     * @param deviceId 设备ID
     * @param ruleId   规则ID
     * @return 如果成功清除返回 true，否则返回 false (例如告警不存在或已清除)
     */
    public static boolean clearAlarm(String deviceId, String ruleId) {
        AlarmState state = getAlarmState(deviceId, ruleId);
        // 只能清除处于激活状态的告警
        if (state != null && state.isActive()) {
            state.setActive(false);
            state.setClearTime(System.currentTimeMillis());
            state.setAcknowledged(false); // 清除后重置确认状态
            state.setAcknowledgeTime(0);
            // 手动清除时，也需要重置相关计数器和状态
            alarmFrequencyCounters.remove(deviceId + ":" + ruleId + ":debounce");
            alarmFrequencyCounters.remove(deviceId + ":" + ruleId + ":freq");
            durationStates.computeIfAbsent(deviceId, k-> new ConcurrentHashMap<>()).remove(ruleId + ":duration");
            return true;
        }
        return false;
    }

    // --- 条件评估方法 ---

    /**
     * 递归评估条件节点 (核心分发方法)
     * @param conditionNode 当前要评估的条件 JsonNode
     * @param context       评估上下文
     * @param results       用于收集评估结果的列表
     * @param ruleId        当前规则的 ID (用于 DURATION 等需要唯一键的状态)
     * @return              当前条件的评估结果 (true/false)
     */
    private static boolean evaluateCondition(JsonNode conditionNode, EvaluationContext context,
                                             List<ConditionResult> results, String ruleId) {
        // 获取条件类型，默认为 UNKNOWN
        String type = conditionNode.path("type").asText("UNKNOWN");

        // 创建用于存储此条件评估详情的对象
        ConditionResult currentResult = new ConditionResult();
        currentResult.setType(type); // 设置类型

        boolean evaluationOutcome; // 存储此条件的最终布尔结果

        try {
            // 根据条件类型分发到具体的评估方法
            switch (type) {
                case "SIMPLE":
                    evaluationOutcome = evaluateSimpleCondition(conditionNode, context, currentResult);
                    break;
                case "AND":
                    // 注意：AND/OR 的子条件评估需要传递 ruleId
                    evaluationOutcome = evaluateCompoundCondition(conditionNode, context, currentResult, true, ruleId);
                    break;
                case "OR":
                    evaluationOutcome = evaluateCompoundCondition(conditionNode, context, currentResult, false, ruleId);
                    break;
                case "NOT":
                    // 注意：NOT 的子条件评估需要传递 ruleId
                    evaluationOutcome = evaluateNotCondition(conditionNode, context, currentResult, ruleId);
                    break;
                case "TIME_WINDOW":
                    evaluationOutcome = evaluateTimeWindowCondition(conditionNode, context, currentResult);
                    break;
                case "CHANGE_RATE":
                    evaluationOutcome = evaluateChangeRateCondition(conditionNode, context, currentResult);
                    break;
                case "PATTERN":
                    evaluationOutcome = evaluatePatternCondition(conditionNode, context, currentResult);
                    break;
                case "SCRIPT":
                    evaluationOutcome = evaluateScriptCondition(conditionNode, context, currentResult);
                    break;
                case "THRESHOLD_BOUNDARY":
                    evaluationOutcome = evaluateThresholdBoundaryCondition(conditionNode, context, currentResult);
                    break;
                case "DURATION":
                    // 注意：DURATION 的子条件评估需要传递 ruleId
                    evaluationOutcome = evaluateDurationCondition(conditionNode, context, currentResult, ruleId);
                    break;
                case "DEVICE_RELATION":
                    evaluationOutcome = evaluateDeviceRelationCondition(conditionNode, context, currentResult);
                    break;
                case "TIME_OF_DAY":
                    evaluationOutcome = evaluateTimeOfDayCondition(conditionNode, context, currentResult);
                    break;
                default:
                    // 未知条件类型，记录错误并返回 false
                    currentResult.setErrorMessage("未知的条件类型: " + type);
                    evaluationOutcome = false;
                    break;
            }
            // 将最终评估结果设置到 ConditionResult 对象中
            currentResult.setResult(evaluationOutcome);

        } catch (Exception e) {
            // 捕获具体条件评估方法中可能抛出的异常
            System.err.printf("评估条件类型 '%s' (规则 '%s') 时出错: %s%n", type, ruleId, e.getMessage());
            // e.printStackTrace(); // 调试时可以取消注释

            currentResult.setResult(false); // 出错时结果为 false
            currentResult.setErrorMessage(String.format("评估条件 '%s' 时发生内部错误: %s", type, e.getMessage()));
            evaluationOutcome = false;
        }

        // 将当前条件的评估结果（无论成功失败）添加到结果列表中
        results.add(currentResult);

        // 返回当前条件的布尔评估结果
        return evaluationOutcome;
    }

    /**
     * 评估简单条件 (SIMPLE)
     * 例如：temperature > 30
     */
    private static boolean evaluateSimpleCondition(JsonNode conditionNode, EvaluationContext context,
                                                   ConditionResult condResult) {
        // 提取条件参数
        String key = conditionNode.path("key").asText();
        String operation = conditionNode.path("operation").asText();
        JsonNode valueNode = conditionNode.path("value"); // 预期的值 (阈值)

        // 记录条件的配置信息到结果对象
        condResult.setKey(key);
        condResult.setOperation(operation);

        // 解析预期值
        Object expectedValue = parseJsonValue(valueNode);
        condResult.setValue(expectedValue != null ? expectedValue.toString() : "null");

        // 从遥测数据中获取实际值
        Map<String, Object> telemetry = context.getCurrentTelemetry();
        Object actualValue = telemetry.get(key);
        condResult.setActualValue(actualValue != null ? actualValue.toString() : "未找到"); // "未找到" 或 "null"

        // 处理实际值不存在的情况
        if (actualValue == null) {
            // 特殊处理 IS_NULL 和 IS_NOT_NULL 操作
            if ("IS_NULL".equalsIgnoreCase(operation)) return true;
            if ("IS_NOT_NULL".equalsIgnoreCase(operation)) return false;
            // 对于其他操作，如果 key 不存在，通常认为条件不满足
            condResult.setErrorMessage("遥测数据中未找到键: " + key);
            return false;
        }
        // 如果实际值存在，处理 IS_NULL 和 IS_NOT_NULL
        if ("IS_NULL".equalsIgnoreCase(operation)) return false;
        if ("IS_NOT_NULL".equalsIgnoreCase(operation)) return true;

        // 执行核心比较操作
        return performOperation(operation, actualValue, expectedValue);
    }

    /**
     * 评估复合条件 (AND/OR)
     * @param isAnd true 表示 AND，false 表示 OR
     * @param ruleId 规则ID，传递给子条件评估
     */
    private static boolean evaluateCompoundCondition(JsonNode conditionNode, EvaluationContext context,
                                                     ConditionResult groupResult, boolean isAnd, String ruleId) {
        JsonNode conditions = conditionNode.path("conditions");
        // 校验 conditions 字段是否存在且为数组
        if (!conditions.isArray() || conditions.isEmpty()) {
            groupResult.setErrorMessage("'conditions' 必须是一个包含至少一个条件的数组");
            return false; // 空的复合条件通常为 false (AND) 或 false (OR)
        }

        // 创建列表存储子条件的评估结果
        List<ConditionResult> subResults = new ArrayList<>();
        groupResult.setSubConditions(subResults); // 关联子结果列表

        boolean overallResult = isAnd; // AND 初始为 true, OR 初始为 false

        // 遍历并递归评估每个子条件
        for (JsonNode subCondition : conditions) {
            // 递归调用 evaluateCondition，传递 ruleId
            boolean subResult = evaluateCondition(subCondition, context, subResults, ruleId);
            if (isAnd) {
                // AND 逻辑：一旦遇到 false，整体结果即为 false，可以提前退出
                if (!subResult) {
                    overallResult = false;
                    break;
                }
            } else { // OR 逻辑
                // OR 逻辑：一旦遇到 true，整体结果即为 true，可以提前退出
                if (subResult) {
                    overallResult = true;
                    break;
                }
            }
        }
        // 返回最终的复合条件评估结果
        return overallResult;
    }

    /**
     * 评估 NOT 条件
     * @param ruleId 规则ID，传递给子条件评估
     */
    private static boolean evaluateNotCondition(JsonNode conditionNode, EvaluationContext context,
                                                ConditionResult notResult, String ruleId) {
        JsonNode subConditionNode = conditionNode.path("condition");
        // 校验 'condition' 字段是否存在且为对象
        if (subConditionNode.isMissingNode() || !subConditionNode.isObject()) {
            notResult.setErrorMessage("NOT 条件缺少有效的 'condition' 字段");
            return false; // 对无效条件取反通常没有意义，返回 false
        }

        // 创建列表存储子条件的评估结果
        List<ConditionResult> subResults = new ArrayList<>();
        notResult.setSubConditions(subResults); // 关联子结果列表

        // 递归评估内部的子条件，传递 ruleId
        boolean subEvaluationResult = evaluateCondition(subConditionNode, context, subResults, ruleId);

        // NOT 条件的核心逻辑：对子条件结果取反
        return !subEvaluationResult;
    }

    /**
     * 评估时间窗口条件 (TIME_WINDOW)
     * 例如：过去 5 分钟的平均温度 > 25
     */
    private static boolean evaluateTimeWindowCondition(JsonNode conditionNode, EvaluationContext context,
                                                       ConditionResult condResult) {
        // 提取参数
        String key = conditionNode.path("key").asText();
        String aggregation = conditionNode.path("aggregation").asText("AVG"); // 默认 AVG
        long timeWindowSeconds = conditionNode.path("timeWindow").asLong(60); // 默认 60 秒
        long timeWindowMs = timeWindowSeconds * 1000;
        String operation = conditionNode.path("operation").asText();
        JsonNode valueNode = conditionNode.path("value"); // 聚合后的阈值

        // 记录配置信息
        condResult.setKey(key);
        condResult.setAggregation(aggregation);
        condResult.setTimeWindow(timeWindowSeconds + "s");
        condResult.setOperation(operation);

        Object thresholdValue = parseJsonValue(valueNode);
        condResult.setValue(thresholdValue != null ? thresholdValue.toString() : "null");

        // 获取时间窗口内的数据
        Map<String, List<TimeValuePair>> deviceCache = deviceDataCache.getOrDefault(context.getDeviceId(), Collections.emptyMap());
        List<TimeValuePair> keyHistory = deviceCache.getOrDefault(key, Collections.emptyList());

        long windowStart = context.getTimestamp() - timeWindowMs;
        List<TimeValuePair> windowData;
        // 从缓存获取数据时需要同步访问列表
        synchronized (keyHistory) {
            // 过滤出时间窗口内的数据点，并创建副本以避免迭代时修改
            windowData = keyHistory.stream()
                    .filter(pair -> pair.getTimestamp() >= windowStart)
                    .collect(Collectors.toList());
        }
        condResult.setSampleCount(windowData.size()); // 记录窗口内的样本数

        // 处理窗口内无数据的情况
        if (windowData.isEmpty() && !"COUNT".equalsIgnoreCase(aggregation)) {
            condResult.setActualValue("时间窗口内无数据");
            // 对 null 执行比较操作 (通常为 false，除非操作是 IS_NULL)
            return performOperation(operation, null, thresholdValue);
        }

        // 计算聚合值
        Object aggregatedValue = calculateAggregation(windowData, aggregation);
        condResult.setActualValue(aggregatedValue != null ? aggregatedValue.toString() : "计算结果为null");

        // 处理聚合结果为 null (通常发生在 AVG, SUM, MIN, MAX 且窗口内无有效数值时)
        if (aggregatedValue == null && !"COUNT".equalsIgnoreCase(aggregation)) {
            // COUNT 聚合结果为 0 是有效值，其他聚合为 null 通常不满足条件
            return performOperation(operation, null, thresholdValue);
        }

        // 评估条件：比较聚合值和阈值
        return performOperation(operation, aggregatedValue, thresholdValue);
    }

    /**
     * 评估变化率条件 (CHANGE_RATE)
     * 例如：温度在过去 10 分钟内上升超过 5 度 (ABSOLUTE)
     * 或：湿度在过去 1 小时内下降超过 10% (PERCENTAGE)
     */
    private static boolean evaluateChangeRateCondition(JsonNode conditionNode, EvaluationContext context,
                                                       ConditionResult condResult) {
        // 提取参数
        String key = conditionNode.path("key").asText();
        long timeWindowSeconds = conditionNode.path("timeWindow").asLong(60); // 默认 60 秒
        long timeWindowMs = timeWindowSeconds * 1000;
        String operation = conditionNode.path("operation").asText(); // GT, LT, etc.
        JsonNode valueNode = conditionNode.path("value"); // 变化率阈值
        String unit = conditionNode.path("unit").asText("PERCENTAGE"); // PERCENTAGE or ABSOLUTE

        // 记录配置信息
        condResult.setKey(key);
        condResult.setTimeWindow(timeWindowSeconds + "s");
        condResult.setOperation(operation);
        condResult.setUnit(unit);

        // 校验阈值必须是数字
        if (!valueNode.isNumber()) {
            condResult.setErrorMessage("变化率阈值 'value' 必须是数字");
            return false;
        }
        double rateThreshold = valueNode.asDouble();
        condResult.setValue(String.valueOf(rateThreshold));

        // 获取时间窗口内的数据
        Map<String, List<TimeValuePair>> deviceCache = deviceDataCache.getOrDefault(context.getDeviceId(), Collections.emptyMap());
        List<TimeValuePair> keyHistory = deviceCache.getOrDefault(key, Collections.emptyList());

        long windowStart = context.getTimestamp() - timeWindowMs;
        List<TimeValuePair> windowData;
        // 同步访问历史数据列表
        synchronized (keyHistory) {
            // 过滤窗口内数据，并按时间升序排序，最后创建副本
            windowData = keyHistory.stream()
                    .filter(pair -> pair.getTimestamp() >= windowStart)
                    .sorted(Comparator.comparingLong(TimeValuePair::getTimestamp)) // 按时间升序
                    .collect(Collectors.toList());
        }
        condResult.setSampleCount(windowData.size()); // 记录样本数

        // 检查是否有足够的数据点计算变化率 (至少需要 2 个点)
        if (windowData.size() < 2) {
            condResult.setActualValue("时间窗口内数据点不足 (< 2)");
            return false;
        }

        // 获取窗口内的第一个和最后一个数据点
        TimeValuePair firstPoint = windowData.get(0);
        TimeValuePair lastPoint = windowData.get(windowData.size() - 1);

        double firstValue, lastValue;
        try {
            // 尝试将值转换为 double
            firstValue = getDoubleValue(firstPoint.getValue());
            lastValue = getDoubleValue(lastPoint.getValue());
            // 记录用于计算的初始值和最终值
            condResult.setInitialValue(String.valueOf(firstValue));
            condResult.setFinalValue(String.valueOf(lastValue));
        } catch (NumberFormatException e) {
            // 如果值无法转换为数值，则无法计算变化率
            condResult.setActualValue("数据无法转换为数值: " + e.getMessage());
            return false;
        }

        // 计算变化率
        double changeRate;
        // 计算时间差（秒），避免除以零
        double timeDiffSeconds = (lastPoint.getTimestamp() - firstPoint.getTimestamp()) / 1000.0;
        if (timeDiffSeconds <= 1e-6) { // 时间差过小，变化率无意义或可能导致除零
            // 如果需要计算单位时间的变化率（例如 度/秒 或 %/秒），则需要处理此情况
            // 对于仅比较总变化量，时间差为零可能表示两个点相同，变化率为0
            // 此处我们计算的是窗口内的总变化量，不除以时间
            // condResult.setActualValue("时间窗口内时间差过小或为零");
            // return false;
        }

        if ("PERCENTAGE".equalsIgnoreCase(unit)) {
            // 计算百分比变化率：(最终值 - 初始值) / abs(初始值) * 100
            if (Math.abs(firstValue) < 1e-9) { // 避免除以接近零的初始值
                // 如果初始值为零，百分比变化无意义，可以按绝对变化处理或报错
                changeRate = lastValue - firstValue; // 按绝对变化处理
                condResult.setActualValue(String.format("%.2f (因初始值为零按绝对值计算)", changeRate));
            } else {
                changeRate = ((lastValue - firstValue) / Math.abs(firstValue)) * 100.0;
                condResult.setActualValue(String.format("%.2f%%", changeRate));
            }
        } else { // ABSOLUTE
            // 计算绝对变化率：最终值 - 初始值
            changeRate = lastValue - firstValue;
            condResult.setActualValue(String.format("%.2f", changeRate));
        }

        // 评估条件：比较计算出的变化率和阈值
        return performOperation(operation, changeRate, rateThreshold);
    }

    /**
     * 评估模式匹配条件 (PATTERN)
     * 例如：日志消息匹配正则表达式 ".*ERROR.*"
     */
    private static boolean evaluatePatternCondition(JsonNode conditionNode, EvaluationContext context,
                                                    ConditionResult condResult) {
        // 提取参数
        String key = conditionNode.path("key").asText();
        String patternStr = conditionNode.path("pattern").asText(); // 正则表达式字符串

        // 记录配置
        condResult.setKey(key);
        condResult.setPattern(patternStr);
        condResult.setOperation("MATCHES"); // 隐含操作是正则匹配
        condResult.setValue(patternStr);    // 期望值就是模式本身

        // 获取实际值
        Map<String, Object> telemetry = context.getCurrentTelemetry();
        Object actualValueObj = telemetry.get(key);

        if (actualValueObj == null) {
            condResult.setActualValue("未找到");
            return false; // null 通常不匹配任何非空模式
        }

        // 将实际值转为字符串进行匹配
        String actualValue = actualValueObj.toString();
        condResult.setActualValue(actualValue);

        try {
            // 使用 Java 的 Pattern.matches 进行全字符串匹配
            boolean matches = Pattern.matches(patternStr, actualValue);
            return matches;
        } catch (java.util.regex.PatternSyntaxException e) {
            // 如果正则表达式语法无效
            condResult.setErrorMessage("正则表达式模式无效: " + e.getMessage());
            return false;
        }
    }

    /**
     * 评估脚本条件 (SCRIPT) - 【注意：当前为占位符实现】
     * 允许执行自定义脚本（如 JavaScript）来判断条件。
     * 警告：执行外部脚本存在严重安全风险，需要安全的沙箱环境！
     */
    private static boolean evaluateScriptCondition(JsonNode conditionNode, EvaluationContext context,
                                                   ConditionResult condResult) {
        String script = conditionNode.path("script").asText();
        condResult.setScript(script);
        condResult.setOperation("SCRIPT_EXEC");

        // --- 实际实现需要脚本引擎 (如 GraalVM JS, Nashorn (deprecated)) ---
        /*
        // Example using GraalVM JS (requires graal-sdk and js dependencies)
        try (org.graalvm.polyglot.Context polyglotCtx = org.graalvm.polyglot.Context.newBuilder("js")
                // .allowIO(false) // Configure sandbox restrictions
                // .allowHostAccess(org.graalvm.polyglot.HostAccess.NONE)
                .build()) {

            org.graalvm.polyglot.Value bindings = polyglotCtx.getBindings("js");
            // 将上下文信息安全地暴露给脚本
            bindings.putMember("telemetry", org.graalvm.polyglot.Value.asValue(context.getCurrentTelemetry()));
            bindings.putMember("deviceId", context.getDeviceId());
            bindings.putMember("timestamp", context.getTimestamp());
            // 暴露缓存和状态需要非常谨慎
            // bindings.putMember("deviceDataCache", ...);
            // bindings.putMember("activeAlarms", ...);

            org.graalvm.polyglot.Value result = polyglotCtx.eval("js", script);

            if (result.isBoolean()) {
                boolean outcome = result.asBoolean();
                condResult.setActualValue(String.valueOf(outcome));
                return outcome;
            } else {
                condResult.setErrorMessage("脚本未返回布尔值");
                condResult.setActualValue(result.toString());
                return false;
            }
        } catch (Exception e) { // Catch PolyglotException, etc.
            condResult.setErrorMessage("脚本执行错误: " + e.getMessage());
            return false;
        }
        */

        // --- 当前占位符实现 ---
        condResult.setErrorMessage("脚本条件类型 (SCRIPT) 暂未实现");
        condResult.setActualValue("N/A");
        return false; // 返回 false 表示条件不满足
    }

    /**
     * 评估阈值边界条件 (THRESHOLD_BOUNDARY)
     * 例如：值在 [10, 20] 范围内 (INSIDE)
     * 或：值小于 5 或大于 50 (OUTSIDE)
     */
    private static boolean evaluateThresholdBoundaryCondition(JsonNode conditionNode, EvaluationContext context,
                                                              ConditionResult condResult) {
        // 提取参数
        String key = conditionNode.path("key").asText();
        JsonNode lowerNode = conditionNode.path("lowerBound"); // 下边界值 (可选)
        JsonNode upperNode = conditionNode.path("upperBound"); // 上边界值 (可选)
        boolean includeLower = conditionNode.path("includeLower").asBoolean(true); // 默认包含下边界
        boolean includeUpper = conditionNode.path("includeUpper").asBoolean(true); // 默认包含上边界
        // checkType: "INSIDE" (在边界内) or "OUTSIDE" (在边界外)
        String checkType = conditionNode.path("checkType").asText("INSIDE"); // 默认检查是否在内部

        // 记录配置
        condResult.setKey(key);
        condResult.setOperation(checkType.toUpperCase() + "_BOUNDS"); // e.g., "INSIDE_BOUNDS"
        condResult.setIncludeLower(includeLower);
        condResult.setIncludeUpper(includeUpper);

        // 获取实际值
        Map<String, Object> telemetry = context.getCurrentTelemetry();
        Object actualValueObj = telemetry.get(key);

        if (actualValueObj == null) {
            condResult.setActualValue("未找到");
            return false; // null 通常不在任何数值边界内或外
        }
        condResult.setActualValue(actualValueObj.toString());

        double actualValue;
        try {
            // 尝试将实际值转为 double
            actualValue = getDoubleValue(actualValueObj);
        } catch (NumberFormatException e) {
            condResult.setErrorMessage("实际值无法转换为数值进行边界比较");
            return false;
        }

        // 解析边界值 (如果存在)
        Double lowerBound = null;
        Double upperBound = null;
        if (lowerNode.isNumber()) {
            lowerBound = lowerNode.asDouble();
            condResult.setLowerBound(String.valueOf(lowerBound));
        } else if (!lowerNode.isMissingNode()){
            condResult.setErrorMessage("下边界值 'lowerBound' 必须是数字");
            return false; // 如果提供了非数字的边界值，则条件无效
        }
        if (upperNode.isNumber()) {
            upperBound = upperNode.asDouble();
            condResult.setUpperBound(String.valueOf(upperBound));
        } else if (!upperNode.isMissingNode()){
            condResult.setErrorMessage("上边界值 'upperBound' 必须是数字");
            return false;
        }

        // 如果上下边界都没有定义，则条件无意义
        if (lowerBound == null && upperBound == null) {
            condResult.setErrorMessage("必须至少定义一个边界 (lowerBound 或 upperBound)");
            return false;
        }


        // --- 执行边界检查 ---

        // 检查是否满足下边界条件 (如果下边界存在)
        boolean lowerCheckMet = true; // 默认满足 (如果没有下边界)
        if (lowerBound != null) {
            lowerCheckMet = includeLower ? (actualValue >= lowerBound) : (actualValue > lowerBound);
        }

        // 检查是否满足上边界条件 (如果上边界存在)
        boolean upperCheckMet = true; // 默认满足 (如果没有上边界)
        if (upperBound != null) {
            upperCheckMet = includeUpper ? (actualValue <= upperBound) : (actualValue < upperBound);
        }

        // --- 根据 checkType 判断最终结果 ---

        if ("INSIDE".equalsIgnoreCase(checkType)) {
            // INSIDE: 必须同时满足下边界和上边界条件
            // 注意：如果只定义了一个边界，例如 >= lowerBound，则 upperCheckMet 始终为 true
            return lowerCheckMet && upperCheckMet;
        } else if ("OUTSIDE".equalsIgnoreCase(checkType)) {
            // OUTSIDE: 必须不满足下边界 或 不满足上边界
            // 即：小于（或小于等于）下界，或者，大于（或大于等于）上界

            // 检查是否低于下边界
            boolean belowLower = false;
            if (lowerBound != null) {
                // 注意这里的逻辑是 lowerCheckMet 的反面
                belowLower = includeLower ? (actualValue < lowerBound) : (actualValue <= lowerBound);
            }

            // 检查是否高于上边界
            boolean aboveUpper = false;
            if (upperBound != null) {
                // 注意这里的逻辑是 upperCheckMet 的反面
                aboveUpper = includeUpper ? (actualValue > upperBound) : (actualValue >= upperBound);
            }

            // OUTSIDE 的最终结果是：低于下界 或 高于上界
            // 如果只定义了一个边界，则仅判断那个边界的 OUTSIDE 条件
            if (lowerBound != null && upperBound == null) return belowLower;
            if (lowerBound == null && upperBound != null) return aboveUpper;
            // 如果定义了双边界，则满足其一即可
            if (lowerBound != null && upperBound != null) return belowLower || aboveUpper;

            //理论上不会执行到这里，因为前面检查了至少一个边界存在
            return false;

        } else {
            condResult.setErrorMessage("未知的 checkType: " + checkType + " (应为 INSIDE 或 OUTSIDE)");
            return false;
        }
    }

    /**
     * 评估持续时间条件 (DURATION)
     * 例如：状态为 "ERROR" 持续了至少 5 分钟
     * @param ruleId 规则ID，用于 DURATION 状态的唯一键
     */
    private static boolean evaluateDurationCondition(JsonNode conditionNode, EvaluationContext context,
                                                     ConditionResult condResult, String ruleId) {
        // 提取参数
        JsonNode innerConditionNode = conditionNode.path("condition"); // 内部要持续满足的条件
        long durationSeconds = conditionNode.path("duration").asLong(); // 要求的持续时间（秒）
        long durationMs = durationSeconds * 1000;

        // 记录配置
        condResult.setDuration(durationSeconds + "s");

        // 校验参数
        if (innerConditionNode.isMissingNode() || !innerConditionNode.isObject()) {
            condResult.setErrorMessage("DURATION 条件缺少有效的内部 'condition' 字段");
            return false;
        }
        if (durationMs <= 0) {
            condResult.setErrorMessage("DURATION 必须大于 0 秒");
            return false;
        }

        // --- 评估内部条件 ---
        List<ConditionResult> innerResults = new ArrayList<>();
        // 递归调用 evaluateCondition 评估内部条件，传递 ruleId
        boolean innerConditionMet = evaluateCondition(innerConditionNode, context, innerResults, ruleId);
        // 将内部条件的评估结果作为子结果附加到 DURATION 的结果中
        condResult.setSubConditions(innerResults);
        // DURATION 条件本身的 "实际值" 可以反映内部条件当前是否满足
        condResult.setActualValue(innerConditionMet ? "内部条件满足" : "内部条件不满足");

        // --- 获取或创建并更新 DURATION 状态 ---
        // StateKey 需要唯一标识这个 DURATION 条件实例 (设备ID + 规则ID + 固定后缀)
        String stateKey = ruleId + ":duration"; // 使用 ruleId 保证唯一性
        // 获取设备对应的 DURATION 状态 Map (线程安全)
        Map<String, DurationState> deviceStates = durationStates.computeIfAbsent(context.getDeviceId(), k -> new ConcurrentHashMap<>());
        // 获取或创建此 DURATION 条件的状态 (线程安全)
        DurationState state = deviceStates.computeIfAbsent(stateKey, k -> new DurationState());

        long currentTimestamp = context.getTimestamp();
        state.setLastUpdateTime(currentTimestamp); // 更新状态的最后活动时间 (用于清理)

        // --- 判断持续时间是否满足 ---
        boolean durationThresholdMet = false; // 最终 DURATION 条件是否满足
        if (innerConditionMet) {
            // --- 内部条件满足 ---
            if (!state.isConditionMet()) {
                // 状态从未满足变为满足：记录开始时间戳，标记状态为满足
                state.setConditionMet(true);
                state.setFirstMetTimestamp(currentTimestamp);
                // 第一次满足时，持续时间肯定不满足
            } else {
                // 状态之前已经是满足的：检查持续时间
                if (state.getFirstMetTimestamp() > 0 && // 确保开始时间已记录
                        (currentTimestamp - state.getFirstMetTimestamp() >= durationMs)) {
                    // 从首次满足到现在的时间差 >= 要求的持续时间
                    durationThresholdMet = true;
                }
            }
        } else {
            // --- 内部条件不满足 ---
            // 重置状态，中断持续计时
            if (state.isConditionMet() || state.getFirstMetTimestamp() != -1) { // 仅在状态需要重置时操作
                state.setConditionMet(false);
                state.setFirstMetTimestamp(-1);
            }
            // 内部条件不满足，则 DURATION 条件肯定不满足
            durationThresholdMet = false;
        }

        // 记录持续时间是否已达标到结果对象
        condResult.setDurationMet(durationThresholdMet);
        // 返回 DURATION 条件的最终评估结果
        return durationThresholdMet;
    }


    /**
     * 评估设备关联条件 (DEVICE_RELATION) - 【占位符实现】
     * 需要访问设备关系图和关联设备的数据/状态。
     */
    private static boolean evaluateDeviceRelationCondition(JsonNode conditionNode, EvaluationContext context,
                                                           ConditionResult condResult) {
        // --- 实际实现需要外部依赖和逻辑 ---
        // 1. 解析关联信息: relatedDeviceId 或 relationType + direction
        // 2. 查询设备关系服务获取关联设备 ID 列表
        // 3. 获取关联设备的最新状态或数据 (可能需要访问缓存或数据库)
        // 4. 对每个关联设备评估 'condition' 中定义的子条件
        // 5. 根据聚合逻辑 (e.g., ANY, ALL, COUNT) 决定最终结果

        condResult.setErrorMessage("设备关联条件 (DEVICE_RELATION) 暂未实现");
        condResult.setActualValue("N/A");
        return false;
    }


    /**
     * 评估时间范围条件 (TIME_OF_DAY)
     * 例如：只在工作日的 9:00 到 17:00 触发
     */
    private static boolean evaluateTimeOfDayCondition(JsonNode conditionNode, EvaluationContext context,
                                                      ConditionResult condResult) {
        // 提取参数
        String startTimeStr = conditionNode.path("startTime").asText(null); // HH:mm:ss or HH:mm
        String endTimeStr = conditionNode.path("endTime").asText(null);   // HH:mm:ss or HH:mm
        List<String> daysOfWeek = new ArrayList<>(); // 星期几列表 (MONDAY, TUESDAY...)
        if (conditionNode.has("daysOfWeek") && conditionNode.path("daysOfWeek").isArray()) {
            for (JsonNode dayNode : conditionNode.path("daysOfWeek")) {
                daysOfWeek.add(dayNode.asText().toUpperCase()); // 转大写以匹配 DayOfWeek 枚举
            }
        }
        // 可以考虑添加 timezone 参数，默认为系统时区
        // String timezoneId = conditionNode.path("timezone").asText(ZoneId.systemDefault().getId());

        // 记录配置
        condResult.setStartTime(startTimeStr);
        condResult.setEndTime(endTimeStr);
        condResult.setDaysOfWeek(daysOfWeek.isEmpty() ? null : daysOfWeek); // 空列表不显示
        condResult.setOperation("IN_SCHEDULE"); // 操作类型

        // 校验时间格式
        if (startTimeStr == null || endTimeStr == null) {
            condResult.setErrorMessage("startTime 和 endTime 必须提供");
            return false;
        }

        try {
            // 获取当前时间信息 (基于评估上下文的时间戳和系统默认时区)
            // ZoneId zoneId = ZoneId.of(timezoneId); // 使用指定的时区
            ZoneId zoneId = ZoneId.systemDefault(); // 或者简单使用系统默认时区
            Instant instant = Instant.ofEpochMilli(context.getTimestamp());
            LocalDateTime currentLdt = LocalDateTime.ofInstant(instant, zoneId);
            LocalTime currentTime = currentLdt.toLocalTime();
            DayOfWeek currentDay = currentLdt.getDayOfWeek();

            // 记录当前评估的时间和星期
            condResult.setCurrentTime(currentTime.format(DateTimeFormatter.ISO_LOCAL_TIME));
            condResult.setCurrentDay(currentDay.toString());

            // 解析配置的开始和结束时间
            // 尝试解析 HH:mm:ss，如果失败，尝试 HH:mm
            LocalTime startTime = parseLenientLocalTime(startTimeStr);
            LocalTime endTime = parseLenientLocalTime(endTimeStr);
            if (startTime == null || endTime == null) {
                condResult.setErrorMessage("无法解析 startTime 或 endTime (格式应为 HH:mm:ss 或 HH:mm)");
                return false;
            }

            // 1. 检查星期几是否匹配 (如果 daysOfWeek 有定义)
            boolean dayMatches = true; // 默认匹配 (如果没有定义星期)
            if (!daysOfWeek.isEmpty()) {
                dayMatches = daysOfWeek.contains(currentDay.toString());
            }

            if (!dayMatches) {
                condResult.setActualValue("星期不匹配");
                return false; // 星期不符，条件直接不满足
            }

            // 2. 检查时间是否在范围内
            boolean timeMatches;
            // 比较时间部分（忽略日期）
            if (startTime.isBefore(endTime)) {
                // --- 情况 1: 时间窗口不跨天 (e.g., 09:00 - 17:00) ---
                // 条件：currentTime >= startTime AND currentTime < endTime
                timeMatches = !currentTime.isBefore(startTime) && currentTime.isBefore(endTime);
            } else if (startTime.equals(endTime)) {
                // --- 情况 2: 开始时间等于结束时间 ---
                // 这通常意味着 24 小时都匹配，或者条件无效？按 24 小时匹配处理。
                // timeMatches = true;
                // 或者认为配置无效
                condResult.setErrorMessage("startTime 和 endTime 不能相同 (除非意为全天)");
                timeMatches = false; // 按无效配置处理
            } else {
                // --- 情况 3: 时间窗口跨天 (e.g., 22:00 - 06:00) ---
                // 条件：currentTime >= startTime OR currentTime < endTime
                timeMatches = !currentTime.isBefore(startTime) || currentTime.isBefore(endTime);
            }

            // 记录时间是否匹配
            condResult.setActualValue(timeMatches ? "在时间范围内" : "不在时间范围内");

            // 最终结果：星期匹配 且 时间匹配
            return dayMatches && timeMatches;

        } catch (DateTimeException e) {
            // 捕获时间解析或时区相关的异常
            condResult.setErrorMessage("处理时间或日期时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 尝试解析时间字符串 (HH:mm:ss 或 HH:mm)
     */
    private static LocalTime parseLenientLocalTime(String timeStr) {
        try {
            // 优先尝试解析完整格式
            return LocalTime.parse(timeStr, DateTimeFormatter.ISO_LOCAL_TIME);
        } catch (DateTimeException e1) {
            try {
                // 如果失败，尝试解析小时和分钟格式
                return LocalTime.parse(timeStr + ":00", DateTimeFormatter.ISO_LOCAL_TIME);
            } catch (DateTimeException e2) {
                return null; // 两种格式都失败
            }
        }
    }

    // --- 辅助方法 ---

    /**
     * 解析 JsonNode 到 Java 基本类型或 String
     * @param node JsonNode
     * @return 解析后的 Object，可能是 String, Boolean, Integer, Long, Double, BigDecimal, 或 null
     */
    private static Object parseJsonValue(JsonNode node) {
        if (node == null || node.isMissingNode() || node.isNull()) {
            return null;
        } else if (node.isTextual()) {
            return node.asText();
        } else if (node.isBoolean()) {
            return node.asBoolean();
        } else if (node.isInt()) {
            return node.asInt();
        } else if (node.isLong()) {
            return node.asLong();
        } else if (node.isDouble() || node.isFloat()) { // 处理浮点数
            return node.asDouble();
        } else if (node.isBigDecimal()) { // 处理高精度数字
            return node.decimalValue();
        } else {
            // 对于数组或对象类型，可以选择返回其文本表示形式，或者不支持
            return node.toString(); // 返回 JSON 字符串表示
            // return null; // 或者返回 null 表示不支持的类型
        }
    }

    /**
     * 执行比较操作 (核心比较逻辑)
     * @param operation 操作符 (e.g., "GT", "EQ", "CONTAINS", "REGEX")
     * @param actualValue 实际值 (来自遥测数据)
     * @param expectedValue 期望值 (来自规则定义)
     * @return 比较结果 (true/false)
     */
    private static boolean performOperation(String operation, Object actualValue, Object expectedValue) {
        // --- 1. 处理 null 值 ---
        // a) 处理 actualValue 为 null 的情况
        if (actualValue == null) {
            if ("EQ".equalsIgnoreCase(operation) || "==".equals(operation)) return expectedValue == null;
            if ("NE".equalsIgnoreCase(operation) || "!=".equals(operation)) return expectedValue != null;
            if ("IS_NULL".equalsIgnoreCase(operation)) return true;
            if ("IS_NOT_NULL".equalsIgnoreCase(operation)) return false;
            // 对于 GT, LT, GE, LE, CONTAINS 等，null 通常不满足条件
            return false;
        }
        // b) 如果 actualValue 不为 null，处理 IS_NULL 和 IS_NOT_NULL
        if ("IS_NULL".equalsIgnoreCase(operation)) return false;
        if ("IS_NOT_NULL".equalsIgnoreCase(operation)) return true;

        // c) 如果 expectedValue 为 null (而 actualValue 不为 null)
        if (expectedValue == null) {
            if ("EQ".equalsIgnoreCase(operation) || "==".equals(operation)) return false; // a != null
            if ("NE".equalsIgnoreCase(operation) || "!=".equals(operation)) return true;  // a != null
            // GT, LT 等与 null 比较无意义，返回 false
            return false;
        }

        // --- 2. 尝试数值比较 ---
        // 仅当 actualValue 和 expectedValue 都是 Number 类型时进行数值比较
        // 注意：需要处理不同 Number 子类（Integer, Double, Long, BigDecimal）
        boolean actualIsNumber = actualValue instanceof Number;
        boolean expectedIsNumber = expectedValue instanceof Number;

        if (actualIsNumber && expectedIsNumber) {
            // 使用 BigDecimal 进行比较以获得最高精度并避免浮点误差
            BigDecimal actualDecimal = toBigDecimal(actualValue);
            BigDecimal expectedDecimal = toBigDecimal(expectedValue);

            // 如果转换失败（理论上不应发生，因为检查了 instanceof Number）
            if (actualDecimal == null || expectedDecimal == null) {
                // 可以选择回退到字符串比较或返回 false
                // Fallback to string comparison below
            } else {
                int cmp = actualDecimal.compareTo(expectedDecimal);
                switch (operation.toUpperCase()) {
                    case "EQ": case "==": return cmp == 0;
                    case "NE": case "!=": return cmp != 0;
                    case "GT": case ">": return cmp > 0;
                    case "LT": case "<": return cmp < 0;
                    case "GE": case ">=": return cmp >= 0;
                    case "LE": case "<=": return cmp <= 0;
                    // 如果操作符不适用于数字，则跳过，继续进行字符串比较
                    default: break;
                }
            }
        }

        // --- 3. 字符串比较 (或作为数值比较的回退) ---
        // 将 actualValue 和 expectedValue 都转换为字符串进行比较
        String actualStr = actualValue.toString();
        String expectedStr = expectedValue.toString();

        switch (operation.toUpperCase()) {
            // 基本字符串比较
            case "EQ": case "==": return actualStr.equals(expectedStr);
            case "NE": case "!=": return !actualStr.equals(expectedStr);
            // 字典序比较 (通常不用于告警，除非明确需要)
            case "GT": case ">": return actualStr.compareTo(expectedStr) > 0;
            case "LT": case "<": return actualStr.compareTo(expectedStr) < 0;
            case "GE": case ">=": return actualStr.compareTo(expectedStr) >= 0;
            case "LE": case "<=": return actualStr.compareTo(expectedStr) <= 0;
            // 字符串包含关系
            case "CONTAINS": return actualStr.contains(expectedStr);
            case "NOT_CONTAINS": return !actualStr.contains(expectedStr);
            case "STARTS_WITH": return actualStr.startsWith(expectedStr);
            case "ENDS_WITH": return actualStr.endsWith(expectedStr);
            // 正则表达式匹配
            case "REGEX":
                try {
                    // 使用 Pattern.matches 进行全匹配
                    return Pattern.matches(expectedStr, actualStr);
                } catch (java.util.regex.PatternSyntaxException e) {
                    System.err.println("无效的正则表达式: " + expectedStr + " - " + e.getMessage());
                    return false; // 无效正则视为不匹配
                }
                // 如果操作符不匹配任何已知字符串操作，返回 false
            default:
                System.err.println("未知的操作符 '" + operation + "' 用于类型 "
                        + actualValue.getClass().getSimpleName() + " 和 "
                        + expectedValue.getClass().getSimpleName());
                return false;
        }
    }

    /**
     * 安全地将 Object (预期为 Number) 转换为 BigDecimal
     */
    private static BigDecimal toBigDecimal(Object number) {
        if (number instanceof BigDecimal) {
            return (BigDecimal) number;
        } else if (number instanceof Double || number instanceof Float) {
            // 直接使用 double 值构造 BigDecimal 可能有精度问题，但通常足够
            return BigDecimal.valueOf(((Number) number).doubleValue());
            // 或者使用 String 转换保证精度: return new BigDecimal(number.toString());
        } else if (number instanceof Long || number instanceof Integer || number instanceof Short || number instanceof Byte) {
            return BigDecimal.valueOf(((Number) number).longValue());
        } else {
            // 尝试通过 toString 转换，可能失败
            try {
                return new BigDecimal(number.toString());
            } catch (NumberFormatException e) {
                return null; // 转换失败
            }
        }
    }


    /**
     * 计算聚合值
     * @param data 窗口内的数据点列表
     * @param aggregation 聚合函数名称 (AVG, SUM, MIN, MAX, COUNT, LATEST, EARLIEST)
     * @return 聚合结果，如果无法计算（如对非数值求和）或窗口为空，可能返回 null 或 0 (对于COUNT)
     */
    private static Object calculateAggregation(List<TimeValuePair> data, String aggregation) {
        // 处理空数据列表
        if (data == null || data.isEmpty()) {
            // COUNT 对空列表结果是 0
            return "COUNT".equalsIgnoreCase(aggregation) ? 0 : null;
        }

        // 对于需要数值计算的聚合，先提取有效的数值
        List<Double> numericValues = null;
        if (!"COUNT".equalsIgnoreCase(aggregation) && !"LATEST".equalsIgnoreCase(aggregation) && !"EARLIEST".equalsIgnoreCase(aggregation)) {
            numericValues = data.stream()
                    .map(pair -> {
                        try {
                            // 尝试将值转为 double
                            return getDoubleValue(pair.getValue());
                        } catch (NumberFormatException e) {
                            return null; // 忽略无法转换为 double 的值
                        }
                    })
                    .filter(Objects::nonNull) // 只保留非 null (即可转换为 double) 的值
                    .collect(Collectors.toList());

            // 如果没有有效的数值，多数聚合无法进行
            if (numericValues.isEmpty()) {
                return null;
            }
        }


        // 根据聚合函数名称进行计算
        switch (aggregation.toUpperCase()) {
            case "COUNT":
                // COUNT 计算的是窗口内总数据点数，无论其类型
                return data.size();
            case "AVG":
                // 计算平均值 (仅对数值有效)
                return numericValues.stream().mapToDouble(Double::doubleValue).average().orElse(Double.NaN); // NaN 表示无法计算
            case "SUM":
                // 计算总和 (仅对数值有效)
                return numericValues.stream().mapToDouble(Double::doubleValue).sum();
            case "MIN":
                // 计算最小值 (仅对数值有效)
                return numericValues.stream().mapToDouble(Double::doubleValue).min().orElse(Double.NaN);
            case "MAX":
                // 计算最大值 (仅对数值有效)
                return numericValues.stream().mapToDouble(Double::doubleValue).max().orElse(Double.NaN);
            case "LATEST":
                // 获取最新的值 (列表第一个元素，假设已按时间降序排序)
                return data.get(0).getValue();
            case "EARLIEST":
                // 获取最早的值 (列表最后一个元素，假设已按时间降序排序)
                return data.get(data.size() - 1).getValue();
            default:
                System.err.println("未知的聚合函数: " + aggregation);
                return null; // 未知聚合类型返回 null
        }
    }

    /**
     * 安全地将 Object 尝试转换为 double
     * @param value 待转换的对象
     * @return 转换后的 double 值
     * @throws NumberFormatException 如果无法转换为 double
     */
    private static double getDoubleValue(Object value) throws NumberFormatException {
        if (value instanceof Number) {
            // 如果是 Number 类型，直接获取 double 值
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            // 如果是 String，尝试解析
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                // 增强错误消息
                throw new NumberFormatException("无法将字符串 '" + value + "' 解析为 double: " + e.getMessage());
            }
        } else if (value instanceof Boolean) {
            // 可以选择将 boolean 转为 0.0 或 1.0
            return ((Boolean) value) ? 1.0 : 0.0;
        }
        // 其他类型无法转换
        throw new NumberFormatException("无法将类型 " + value.getClass().getName() + " 的值 '" + value + "' 转换为 double");
    }

    /**
     * 深拷贝 Map<String, Object>，用于存储遥测快照
     * 这是一个简单的实现，可能无法处理复杂的嵌套对象或循环引用。
     * @param original 原始 Map
     * @return 深拷贝后的 Map
     */
    private static Map<String, Object> deepCopyMap(Map<String, Object> original) {
        if (original == null) {
            return null;
        }
        Map<String, Object> copy = new HashMap<>();
        for (Map.Entry<String, Object> entry : original.entrySet()) {
            // 简单值直接复制，对于 Map 或 List 需要递归复制（这里简化处理，只复制顶层）
            // 注意：如果遥测数据包含嵌套结构，这里需要更复杂的深拷贝逻辑
            copy.put(entry.getKey(), entry.getValue()); // 浅拷贝值
        }
        return copy;
    }


    // --- 仅用于内部测试或演示 ---
    /*
    public static void main(String[] args) throws InterruptedException {
        // 示例：注册模板
        String templateJson = "{\n" +
                              "  \"type\": \"SIMPLE\",\n" +
                              "  \"key\": \"temperature\",\n" + // 默认 key
                              "  \"operation\": \"GT\",\n" + // 默认操作
                              "  \"value\": 30 \n" + // 默认阈值
                              "}";
        registerConditionTemplate("highTempWarning", templateJson);

        // 示例：使用模板的规则，并覆盖 key 和 value
        String ruleWithTemplate = "{\n" +
                "  \"id\": \"rule-template-test\",\n" +
                "  \"name\": \"High Humidity Alert (using template)\",\n" +
                "  \"severity\": \"WARNING\",\n" +
                "  \"condition\": {\n" +
                "    \"templateId\": \"highTempWarning\", \n" + // 使用模板
                "    \"key\": \"humidity\", \n" +             // 覆盖 key
                "    \"value\": 80 \n" +                    // 覆盖 value (操作符 GT 仍来自模板)
                "  }\n" +
                "}";

        String telemetryHighHumidity = "{\"deviceId\": \"dev-template-1\", \"humidity\": 85, \"temperature\": 25, \"timestamp\": "+System.currentTimeMillis()+"}";
        AlarmEvaluationResult resultTemplate = evaluateRule(telemetryHighHumidity, ruleWithTemplate);
        System.out.println("Template Rule Triggered: " + resultTemplate.isTriggered()); // Expected: true
        System.out.println("Condition Key: " + resultTemplate.getConditionResults().get(0).getKey()); // Expected: humidity
        System.out.println("Condition Value: " + resultTemplate.getConditionResults().get(0).getValue()); // Expected: 80
        System.out.println("Condition Operation: " + resultTemplate.getConditionResults().get(0).getOperation()); // Expected: GT

        // 示例：测试 DURATION
        testDurationLogic();

        // 关闭调度器 (在应用关闭时调用)
        scheduler.shutdown();
    }

    private static void testDurationLogic() throws InterruptedException {
         String ruleId = "duration-main-test";
         String deviceId = "dev-duration-main";
         String ruleJson = "{\n" +
                 "  \"id\": \"" + ruleId + "\",\n" +
                 "  \"condition\": {\n" +
                 "    \"type\": \"DURATION\",\n" +
                 "    \"duration\": 3, \n" + // 持续 3 秒
                 "    \"condition\": {\n" +
                 "        \"type\": \"SIMPLE\",\n" +
                 "        \"key\": \"pressure\",\n" +
                 "        \"operation\": \"GT\",\n" +
                 "        \"value\": 100\n" +
                 "    }\n" +
                 "  }\n" +
                 "}";
          ObjectMapper mapper = new ObjectMapper();
          JsonNode ruleNode = null;
          try {
              ruleNode = mapper.readTree(ruleJson);
          } catch(IOException e){ e.printStackTrace(); return; }

          long t0 = System.currentTimeMillis();
          Map<String, Object> telemetry1 = Map.of("deviceId", deviceId, "pressure", 110, "timestamp", t0);
          Map<String, Object> telemetry2 = Map.of("deviceId", deviceId, "pressure", 120, "timestamp", t0 + 1500); // 1.5s
          Map<String, Object> telemetry3 = Map.of("deviceId", deviceId, "pressure", 90, "timestamp", t0 + 2500); // 2.5s (中断)
          Map<String, Object> telemetry4 = Map.of("deviceId", deviceId, "pressure", 115, "timestamp", t0 + 4000); // 4s (重新开始)
          Map<String, Object> telemetry5 = Map.of("deviceId", deviceId, "pressure", 125, "timestamp", t0 + 7500); // 7.5s (满足3s)

          AlarmEvaluationResult r1 = evaluateRule(telemetry1, ruleNode);
          System.out.println("t=0s: Triggered=" + r1.isTriggered() + ", DurationMet=" + r1.getConditionResults().get(0).getDurationMet()); // F, F
          Thread.sleep(10); // 确保时间戳不同

          AlarmEvaluationResult r2 = evaluateRule(telemetry2, ruleNode);
          System.out.println("t=1.5s: Triggered=" + r2.isTriggered() + ", DurationMet=" + r2.getConditionResults().get(0).getDurationMet());// F, F
          Thread.sleep(10);

          AlarmEvaluationResult r3 = evaluateRule(telemetry3, ruleNode);
          System.out.println("t=2.5s: Triggered=" + r3.isTriggered() + ", DurationMet=" + r3.getConditionResults().get(0).getDurationMet());// F, F (内部条件 F)
          Thread.sleep(10);

          AlarmEvaluationResult r4 = evaluateRule(telemetry4, ruleNode);
          System.out.println("t=4s: Triggered=" + r4.isTriggered() + ", DurationMet=" + r4.getConditionResults().get(0).getDurationMet());// F, F (计时重置)
          Thread.sleep(10);

          AlarmEvaluationResult r5 = evaluateRule(telemetry5, ruleNode);
          System.out.println("t=7.5s: Triggered=" + r5.isTriggered() + ", DurationMet=" + r5.getConditionResults().get(0).getDurationMet());// T, T (4s -> 7.5s > 3s)
    }
    */

    // --- 公开状态访问接口 (可选, 用于监控/调试) ---
    public static Map<String, Map<String, AlarmState>> getActiveAlarms() {
        // 返回不可修改的视图，防止外部直接修改内部状态
        return Collections.unmodifiableMap(activeAlarms);
    }

    public static Map<String, Map<String, List<TimeValuePair>>> getDeviceDataCache() {
        return Collections.unmodifiableMap(deviceDataCache);
    }

    public static Map<String, FrequencyCounter> getAlarmFrequencyCounters() {
        return Collections.unmodifiableMap(alarmFrequencyCounters);
    }

    public static Map<String, Map<String, DurationState>> getDurationStates() {
        return Collections.unmodifiableMap(durationStates);
    }
}
