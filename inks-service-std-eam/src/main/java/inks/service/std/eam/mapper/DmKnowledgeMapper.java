package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmKnowledgeEntity;
import inks.service.std.eam.domain.pojo.DmKnowledgePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备知识库(DmKnowledge)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 11:22:51
 */
@Mapper
public interface DmKnowledgeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmKnowledgePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmKnowledgePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param dmKnowledgeEntity 实例对象
     * @return 影响行数
     */
    int insert(DmKnowledgeEntity dmKnowledgeEntity);


    /**
     * 修改数据
     *
     * @param dmKnowledgeEntity 实例对象
     * @return 影响行数
     */
    int update(DmKnowledgeEntity dmKnowledgeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

}

