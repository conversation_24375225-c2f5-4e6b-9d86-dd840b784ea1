package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolmaintenancePojo;
import inks.service.std.eam.domain.pojo.DmToolmaintenanceitemdetailPojo;
import com.github.pagehelper.PageInfo;

/**
 * 工装具保养记录表(DmToolmaintenance)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:20
 */
public interface DmToolmaintenanceService {

    DmToolmaintenancePojo getEntity(String key,String tid);

    PageInfo<DmToolmaintenanceitemdetailPojo> getPageList(QueryParam queryParam);

    DmToolmaintenancePojo getBillEntity(String key,String tid);

    PageInfo<DmToolmaintenancePojo> getBillList(QueryParam queryParam);

    PageInfo<DmToolmaintenancePojo> getPageTh(QueryParam queryParam);

    DmToolmaintenancePojo insert(DmToolmaintenancePojo dmToolmaintenancePojo);

    DmToolmaintenancePojo update(DmToolmaintenancePojo dmToolmaintenancepojo);

    int delete(String key,String tid);

}
