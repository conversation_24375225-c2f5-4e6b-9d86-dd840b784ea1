package inks.service.std.eam.config.old;

import org.junit.Test;

public class JsonPathUtilTest1 {

    private final String json = "{\n" +
            "  \"msg\": {\n" +
            "    \"info\": {\n" +
            "      \"code\": \"ds01\",\n" +
            "      \"date\": \"2024-06-08 13:42:00\",\n" +
            "      \"content\": [\n" +
            "        {\n" +
            "          \"key\": \"temp" +
            "\",\n" +
            "          \"value\": 37\n" +
            "        },\n" +
            "        {\n" +
            "          \"key\": \"dianya\",\n" +
            "          \"value\": 220V\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    \"msgtype\": \"info\",\n" +
            "    \"Trigger\": 0,\n" +
            "    \"sn\": \"2873406DCF72\"\n" +
            "  },\n" +
            "  \"modulecode\": \"saiot\"\n" +
            "}";

    @Test
    public void testPrintResults() {
        System.out.println("msg.info.code: " + JsonPathUtil.getByJsonPath(json, "msg.info.code"));
        System.out.println("msg.info.content[0].key: " + JsonPathUtil.getByJsonPath(json, "msg.info.content[0].key"));
        System.out.println("msg.info.content[1].value: " + JsonPathUtil.getByJsonPath(json, "msg.info.content[1].value"));
        System.out.println("msg.info.content: " + JsonPathUtil.getByJsonPath(json, "msg.info.content"));
        System.out.println("msg.info.content[]: " + JsonPathUtil.getByJsonPath(json, "msg.info.content[]"));
        System.out.println("msg.info.content[].key: " + JsonPathUtil.getByJsonPath(json, "msg.info.content[].key"));
        System.out.println("msg.info.content[5].key: " + JsonPathUtil.getByJsonPath(json, "msg.info.content[5].key"));
        System.out.println("msg.info.notexist: " + JsonPathUtil.getByJsonPath(json, "msg.info.notexist"));
    }
}
