package inks.service.std.eam.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 设备台账(DmDevice)实体类
 *
 * <AUTHOR>
 * @since 2023-06-06 12:59:48
 */
public class DmDevicePojo implements Serializable {
    private static final long serialVersionUID = 142217117475453922L;
    // id
    @Excel(name = "id")
    private String id;
    // 分组Guid
    @Excel(name = "分组Guid")
    private String devgroupid;
    // 编码
    @Excel(name = "编码")
    private String devcode;
    // 名称
    @Excel(name = "名称")
    private String devname;
    // 规格
    @Excel(name = "规格")
    private String devspec;
    // 单位
    @Excel(name = "单位")
    private String devunit;
    // 拼音码
    @Excel(name = "拼音码")
    private String devpinyin;
    // 分组编码
    @Excel(name = "分组编码")
    private String groupcode;
    // 类型
    @Excel(name = "类型")
    private String groupname;
    // 序号
    @Excel(name = "序号")
    private Integer groupno;
    // 附图1
    @Excel(name = "附图1")
    private String devphoto1;
    // 附图2
    @Excel(name = "附图2")
    private String devphoto2;
    // 制造日期
    @Excel(name = "制造日期")
    private Date makedate;
    // 启用日期
    @Excel(name = "启用日期")
    private Date startdate;
    // 设备原值
    @Excel(name = "设备原值")
    private Double inprice;
    // 当前净值
    @Excel(name = "当前净值")
    private Double nowprice;
    // 使用年份
    @Excel(name = "使用年份")
    private Integer depreciation;
    // 使用状态
    @Excel(name = "使用状态")
    private String usestate;
    // 使用位置
    @Excel(name = "使用位置")
    private String uselocation;
    // 使用部门
    @Excel(name = "使用部门")
    private String usebranch;
    // 使用人员
    @Excel(name = "使用人员")
    private String usestaff;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 有效标识
    @Excel(name = "有效标识")
    private Integer enabledmark;
    // 删除标识
    @Excel(name = "删除标识")
    private Integer deletemark;
    // 删除人员
    @Excel(name = "删除人员")
    private String deletelister;
    // 删除日期
    @Excel(name = "删除日期")
    private Date deletedate;
    // 处置标识
    @Excel(name = "处置标识")
    private Integer disannulmark;
    // 外置日期
    @Excel(name = "外置日期")
    private Date disannuldate;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<DmDeviceitemPojo> item;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 分组Guid
    public String getDevgroupid() {
        return devgroupid;
    }

    public void setDevgroupid(String devgroupid) {
        this.devgroupid = devgroupid;
    }

    // 编码
    public String getDevcode() {
        return devcode;
    }

    public void setDevcode(String devcode) {
        this.devcode = devcode;
    }

    // 名称
    public String getDevname() {
        return devname;
    }

    public void setDevname(String devname) {
        this.devname = devname;
    }

    // 规格
    public String getDevspec() {
        return devspec;
    }

    public void setDevspec(String devspec) {
        this.devspec = devspec;
    }

    // 单位
    public String getDevunit() {
        return devunit;
    }

    public void setDevunit(String devunit) {
        this.devunit = devunit;
    }

    // 拼音码
    public String getDevpinyin() {
        return devpinyin;
    }

    public void setDevpinyin(String devpinyin) {
        this.devpinyin = devpinyin;
    }

    // 分组编码
    public String getGroupcode() {
        return groupcode;
    }

    public void setGroupcode(String groupcode) {
        this.groupcode = groupcode;
    }

    // 类型
    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    // 序号
    public Integer getGroupno() {
        return groupno;
    }

    public void setGroupno(Integer groupno) {
        this.groupno = groupno;
    }

    // 附图1
    public String getDevphoto1() {
        return devphoto1;
    }

    public void setDevphoto1(String devphoto1) {
        this.devphoto1 = devphoto1;
    }

    // 附图2
    public String getDevphoto2() {
        return devphoto2;
    }

    public void setDevphoto2(String devphoto2) {
        this.devphoto2 = devphoto2;
    }

    // 制造日期
    public Date getMakedate() {
        return makedate;
    }

    public void setMakedate(Date makedate) {
        this.makedate = makedate;
    }

    // 启用日期
    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }

    // 设备原值
    public Double getInprice() {
        return inprice;
    }

    public void setInprice(Double inprice) {
        this.inprice = inprice;
    }

    // 当前净值
    public Double getNowprice() {
        return nowprice;
    }

    public void setNowprice(Double nowprice) {
        this.nowprice = nowprice;
    }

    // 使用年份
    public Integer getDepreciation() {
        return depreciation;
    }

    public void setDepreciation(Integer depreciation) {
        this.depreciation = depreciation;
    }

    // 使用状态
    public String getUsestate() {
        return usestate;
    }

    public void setUsestate(String usestate) {
        this.usestate = usestate;
    }

    // 使用位置
    public String getUselocation() {
        return uselocation;
    }

    public void setUselocation(String uselocation) {
        this.uselocation = uselocation;
    }

    // 使用部门
    public String getUsebranch() {
        return usebranch;
    }

    public void setUsebranch(String usebranch) {
        this.usebranch = usebranch;
    }

    // 使用人员
    public String getUsestaff() {
        return usestaff;
    }

    public void setUsestaff(String usestaff) {
        this.usestaff = usestaff;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 删除标识
    public Integer getDeletemark() {
        return deletemark;
    }

    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }

    // 删除人员
    public String getDeletelister() {
        return deletelister;
    }

    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }

    // 删除日期
    public Date getDeletedate() {
        return deletedate;
    }

    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }

    // 处置标识
    public Integer getDisannulmark() {
        return disannulmark;
    }

    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }

    // 外置日期
    public Date getDisannuldate() {
        return disannuldate;
    }

    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


    public List<DmDeviceitemPojo> getItem() {
        return item;
    }

    public void setItem(List<DmDeviceitemPojo> item) {
        this.item = item;
    }


}

