package inks.service.std.eam.iotrule.rule;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

import static org.junit.Assert.*;

/**
 * AdvancedAlarmRuleEvaluator 的单元测试.
 */
public class AdvancedAlarmRuleEvaluatorTest {

    private static ObjectMapper objectMapper;

    @BeforeClass
    public static void setUpClass() {
        objectMapper = new ObjectMapper();
        // 注册测试模板
        String templateJson = "{\"type\":\"SIMPLE\",\"key\":\"defaultKey\",\"operation\":\"GT\",\"value\":0}";
        AdvancedAlarmRuleEvaluator.registerConditionTemplate("testTemplate", templateJson);
    }

    @Before
    public void setUp() {
        // 每个测试前可以选择性地进行全局清理，但更推荐在测试方法内部针对特定deviceId清理
    }

    // --- 基础条件测试 ---

    /**
     * 测试场景: 简单的 GT (大于) 条件，预期触发。
     * 规则: temperature > 30
     * 数据: temperature = 35
     */
    @Test
    public void testSimpleRule_Trigger() throws JsonProcessingException {
        String ruleId = "simple-trigger-rule";
        String deviceId = "dev-simple-trigger";
        // 清理此设备的潜在旧状态
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId);
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"SIMPLE\",\n" +
                "    \"key\": \"temperature\",\n" +
                "    \"operation\": \"GT\",\n" +
                "    \"value\": 30\n" +
                "  }\n" +
                "}", ruleId);
        String telemetryJson = String.format("{\"deviceId\": \"%s\", \"temperature\": 35}", deviceId);

        AlarmEvaluationResult result = AdvancedAlarmRuleEvaluator.evaluateRule(telemetryJson, ruleJson);

        assertTrue("规则应触发", result.isTriggered());
        assertEquals(ruleId, result.getRuleId());
        assertEquals(deviceId, result.getDeviceId());
        assertNull("不应有错误消息", result.getErrorMessage());
        assertEquals("SIMPLE", result.getConditionResults().get(0).getType());
        assertTrue("简单条件结果应为 true", result.getConditionResults().get(0).isResult());
    }

    /**
     * 测试场景: 简单的 GT (大于) 条件，数据不满足，预期不触发。
     * 规则: temperature > 30
     * 数据: temperature = 25
     */
    @Test
    public void testSimpleRule_NotTrigger() throws JsonProcessingException {
        String ruleId = "simple-not-trigger-rule";
        String deviceId = "dev-simple-not-trigger";
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId); // 清理状态
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"SIMPLE\",\n" +
                "    \"key\": \"temperature\",\n" +
                "    \"operation\": \"GT\",\n" +
                "    \"value\": 30\n" +
                "  }\n" +
                "}", ruleId);
        String telemetryJson = String.format("{\"deviceId\": \"%s\", \"temperature\": 25}", deviceId);

        AlarmEvaluationResult result = AdvancedAlarmRuleEvaluator.evaluateRule(telemetryJson, ruleJson);

        assertFalse("规则不应触发", result.isTriggered());
        assertNull("不应有错误消息", result.getErrorMessage());
        assertFalse("简单条件结果应为 false", result.getConditionResults().get(0).isResult());
    }

    /**
     * 测试场景: AND 条件，所有子条件都满足，预期触发。
     * 规则: temperature > 30 AND humidity < 50
     * 数据: temperature = 35, humidity = 40
     */
    @Test
    public void testAndRule_Trigger() throws JsonProcessingException {
        String ruleId = "and-trigger-rule";
        String deviceId = "dev-and-trigger";
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId); // 清理状态
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"AND\",\n" +
                "    \"conditions\": [\n" +
                "      {\"type\": \"SIMPLE\", \"key\": \"temperature\", \"operation\": \"GT\", \"value\": 30},\n" +
                "      {\"type\": \"SIMPLE\", \"key\": \"humidity\", \"operation\": \"LT\", \"value\": 50}\n" +
                "    ]\n" +
                "  }\n" +
                "}", ruleId);
        String telemetryJson = String.format("{\"deviceId\": \"%s\", \"temperature\": 35, \"humidity\": 40}", deviceId);

        AlarmEvaluationResult result = AdvancedAlarmRuleEvaluator.evaluateRule(telemetryJson, ruleJson);

        assertTrue("AND规则应触发", result.isTriggered());
        assertNull("不应有错误消息", result.getErrorMessage());
        assertEquals("AND", result.getConditionResults().get(0).getType());
        assertEquals(2, result.getConditionResults().get(0).getSubConditions().size());
        assertTrue("AND规则子条件1应为true", result.getConditionResults().get(0).getSubConditions().get(0).isResult());
        assertTrue("AND规则子条件2应为true", result.getConditionResults().get(0).getSubConditions().get(1).isResult());
    }

    /**
     * 测试场景: OR 条件，其中一个子条件满足，预期触发。
     * 规则: temperature > 30 OR pressure == "HIGH"
     * 数据: temperature = 25, pressure = "HIGH"
     */
    @Test
    public void testOrRule_Trigger() throws JsonProcessingException {
        String ruleId = "or-trigger-rule";
        String deviceId = "dev-or-trigger";
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId); // 清理状态
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"OR\",\n" +
                "    \"conditions\": [\n" +
                "      {\"type\": \"SIMPLE\", \"key\": \"temperature\", \"operation\": \"GT\", \"value\": 30},\n" +
                "      {\"type\": \"SIMPLE\", \"key\": \"pressure\", \"operation\": \"EQ\", \"value\": \"HIGH\"}\n" +
                "    ]\n" +
                "  }\n" +
                "}", ruleId);
        String telemetryJson = String.format("{\"deviceId\": \"%s\", \"temperature\": 25, \"pressure\": \"HIGH\"}", deviceId);

        AlarmEvaluationResult result = AdvancedAlarmRuleEvaluator.evaluateRule(telemetryJson, ruleJson);

        assertTrue("OR规则应触发", result.isTriggered());
        assertNull("不应有错误消息", result.getErrorMessage());
        assertEquals("OR", result.getConditionResults().get(0).getType());
        assertFalse("OR规则子条件1应为false", result.getConditionResults().get(0).getSubConditions().get(0).isResult());
        assertTrue("OR规则子条件2应为true", result.getConditionResults().get(0).getSubConditions().get(1).isResult());
    }

    /**
     * 测试场景: NOT 条件，内部条件为 false，预期触发。
     * 规则: NOT (status == "OFFLINE")
     * 数据: status = "ONLINE"
     */
    @Test
    public void testNotRule_Trigger() throws JsonProcessingException {
        String ruleId = "not-trigger-rule";
        String deviceId = "dev-not-trigger";
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId); // 清理状态
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"NOT\",\n" +
                "    \"condition\": {\n" +
                "       \"type\": \"SIMPLE\", \"key\": \"status\", \"operation\": \"EQ\", \"value\": \"OFFLINE\"\n" +
                "     }\n" +
                "  }\n" +
                "}", ruleId);
        String telemetryJson = String.format("{\"deviceId\": \"%s\", \"status\": \"ONLINE\"}", deviceId);

        AlarmEvaluationResult result = AdvancedAlarmRuleEvaluator.evaluateRule(telemetryJson, ruleJson);

        assertTrue("NOT规则应触发 (因为内部条件为false)", result.isTriggered());
        assertNull("不应有错误消息", result.getErrorMessage());
        assertEquals("NOT", result.getConditionResults().get(0).getType());
        assertFalse("NOT规则内部条件应为false", result.getConditionResults().get(0).getSubConditions().get(0).isResult());
    }

    // --- 时间窗口与变化率测试 ---

    /**
     * 测试场景: 时间窗口条件，计算 AVG (平均值) 并满足条件，预期触发。
     * 规则: 过去 60 秒 value 的平均值 > 24
     * 数据: 窗口内有 20, 30, 40 (平均值 30)
     */
    @Test
    public void testTimeWindowRule_AvgTrigger() throws JsonProcessingException {
        String ruleId = "timewin-avg-trigger-rule";
        String deviceId = "dev-timewin-avg";
        long t0 = System.currentTimeMillis();

        // --- 使用测试辅助方法清理特定设备的缓存 ---
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId);

        // --- 填充历史数据 ---
        AdvancedAlarmRuleEvaluator.evaluateRule(String.format("{\"deviceId\": \"%s\", \"value\": 10, \"timestamp\": %d}", deviceId, t0 - 70000), "{\"id\":\"dummy-rule-for-cache-1\"}"); // 早于窗口
        AdvancedAlarmRuleEvaluator.evaluateRule(String.format("{\"deviceId\": \"%s\", \"value\": 20, \"timestamp\": %d}", deviceId, t0 - 40000), "{\"id\":\"dummy-rule-for-cache-2\"}"); // 窗口内
        AdvancedAlarmRuleEvaluator.evaluateRule(String.format("{\"deviceId\": \"%s\", \"value\": 30, \"timestamp\": %d}", deviceId, t0 - 20000), "{\"id\":\"dummy-rule-for-cache-3\"}"); // 窗口内

        // --- 定义要测试的规则 ---
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"TIME_WINDOW\",\n" +
                "    \"key\": \"value\",\n" +
                "    \"aggregation\": \"AVG\",\n" +
                "    \"timeWindow\": 60, \n" + // 60秒窗口
                "    \"operation\": \"GT\",\n" +
                "    \"value\": 24\n" +
                "  }\n" +
                "}", ruleId);
        // --- 发送当前数据点并进行评估 ---
        String currentTelemetry = String.format("{\"deviceId\": \"%s\", \"value\": 40, \"timestamp\": %d}", deviceId, t0); // 当前数据点也在窗口内

        AlarmEvaluationResult result = AdvancedAlarmRuleEvaluator.evaluateRule(currentTelemetry, ruleJson);

        // --- 断言结果 ---
        assertTrue("时间窗口AVG规则应触发 ((20+30+40)/3 = 30 > 24)", result.isTriggered());
        assertNull("不应有错误消息", result.getErrorMessage());
        assertEquals("TIME_WINDOW", result.getConditionResults().get(0).getType());
        assertEquals("窗口内应有3个样本点", 3, (int) result.getConditionResults().get(0).getSampleCount());
        assertEquals("聚合值应约为30.0", 30.0, Double.parseDouble(result.getConditionResults().get(0).getActualValue()), 0.001);
    }

    /**
     * 测试场景: 变化率条件，计算 ABSOLUTE (绝对值) 变化并满足条件，预期触发。
     * 规则: 过去 60 秒 level 的绝对变化 >= 8
     * 数据: 窗口内从 10 变为 19 (变化 9)
     */
    @Test
    public void testChangeRateRule_AbsoluteTrigger() throws JsonProcessingException {
        String ruleId = "changerate-abs-trigger-rule";
        String deviceId = "dev-changerate-abs";
        long t0 = System.currentTimeMillis();

        // --- 使用测试辅助方法清理特定设备的缓存 ---
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId);

        // --- 填充历史数据 ---
        AdvancedAlarmRuleEvaluator.evaluateRule(String.format("{\"deviceId\": \"%s\", \"level\": 10, \"timestamp\": %d}", deviceId, t0 - 40000), "{\"id\":\"dummy-rule-for-cache-4\"}"); // 窗口内起点
        AdvancedAlarmRuleEvaluator.evaluateRule(String.format("{\"deviceId\": \"%s\", \"level\": 15, \"timestamp\": %d}", deviceId, t0 - 20000), "{\"id\":\"dummy-rule-for-cache-5\"}"); // 窗口内中间点

        // --- 定义要测试的规则 ---
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"CHANGE_RATE\",\n" +
                "    \"key\": \"level\",\n" +
                "    \"timeWindow\": 60,\n" +
                "    \"unit\": \"ABSOLUTE\",\n" +
                "    \"operation\": \"GE\",\n" +
                "    \"value\": 8\n" +
                "  }\n" +
                "}", ruleId);
        // --- 发送当前数据点并进行评估 ---
        String currentTelemetry = String.format("{\"deviceId\": \"%s\", \"level\": 19, \"timestamp\": %d}", deviceId, t0); // 当前数据点，窗口内终点

        AlarmEvaluationResult result = AdvancedAlarmRuleEvaluator.evaluateRule(currentTelemetry, ruleJson);

        // --- 断言结果 ---
        assertTrue("变化率绝对值规则应触发 (19 - 10 = 9 >= 8)", result.isTriggered());
        assertNull("不应有错误消息", result.getErrorMessage());
        assertEquals("CHANGE_RATE", result.getConditionResults().get(0).getType());
        assertEquals("10.0", result.getConditionResults().get(0).getInitialValue());
        assertEquals("19.0", result.getConditionResults().get(0).getFinalValue());
        // 验证变化率的实际值（注意格式化可能带来的微小差异）
        assertEquals("变化率实际值应约为9.0", 9.0, Double.parseDouble(result.getConditionResults().get(0).getActualValue().replace("%","")), 0.001);
    }

    // --- 其他条件类型测试 ---

    /**
     * 测试场景: 模式匹配条件，字符串包含 "ERROR"，预期触发。
     * 规则: logMessage matches ".*ERROR.*"
     * 数据: logMessage = "System failed with ERROR code 123"
     */
    @Test
    public void testPatternRule_Match() throws JsonProcessingException {
        String ruleId = "pattern-match-rule";
        String deviceId = "dev-pattern-match";
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId); // 清理状态
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"PATTERN\",\n" +
                "    \"key\": \"logMessage\",\n" +
                "    \"pattern\": \".*ERROR.*\"\n" +
                "  }\n" +
                "}", ruleId);
        String telemetryJson = String.format("{\"deviceId\": \"%s\", \"logMessage\": \"System failed with ERROR code 123\"}", deviceId);

        AlarmEvaluationResult result = AdvancedAlarmRuleEvaluator.evaluateRule(telemetryJson, ruleJson);

        assertTrue("模式匹配规则应触发", result.isTriggered());
        assertNull("不应有错误消息", result.getErrorMessage());
        assertEquals("PATTERN", result.getConditionResults().get(0).getType());
    }

    /**
     * 测试场景: 阈值边界条件，检查值是否在边界内部 (>= lower, < upper)，预期触发。
     * 规则: voltage >= 10.5 AND voltage < 12.5 (INSIDE)
     * 数据: voltage = 11.0
     */
    @Test
    public void testThresholdBoundaryRule_Inside() throws JsonProcessingException {
        String ruleId = "boundary-inside-rule";
        String deviceId = "dev-boundary-inside";
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId); // 清理状态
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"THRESHOLD_BOUNDARY\",\n" +
                "    \"key\": \"voltage\",\n" +
                "    \"lowerBound\": 10.5,\n" +
                "    \"upperBound\": 12.5,\n" +
                "    \"includeLower\": true,\n" +
                "    \"includeUpper\": false,\n" +
                "    \"checkType\": \"INSIDE\"\n" +
                "  }\n" +
                "}", ruleId);
        String telemetryJson = String.format("{\"deviceId\": \"%s\", \"voltage\": 11.0}", deviceId);

        AlarmEvaluationResult result = AdvancedAlarmRuleEvaluator.evaluateRule(telemetryJson, ruleJson);

        assertTrue("阈值边界INSIDE规则应触发 (11.0 >= 10.5 and 11.0 < 12.5)", result.isTriggered());
        assertNull("不应有错误消息", result.getErrorMessage());
        assertEquals("INSIDE_BOUNDS", result.getConditionResults().get(0).getOperation());
    }

    /**
     * 测试场景: 阈值边界条件，检查值是否在边界外部 (<= lower OR >= upper)，预期触发。
     * 规则: rpm <= 1000 OR rpm >= 5000 (OUTSIDE, 不包含边界)
     * 数据: rpm = 6000
     */
    @Test
    public void testThresholdBoundaryRule_Outside() throws JsonProcessingException {
        String ruleId = "boundary-outside-rule";
        String deviceId = "dev-boundary-outside";
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId); // 清理状态
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"THRESHOLD_BOUNDARY\",\n" +
                "    \"key\": \"rpm\",\n" +
                "    \"lowerBound\": 1000,\n" +
                "    \"upperBound\": 5000,\n" +
                "    \"includeLower\": false,\n" +
                "    \"includeUpper\": false,\n" +
                "    \"checkType\": \"OUTSIDE\"\n" +
                "  }\n" +
                "}", ruleId);
        String telemetryJson = String.format("{\"deviceId\": \"%s\", \"rpm\": 6000}", deviceId);

        AlarmEvaluationResult result = AdvancedAlarmRuleEvaluator.evaluateRule(telemetryJson, ruleJson);

        assertTrue("阈值边界OUTSIDE规则应触发 (6000 >= 5000)", result.isTriggered());
        assertNull("不应有错误消息", result.getErrorMessage());
        assertEquals("OUTSIDE_BOUNDS", result.getConditionResults().get(0).getOperation());
    }

    /**
     * 测试场景: 持续时间条件，内部条件连续满足指定时长，预期触发。
     * 规则: status == "ON" 持续 5 秒
     * 数据: status = "ON" 在 t=0s, t=6s
     */
    @Test
    public void testDurationRule_Met() throws JsonProcessingException {
        String ruleId = "duration-test-met";
        String deviceId = "dev-duration-met";

        // --- 使用测试辅助方法清理特定设备的缓存 ---
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId);

        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"DURATION\",\n" +
                "    \"duration\": 5, \n" +
                "    \"condition\": {\n" +
                "        \"type\": \"SIMPLE\", \"key\": \"status\", \"operation\": \"EQ\", \"value\": \"ON\"\n" +
                "    }\n" +
                "  }\n" +
                "}", ruleId);

        long t0 = System.currentTimeMillis();
        String telemetry1 = String.format("{\"deviceId\": \"%s\", \"status\": \"ON\", \"timestamp\": %d}", deviceId, t0);
        String telemetry3 = String.format("{\"deviceId\": \"%s\", \"status\": \"ON\", \"timestamp\": %d}", deviceId, t0 + 6000); // 6秒后

        // 第一次评估，状态变为ON，但不满足持续时间
        AlarmEvaluationResult result1 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry1, ruleJson);
        assertFalse("初始不触发", result1.isTriggered());
        assertNotNull("Duration 条件应有子条件结果", result1.getConditionResults().get(0).getSubConditions());
        assertTrue("内部条件应满足", result1.getConditionResults().get(0).getSubConditions().get(0).isResult());
        assertFalse("持续时间初始未满足", result1.getConditionResults().get(0).getDurationMet());


        // 第二次评估，距离第一次超过5秒，应触发
        AlarmEvaluationResult result3 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry3, ruleJson);
        assertTrue("6秒后应触发", result3.isTriggered());
        assertNotNull("Duration 条件应有子条件结果", result3.getConditionResults().get(0).getSubConditions());
        assertTrue("内部条件应满足", result3.getConditionResults().get(0).getSubConditions().get(0).isResult());
        assertTrue("持续时间应满足", result3.getConditionResults().get(0).getDurationMet());
    }

    /**
     * 测试场景: 特定时间范围条件，检查评估时间是否在工作日的工作时间段内，预期触发。
     * 规则: 时间在周一至周五的 09:00:00 到 17:00:00 之间
     * 数据: 时间戳设置为周一中午
     */
    @Test
    public void testTimeOfDayRule_InSchedule() throws JsonProcessingException {
        String ruleId = "tod-in-schedule";
        String deviceId = "dev-tod-in";
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId); // 清理状态
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"TIME_OF_DAY\",\n" +
                "    \"startTime\": \"09:00:00\",\n" +
                "    \"endTime\": \"17:00:00\",\n" +
                "    \"daysOfWeek\": [\"MONDAY\", \"TUESDAY\", \"WEDNESDAY\", \"THURSDAY\", \"FRIDAY\"]\n" +
                "  }\n" +
                "}", ruleId);

        // 手动构造一个在周一中午的时间戳 (确保测试运行时此日期是周一，或修改为固定日期)
        LocalDateTime workDateTime = LocalDateTime.now().withHour(12).withMinute(0).withSecond(0);
        // 找到最近的周一
        while(workDateTime.getDayOfWeek() != DayOfWeek.MONDAY) {
            workDateTime = workDateTime.minusDays(1);
        }

        long workTimestamp = workDateTime.atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
        String telemetryJsonWorkHour = String.format("{\"deviceId\": \"%s\", \"value\": 1, \"timestamp\": %d}", deviceId, workTimestamp);

        AlarmEvaluationResult resultWorkHour = AdvancedAlarmRuleEvaluator.evaluateRule(telemetryJsonWorkHour, ruleJson);
        assertTrue("周一中午应在时间范围内", resultWorkHour.isTriggered());
        assertNull("不应有错误消息", resultWorkHour.getErrorMessage());
        assertEquals("TIME_OF_DAY", resultWorkHour.getConditionResults().get(0).getType());
    }

    // --- 功能性测试 (去抖动, 清除条件, 模板) ---

    /**
     * 测试场景: 去抖动功能，要求条件连续满足3次才触发。
     * 规则: count > 5, 去抖动配置 {count: 3, timeWindow: 10}
     * 数据: 发送3次满足条件的数据，前两次被去抖动，第3次触发。第4次超出时间窗口，重新计数。
     */
    @Test
    public void testDebounce() throws JsonProcessingException {
        String ruleId = "debounce-rule";
        String deviceId = "dev-debounce";

        // --- 使用测试辅助方法清理特定设备的缓存 ---
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId);

        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\"type\": \"SIMPLE\", \"key\": \"count\", \"operation\": \"GT\", \"value\": 5},\n" +
                "  \"debounce\": { \"count\": 3, \"timeWindow\": 10 } \n" +
                "}", ruleId);

        long t0 = System.currentTimeMillis();
        String telemetry1 = String.format("{\"deviceId\": \"%s\", \"count\": 6, \"timestamp\": %d}", deviceId, t0);
        String telemetry2 = String.format("{\"deviceId\": \"%s\", \"count\": 7, \"timestamp\": %d}", deviceId, t0 + 1000);
        String telemetry3 = String.format("{\"deviceId\": \"%s\", \"count\": 8, \"timestamp\": %d}", deviceId, t0 + 2000);
        // 注意：这里的 t0 + 12000 可能不会让计数器完全过期，取决于 FrequencyCounter 的实现细节和测试执行速度
        // 更可靠的方法是手动控制时间或确保等待足够长的时间
        String telemetry4 = String.format("{\"deviceId\": \"%s\", \"count\": 9, \"timestamp\": %d}", deviceId, t0 + 15000); // 增大时间差

        AlarmEvaluationResult r1 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry1, ruleJson);
        assertFalse("第1次触发，应被去抖动", r1.isTriggered());
        assertTrue(r1.isDebounced());
        assertEquals("计数 1/3 (窗口 10s)", r1.getDebounceInfo());


        AlarmEvaluationResult r2 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry2, ruleJson);
        assertFalse("第2次触发，应被去抖动", r2.isTriggered());
        assertTrue(r2.isDebounced());
        assertEquals("计数 2/3 (窗口 10s)", r2.getDebounceInfo());

        AlarmEvaluationResult r3 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry3, ruleJson);
        assertTrue("第3次触发，应解除去抖动并触发", r3.isTriggered());
        assertFalse(r3.isDebounced());

        // 模拟时间流逝，确保计数器有机会过期
        // (在实际测试中，如果需要精确控制时间，可能需要 Mock 时间服务)
        // 这里假设 t0+15000 足够让计数器过期

        AlarmEvaluationResult r4 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry4, ruleJson);
        assertFalse("超出窗口后第1次触发，应被去抖动", r4.isTriggered());
        assertTrue(r4.isDebounced());
        assertEquals("计数 1/3 (窗口 10s)", r4.getDebounceInfo()); // 计数器重置后从1开始
    }

    /**
     * 测试场景: 清除条件功能。告警触发后，当满足清除条件时，告警状态变为非激活。
     * 规则: 触发条件 temp > 50, 清除条件 temp <= 40
     * 数据: temp=55 (触发) -> temp=52 (持续) -> temp=38 (清除) -> temp=39 (已清除)
     */
    @Test
    public void testClearCondition() throws JsonProcessingException {
        String ruleId = "clear-cond-rule";
        String deviceId = "dev-clear-cond";

        // --- 使用测试辅助方法清理特定设备的缓存 ---
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId);

        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\"type\": \"SIMPLE\", \"key\": \"temp\", \"operation\": \"GT\", \"value\": 50},\n" +
                "  \"clearCondition\": {\"type\": \"SIMPLE\", \"key\": \"temp\", \"operation\": \"LE\", \"value\": 40}\n" +
                "}", ruleId);

        long t0 = System.currentTimeMillis();
        String triggerTelemetry = String.format("{\"deviceId\": \"%s\", \"temp\": 55, \"timestamp\": %d}", deviceId, t0);
        String stillHighTelemetry = String.format("{\"deviceId\": \"%s\", \"temp\": 52, \"timestamp\": %d}", deviceId, t0 + 1000);
        String clearTelemetry = String.format("{\"deviceId\": \"%s\", \"temp\": 38, \"timestamp\": %d}", deviceId, t0 + 2000);
        String afterClearTelemetry = String.format("{\"deviceId\": \"%s\", \"temp\": 39, \"timestamp\": %d}", deviceId, t0 + 3000);

        // 1. 触发
        AlarmEvaluationResult r1 = AdvancedAlarmRuleEvaluator.evaluateRule(triggerTelemetry, ruleJson);
        assertTrue("告警应触发", r1.isTriggered());
        assertFalse("告警不应清除", r1.isCleared());

        // 2. 持续 (无抑制配置，重复触发)
        AlarmEvaluationResult r2 = AdvancedAlarmRuleEvaluator.evaluateRule(stillHighTelemetry, ruleJson);
        assertTrue("告警持续触发 (无抑制)", r2.isTriggered()); // 状态已经是 active，但本次评估结果 triggered 仍为 true
        assertFalse("告警仍未清除", r2.isCleared());

        // 3. 清除
        AlarmEvaluationResult r3 = AdvancedAlarmRuleEvaluator.evaluateRule(clearTelemetry, ruleJson);
        assertFalse("告警不应触发", r3.isTriggered()); // 触发条件不满足
        assertTrue("告警应被清除", r3.isCleared());   // 清除条件满足
        assertNotNull("应有清除条件结果", r3.getClearConditionResults());
        assertTrue("清除条件评估结果应为 true", r3.getClearConditionResults().get(0).isResult());

        // 4. 清除后
        AlarmEvaluationResult r4 = AdvancedAlarmRuleEvaluator.evaluateRule(afterClearTelemetry, ruleJson);
        assertFalse("告警已清除，不应触发", r4.isTriggered());
        assertFalse("告警已清除，不应再次清除", r4.isCleared()); // 清除条件不再影响状态
    }

    /**
     * 测试场景: 条件模板的使用。规则使用模板，并覆盖模板中的部分参数。
     * 规则: 使用 testTemplate (默认 key=defaultKey, op=GT, value=0), 覆盖 key=overrideKey, value=100
     * 数据: overrideKey = 150
     * 预期: 使用 overrideKey > 100 进行判断并触发。
     */
    @Test
    public void testTemplateUsage() throws JsonProcessingException {
        String ruleId = "template-usage-rule";
        String deviceId = "dev-template-usage";
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId); // 清理状态
        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\n" +
                "    \"templateId\": \"testTemplate\", \n" +
                "    \"key\": \"overrideKey\", \n" +
                "    \"value\": 100 \n" +
                "  }\n" +
                "}", ruleId);
        String telemetryJson = String.format("{\"deviceId\": \"%s\", \"overrideKey\": 150, \"defaultKey\": -10}", deviceId);

        AlarmEvaluationResult result = AdvancedAlarmRuleEvaluator.evaluateRule(telemetryJson, ruleJson);

        assertTrue("使用模板并覆盖参数的规则应触发 (150 > 100)", result.isTriggered());
        assertNull("不应有错误消息", result.getErrorMessage());
        assertEquals("SIMPLE", result.getConditionResults().get(0).getType());
        assertEquals("overrideKey", result.getConditionResults().get(0).getKey());
        assertEquals("GT", result.getConditionResults().get(0).getOperation());
        assertEquals("100", result.getConditionResults().get(0).getValue());
    }
}
