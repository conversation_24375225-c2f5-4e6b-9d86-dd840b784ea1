package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.IotRelationEntity;
import inks.service.std.eam.domain.pojo.IotRelationPojo;
import inks.service.std.eam.mapper.IotRelationMapper;
import inks.service.std.eam.service.IotRelationService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 实体关系映射表(IotRelation)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17 11:02:54
 */
@Service("iotRelationService")
public class IotRelationServiceImpl implements IotRelationService {
    @Resource
    private IotRelationMapper iotRelationMapper;

    @Override
    public IotRelationPojo getEntity(String key, String tid) {
        return this.iotRelationMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<IotRelationPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotRelationPojo> lst = iotRelationMapper.getPageList(queryParam);
            PageInfo<IotRelationPojo> pageInfo = new PageInfo<IotRelationPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public IotRelationPojo insert(IotRelationPojo iotRelationPojo) {
        //初始化NULL字段
        cleanNull(iotRelationPojo);
        IotRelationEntity iotRelationEntity = new IotRelationEntity();
        BeanUtils.copyProperties(iotRelationPojo, iotRelationEntity);
        //生成雪花id
        iotRelationEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        iotRelationEntity.setRevision(1);  //乐观锁
        this.iotRelationMapper.insert(iotRelationEntity);
        return this.getEntity(iotRelationEntity.getId(), iotRelationEntity.getTenantid());
    }


    @Override
    public IotRelationPojo update(IotRelationPojo iotRelationPojo) {
        IotRelationEntity iotRelationEntity = new IotRelationEntity();
        BeanUtils.copyProperties(iotRelationPojo, iotRelationEntity);
        this.iotRelationMapper.update(iotRelationEntity);
        return this.getEntity(iotRelationEntity.getId(), iotRelationEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotRelationMapper.delete(key, tid);
    }


    private static void cleanNull(IotRelationPojo iotRelationPojo) {
        if (iotRelationPojo.getFromid() == null) iotRelationPojo.setFromid("");
        if (iotRelationPojo.getFromtype() == null) iotRelationPojo.setFromtype("");
        if (iotRelationPojo.getToid() == null) iotRelationPojo.setToid("");
        if (iotRelationPojo.getTotype() == null) iotRelationPojo.setTotype("");
        if (iotRelationPojo.getRelationtypegroup() == null) iotRelationPojo.setRelationtypegroup("");
        if (iotRelationPojo.getRelationtype() == null) iotRelationPojo.setRelationtype("");
        if (iotRelationPojo.getAdditionalinfo() == null) iotRelationPojo.setAdditionalinfo("");
        if (iotRelationPojo.getRemark() == null) iotRelationPojo.setRemark("");
        if (iotRelationPojo.getRownum() == null) iotRelationPojo.setRownum(0);
        if (iotRelationPojo.getCreateby() == null) iotRelationPojo.setCreateby("");
        if (iotRelationPojo.getCreatebyid() == null) iotRelationPojo.setCreatebyid("");
        if (iotRelationPojo.getCreatedate() == null) iotRelationPojo.setCreatedate(new Date());
        if (iotRelationPojo.getLister() == null) iotRelationPojo.setLister("");
        if (iotRelationPojo.getListerid() == null) iotRelationPojo.setListerid("");
        if (iotRelationPojo.getModifydate() == null) iotRelationPojo.setModifydate(new Date());
        if (iotRelationPojo.getCustom1() == null) iotRelationPojo.setCustom1("");
        if (iotRelationPojo.getCustom2() == null) iotRelationPojo.setCustom2("");
        if (iotRelationPojo.getCustom3() == null) iotRelationPojo.setCustom3("");
        if (iotRelationPojo.getCustom4() == null) iotRelationPojo.setCustom4("");
        if (iotRelationPojo.getCustom5() == null) iotRelationPojo.setCustom5("");
        if (iotRelationPojo.getCustom6() == null) iotRelationPojo.setCustom6("");
        if (iotRelationPojo.getCustom7() == null) iotRelationPojo.setCustom7("");
        if (iotRelationPojo.getCustom8() == null) iotRelationPojo.setCustom8("");
        if (iotRelationPojo.getCustom9() == null) iotRelationPojo.setCustom9("");
        if (iotRelationPojo.getCustom10() == null) iotRelationPojo.setCustom10("");
        if (iotRelationPojo.getTenantid() == null) iotRelationPojo.setTenantid("");
        if (iotRelationPojo.getTenantname() == null) iotRelationPojo.setTenantname("");
        if (iotRelationPojo.getRevision() == null) iotRelationPojo.setRevision(0);
    }

    @Override
    public List<IotRelationPojo> getList(String fromid, String fromtype, String toid, String totype, String tenantid) {
        List<IotRelationPojo> relationLst = this.iotRelationMapper.getList(fromid, fromtype, toid, totype, tenantid);

        // 1. 批量收集所有deviceId、assetId
        Set<String> deviceIds = new HashSet<>();
        Set<String> assetIds = new HashSet<>();
        for (IotRelationPojo rel : relationLst) {
            if ("device".equals(rel.getFromtype())) deviceIds.add(rel.getFromid());
            if ("device".equals(rel.getTotype())) deviceIds.add(rel.getToid());
            if ("asset".equals(rel.getFromtype())) assetIds.add(rel.getFromid());
            if ("asset".equals(rel.getTotype())) assetIds.add(rel.getToid());
        }

        // 2. 查询设备名，转map
        Map<String, String> deviceNameMap = new HashMap<>();
        if (!deviceIds.isEmpty()) {
            List<Map<String, String>> deviceList = iotRelationMapper.getDeviceNamesByIds(deviceIds, tenantid);
            for (Map<String, String> map : deviceList) {
                deviceNameMap.put(map.get("id"), map.get("devName"));
            }
        }

        // 3. 查询资产名，转map
        Map<String, String> assetNameMap = new HashMap<>();
        if (!assetIds.isEmpty()) {
            List<Map<String, String>> assetList = iotRelationMapper.getAssetNamesByIds(assetIds, tenantid);
            for (Map<String, String> map : assetList) {
                assetNameMap.put(map.get("id"), map.get("assetName"));
            }
        }

        // 4. 循环填充名称
        for (IotRelationPojo rel : relationLst) {
            if ("device".equals(rel.getFromtype()))
                rel.setFromname(deviceNameMap.getOrDefault(rel.getFromid(), "未找到设备"));
            else if ("asset".equals(rel.getFromtype()))
                rel.setFromname(assetNameMap.getOrDefault(rel.getFromid(), "未找到资产"));
            else
                rel.setFromname("不支持的类型：" + rel.getFromtype());

            if ("device".equals(rel.getTotype()))
                rel.setToname(deviceNameMap.getOrDefault(rel.getToid(), "未找到设备"));
            else if ("asset".equals(rel.getTotype()))
                rel.setToname(assetNameMap.getOrDefault(rel.getToid(), "未找到资产"));
            else
                rel.setToname("不支持的类型：" + rel.getTotype());
        }
        return relationLst;
    }


}