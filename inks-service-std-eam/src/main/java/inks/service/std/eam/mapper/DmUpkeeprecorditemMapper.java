package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmUpkeeprecorditemEntity;
import inks.service.std.eam.domain.pojo.DmUpkeeprecorditemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实施备件(DmUpkeeprecorditem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-06 15:43:02
 */
@Mapper
public interface DmUpkeeprecorditemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeeprecorditemPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmUpkeeprecorditemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmUpkeeprecorditemPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);


    /**
     * 新增数据
     *
     * @param dmUpkeeprecorditemEntity 实例对象
     * @return 影响行数
     */
    int insert(DmUpkeeprecorditemEntity dmUpkeeprecorditemEntity);


    /**
     * 修改数据
     *
     * @param dmUpkeeprecorditemEntity 实例对象
     * @return 影响行数
     */
    int update(DmUpkeeprecorditemEntity dmUpkeeprecorditemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

}

