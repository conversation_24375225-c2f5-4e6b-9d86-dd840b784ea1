package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmDeviceEntity;
import inks.service.std.eam.domain.extend.DevicePojo;
import inks.service.std.eam.domain.pojo.DmDevicePojo;
import inks.service.std.eam.domain.pojo.DmDeviceitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashSet;
import java.util.List;

/**
 * 设备台账(DmDevice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-06 12:59:49
 */
@Mapper
public interface DmDeviceMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmDevicePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmDeviceitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmDevicePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmDeviceEntity 实例对象
     * @return 影响行数
     */
    int insert(DmDeviceEntity dmDeviceEntity);


    /**
     * 修改数据
     *
     * @param dmDeviceEntity 实例对象
     * @return 影响行数
     */
    int update(DmDeviceEntity dmDeviceEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param dmDevicePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(DmDevicePojo dmDevicePojo);

    List<DevicePojo> getDeviceByDevids(@Param("devIds") List<String> devIds, @Param("tid") String tid);

    int updateUseState(@Param("unFinishDevIdsTodaySet") HashSet<String> unFinishDevIdsTodaySet, @Param("usestate") String usestate, @Param("tid") String tid);

    List<String> getDevNamesByDevids(@Param("unFinishDevsInPlan") List<String> unFinishDevsInPlan, @Param("tid") String tid);

    int checkDevcode(@Param("devcode") String devcode, @Param("id") String id, @Param("tenantid") String tenantid);

    String getMaxDevCode(@Param("tenantid") String tenantid);
}

