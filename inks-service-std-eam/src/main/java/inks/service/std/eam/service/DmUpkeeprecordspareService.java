package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmUpkeeprecordsparePojo;

import java.util.List;

/**
 * 实施备件(DmUpkeeprecordspare)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-06 15:42:45
 */
public interface DmUpkeeprecordspareService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeeprecordsparePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeeprecordsparePojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmUpkeeprecordsparePojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param dmUpkeeprecordsparePojo 实例对象
     * @return 实例对象
     */
    DmUpkeeprecordsparePojo insert(DmUpkeeprecordsparePojo dmUpkeeprecordsparePojo);

    /**
     * 修改数据
     *
     * @param dmUpkeeprecordsparepojo 实例对象
     * @return 实例对象
     */
    DmUpkeeprecordsparePojo update(DmUpkeeprecordsparePojo dmUpkeeprecordsparepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param dmUpkeeprecordsparepojo 实例对象
     * @return 实例对象
     */
    DmUpkeeprecordsparePojo clearNull(DmUpkeeprecordsparePojo dmUpkeeprecordsparepojo);
}
