package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolinfoPojo;
import inks.service.std.eam.domain.DmToolinfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具基础信息(Dm_ToolInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-09 16:30:36
 */
@Mapper
public interface DmToolinfoMapper {

    DmToolinfoPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolinfoPojo> getPageList(QueryParam queryParam);

    int insert(DmToolinfoEntity dmToolinfoEntity);

    int update(DmToolinfoEntity dmToolinfoEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    DmToolinfoPojo getEntityByToolUsageItemid(String toolusageitemid, String tid);
}

