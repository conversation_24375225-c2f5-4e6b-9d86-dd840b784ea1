package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotAssetPojo;
import inks.service.std.eam.domain.IotAssetEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 资产(Iot_Asset)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-14 13:01:05
 */
@Mapper
public interface IotAssetMapper {

    IotAssetPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotAssetPojo> getPageList(QueryParam queryParam);

    int insert(IotAssetEntity iotAssetEntity);

    int update(IotAssetEntity iotAssetEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

