package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmUpkeeprecordspareEntity;
import inks.service.std.eam.domain.pojo.DmUpkeeprecordsparePojo;
import inks.service.std.eam.mapper.DmUpkeeprecordspareMapper;
import inks.service.std.eam.service.DmUpkeeprecordspareService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 实施备件(DmUpkeeprecordspare)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-06 15:42:45
 */
@Service("dmUpkeeprecordspareService")
public class DmUpkeeprecordspareServiceImpl implements DmUpkeeprecordspareService {
    @Resource
    private DmUpkeeprecordspareMapper dmUpkeeprecordspareMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmUpkeeprecordsparePojo getEntity(String key, String tid) {
        return this.dmUpkeeprecordspareMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeeprecordsparePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeeprecordsparePojo> lst = dmUpkeeprecordspareMapper.getPageList(queryParam);
            PageInfo<DmUpkeeprecordsparePojo> pageInfo = new PageInfo<DmUpkeeprecordsparePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DmUpkeeprecordsparePojo> getList(String Pid, String tid) {
        try {
            List<DmUpkeeprecordsparePojo> lst = dmUpkeeprecordspareMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param dmUpkeeprecordsparePojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeeprecordsparePojo insert(DmUpkeeprecordsparePojo dmUpkeeprecordsparePojo) {
        //初始化item的NULL
        DmUpkeeprecordsparePojo itempojo = this.clearNull(dmUpkeeprecordsparePojo);
        DmUpkeeprecordspareEntity dmUpkeeprecordspareEntity = new DmUpkeeprecordspareEntity();
        BeanUtils.copyProperties(itempojo, dmUpkeeprecordspareEntity);
        //生成雪花id
        dmUpkeeprecordspareEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmUpkeeprecordspareEntity.setRevision(1);  //乐观锁
        this.dmUpkeeprecordspareMapper.insert(dmUpkeeprecordspareEntity);
        return this.getEntity(dmUpkeeprecordspareEntity.getId(), dmUpkeeprecordspareEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmUpkeeprecordsparePojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeeprecordsparePojo update(DmUpkeeprecordsparePojo dmUpkeeprecordsparePojo) {
        DmUpkeeprecordspareEntity dmUpkeeprecordspareEntity = new DmUpkeeprecordspareEntity();
        BeanUtils.copyProperties(dmUpkeeprecordsparePojo, dmUpkeeprecordspareEntity);
        this.dmUpkeeprecordspareMapper.update(dmUpkeeprecordspareEntity);
        return this.getEntity(dmUpkeeprecordspareEntity.getId(), dmUpkeeprecordspareEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmUpkeeprecordspareMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param dmUpkeeprecordsparePojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeeprecordsparePojo clearNull(DmUpkeeprecordsparePojo dmUpkeeprecordsparePojo) {
        //初始化NULL字段
        if (dmUpkeeprecordsparePojo.getPid() == null) dmUpkeeprecordsparePojo.setPid("");
        if (dmUpkeeprecordsparePojo.getSpareid() == null) dmUpkeeprecordsparePojo.setSpareid("");
        if (dmUpkeeprecordsparePojo.getPlanqty() == null) dmUpkeeprecordsparePojo.setPlanqty(0D);
        if (dmUpkeeprecordsparePojo.getQuantity() == null) dmUpkeeprecordsparePojo.setQuantity(0D);
        if (dmUpkeeprecordsparePojo.getPrice() == null) dmUpkeeprecordsparePojo.setPrice(0D);
        if (dmUpkeeprecordsparePojo.getAmount() == null) dmUpkeeprecordsparePojo.setAmount(0D);
        if (dmUpkeeprecordsparePojo.getRemark() == null) dmUpkeeprecordsparePojo.setRemark("");
        if (dmUpkeeprecordsparePojo.getRownum() == null) dmUpkeeprecordsparePojo.setRownum(0);
        if (dmUpkeeprecordsparePojo.getStoreid() == null) dmUpkeeprecordsparePojo.setStoreid("");
        if (dmUpkeeprecordsparePojo.getLocation() == null) dmUpkeeprecordsparePojo.setLocation("");
        if (dmUpkeeprecordsparePojo.getTaxprice() == null) dmUpkeeprecordsparePojo.setTaxprice(0D);
        if (dmUpkeeprecordsparePojo.getTaxamount() == null) dmUpkeeprecordsparePojo.setTaxamount(0D);
        if (dmUpkeeprecordsparePojo.getItemtaxrate() == null) dmUpkeeprecordsparePojo.setItemtaxrate(0D);
        if (dmUpkeeprecordsparePojo.getCustom1() == null) dmUpkeeprecordsparePojo.setCustom1("");
        if (dmUpkeeprecordsparePojo.getCustom2() == null) dmUpkeeprecordsparePojo.setCustom2("");
        if (dmUpkeeprecordsparePojo.getCustom3() == null) dmUpkeeprecordsparePojo.setCustom3("");
        if (dmUpkeeprecordsparePojo.getCustom4() == null) dmUpkeeprecordsparePojo.setCustom4("");
        if (dmUpkeeprecordsparePojo.getCustom5() == null) dmUpkeeprecordsparePojo.setCustom5("");
        if (dmUpkeeprecordsparePojo.getCustom6() == null) dmUpkeeprecordsparePojo.setCustom6("");
        if (dmUpkeeprecordsparePojo.getCustom7() == null) dmUpkeeprecordsparePojo.setCustom7("");
        if (dmUpkeeprecordsparePojo.getCustom8() == null) dmUpkeeprecordsparePojo.setCustom8("");
        if (dmUpkeeprecordsparePojo.getTenantid() == null) dmUpkeeprecordsparePojo.setTenantid("");
        if (dmUpkeeprecordsparePojo.getRevision() == null) dmUpkeeprecordsparePojo.setRevision(0);
        return dmUpkeeprecordsparePojo;
    }
}
