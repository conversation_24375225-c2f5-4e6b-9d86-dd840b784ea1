package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmPatrolrecitemEntity;
import inks.service.std.eam.domain.pojo.DmPatrolrecitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检检点明细(DmPatrolrecitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 13:10:07
 */
@Mapper
public interface DmPatrolrecitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolrecitemPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmPatrolrecitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmPatrolrecitemPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);


    /**
     * 新增数据
     *
     * @param dmPatrolrecitemEntity 实例对象
     * @return 影响行数
     */
    int insert(DmPatrolrecitemEntity dmPatrolrecitemEntity);


    /**
     * 修改数据
     *
     * @param dmPatrolrecitemEntity 实例对象
     * @return 影响行数
     */
    int update(DmPatrolrecitemEntity dmPatrolrecitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

}

