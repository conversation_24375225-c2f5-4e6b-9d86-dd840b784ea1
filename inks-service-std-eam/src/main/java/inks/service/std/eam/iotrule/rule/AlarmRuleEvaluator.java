package inks.service.std.eam.iotrule.rule;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import inks.service.std.eam.iotrule.alarm.AlarmCondition;
import inks.service.std.eam.iotrule.alarm.SimpleCondition;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class AlarmRuleEvaluator {
    // 示例使用
    public static void main(String[] args) {
        // 规则JSON
        String ruleJson = "{\n" +
                "  \"name\": \"高温高压告警\",\n" +
                "  \"type\": \"SIMPLE\",\n" +
                "  \"condition\": {\n" +
                "    \"type\": \"AND\",\n" +
                "    \"conditions\": [\n" +
                "      {\n" +
                "        \"type\": \"SIMPLE\",\n" +
                "        \"key\": \"temp\",\n" +
                "        \"operation\": \"GREATER\",\n" +
                "        \"value\": 50\n" +
                "      },\n" +
                "      {\n" +
                "        \"type\": \"SIMPLE\",\n" +
                "        \"key\": \"dianya\",\n" +
                "        \"operation\": \"EQUALS\",\n" +
                "        \"value\": \"220V\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  \"severity\": \"WARNING\",\n" +
                "  \"dashboardId\": \"dashboard-001\"\n" +
                "}";

        // 遥测数据JSON
        String telemetryJson = "{\"temp\":170,\"dianya\":\"220V\"}";

        // 评估规则
        AlarmEvaluationResult result = evaluateRule(telemetryJson, ruleJson);

        // 打印结果
        System.out.println(result);
    }
    /**
     * 根据JSON格式的规则定义和遥测数据判断是否触发告警
     * 
     * @param ruleJson JSON格式的规则定义(警告规则内容)
     * @param mqttJson JSON格式的遥测数据
     * @return 告警结果，包含是否触发及详细信息
     */
    public static AlarmEvaluationResult evaluateRule(String mqttJson, String ruleJson) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            
            // 解析规则和遥测数据
            JsonNode ruleNode = mapper.readTree(ruleJson);
            Map<String, Object> telemetry = mapper.readValue(mqttJson, Map.class);
            
            // 创建评估结果对象
            AlarmEvaluationResult result = new AlarmEvaluationResult();
            result.setRuleName(ruleNode.path("name").asText("未命名规则"));
            result.setRuleType(ruleNode.path("type").asText("SIMPLE"));
            result.setConfiguration(ruleJson);
            result.setSeverity(ruleNode.path("severity").asText("WARNING"));
            result.setTelemetryData(telemetry);
            
            // 解析和评估条件
            JsonNode conditionNode = ruleNode.path("condition");
            boolean triggered = evaluateCondition(conditionNode, telemetry, result.getConditionResults());
            result.setTriggered(triggered);
            
            return result;
        } catch (IOException e) {
            AlarmEvaluationResult errorResult = new AlarmEvaluationResult();
            errorResult.setTriggered(false);
            errorResult.setErrorMessage("解析JSON出错: " + e.getMessage());
            return errorResult;
        }
    }
    
    /**
     * 递归评估条件节点
     */
    private static boolean evaluateCondition(JsonNode conditionNode, Map<String, Object> telemetry, 
                                            List<ConditionResult> results) {
        String type = conditionNode.path("type").asText("UNKNOWN");
        
        if ("SIMPLE".equals(type)) {
            // 处理简单条件
            String key = conditionNode.path("key").asText();
            String operation = conditionNode.path("operation").asText();
            JsonNode valueNode = conditionNode.path("value");
            
            Object value;
            if (valueNode.isNumber()) {
                value = valueNode.isInt() ? valueNode.asInt() : valueNode.asDouble();
            } else {
                value = valueNode.asText();
            }
            
            // 创建并评估简单条件
            SimpleCondition condition = new SimpleCondition(key, operation, value);
            boolean result = condition.evaluate(telemetry);
            
            // 记录条件结果
            ConditionResult condResult = new ConditionResult();
            condResult.setType("SIMPLE");
            condResult.setKey(key);
            condResult.setOperation(operation);
            condResult.setValue(value.toString());
            condResult.setActualValue(telemetry.containsKey(key) ? telemetry.get(key).toString() : "未找到");
            condResult.setResult(result);
            results.add(condResult);
            
            return result;
            
        } else if ("AND".equals(type) || "OR".equals(type)) {
            // 处理复合条件
            JsonNode conditions = conditionNode.path("conditions");
            List<AlarmCondition> conditionList = new ArrayList<>();
            List<ConditionResult> subResults = new ArrayList<>();
            
            // 递归处理每个子条件
            for (JsonNode subCondition : conditions) {
                boolean subResult = evaluateCondition(subCondition, telemetry, subResults);
                // 对于AND条件，可以提前退出
                if ("AND".equals(type) && !subResult) {
                    ConditionResult groupResult = new ConditionResult();
                    groupResult.setType(type);
                    groupResult.setResult(false);
                    groupResult.setSubConditions(subResults);
                    results.add(groupResult);
                    return false;
                }
                // 对于OR条件，可以提前退出
                if ("OR".equals(type) && subResult) {
                    ConditionResult groupResult = new ConditionResult();
                    groupResult.setType(type);
                    groupResult.setResult(true);
                    groupResult.setSubConditions(subResults);
                    results.add(groupResult);
                    return true;
                }
            }
            
            // 如果没有提前退出，计算最终结果
            boolean finalResult = "AND".equals(type);  // AND默认为true，OR默认为false
            
            ConditionResult groupResult = new ConditionResult();
            groupResult.setType(type);
            groupResult.setResult(finalResult);
            groupResult.setSubConditions(subResults);
            results.add(groupResult);
            
            return finalResult;
        }
        
        // 未知条件类型
        ConditionResult unknownResult = new ConditionResult();
        unknownResult.setType("UNKNOWN");
        unknownResult.setResult(false);
        unknownResult.setErrorMessage("未知的条件类型: " + type);
        results.add(unknownResult);
        
        return false;
    }
    
    // 告警评估结果类
    public static class AlarmEvaluationResult {
        private String ruleName;
        private String ruleType;
        private String configuration;// 警告规则内容
        private String severity;
        private boolean triggered;
        private Map<String, Object> telemetryData;
        private List<ConditionResult> conditionResults = new ArrayList<>();
        private String errorMessage;
        
        // Getters and Setters
        public String getRuleName() { return ruleName; }
        public void setRuleName(String ruleName) { this.ruleName = ruleName; }
        
        public String getRuleType() { return ruleType; }
        public void setRuleType(String ruleType) { this.ruleType = ruleType; }
        public String getConfiguration() { return configuration; }
        public void setConfiguration(String configuration) { this.configuration = configuration; }
        
        public String getSeverity() { return severity; }
        public void setSeverity(String severity) { this.severity = severity; }
        
        public boolean isTriggered() { return triggered; }
        public void setTriggered(boolean triggered) { this.triggered = triggered; }
        
        public Map<String, Object> getTelemetryData() { return telemetryData; }
        public void setTelemetryData(Map<String, Object> telemetryData) { this.telemetryData = telemetryData; }
        
        public List<ConditionResult> getConditionResults() { return conditionResults; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("告警评估结果:\n");
            sb.append("规则名称: ").append(ruleName).append("\n");
            sb.append("规则类型: ").append(ruleType).append("\n");
            sb.append("告警级别: ").append(severity).append("\n");
            sb.append("是否触发: ").append(triggered ? "是" : "否").append("\n");
            
            if (errorMessage != null && !errorMessage.isEmpty()) {
                sb.append("错误信息: ").append(errorMessage).append("\n");
            }
            
            sb.append("遥测数据: ").append(telemetryData).append("\n");
            sb.append("警告规则: ").append(configuration).append("\n");
            sb.append("条件评估详情:\n");
            printConditionResults(conditionResults, sb, 1);
            
            return sb.toString();
        }
        
        private void printConditionResults(List<ConditionResult> results, StringBuilder sb, int depth) {
            //String indent = "  ".repeat(depth); jdk11
            // 如果要兼容 Java8，可以这样写：
            StringBuilder indentBuilder = new StringBuilder();
            for (int i = 0; i < depth; i++) {
                indentBuilder.append("  ");
            }
            String indent = indentBuilder.toString();
            for (ConditionResult result : results) {
                sb.append(indent);
                if ("SIMPLE".equals(result.getType())) {
                    sb.append("- 条件: ").append(result.getKey()).append(" ")
                      .append(formatOperation(result.getOperation())).append(" ")
                      .append(result.getValue()).append("\n");
                    sb.append(indent).append("  实际值: ").append(result.getActualValue()).append("\n");
                    sb.append(indent).append("  结果: ").append(result.isResult() ? "满足" : "不满足").append("\n");
                } else if ("AND".equals(result.getType()) || "OR".equals(result.getType())) {
                    sb.append("- 复合条件 (").append(result.getType()).append(")").append("\n");
                    sb.append(indent).append("  结果: ").append(result.isResult() ? "满足" : "不满足").append("\n");
                    printConditionResults(result.getSubConditions(), sb, depth + 1);
                }
            }
        }
        
        private String formatOperation(String operation) {
            switch (operation) {
                case "EQUALS": return "=";
                case "NOT_EQUALS": return "!=";
                case "GREATER": return ">";
                case "LESS": return "<";
                case "GREATER_OR_EQUAL": return ">=";
                case "LESS_OR_EQUAL": return "<=";
                default: return operation;
            }
        }
    }
    
    // 条件评估结果类
    public static class ConditionResult {
        private String type;
        private String key;
        private String operation;
        private String value;
        private String actualValue;
        private boolean result;
        private List<ConditionResult> subConditions = new ArrayList<>();
        private String errorMessage;
        
        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getKey() { return key; }
        public void setKey(String key) { this.key = key; }
        
        public String getOperation() { return operation; }
        public void setOperation(String operation) { this.operation = operation; }
        
        public String getValue() { return value; }
        public void setValue(String value) { this.value = value; }
        
        public String getActualValue() { return actualValue; }
        public void setActualValue(String actualValue) { this.actualValue = actualValue; }
        
        public boolean isResult() { return result; }
        public void setResult(boolean result) { this.result = result; }
        
        public List<ConditionResult> getSubConditions() { return subConditions; }
        public void setSubConditions(List<ConditionResult> subConditions) { this.subConditions = subConditions; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
    

}