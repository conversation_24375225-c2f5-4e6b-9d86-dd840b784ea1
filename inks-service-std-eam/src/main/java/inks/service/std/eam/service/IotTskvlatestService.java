package inks.service.std.eam.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotTskvlatestPojo;

import java.util.List;

/**
 * 最新时间序列遥测数据快照表(Iot_TsKvLatest)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-17 10:44:01
 */
public interface IotTskvlatestService {

    IotTskvlatestPojo getEntity(String key,String tid);

    PageInfo<IotTskvlatestPojo> getPageList(QueryParam queryParam);

    IotTskvlatestPojo insert(IotTskvlatestPojo iotTskvlatestPojo);

    IotTskvlatestPojo update(IotTskvlatestPojo iotTskvlatestpojo);

    int delete(String key,String tid);

    int batchLatest(JSONObject mqttJSON);

    List<IotTskvlatestPojo> getList(String entityid, String tenantid);
}
