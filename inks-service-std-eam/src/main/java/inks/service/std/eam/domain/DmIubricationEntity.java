package inks.service.std.eam.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备润滑(DmIubrication)实体类
 *
 * <AUTHOR>
 * @since 2023-06-10 12:40:14
 */
public class DmIubricationEntity implements Serializable {
    private static final long serialVersionUID = -24322010492752889L;
    // ID
    private String id;
    // 润滑编号
    private String refno;
    // 润滑名称
    private String billtype;
    // 责任人
    private String operator;
    // 设备编号
    private String deviceid;
    // 设备名称
    private String devicename;
    // 人员编号
    private String useid;
    // 人员名称
    private String usestaff;
    // 润滑内容
    private String iubdetails;
    // 润滑方案
    private String iubplan;
    // 安全规则
    private String safetyrules;
    // 审核员id
    private String assessorid;
    // 审核日期
    private Date assessdate;
    // 审核人
    private String assessor;
    // 单据日期
    private Date billdate;
    // 操作内容
    private String opedetails;
    // 备注
    private String remark;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 新建日期
    private Date createdate;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 修改日期
    private Date modifydate;
    // 有效标识
    private Integer enabledmark;
    // 删除标识
    private Integer deletemark;
    // 删除人员
    private String deletelister;
    // 删除日期
    private Date deletedate;
    // 租户id
    private String tenantid;
    // 乐观锁
    private Integer revision;

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 润滑编号
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 润滑名称
    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }

    // 责任人
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    // 设备编号
    public String getDeviceid() {
        return deviceid;
    }

    public void setDeviceid(String deviceid) {
        this.deviceid = deviceid;
    }

    // 设备名称
    public String getDevicename() {
        return devicename;
    }

    public void setDevicename(String devicename) {
        this.devicename = devicename;
    }

    // 人员编号
    public String getUseid() {
        return useid;
    }

    public void setUseid(String useid) {
        this.useid = useid;
    }

    // 人员名称
    public String getUsestaff() {
        return usestaff;
    }

    public void setUsestaff(String usestaff) {
        this.usestaff = usestaff;
    }

    // 润滑内容
    public String getIubdetails() {
        return iubdetails;
    }

    public void setIubdetails(String iubdetails) {
        this.iubdetails = iubdetails;
    }

    // 润滑方案
    public String getIubplan() {
        return iubplan;
    }

    public void setIubplan(String iubplan) {
        this.iubplan = iubplan;
    }

    // 安全规则
    public String getSafetyrules() {
        return safetyrules;
    }

    public void setSafetyrules(String safetyrules) {
        this.safetyrules = safetyrules;
    }

    // 审核员id
    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }

    // 审核日期
    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }

    // 审核人
    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }

    // 单据日期
    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }

    // 操作内容
    public String getOpedetails() {
        return opedetails;
    }

    public void setOpedetails(String opedetails) {
        this.opedetails = opedetails;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 删除标识
    public Integer getDeletemark() {
        return deletemark;
    }

    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }

    // 删除人员
    public String getDeletelister() {
        return deletelister;
    }

    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }

    // 删除日期
    public Date getDeletedate() {
        return deletedate;
    }

    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

