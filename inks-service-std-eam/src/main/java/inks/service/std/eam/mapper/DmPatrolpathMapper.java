package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmPatrolpathEntity;
import inks.service.std.eam.domain.pojo.DmPatrolpathPojo;
import inks.service.std.eam.domain.pojo.DmPatrolpathitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡检路线(DmPatrolpath)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 12:57:52
 */
@Mapper
public interface DmPatrolpathMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolpathPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmPatrolpathitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmPatrolpathPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmPatrolpathEntity 实例对象
     * @return 影响行数
     */
    int insert(DmPatrolpathEntity dmPatrolpathEntity);


    /**
     * 修改数据
     *
     * @param dmPatrolpathEntity 实例对象
     * @return 影响行数
     */
    int update(DmPatrolpathEntity dmPatrolpathEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param dmPatrolpathPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(DmPatrolpathPojo dmPatrolpathPojo);

    /**
     * 修改数据
     *
     * @param dmPatrolpathEntity 实例对象
     * @return 影响行数
     */
    int approval(DmPatrolpathEntity dmPatrolpathEntity);
}

