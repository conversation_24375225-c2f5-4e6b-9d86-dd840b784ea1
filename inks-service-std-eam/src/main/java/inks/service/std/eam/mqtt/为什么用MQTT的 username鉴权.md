**为什么 ThingsBoard 用 MQTT 的 `username`（存放 Access Token）做鉴权，而不是直接用 `clientId`？**

---

### 1 · MQTT 规范本身就把 `username / password` 留给“身份验证”

* MQTT `CONNECT` 报文专门提供了 `username` 和 `password` 字段；
  ThingsBoard 只是把 **设备的 Access Token 填进 `username`**，密码留空即可。官方文档和示例都这样写：

  ````bash
  mosquitto_pub -h demo.thingsboard.io \
    -t v1/devices/me/telemetry \
    -u "$ACCESS_TOKEN" -m '{"temperature":25}'
  ``` :contentReference[oaicite:0]{index=0}

  ````
* 在其“Access Token 鉴权”说明里也明确：**客户端必须将 Access Token 作为 MQTT `username` 发送**。([ThingsBoard][1])

> 这样遵循了协议设计意图，又避免额外字段扩展。

---

### 2 · 保留 `clientId` 做“会话标识”，能解决多连/重连场景

* `clientId` 用来区分同一 Broker 上的不同网络会话，与**安全**关系不大。
  把凭证塞进 `clientId` 会导致：

    1. 同一设备两条连接（主备/多网口）只能用同一个 ID，会互相踢线；
    2. 想临时改 `clientId`（例如断电后随机生成）却要同时换掉凭证，运维成本大。

分离两者后，设备可保持 **固定 Token + 灵活 clientId** 的组合，既安全又易于高可用部署。

---

### 3 · Token 可重用于 HTTP、CoAP 等多协议，统一鉴权模型

ThingsBoard 所有南向协议都支持 “Access Token” 这种凭证形式；把 Token 放在 `username`
字段，**MQTT 层无需独立生成/维护另一套密钥**，大大简化了设备侧与平台侧的对接和运维。([ThingsBoard][2])

---

### 4 · 兼容老旧 MQTT 3.1 库的 `clientId` 长度限制

* MQTT 3.1 规范要求 Broker **至少** 接受 23 字节的 `clientId`，很多旧库、嵌入式栈仍硬编码此限制；
  Access Token 往往是 32 \~ 64 字符的 UUID / 随机串，直接放进 `clientId` 会被拒绝。([Stack Overflow][3])
* 把 Token 放到 `username` 字段就没有这个顾虑，客户端也不必升级到最新协议栈。

---

### 5 · 资源受限设备更省流量

* 只填一个 `username`，`password` 置空，比 “clientId+username+password” 组合少几十字节；
  **对低带宽、低功耗节点更友好**。官方“设备凭证比较”一节也把 “低开销、易于使用” 作为 Access Token 的主要优点。([ThingsBoard][2])

---

### 6 · 如果**确实**要基于 `clientId` 做安全校验，ThingsBoard 也提供了可选方案

把设备凭证类型改成 **MQTT Basic（clientId + username + password）**，平台就会同时验证 ID、用户名和密码；只是在多数 IoT 场景下没 Token 方案来得简洁。([ThingsBoard][4])

---

#### 总结

* **`username` 用来送凭证是 MQTT 的“正途”，`clientId` 仍承担会话唯一标识。**
* 这样既满足跨协议一致的 Token 体系，又回避 `clientId` 长度与多连接冲突问题，且对资源受限设备友好。
* ThingsBoard 仍保留客户端按需切换到 “clientId+username+password” 或 X.509 证书等更强鉴权模式，开发者可按项目安全等级选择。

[1]: https://thingsboard.io/docs/pe/user-guide/access-token/ "Access Token based authentication for MQTT | ThingsBoard Professional Edition"
[2]: https://thingsboard.io/docs/user-guide/device-credentials/ "Device authentication options | ThingsBoard Community Edition"
[3]: https://stackoverflow.com/questions/45774538/what-is-the-maximum-length-of-client-id-in-mqtt "What is the maximum length of client ID in MQTT? - Stack Overflow"
[4]: https://thingsboard.io/docs/user-guide/basic-mqtt/?utm_source=chatgpt.com "Basic MQTT authentication | ThingsBoard Community Edition"
