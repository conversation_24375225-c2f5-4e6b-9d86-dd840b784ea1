package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.DmDevicePojo;
import inks.service.std.eam.service.DmDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 设备台账(Dm_Device)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-06 12:47:48
 */
@RestController
@RequestMapping("D09M01B1")
@Api(tags = "D09M01B1:设备台账")
public class D09M01B1Controller extends DmDeviceController {
    @Resource
    private DmDeviceService dmDeviceService;

    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "获取生成设备编码(获取数据库中devcode最大值+1)", notes = "获取生成设备编码(获取devcode最大值+1)", produces = "application/json")
    @RequestMapping(value = "/getMaxDevCode", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Device.List")
    public R<String> getMaxDevCode() {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String maxDevCode = this.dmDeviceService.getMaxDevCode(loginUser.getTenantid());
            //maxDevCode加1 且不足4位前面补0
            String devCode = String.format("%04d", Integer.parseInt(maxDevCode) + 1);
            return R.ok(devCode);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取未报修设备Dm_Device.UseState not in('报修','维修中')", notes = "获取未报修设备(UseState!='报修','维修中')", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Device.List")
    public R<PageInfo<DmDevicePojo>> getOnlinePageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_Device.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Dm_Device.UseState not in('报修','维修中')";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmDeviceService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
