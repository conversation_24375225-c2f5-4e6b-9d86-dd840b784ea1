package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.IotRulenodePojo;
import inks.service.std.eam.service.IotRulenodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 规则链节点配置(Iot_RuleNode)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-08 13:01:06
 */
@RestController
@RequestMapping("D09M32B2")
@Api(tags = "D09M32B2:规则链节点配置")
public class D09M32B2Controller extends IotRulenodeController {

    @Resource
    private IotRulenodeService iotRulenodeService;
    @Resource
    private TokenService tokenService;


    @ApiOperation(value = "按条件查询", notes = "", produces = "application/json")
    @RequestMapping(value = "/getListByChainid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_RuleNode.List")
    public R<List<IotRulenodePojo>> getListByChainid(String chainid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotRulenodeService.getListByChainid(chainid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
