package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmMoldPojo;
import com.github.pagehelper.PageInfo;

/**
 * 模具管理(Dm_Mold)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-13 14:12:19
 */
public interface DmMoldService {

    DmMoldPojo getEntity(String key,String tid);

    PageInfo<DmMoldPojo> getPageList(QueryParam queryParam);

    DmMoldPojo insert(DmMoldPojo dmMoldPojo);

    DmMoldPojo update(DmMoldPojo dmMoldpojo);

    int delete(String key,String tid);
}
