package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmKnowledgePojo;

/**
 * 设备知识库(DmKnowledge)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 11:22:51
 */
public interface DmKnowledgeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmKnowledgePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmKnowledgePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmKnowledgePojo 实例对象
     * @return 实例对象
     */
    DmKnowledgePojo insert(DmKnowledgePojo dmKnowledgePojo);

    /**
     * 修改数据
     *
     * @param dmKnowledgepojo 实例对象
     * @return 实例对象
     */
    DmKnowledgePojo update(DmKnowledgePojo dmKnowledgepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
