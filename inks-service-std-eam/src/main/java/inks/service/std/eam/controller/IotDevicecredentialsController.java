package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotDevicecredentialsPojo;
import inks.service.std.eam.service.IotDevicecredentialsService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 设备鉴权信息表(Iot_DeviceCredentials)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:14
 */
//@RestController
//@RequestMapping("iotDevicecredentials")
public class IotDevicecredentialsController {

    @Resource
    private IotDevicecredentialsService iotDevicecredentialsService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(IotDevicecredentialsController.class);


    @ApiOperation(value=" 获取设备鉴权信息表详细信息", notes="获取设备鉴权信息表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_DeviceCredentials.List")
    public R<IotDevicecredentialsPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotDevicecredentialsService.getEntity(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_DeviceCredentials.List")
    public R<PageInfo<IotDevicecredentialsPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Iot_DeviceCredentials.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.iotDevicecredentialsService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增设备鉴权信息表", notes="新增设备鉴权信息表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_DeviceCredentials.Add")
    public R<IotDevicecredentialsPojo> create(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
       IotDevicecredentialsPojo iotDevicecredentialsPojo = JSONArray.parseObject(json,IotDevicecredentialsPojo.class);       
            iotDevicecredentialsPojo.setCreateby(loginUser.getRealName());   // 创建者
            iotDevicecredentialsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            iotDevicecredentialsPojo.setCreatedate(new Date());   // 创建时间
            iotDevicecredentialsPojo.setLister(loginUser.getRealname());   // 制表
            iotDevicecredentialsPojo.setListerid(loginUser.getUserid());    // 制表id  
            iotDevicecredentialsPojo.setModifydate(new Date());   //修改时间
            iotDevicecredentialsPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.iotDevicecredentialsService.insert(iotDevicecredentialsPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改设备鉴权信息表", notes="修改设备鉴权信息表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_DeviceCredentials.Edit")
    public R<IotDevicecredentialsPojo> update(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
         IotDevicecredentialsPojo iotDevicecredentialsPojo = JSONArray.parseObject(json,IotDevicecredentialsPojo.class);
            iotDevicecredentialsPojo.setLister(loginUser.getRealname());   // 制表
            iotDevicecredentialsPojo.setListerid(loginUser.getUserid());    // 制表id  
            iotDevicecredentialsPojo.setTenantid(loginUser.getTenantid());   //租户id
            iotDevicecredentialsPojo.setModifydate(new Date());   //修改时间
//            iotDevicecredentialsPojo.setAssessor(""); // 审核员
//            iotDevicecredentialsPojo.setAssessorid(""); // 审核员id
//            iotDevicecredentialsPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.iotDevicecredentialsService.update(iotDevicecredentialsPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除设备鉴权信息表", notes="删除设备鉴权信息表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_DeviceCredentials.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotDevicecredentialsService.delete(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_DeviceCredentials.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        IotDevicecredentialsPojo iotDevicecredentialsPojo = this.iotDevicecredentialsService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(iotDevicecredentialsPojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

