package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolmaintenancePojo;
import inks.service.std.eam.domain.pojo.DmToolmaintenanceitemdetailPojo;
import inks.service.std.eam.domain.DmToolmaintenanceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具保养记录表(DmToolmaintenance)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:20
 */
@Mapper
public interface DmToolmaintenanceMapper {

    DmToolmaintenancePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolmaintenanceitemdetailPojo> getPageList(QueryParam queryParam);

    List<DmToolmaintenancePojo> getPageTh(QueryParam queryParam);

    int insert(DmToolmaintenanceEntity dmToolmaintenanceEntity);

    int update(DmToolmaintenanceEntity dmToolmaintenanceEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(DmToolmaintenancePojo dmToolmaintenancePojo);
}

