package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmToolusageitemEntity;
import inks.service.std.eam.domain.pojo.DmToolusageitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 工装具使用记录子表(DmToolusageitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:35
 */
 @Mapper
public interface DmToolusageitemMapper {

    DmToolusageitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolusageitemPojo> getPageList(QueryParam queryParam);

    List<DmToolusageitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(DmToolusageitemEntity dmToolusageitemEntity);

    int update(DmToolusageitemEntity dmToolusageitemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    int getSumUsedQtyAfterDate(String toolid, Date maintdate, String tid);
}

