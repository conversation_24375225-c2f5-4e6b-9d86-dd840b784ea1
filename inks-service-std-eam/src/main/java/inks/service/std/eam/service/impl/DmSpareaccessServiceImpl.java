package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmSpareaccessEntity;
import inks.service.std.eam.domain.DmSpareaccessitemEntity;
import inks.service.std.eam.domain.pojo.DmSpareaccessPojo;
import inks.service.std.eam.domain.pojo.DmSpareaccessitemPojo;
import inks.service.std.eam.domain.pojo.DmSpareaccessitemdetailPojo;
import inks.service.std.eam.domain.pojo.DmSpareinventoryPojo;
import inks.service.std.eam.mapper.DmSpareMapper;
import inks.service.std.eam.mapper.DmSpareaccessMapper;
import inks.service.std.eam.mapper.DmSpareaccessitemMapper;
import inks.service.std.eam.service.DmSpareaccessService;
import inks.service.std.eam.service.DmSpareaccessitemService;
import inks.service.std.eam.service.DmSpareinventoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 备件出入(DmSpareaccess)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 10:09:25
 */
@Service("dmSpareaccessService")
public class DmSpareaccessServiceImpl implements DmSpareaccessService {
    @Resource
    private DmSpareaccessMapper dmSpareaccessMapper;

    @Resource
    private DmSpareaccessitemMapper dmSpareaccessitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private DmSpareaccessitemService dmSpareaccessitemService;
    @Resource
    private DmSpareinventoryService dmSpareinventoryService;
    @Resource
    private DmSpareMapper dmSpareMapper;

    private static void cleanNull(DmSpareaccessPojo dmSpareaccessPojo) {
        //初始化NULL字段
        if (dmSpareaccessPojo.getRefno() == null) dmSpareaccessPojo.setRefno("");
        if (dmSpareaccessPojo.getBilldate() == null) dmSpareaccessPojo.setBilldate(new Date());
        if (dmSpareaccessPojo.getBilltype() == null) dmSpareaccessPojo.setBilltype("");
        if (dmSpareaccessPojo.getBilltitle() == null) dmSpareaccessPojo.setBilltitle("");
        if (dmSpareaccessPojo.getDirection() == null) dmSpareaccessPojo.setDirection("");
        if (dmSpareaccessPojo.getGroupid() == null) dmSpareaccessPojo.setGroupid("");
        if (dmSpareaccessPojo.getStoreid() == null) dmSpareaccessPojo.setStoreid("");
        if (dmSpareaccessPojo.getOperator() == null) dmSpareaccessPojo.setOperator("");
        if (dmSpareaccessPojo.getSummary() == null) dmSpareaccessPojo.setSummary("");
        if (dmSpareaccessPojo.getReturnuid() == null) dmSpareaccessPojo.setReturnuid("");
        if (dmSpareaccessPojo.getOrguid() == null) dmSpareaccessPojo.setOrguid("");
        if (dmSpareaccessPojo.getPlusinfo() == null) dmSpareaccessPojo.setPlusinfo("");
        if (dmSpareaccessPojo.getCustom1() == null) dmSpareaccessPojo.setCustom1("");
        if (dmSpareaccessPojo.getCustom2() == null) dmSpareaccessPojo.setCustom2("");
        if (dmSpareaccessPojo.getCustom3() == null) dmSpareaccessPojo.setCustom3("");
        if (dmSpareaccessPojo.getCustom4() == null) dmSpareaccessPojo.setCustom4("");
        if (dmSpareaccessPojo.getCustom5() == null) dmSpareaccessPojo.setCustom5("");
        if (dmSpareaccessPojo.getCustom6() == null) dmSpareaccessPojo.setCustom6("");
        if (dmSpareaccessPojo.getCustom7() == null) dmSpareaccessPojo.setCustom7("");
        if (dmSpareaccessPojo.getCustom8() == null) dmSpareaccessPojo.setCustom8("");
        if (dmSpareaccessPojo.getLister() == null) dmSpareaccessPojo.setLister("");
        if (dmSpareaccessPojo.getListerid() == null) dmSpareaccessPojo.setListerid("");
        if (dmSpareaccessPojo.getCreateby() == null) dmSpareaccessPojo.setCreateby("");
        if (dmSpareaccessPojo.getCreatebyid() == null) dmSpareaccessPojo.setCreatebyid("");
        if (dmSpareaccessPojo.getCreatedate() == null) dmSpareaccessPojo.setCreatedate(new Date());
        if (dmSpareaccessPojo.getModifydate() == null) dmSpareaccessPojo.setModifydate(new Date());
        if (dmSpareaccessPojo.getAssessorid() == null) dmSpareaccessPojo.setAssessorid("");
        if (dmSpareaccessPojo.getAssessor() == null) dmSpareaccessPojo.setAssessor("");
        if (dmSpareaccessPojo.getAssessdate() == null) dmSpareaccessPojo.setAssessdate(new Date());
        if (dmSpareaccessPojo.getTenantid() == null) dmSpareaccessPojo.setTenantid("");
        if (dmSpareaccessPojo.getRevision() == null) dmSpareaccessPojo.setRevision(0);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmSpareaccessPojo getEntity(String key, String tid) {
        return this.dmSpareaccessMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmSpareaccessitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmSpareaccessitemdetailPojo> lst = dmSpareaccessMapper.getPageList(queryParam);
            PageInfo<DmSpareaccessitemdetailPojo> pageInfo = new PageInfo<DmSpareaccessitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmSpareaccessPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            DmSpareaccessPojo dmSpareaccessPojo = this.dmSpareaccessMapper.getEntity(key, tid);
            //读取子表
            dmSpareaccessPojo.setItem(dmSpareaccessitemMapper.getList(dmSpareaccessPojo.getId(), dmSpareaccessPojo.getTenantid()));
            return dmSpareaccessPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmSpareaccessPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmSpareaccessPojo> lst = dmSpareaccessMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(dmSpareaccessitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<DmSpareaccessPojo> pageInfo = new PageInfo<DmSpareaccessPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmSpareaccessPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmSpareaccessPojo> lst = dmSpareaccessMapper.getPageTh(queryParam);
            PageInfo<DmSpareaccessPojo> pageInfo = new PageInfo<DmSpareaccessPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmSpareaccessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmSpareaccessPojo insert(DmSpareaccessPojo dmSpareaccessPojo) {
        String tid = dmSpareaccessPojo.getTenantid();
        //清除空值
        cleanNull(dmSpareaccessPojo);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmSpareaccessEntity dmSpareaccessEntity = new DmSpareaccessEntity();
        BeanUtils.copyProperties(dmSpareaccessPojo, dmSpareaccessEntity);

        //设置id和新建日期
        dmSpareaccessEntity.setId(id);
        dmSpareaccessEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmSpareaccessMapper.insert(dmSpareaccessEntity);
        //Item子表处理
        List<DmSpareaccessitemPojo> lst = dmSpareaccessPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                DmSpareaccessitemPojo itemPojo = this.dmSpareaccessitemService.clearNull(lst.get(i));
                DmSpareaccessitemEntity dmSpareaccessitemEntity = new DmSpareaccessitemEntity();
                BeanUtils.copyProperties(itemPojo, dmSpareaccessitemEntity);
                //设置id和Pid
                dmSpareaccessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmSpareaccessitemEntity.setPid(id);
                dmSpareaccessitemEntity.setTenantid(tid);
                dmSpareaccessitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmSpareaccessitemMapper.insert(dmSpareaccessitemEntity);
                // 插入备件库存
                // 检查备件库存DB中是否存在
                DmSpareinventoryPojo spareInventoryPojoDB = dmSpareinventoryService.getEntityBySpareid(itemPojo.getSpareid(), itemPojo.getLocation(), itemPojo.getBatchno(), tid);
                if (spareInventoryPojoDB != null) {
                    spareInventoryPojoDB.setEnduid(dmSpareaccessPojo.getRefno());
                    if ("备件入库".equals(dmSpareaccessPojo.getBilltype())) {
                        spareInventoryPojoDB.setEndinuid(dmSpareaccessPojo.getRefno());
                        spareInventoryPojoDB.setEndindate(new Date());
                        spareInventoryPojoDB.setQuantity(spareInventoryPojoDB.getQuantity() + itemPojo.getQuantity());
                        spareInventoryPojoDB.setAmount(spareInventoryPojoDB.getAmount() + itemPojo.getAmount());
                    } else {
                        spareInventoryPojoDB.setEndoutuid(dmSpareaccessPojo.getRefno());
                        spareInventoryPojoDB.setEndoutdate(new Date());
                        spareInventoryPojoDB.setQuantity(spareInventoryPojoDB.getQuantity() - itemPojo.getQuantity());
                        spareInventoryPojoDB.setAmount(spareInventoryPojoDB.getAmount() - itemPojo.getAmount());
                    }
                    dmSpareinventoryService.update(spareInventoryPojoDB);
                    dmSpareMapper.updateIvQuantity(spareInventoryPojoDB.getQuantity(), dmSpareaccessitemEntity.getSpareid(), tid);
                } else {
                    DmSpareinventoryPojo spareInventoryPojo = new DmSpareinventoryPojo();
                    spareInventoryPojo.setSpareid(itemPojo.getSpareid());
                    spareInventoryPojo.setStoreid(dmSpareaccessPojo.getStoreid());
                    spareInventoryPojo.setQuantity(itemPojo.getQuantity());
                    spareInventoryPojo.setBatchno(itemPojo.getBatchno());
                    spareInventoryPojo.setLocation(itemPojo.getLocation());
                    spareInventoryPojo.setEnduid(dmSpareaccessPojo.getRefno());
                    if ("备件入库".equals(dmSpareaccessPojo.getBilltype())) {
                        spareInventoryPojo.setEndinuid(dmSpareaccessPojo.getRefno());
                        spareInventoryPojo.setEndindate(new Date());
                        spareInventoryPojo.setQuantity(itemPojo.getQuantity());
                        spareInventoryPojo.setAmount(itemPojo.getAmount());
                    } else {
                        spareInventoryPojo.setEndoutuid(dmSpareaccessPojo.getRefno());
                        spareInventoryPojo.setEndoutdate(new Date());
                        spareInventoryPojo.setQuantity(0 - itemPojo.getQuantity());
                        spareInventoryPojo.setAmount(0 - itemPojo.getAmount());
                    }
                    spareInventoryPojo.setTenantid(tid);
                    dmSpareinventoryService.insert(spareInventoryPojo);
                    dmSpareMapper.updateIvQuantity(spareInventoryPojo.getQuantity(), dmSpareaccessitemEntity.getSpareid(), tid);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmSpareaccessEntity.getId(), dmSpareaccessEntity.getTenantid());
    }

    /**
     * 修改数据
     *
     * @param dmSpareaccessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmSpareaccessPojo update(DmSpareaccessPojo dmSpareaccessPojo) {
        //主表更改
        DmSpareaccessEntity dmSpareaccessEntity = new DmSpareaccessEntity();
        BeanUtils.copyProperties(dmSpareaccessPojo, dmSpareaccessEntity);
        this.dmSpareaccessMapper.update(dmSpareaccessEntity);
        if (dmSpareaccessPojo.getItem() != null) {
            //Item子表处理
            List<DmSpareaccessitemPojo> lst = dmSpareaccessPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = dmSpareaccessMapper.getDelItemIds(dmSpareaccessPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.dmSpareaccessitemMapper.delete(lstDelIds.get(i), dmSpareaccessEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    DmSpareaccessitemEntity dmSpareaccessitemEntity = new DmSpareaccessitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        DmSpareaccessitemPojo itemPojo = this.dmSpareaccessitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, dmSpareaccessitemEntity);
                        //设置id和Pid
                        dmSpareaccessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        dmSpareaccessitemEntity.setPid(dmSpareaccessEntity.getId());  // 主表 id
                        dmSpareaccessitemEntity.setTenantid(dmSpareaccessPojo.getTenantid());   // 租户id
                        dmSpareaccessitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.dmSpareaccessitemMapper.insert(dmSpareaccessitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), dmSpareaccessitemEntity);
                        dmSpareaccessitemEntity.setTenantid(dmSpareaccessPojo.getTenantid());
                        this.dmSpareaccessitemMapper.update(dmSpareaccessitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmSpareaccessEntity.getId(), dmSpareaccessEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        DmSpareaccessPojo dmSpareaccessPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<DmSpareaccessitemPojo> lst = dmSpareaccessPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.dmSpareaccessitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.dmSpareaccessMapper.delete(key, tid);
    }

    /**
     * 审核数据
     *
     * @param dmSpareaccessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmSpareaccessPojo approval(DmSpareaccessPojo dmSpareaccessPojo) {
        //主表更改
        DmSpareaccessEntity dmSpareaccessEntity = new DmSpareaccessEntity();
        BeanUtils.copyProperties(dmSpareaccessPojo, dmSpareaccessEntity);
        this.dmSpareaccessMapper.approval(dmSpareaccessEntity);
        //返回Bill实例
        return this.getBillEntity(dmSpareaccessEntity.getId(), dmSpareaccessEntity.getTenantid());
    }
}
