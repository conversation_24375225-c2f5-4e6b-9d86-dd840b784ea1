package inks.service.std.eam.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 工装具校验主表(DmToolinspect)实体类
 *
 * <AUTHOR>
 * @since 2025-06-19 10:17:22
 */
public class DmToolinspectEntity implements Serializable {
    private static final long serialVersionUID = 464021979155652343L;
     // id
    private String id;
     // 流水号
    private String refno;
     // 单据日期
    private Date billdate;
     // 类型
    private String billtype;
     // 标题
    private String billtitle;
     // 校验类型 (1:内部校验, 2:外发校验)
    private Integer inspecttype;
     // 操作员id
    private String operatorid;
     // 操作员姓名
    private String operator;
     // item行数
    private Integer itemcount;
     // 完成行数
    private Integer finishcount;
     // 外发校验机构
    private String agency;
     // 外发送检日期
    private Date sentdate;
     // 摘要
    private String summary;
     // 创建人
    private String createby;
     // 创建人id
    private String createbyid;
     // 创建时间
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
    private String custom1;
    private String custom2;
    private String custom3;
    private String custom4;
    private String custom5;
    private String custom6;
    private String custom7;
    private String custom8;
    private String custom9;
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 流水号
    public String getRefno() {
        return refno;
    }
    
    public void setRefno(String refno) {
        this.refno = refno;
    }
        
   // 单据日期
    public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
   // 类型
    public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
   // 标题
    public String getBilltitle() {
        return billtitle;
    }
    
    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
        
   // 校验类型 (1:内部校验, 2:外发校验)
    public Integer getInspecttype() {
        return inspecttype;
    }
    
    public void setInspecttype(Integer inspecttype) {
        this.inspecttype = inspecttype;
    }
        
   // 操作员id
    public String getOperatorid() {
        return operatorid;
    }
    
    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }
        
   // 操作员姓名
    public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
        
   // item行数
    public Integer getItemcount() {
        return itemcount;
    }
    
    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }
        
   // 完成行数
    public Integer getFinishcount() {
        return finishcount;
    }
    
    public void setFinishcount(Integer finishcount) {
        this.finishcount = finishcount;
    }
        
   // 外发校验机构
    public String getAgency() {
        return agency;
    }
    
    public void setAgency(String agency) {
        this.agency = agency;
    }
        
   // 外发送检日期
    public Date getSentdate() {
        return sentdate;
    }
    
    public void setSentdate(Date sentdate) {
        this.sentdate = sentdate;
    }
        
   // 摘要
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
   // 创建人
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建人id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 创建时间
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

