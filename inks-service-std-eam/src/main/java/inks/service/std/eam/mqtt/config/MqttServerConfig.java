package inks.service.std.eam.mqtt.config;

import org.dromara.mica.mqtt.spring.server.MqttServerTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * MQTT服务器配置类
 *
 * <AUTHOR>
 */
@Configuration
public class MqttServerConfig {

    @Autowired(required = false)
    private MqttServerTemplate mqttServerTemplate;

    @PostConstruct
    public void init() {
        System.out.println("MQTT Server Config initialized");
        if (mqttServerTemplate != null) {
            System.out.println("MQTT Server Template: " + mqttServerTemplate);
        } else {
            System.out.println("MQTT Server Template: not available yet");
        }
    }
}
