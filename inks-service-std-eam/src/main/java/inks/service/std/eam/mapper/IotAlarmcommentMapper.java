package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotAlarmcommentPojo;
import inks.service.std.eam.domain.IotAlarmcommentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 告警评论表(Iot_AlarmComment)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-24 15:27:08
 */
@Mapper
public interface IotAlarmcommentMapper {

    IotAlarmcommentPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotAlarmcommentPojo> getPageList(QueryParam queryParam);

    int insert(IotAlarmcommentEntity iotAlarmcommentEntity);

    int update(IotAlarmcommentEntity iotAlarmcommentEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    /**
     * 根据警报ID查询评论列表
     * @param alarmId 警报ID
     * @param tid 租户ID
     * @return 评论列表
     */
    List<IotAlarmcommentPojo> getCommentsByAlarmId(@Param("alarmId") String alarmId, @Param("tid") String tid);

}

