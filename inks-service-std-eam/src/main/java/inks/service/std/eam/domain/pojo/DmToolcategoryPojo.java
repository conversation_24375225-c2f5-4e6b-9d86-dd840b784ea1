package inks.service.std.eam.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 工装具类别表(DmToolcategory)实体类
 *
 * <AUTHOR>
 * @since 2025-06-10 12:48:40
 */
public class DmToolcategoryPojo implements Serializable {
    private static final long serialVersionUID = 779443557493097187L;
    // id
    @Excel(name = "id")
    private String id;
    // 父类别id
    @Excel(name = "父类别id")
    private String parentid;
    // 类别编码
    @Excel(name = "类别编码")
    private String catecode;
    // 类别名称
    @Excel(name = "类别名称")
    private String catename;
    // 保养警告周期(天)
    @Excel(name = "保养警告周期(天)")
    private Integer maintdays;
    // 预警量(保养)
    @Excel(name = "预警量(保养)")
    private Integer maintwarnqty;
    // 限制量(保养)
    @Excel(name = "限制量(保养)")
    private Integer maintlimitqty;
    // 限制保养次数
    @Excel(name = "限制保养次数")
    private Integer maintlimitcount;
    // 报废停用周期(天)
    @Excel(name = "报废停用周期(天)")
    private Integer scrapdays;
    // 预警量(报废)
    @Excel(name = "预警量(报废)")
    private Integer scrapwarnqty;
    // 限制量(报废)
    @Excel(name = "限制量(报废)")
    private Integer scraplimitqty;
     // 检验警告周期(天)
    @Excel(name = "检验警告周期(天)")
    private Integer inspdays;
     // 校验类型 (0:内部校验, 1:外发校验)
    @Excel(name = "校验类型 (0:内部校验, 1:外发校验)")
    private Integer insptype;
     // 有效性
    @Excel(name = "有效性")
    private Integer enabledmark;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 父类别id
    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    // 类别编码
    public String getCatecode() {
        return catecode;
    }

    public void setCatecode(String catecode) {
        this.catecode = catecode;
    }

    // 类别名称
    public String getCatename() {
        return catename;
    }

    public void setCatename(String catename) {
        this.catename = catename;
    }

    // 保养警告周期(天)
    public Integer getMaintdays() {
        return maintdays;
    }

    public void setMaintdays(Integer maintdays) {
        this.maintdays = maintdays;
    }

    // 预警量(保养)
    public Integer getMaintwarnqty() {
        return maintwarnqty;
    }

    public void setMaintwarnqty(Integer maintwarnqty) {
        this.maintwarnqty = maintwarnqty;
    }

    // 限制量(保养)
    public Integer getMaintlimitqty() {
        return maintlimitqty;
    }

    public void setMaintlimitqty(Integer maintlimitqty) {
        this.maintlimitqty = maintlimitqty;
    }

    // 限制保养次数
    public Integer getMaintlimitcount() {
        return maintlimitcount;
    }

    public void setMaintlimitcount(Integer maintlimitcount) {
        this.maintlimitcount = maintlimitcount;
    }

    // 报废停用周期(天)
    public Integer getScrapdays() {
        return scrapdays;
    }

    public void setScrapdays(Integer scrapdays) {
        this.scrapdays = scrapdays;
    }

    // 预警量(报废)
    public Integer getScrapwarnqty() {
        return scrapwarnqty;
    }

    public void setScrapwarnqty(Integer scrapwarnqty) {
        this.scrapwarnqty = scrapwarnqty;
    }

    // 限制量(报废)
    public Integer getScraplimitqty() {
        return scraplimitqty;
    }

    public void setScraplimitqty(Integer scraplimitqty) {
        this.scraplimitqty = scraplimitqty;
    }

   // 检验警告周期(天)
    public Integer getInspdays() {
        return inspdays;
    }

    public void setInspdays(Integer inspdays) {
        this.inspdays = inspdays;
    }

   // 校验类型 (0:内部校验, 1:外发校验)
    public Integer getInsptype() {
        return insptype;
    }

    public void setInsptype(Integer insptype) {
        this.insptype = insptype;
    }

   // 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

