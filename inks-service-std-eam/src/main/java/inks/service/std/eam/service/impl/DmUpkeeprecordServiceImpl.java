package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmUpkeeprecordEntity;
import inks.service.std.eam.domain.DmUpkeeprecorditemEntity;
import inks.service.std.eam.domain.DmUpkeeprecordspareEntity;
import inks.service.std.eam.domain.pojo.DmUpkeeprecordPojo;
import inks.service.std.eam.domain.pojo.DmUpkeeprecorditemPojo;
import inks.service.std.eam.domain.pojo.DmUpkeeprecorditemdetailPojo;
import inks.service.std.eam.domain.pojo.DmUpkeeprecordsparePojo;
import inks.service.std.eam.mapper.DmUpkeepflowitemMapper;
import inks.service.std.eam.mapper.DmUpkeeprecordMapper;
import inks.service.std.eam.mapper.DmUpkeeprecorditemMapper;
import inks.service.std.eam.mapper.DmUpkeeprecordspareMapper;
import inks.service.std.eam.service.DmUpkeeprecordService;
import inks.service.std.eam.service.DmUpkeeprecorditemService;
import inks.service.std.eam.service.DmUpkeeprecordspareService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 保养实施(DmUpkeeprecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-09 13:59:38
 */
@Service("dmUpkeeprecordService")
public class DmUpkeeprecordServiceImpl implements DmUpkeeprecordService {
    @Resource
    private DmUpkeeprecordMapper dmUpkeeprecordMapper;

    @Resource
    private DmUpkeeprecorditemMapper dmUpkeeprecorditemMapper;
    @Resource
    private DmUpkeepflowitemMapper dmUpkeepflowitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private DmUpkeeprecorditemService dmUpkeeprecorditemService;
    @Resource
    private DmUpkeeprecordspareService dmUpkeeprecordspareService;
    @Resource
    private DmUpkeeprecordspareMapper dmUpkeeprecordspareMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmUpkeeprecordPojo getEntity(String key, String tid) {
        return this.dmUpkeeprecordMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeeprecorditemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeeprecorditemdetailPojo> lst = dmUpkeeprecordMapper.getPageList(queryParam);
            PageInfo<DmUpkeeprecorditemdetailPojo> pageInfo = new PageInfo<DmUpkeeprecorditemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmUpkeeprecordPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            DmUpkeeprecordPojo dmUpkeeprecordPojo = this.dmUpkeeprecordMapper.getEntity(key, tid);
            //读取子表
            dmUpkeeprecordPojo.setItem(dmUpkeeprecorditemMapper.getList(dmUpkeeprecordPojo.getId(), dmUpkeeprecordPojo.getTenantid()));
            //读取备件子表
            dmUpkeeprecordPojo.setSpare(dmUpkeeprecordspareMapper.getList(dmUpkeeprecordPojo.getId(), dmUpkeeprecordPojo.getTenantid()));
            //这里的planid关联的保养方案的子表List
            dmUpkeeprecordPojo.setFlowitem(dmUpkeepflowitemMapper.getList(dmUpkeeprecordPojo.getFlowid(), dmUpkeeprecordPojo.getTenantid()));
            return dmUpkeeprecordPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeeprecordPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeeprecordPojo> lst = dmUpkeeprecordMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(dmUpkeeprecorditemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setSpare(dmUpkeeprecordspareMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<DmUpkeeprecordPojo> pageInfo = new PageInfo<DmUpkeeprecordPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeeprecordPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeeprecordPojo> lst = dmUpkeeprecordMapper.getPageTh(queryParam);
            PageInfo<DmUpkeeprecordPojo> pageInfo = new PageInfo<DmUpkeeprecordPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmUpkeeprecordPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmUpkeeprecordPojo insert(DmUpkeeprecordPojo dmUpkeeprecordPojo) {
        List<DmUpkeeprecorditemPojo> item = dmUpkeeprecordPojo.getItem();
//初始化NULL字段
        if (dmUpkeeprecordPojo.getRefno() == null) dmUpkeeprecordPojo.setRefno("");
        if (dmUpkeeprecordPojo.getBilldate() == null) dmUpkeeprecordPojo.setBilldate(new Date());
        if (dmUpkeeprecordPojo.getBilltype() == null) dmUpkeeprecordPojo.setBilltype("");
        if (dmUpkeeprecordPojo.getBilltitle() == null) dmUpkeeprecordPojo.setBilltitle("");
        if (dmUpkeeprecordPojo.getPlanid() == null) dmUpkeeprecordPojo.setPlanid("");
        if (dmUpkeeprecordPojo.getItemlevel() == null) dmUpkeeprecordPojo.setItemlevel("");
        if (dmUpkeeprecordPojo.getItemname() == null) dmUpkeeprecordPojo.setItemname("");
        if (dmUpkeeprecordPojo.getWorktime() == null) dmUpkeeprecordPojo.setWorktime(0D);
        if (dmUpkeeprecordPojo.getOperator() == null) dmUpkeeprecordPojo.setOperator("");
        if (dmUpkeeprecordPojo.getSolution() == null) dmUpkeeprecordPojo.setSolution("");
        if (dmUpkeeprecordPojo.getItemcount() == null) dmUpkeeprecordPojo.setItemcount(item.size());
        if (dmUpkeeprecordPojo.getFinishcount() == null) {
            dmUpkeeprecordPojo.setFinishcount((int) item.stream()
                    .filter(x -> {
                        Integer finishmark = x.getFinishmark();
                        return finishmark != null && finishmark.equals(1);
                    })
                    .count());
        }
        if (dmUpkeeprecordPojo.getSummary() == null) dmUpkeeprecordPojo.setSummary("");
        if (dmUpkeeprecordPojo.getConfirmer() == null) dmUpkeeprecordPojo.setConfirmer("");
        if (dmUpkeeprecordPojo.getCustom1() == null) dmUpkeeprecordPojo.setCustom1("");
        if (dmUpkeeprecordPojo.getCustom2() == null) dmUpkeeprecordPojo.setCustom2("");
        if (dmUpkeeprecordPojo.getCustom3() == null) dmUpkeeprecordPojo.setCustom3("");
        if (dmUpkeeprecordPojo.getCustom4() == null) dmUpkeeprecordPojo.setCustom4("");
        if (dmUpkeeprecordPojo.getCustom5() == null) dmUpkeeprecordPojo.setCustom5("");
        if (dmUpkeeprecordPojo.getCustom6() == null) dmUpkeeprecordPojo.setCustom6("");
        if (dmUpkeeprecordPojo.getCustom7() == null) dmUpkeeprecordPojo.setCustom7("");
        if (dmUpkeeprecordPojo.getCustom8() == null) dmUpkeeprecordPojo.setCustom8("");
        if (dmUpkeeprecordPojo.getListerid() == null) dmUpkeeprecordPojo.setListerid("");
        if (dmUpkeeprecordPojo.getLister() == null) dmUpkeeprecordPojo.setLister("");
        if (dmUpkeeprecordPojo.getCreateby() == null) dmUpkeeprecordPojo.setCreateby("");
        if (dmUpkeeprecordPojo.getCreatebyid() == null) dmUpkeeprecordPojo.setCreatebyid("");
        if (dmUpkeeprecordPojo.getCreatedate() == null) dmUpkeeprecordPojo.setCreatedate(new Date());
        if (dmUpkeeprecordPojo.getModifydate() == null) dmUpkeeprecordPojo.setModifydate(new Date());
        if (dmUpkeeprecordPojo.getAssessorid() == null) dmUpkeeprecordPojo.setAssessorid("");
        if (dmUpkeeprecordPojo.getAssessor() == null) dmUpkeeprecordPojo.setAssessor("");
        if (dmUpkeeprecordPojo.getAssessdate() == null) dmUpkeeprecordPojo.setAssessdate(new Date());
        if (dmUpkeeprecordPojo.getTenantid() == null) dmUpkeeprecordPojo.setTenantid("");
        if (dmUpkeeprecordPojo.getRevision() == null) dmUpkeeprecordPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmUpkeeprecordEntity dmUpkeeprecordEntity = new DmUpkeeprecordEntity();
        BeanUtils.copyProperties(dmUpkeeprecordPojo, dmUpkeeprecordEntity);

        //设置id和新建日期
        dmUpkeeprecordEntity.setId(id);
        dmUpkeeprecordEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmUpkeeprecordMapper.insert(dmUpkeeprecordEntity);
        //Item子表处理
        List<DmUpkeeprecorditemPojo> lst = item;
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                DmUpkeeprecorditemPojo itemPojo = this.dmUpkeeprecorditemService.clearNull(lst.get(i));
                DmUpkeeprecorditemEntity dmUpkeeprecorditemEntity = new DmUpkeeprecorditemEntity();
                BeanUtils.copyProperties(itemPojo, dmUpkeeprecorditemEntity);
                //设置id和Pid
                dmUpkeeprecorditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmUpkeeprecorditemEntity.setPid(id);
                dmUpkeeprecorditemEntity.setTenantid(dmUpkeeprecordPojo.getTenantid());
                dmUpkeeprecorditemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmUpkeeprecorditemMapper.insert(dmUpkeeprecorditemEntity);
            }
        }
        //Spare子表处理
        List<DmUpkeeprecordsparePojo> spare = dmUpkeeprecordPojo.getSpare();
        if (spare != null) {
            //循环每个spare子表
            for (int i = 0; i < spare.size(); i++) {
                //初始化spare的NULL
                DmUpkeeprecordsparePojo sparePojo = this.dmUpkeeprecordspareService.clearNull(spare.get(i));
                DmUpkeeprecordspareEntity dmUpkeeprecordspareEntity = new DmUpkeeprecordspareEntity();
                BeanUtils.copyProperties(sparePojo, dmUpkeeprecordspareEntity);
                //设置id和Pid
                dmUpkeeprecordspareEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmUpkeeprecordspareEntity.setPid(id);
                dmUpkeeprecordspareEntity.setTenantid(dmUpkeeprecordPojo.getTenantid());
                dmUpkeeprecordspareEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmUpkeeprecordspareMapper.insert(dmUpkeeprecordspareEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmUpkeeprecordEntity.getId(), dmUpkeeprecordEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmUpkeeprecordPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmUpkeeprecordPojo update(DmUpkeeprecordPojo dmUpkeeprecordPojo) {
        List<DmUpkeeprecorditemPojo> item = dmUpkeeprecordPojo.getItem();
        List<DmUpkeeprecordsparePojo> spare = dmUpkeeprecordPojo.getSpare();
        dmUpkeeprecordPojo.setItemcount(item.size());
        dmUpkeeprecordPojo.setFinishcount((int) item.stream().filter(x -> x.getFinishmark().equals(1)).count());
        //主表更改
        DmUpkeeprecordEntity dmUpkeeprecordEntity = new DmUpkeeprecordEntity();
        BeanUtils.copyProperties(dmUpkeeprecordPojo, dmUpkeeprecordEntity);
        this.dmUpkeeprecordMapper.update(dmUpkeeprecordEntity);
        if (item != null) {
            //Item子表处理
            List<DmUpkeeprecorditemPojo> lst = item;
            //获取被删除的Item
            List<String> lstDelIds = dmUpkeeprecordMapper.getDelItemIds(dmUpkeeprecordPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.dmUpkeeprecorditemMapper.delete(lstDelIds.get(i), dmUpkeeprecordEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    DmUpkeeprecorditemEntity dmUpkeeprecorditemEntity = new DmUpkeeprecorditemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        DmUpkeeprecorditemPojo itemPojo = this.dmUpkeeprecorditemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, dmUpkeeprecorditemEntity);
                        //设置id和Pid
                        dmUpkeeprecorditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        dmUpkeeprecorditemEntity.setPid(dmUpkeeprecordEntity.getId());  // 主表 id
                        dmUpkeeprecorditemEntity.setTenantid(dmUpkeeprecordPojo.getTenantid());   // 租户id
                        dmUpkeeprecorditemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.dmUpkeeprecorditemMapper.insert(dmUpkeeprecorditemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), dmUpkeeprecorditemEntity);
                        dmUpkeeprecorditemEntity.setTenantid(dmUpkeeprecordPojo.getTenantid());
                        this.dmUpkeeprecorditemMapper.update(dmUpkeeprecorditemEntity);
                    }
                }
            }
        }

        if (spare != null) {
            //Spare子表处理
            List<DmUpkeeprecordsparePojo> lst = spare;
            //获取被删除的Spare
            List<String> lstDelIds = dmUpkeeprecordMapper.getDelSpareIds(dmUpkeeprecordPojo);
            if (lstDelIds != null) {
                //循环每个删除spare子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.dmUpkeeprecordspareMapper.delete(lstDelIds.get(i), dmUpkeeprecordEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个spare子表
                for (int i = 0; i < lst.size(); i++) {
                    DmUpkeeprecordspareEntity dmUpkeeprecordspareEntity = new DmUpkeeprecordspareEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化spare的NULL
                        DmUpkeeprecordsparePojo sparePojo = this.dmUpkeeprecordspareService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(sparePojo, dmUpkeeprecordspareEntity);
                        //设置id和Pid
                        dmUpkeeprecordspareEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                        dmUpkeeprecordspareEntity.setPid(dmUpkeeprecordEntity.getId());
                        dmUpkeeprecordspareEntity.setTenantid(dmUpkeeprecordPojo.getTenantid());
                        dmUpkeeprecordspareEntity.setRevision(1);  //乐观锁
                        //插入子表
                        this.dmUpkeeprecordspareMapper.insert(dmUpkeeprecordspareEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), dmUpkeeprecordspareEntity);
                        dmUpkeeprecordspareEntity.setTenantid(dmUpkeeprecordPojo.getTenantid());
                        this.dmUpkeeprecordspareMapper.update(dmUpkeeprecordspareEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmUpkeeprecordEntity.getId(), dmUpkeeprecordEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        DmUpkeeprecordPojo dmUpkeeprecordPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<DmUpkeeprecorditemPojo> lst = dmUpkeeprecordPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.dmUpkeeprecorditemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        //Spare子表处理
        List<DmUpkeeprecordsparePojo> lstSpare = dmUpkeeprecordPojo.getSpare();
        if (lstSpare != null) {
            //循环每个删除spare子表
            for (int i = 0; i < lstSpare.size(); i++) {
                this.dmUpkeeprecordspareMapper.delete(lstSpare.get(i).getId(), tid);
            }
        }
        return this.dmUpkeeprecordMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param dmUpkeeprecordPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmUpkeeprecordPojo approval(DmUpkeeprecordPojo dmUpkeeprecordPojo) {
        //主表更改
        DmUpkeeprecordEntity dmUpkeeprecordEntity = new DmUpkeeprecordEntity();
        BeanUtils.copyProperties(dmUpkeeprecordPojo, dmUpkeeprecordEntity);
        this.dmUpkeeprecordMapper.approval(dmUpkeeprecordEntity);
        //返回Bill实例
        return this.getBillEntity(dmUpkeeprecordEntity.getId(), dmUpkeeprecordEntity.getTenantid());
    }

    @Override
    public int getSumFinishCount(String planid, String plandate, String tid) {
        return this.dmUpkeeprecordMapper.getSumFinishCount(planid, plandate, tid);
    }

    @Override
    public List<String> getAllDevId(String planid, String plandate, String tid) {
        return this.dmUpkeeprecordMapper.getAllDevId(planid, plandate, tid);
    }

    @Override
    public List<String> getAllFinishDevId(String planid, String date, String tid) {
        return this.dmUpkeeprecordMapper.getAllFinishDevId(planid, date, tid);
    }

    @Override
    public List<DmUpkeeprecordPojo> getListByPlanidAndDate(String planid, String plandate, String tid) {
        return this.dmUpkeeprecordMapper.getListByPlanidAndDate(planid, plandate, tid);
    }
}
