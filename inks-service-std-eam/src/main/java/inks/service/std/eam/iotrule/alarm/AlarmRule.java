package inks.service.std.eam.iotrule.alarm;

import java.util.*;

// 告警规则
public class AlarmRule {
    private String name;
    private String type;              // SIMPLE, DURATION, REPEATING
    private AlarmCondition condition; // 根条件(可以是组合条件)
    private long duration;            // 持续时间(毫秒), 用于DURATION类型
    private int repeatCount;          // 重复次数, 用于REPEATING类型
    
    public AlarmRule(String name, String type, AlarmCondition condition) {
        this.name = name;
        this.type = type;
        this.condition = condition;
        this.duration = 0;
        this.repeatCount = 0;
    }
    
    public AlarmRule(String name, String type, AlarmCondition condition, long duration) {
        this(name, type, condition);
        this.duration = duration;
    }
    
    public AlarmRule(String name, String type, AlarmCondition condition, int repeatCount) {
        this(name, type, condition);
        this.repeatCount = repeatCount;
    }
    
    // 评估是否符合告警条件
    public boolean evaluate(Map<String, Object> telemetry) {
        return condition.evaluate(telemetry);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public AlarmCondition getCondition() {
        return condition;
    }

    public void setCondition(AlarmCondition condition) {
        this.condition = condition;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public int getRepeatCount() {
        return repeatCount;
    }

    public void setRepeatCount(int repeatCount) {
        this.repeatCount = repeatCount;
    }

    // Getters and Setters...
}

// 复合条件(AND)
class AndCondition implements AlarmCondition {
    private List<AlarmCondition> conditions;
    
    public AndCondition(List<AlarmCondition> conditions) {
        this.conditions = conditions;
    }
    
    @Override
    public boolean evaluate(Map<String, Object> telemetry) {
        for (AlarmCondition condition : conditions) {
            if (!condition.evaluate(telemetry)) {
                return false;
            }
        }
        return true;
    }
}

// 复合条件(OR)
class OrCondition implements AlarmCondition {
    private List<AlarmCondition> conditions;
    
    public OrCondition(List<AlarmCondition> conditions) {
        this.conditions = conditions;
    }
    
    @Override
    public boolean evaluate(Map<String, Object> telemetry) {
        for (AlarmCondition condition : conditions) {
            if (condition.evaluate(telemetry)) {
                return true;
            }
        }
        return false;
    }
}