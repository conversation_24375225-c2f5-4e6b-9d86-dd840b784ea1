package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmUpkeepflowPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepflowitemdetailPojo;

/**
 * 保养方案(DmUpkeepflow)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-06 14:35:37
 */
public interface DmUpkeepflowService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeepflowPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeepflowitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeepflowPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeepflowPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeepflowPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmUpkeepflowPojo 实例对象
     * @return 实例对象
     */
    DmUpkeepflowPojo insert(DmUpkeepflowPojo dmUpkeepflowPojo);

    /**
     * 修改数据
     *
     * @param dmUpkeepflowpojo 实例对象
     * @return 实例对象
     */
    DmUpkeepflowPojo update(DmUpkeepflowPojo dmUpkeepflowpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param dmUpkeepflowPojo 实例对象
     * @return 实例对象
     */
    DmUpkeepflowPojo approval(DmUpkeepflowPojo dmUpkeepflowPojo);
}
