package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmIubricationEntity;
import inks.service.std.eam.domain.pojo.DmIubricationPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备润滑(DmIubrication)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 12:40:14
 */
@Mapper
public interface DmIubricationMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmIubricationPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmIubricationPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param dmIubricationEntity 实例对象
     * @return 影响行数
     */
    int insert(DmIubricationEntity dmIubricationEntity);


    /**
     * 修改数据
     *
     * @param dmIubricationEntity 实例对象
     * @return 影响行数
     */
    int update(DmIubricationEntity dmIubricationEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改数据
     *
     * @param dmIubricationEntity 实例对象
     * @return 影响行数
     */
    int approval(DmIubricationEntity dmIubricationEntity);
}

