package inks.service.std.eam.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 资产配置模板表(IotAssetprofile)实体类
 *
 * <AUTHOR>
 * @since 2025-05-10 14:48:02
 */
public class IotAssetprofilePojo implements Serializable {
    private static final long serialVersionUID = 187127937490492089L;
     // 资产配置ID
    @Excel(name = "资产配置ID") 
    private String id;
     // 配置名称
    @Excel(name = "配置名称") 
    private String profname;
     // 图标路径
    @Excel(name = "图标路径") 
    private String image;
     // 描述
    @Excel(name = "描述") 
    private String description;
     // 是否默认配置
    @Excel(name = "是否默认配置") 
    private Integer isdefault;
     // 默认规则链ID
    @Excel(name = "默认规则链ID") 
    private String defaultrulechainid;
     // 默认仪表盘ID
    @Excel(name = "默认仪表盘ID") 
    private String defaultdashboardid;
     // 默认队列名称
    @Excel(name = "默认队列名称") 
    private String defaultqueuename;
     // 边缘规则链ID
    @Excel(name = "边缘规则链ID") 
    private String defaultedgerulechainid;
     // 外部系统标识
    @Excel(name = "外部系统标识") 
    private String externalid;
     // 版本号
    @Excel(name = "版本号") 
    private Long version;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 顺序
    @Excel(name = "顺序") 
    private Integer rownum;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 自定义6
    @Excel(name = "自定义6") 
    private String custom6;
     // 自定义7
    @Excel(name = "自定义7") 
    private String custom7;
     // 自定义8
    @Excel(name = "自定义8") 
    private String custom8;
     // 自定义9
    @Excel(name = "自定义9") 
    private String custom9;
     // 自定义10
    @Excel(name = "自定义10") 
    private String custom10;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;

    //               Iot_RuleChain.RuleChainName,
    //               Iot_Dashboard.Title as DashboardTitle
    private String rulechainname;
    private String dashboardtitle;

   // 资产配置ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public String getRulechainname() {
        return rulechainname;
    }

    public void setRulechainname(String rulechainname) {
        this.rulechainname = rulechainname;
    }

    public String getDashboardtitle() {
        return dashboardtitle;
    }

    public void setDashboardtitle(String dashboardtitle) {
        this.dashboardtitle = dashboardtitle;
    }

    // 配置名称
    public String getProfname() {
        return profname;
    }
    
    public void setProfname(String profname) {
        this.profname = profname;
    }
        
   // 图标路径
    public String getImage() {
        return image;
    }
    
    public void setImage(String image) {
        this.image = image;
    }
        
   // 描述
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
        
   // 是否默认配置
    public Integer getIsdefault() {
        return isdefault;
    }
    
    public void setIsdefault(Integer isdefault) {
        this.isdefault = isdefault;
    }
        
   // 默认规则链ID
    public String getDefaultrulechainid() {
        return defaultrulechainid;
    }
    
    public void setDefaultrulechainid(String defaultrulechainid) {
        this.defaultrulechainid = defaultrulechainid;
    }
        
   // 默认仪表盘ID
    public String getDefaultdashboardid() {
        return defaultdashboardid;
    }
    
    public void setDefaultdashboardid(String defaultdashboardid) {
        this.defaultdashboardid = defaultdashboardid;
    }
        
   // 默认队列名称
    public String getDefaultqueuename() {
        return defaultqueuename;
    }
    
    public void setDefaultqueuename(String defaultqueuename) {
        this.defaultqueuename = defaultqueuename;
    }
        
   // 边缘规则链ID
    public String getDefaultedgerulechainid() {
        return defaultedgerulechainid;
    }
    
    public void setDefaultedgerulechainid(String defaultedgerulechainid) {
        this.defaultedgerulechainid = defaultedgerulechainid;
    }
        
   // 外部系统标识
    public String getExternalid() {
        return externalid;
    }
    
    public void setExternalid(String externalid) {
        this.externalid = externalid;
    }
        
   // 版本号
    public Long getVersion() {
        return version;
    }
    
    public void setVersion(Long version) {
        this.version = version;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 顺序
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

