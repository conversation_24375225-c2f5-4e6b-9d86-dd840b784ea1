package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotRelationPojo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 实体关系映射表(Iot_Relation)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-17 11:02:53
 */
public interface IotRelationService {

    IotRelationPojo getEntity(String key,String tid);

    PageInfo<IotRelationPojo> getPageList(QueryParam queryParam);

    IotRelationPojo insert(IotRelationPojo iotRelationPojo);

    IotRelationPojo update(IotRelationPojo iotRelationpojo);

    int delete(String key,String tid);

    List<IotRelationPojo> getList(String fromid, String fromtype, String toid, String totype, String tenantid);
}
