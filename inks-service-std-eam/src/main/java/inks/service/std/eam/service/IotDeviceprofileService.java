package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotDeviceprofilePojo;
import com.github.pagehelper.PageInfo;

/**
 * 设备配置模板表(Iot_DeviceProfile)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:25
 */
public interface IotDeviceprofileService {

    IotDeviceprofilePojo getEntity(String key,String tid);

    PageInfo<IotDeviceprofilePojo> getPageList(QueryParam queryParam);

    IotDeviceprofilePojo insert(IotDeviceprofilePojo iotDeviceprofilePojo);

    IotDeviceprofilePojo update(IotDeviceprofilePojo iotDeviceprofilepojo);

    int delete(String key,String tid);
}
