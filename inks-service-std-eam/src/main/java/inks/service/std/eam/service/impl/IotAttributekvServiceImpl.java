package inks.service.std.eam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.IotAttributekvEntity;
import inks.service.std.eam.domain.pojo.IotAttributekvPojo;
import inks.service.std.eam.mapper.IotAttributekvMapper;
import inks.service.std.eam.service.IotAttributekvService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 实体属性存储表(IotAttributekv)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-07 10:06:39
 */
@Service("iotAttributekvService")
public class IotAttributekvServiceImpl implements IotAttributekvService {
    @Resource
    private IotAttributekvMapper iotAttributekvMapper;

    @Override
    public IotAttributekvPojo getEntity(String key, String tid) {
        return this.iotAttributekvMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<IotAttributekvPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotAttributekvPojo> lst = iotAttributekvMapper.getPageList(queryParam);
            PageInfo<IotAttributekvPojo> pageInfo = new PageInfo<IotAttributekvPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public IotAttributekvPojo insert(IotAttributekvPojo iotAttributekvPojo) {
        //初始化NULL字段
        cleanNull(iotAttributekvPojo);
        IotAttributekvEntity iotAttributekvEntity = new IotAttributekvEntity();
        BeanUtils.copyProperties(iotAttributekvPojo, iotAttributekvEntity);
        //生成雪花id
        iotAttributekvEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        iotAttributekvEntity.setRevision(1);  //乐观锁
        this.iotAttributekvMapper.insert(iotAttributekvEntity);
        return this.getEntity(iotAttributekvEntity.getId(), iotAttributekvEntity.getTenantid());
    }


    @Override
    public IotAttributekvPojo update(IotAttributekvPojo iotAttributekvPojo) {
        IotAttributekvEntity iotAttributekvEntity = new IotAttributekvEntity();
        BeanUtils.copyProperties(iotAttributekvPojo, iotAttributekvEntity);
        this.iotAttributekvMapper.update(iotAttributekvEntity);
        return this.getEntity(iotAttributekvEntity.getId(), iotAttributekvEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotAttributekvMapper.delete(key, tid);
    }


    private static void cleanNull(IotAttributekvPojo iotAttributekvPojo) {
        if (iotAttributekvPojo.getEntityid() == null) iotAttributekvPojo.setEntityid("");
        if (iotAttributekvPojo.getAttributetype() == null) iotAttributekvPojo.setAttributetype(0);
        if (iotAttributekvPojo.getAttributekey() == null) iotAttributekvPojo.setAttributekey(0);
        //if (iotAttributekvPojo.getAttributev() == null) iotAttributekvPojo.setAttributev("");
        //if (iotAttributekvPojo.getBoolv() == null) iotAttributekvPojo.setBoolv(0);
        //if (iotAttributekvPojo.getStrv() == null) iotAttributekvPojo.setStrv("");
        //if (iotAttributekvPojo.getDblv() == null) iotAttributekvPojo.setDblv(0D);
        //if (iotAttributekvPojo.getJsonv() == null) iotAttributekvPojo.setJsonv("{}");
        if (iotAttributekvPojo.getRemark() == null) iotAttributekvPojo.setRemark("");
        if (iotAttributekvPojo.getRownum() == null) iotAttributekvPojo.setRownum(0);
        if (iotAttributekvPojo.getCreateby() == null) iotAttributekvPojo.setCreateby("");
        if (iotAttributekvPojo.getCreatebyid() == null) iotAttributekvPojo.setCreatebyid("");
        if (iotAttributekvPojo.getCreatedate() == null) iotAttributekvPojo.setCreatedate(new Date());
        if (iotAttributekvPojo.getLister() == null) iotAttributekvPojo.setLister("");
        if (iotAttributekvPojo.getListerid() == null) iotAttributekvPojo.setListerid("");
        if (iotAttributekvPojo.getModifydate() == null) iotAttributekvPojo.setModifydate(new Date());
        if (iotAttributekvPojo.getCustom1() == null) iotAttributekvPojo.setCustom1("");
        if (iotAttributekvPojo.getCustom2() == null) iotAttributekvPojo.setCustom2("");
        if (iotAttributekvPojo.getCustom3() == null) iotAttributekvPojo.setCustom3("");
        if (iotAttributekvPojo.getCustom4() == null) iotAttributekvPojo.setCustom4("");
        if (iotAttributekvPojo.getCustom5() == null) iotAttributekvPojo.setCustom5("");
        if (iotAttributekvPojo.getCustom6() == null) iotAttributekvPojo.setCustom6("");
        if (iotAttributekvPojo.getCustom7() == null) iotAttributekvPojo.setCustom7("");
        if (iotAttributekvPojo.getCustom8() == null) iotAttributekvPojo.setCustom8("");
        if (iotAttributekvPojo.getCustom9() == null) iotAttributekvPojo.setCustom9("");
        if (iotAttributekvPojo.getCustom10() == null) iotAttributekvPojo.setCustom10("");
        if (iotAttributekvPojo.getTenantid() == null) iotAttributekvPojo.setTenantid("");
        if (iotAttributekvPojo.getTenantname() == null) iotAttributekvPojo.setTenantname("");
        if (iotAttributekvPojo.getRevision() == null) iotAttributekvPojo.setRevision(0);
    }


    /**
     * 对于您提供的 attribute_kv 表结构，其中的 attribute_type 字段用于区分不同类型的属性。根据 ThingsBoard 的文档和通用实践，属性主要分为以下几种类型，
     * 它们各自有不同的逻辑和用途：
     * <p>
     * 服务器端属性 (Server-side Attributes):
     * 逻辑: 这些属性由服务器设置和管理，通常用于存储与实体相关的元数据、配置信息或服务器端应用程序需要的任何其他数据。设备本身通常不能直接修改服务器端属性。
     * 用途: 例如，可以用来存储设备的位置信息、客户分配信息、或者特定于服务器端规则逻辑的标志。这些属性可以通过 ThingsBoard 的用户界面 (UI) 或 REST API进行配置和更新。
     * <p>
     * 客户端属性 (Client-side Attributes):
     * 逻辑: 这些属性由连接到 ThingsBoard 的设备（客户端）报告和设置。它们代表了设备自身的状态或信息。服务器端可以将这些属性视为只读的，主要用于展示和分析。
     * 用途: 例如，设备可以用它来报告固件版本、型号、序列号、IP 地址或其他半静态的设备特定信息。
     * <p>
     * 共享属性 (Shared Attributes):
     * 逻辑: 这是一种特殊的属性类型，主要用于设备和服务器之间的双向通信和状态同步。服务器可以设置共享属性的值，而设备可以订阅这些属性的更新。反过来，设备也可以更新共享属性的值
     * （尽管这通常是通过向服务器发送遥测或属性更新请求，然后由服务器逻辑来更新共享属性）。
     * 用途: 非常适用于需要从服务器向设备推送配置更改或命令的场景。例如，服务器可以更新一个名为 targetTemperature 的共享属性，设备订阅该属性并在其值更改时调整其操作。
     * 设备也可以向服务器报告其当前配置，服务器可以将其与期望的配置（存储为共享属性）进行比较。
     * 关于 attribute_type 的具体整数值:
     * <p>
     * 您提供的表结构中 attribute_type 是一个 integer 类型。ThingsBoard 的源代码或更详细的数据库文档会明确定义这些整数值具体对应哪种属性类型
     * （例如，0 可能代表服务器端，1 代表客户端，2 代表共享属性，但这只是一个假设）。 如果您需要确切的整数映射，建议查阅 ThingsBoard 的官方源码或更底层的开发文档。
     * 总结:
     * attribute_kv 表中的 attribute_type 字段是区分属性范围和行为的关键。
     * 服务器端属性 由服务器控制。
     * 客户端属性 由设备报告。
     * 共享属性 用于服务器和设备之间的双向配置和状态同步。
     */
    @Override
    public int batchLatest(JSONObject mqttJSON) {
        String entityid = mqttJSON.getString("entityid");
        //属性类型：1.client设备上报的属性/2.server服务端下发的属性
        Integer attrtype = mqttJSON.getInteger("attrtype");
        List<IotAttributekvPojo> list = new ArrayList<>();
        Date now = new Date();

        // 遍历遥测键值（排除元数据字段）
        for (String key : mqttJSON.keySet()) {
            // 排除元数据字段
            if (Arrays.asList("entityid", "ts", "sn", "attrtype").contains(key)) continue;

            Object val = mqttJSON.get(key);
            IotAttributekvPojo p = new IotAttributekvPojo();
            p.setId(inksSnowflake.getSnowflake().nextIdStr());
            p.setEntityid(entityid);
            p.setAttributetype(attrtype);// 属性类型：1.client设备上报的属性/2.server服务端下发的属性
            p.setAttributekey(0); //TODO 根据字典来通过key确定keyid
            p.setAttributev(key);
            // 获取系统当前时间戳 毫秒 不采用ts
            p.setLastupdatets(now.getTime());
            p.setCreatedate(now);
            p.setTenantid("");
            p.setRevision(0);
            // 值类型处理（与历史表一致）
            if (val instanceof Boolean) {
                p.setBoolv(((Boolean) val) ? 1 : 0);
            } else if (val instanceof Number) {
                if (val instanceof Integer || val instanceof Long) {
                    p.setLongv(((Number) val).longValue());
                } else {
                    p.setDblv(((Number) val).doubleValue());
                }
            } else {
                String s = String.valueOf(val);
                if (s.startsWith("{") || s.startsWith("[")) {
                    p.setJsonv(s);
                } else {
                    p.setStrv(s);
                }
            }
            list.add(p);
        }

        // 批量 Upsert
        int count = 0;
        if (!list.isEmpty()) {
            count = iotAttributekvMapper.batchLatest(list);
        }
        return count;
    }

}
