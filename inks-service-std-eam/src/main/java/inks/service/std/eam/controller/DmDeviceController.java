package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.DmDevicePojo;
import inks.service.std.eam.domain.pojo.DmDeviceitemPojo;
import inks.service.std.eam.domain.pojo.DmDeviceitemdetailPojo;
import inks.service.std.eam.service.DmDeviceService;
import inks.service.std.eam.service.DmDeviceitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 设备台账(DmDevice)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-06 12:59:47
 */
public class DmDeviceController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(DmDeviceController.class);
    /**
     * 服务对象
     */
    @Resource
    private DmDeviceService dmDeviceService;
    /**
     * 服务对象Item
     */
    @Resource
    private DmDeviceitemService dmDeviceitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private UtilsFeignService utilsFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取设备台账详细信息", notes = "获取设备台账详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Device.List")
    public R<DmDevicePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmDeviceService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Device.List")
    public R<PageInfo<DmDeviceitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_Device.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.dmDeviceService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取设备台账详细信息", notes = "获取设备台账详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Device.List")
    public R<DmDevicePojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmDeviceService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Device.List")
    public R<PageInfo<DmDevicePojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_Device.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.dmDeviceService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Device.List")
    public R<PageInfo<DmDevicePojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_Device.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.dmDeviceService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增设备台账", notes = "新增设备台账", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Device.Add")
    public R<DmDevicePojo> create(@RequestBody String json) {
        try {
            DmDevicePojo dmDevicePojo = JSONArray.parseObject(json, DmDevicePojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmDevicePojo.setCreateby(loginUser.getRealName());   // 创建者
            dmDevicePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            dmDevicePojo.setCreatedate(new Date());   // 创建时间
            dmDevicePojo.setLister(loginUser.getRealname());   // 制表
            dmDevicePojo.setListerid(loginUser.getUserid());    // 制表id            
            dmDevicePojo.setModifydate(new Date());   //修改时间
            dmDevicePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.dmDeviceService.insert(dmDevicePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改设备台账", notes = "修改设备台账", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Device.Edit")
    public R<DmDevicePojo> update(@RequestBody String json) {
        try {
            DmDevicePojo dmDevicePojo = JSONArray.parseObject(json, DmDevicePojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmDevicePojo.setLister(loginUser.getRealname());   // 制表
            dmDevicePojo.setListerid(loginUser.getUserid());    // 制表id   
            dmDevicePojo.setModifydate(new Date());   //修改时间
            dmDevicePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.dmDeviceService.update(dmDevicePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除设备台账", notes = "删除设备台账", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Device.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmDeviceService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增设备台账Item", notes = "新增设备台账Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Device.Add")
    public R<DmDeviceitemPojo> createItem(@RequestBody String json) {
        try {
            DmDeviceitemPojo dmDeviceitemPojo = JSONArray.parseObject(json, DmDeviceitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmDeviceitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.dmDeviceitemService.insert(dmDeviceitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除设备台账Item", notes = "删除设备台账Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Device.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmDeviceitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Device.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        DmDevicePojo dmDevicePojo = this.dmDeviceService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(dmDevicePojo);
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = dmDevicePojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    DmDeviceitemPojo dmDeviceitemPojo = new DmDeviceitemPojo();
                    dmDevicePojo.getItem().add(dmDeviceitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(dmDevicePojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "批量云打印DmDevice单据(ids)", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printBatchWebBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Device.Print")
    public R<String> printBatchWebBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<String> lstptJson = new ArrayList<>();
            String ptRefNoMain = "";
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");
            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            for (String key : lstkeys) {
                //=========获取单据表头信息========
                DmDevicePojo dmDevicePojo = this.dmDeviceService.getEntity(key, loginUser.getTenantid());
                if (dmDevicePojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }
//                if (printapproved != null && printapproved.equals("true") && dmDevicePojo.getAssessor().equals("")) {
//                    throw new BaseBusinessException("请先审核单据");
//                }
                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(dmDevicePojo);

                // 获取单据表头.加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
                //=========获取单据Item信息========
                List<DmDeviceitemPojo> lstitem = this.dmDeviceitemService.getList(key, loginUser.getTenantid());
                // 单据Item. 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = attrcostListToMaps(lstitem);
                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();
                maprow.put("row", lst);
                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
                lstptJson.add(ptJson);

            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            String[] lstRefno = ptRefNoMain.split(",");
            mapPrint.put("msg", "DmDevice：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "批量云打印单据", notes = "json={KEY,KEY},ptid打印模版", produces = "application/json")
    @RequestMapping(value = "/printBatchBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Device.Print")
    public void printBatchBill(@RequestBody String json, String ptid) throws IOException, JRException {
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            String content;
            if (reportsPojo != null) {
                content = reportsPojo.getRptdata();
            } else {
                throw new BaseBusinessException("未找到报表");
            }
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            String printapproved = "";
            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            if (r.getCode() == 200) {
                Map<String, String> tencfg = r.getData();
                printapproved = tencfg.get("system.bill.printapproved");

            } else {
                throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            }
            //数据填充
            JasperPrint printAll = new JasperPrint();
            for (int a = 0; a < lstkeys.size(); a++) {
                String key = lstkeys.get(a);
                //=========获取单据表头信息========
                DmDevicePojo dmDevicePojo = this.dmDeviceService.getEntity(key, loginUser.getTenantid());
                if (dmDevicePojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }

                //表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(dmDevicePojo);
                // 加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
                // 判定是否需要追行
                if (reportsPojo.getPagerow() > 0) {
                    int index = 0;
                    // 取行余数
                    index = dmDevicePojo.getItem().size() % reportsPojo.getPagerow();
                    if (index > 0) {
                        // 补全空白行
                        for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                            DmDeviceitemPojo dmDeviceitemPojo = new DmDeviceitemPojo();
                            dmDevicePojo.getItem().add(dmDeviceitemPojo);
                        }
                    }
                }

                // 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = attrcostListToMaps(dmDevicePojo.getItem());
                //item转数据源
                JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);

                //报表生成
                InputStream stream = new ByteArrayInputStream(content.getBytes());
                //编译报表
                JasperReport jasperReport = JasperCompileManager.compileReport(stream);

                //数据填充
                JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
                if (a == 0) {
                    printAll = print;
                } else {
                    List<JRPrintPage> pages = print.getPages();
                    for (JRPrintPage page : pages) {
                        printAll.addPage(page);
                    }
                }
            }
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(printAll, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

