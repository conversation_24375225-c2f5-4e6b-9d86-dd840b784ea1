package inks.service.std.eam.mqtt.service;

import org.dromara.mica.mqtt.codec.MqttQoS;
import org.dromara.mica.mqtt.core.server.auth.IMqttServerPublishPermission;
import org.springframework.stereotype.Service;
import org.tio.core.ChannelContext;

/**
 * MQTT服务器发布权限处理器
 *
 * <AUTHOR>
 */
@Service
public class MqttServerPublishPermission implements IMqttServerPublishPermission {

    @Override
    public boolean hasPermission(ChannelContext context, String clientId, String userName, MqttQoS mqttQoS, boolean retain) {
        // 发布权限控制
        System.out.println("MQTT Publish Permission Check - ClientId: " + clientId + ", QoS: " + mqttQoS + ", Retain: " + retain);

        // 这里可以实现更复杂的权限控制逻辑
        // 例如：某些用户只能使用特定的QoS级别

        // 示例：限制QoS级别
        if (mqttQoS == MqttQoS.QOS2 && !"admin".equals(userName)) {
            System.out.println("QoS 2 publish denied for non-admin client: " + clientId);
            return false;
        }

        // 示例：限制保留消息
        if (retain && !"admin".equals(userName)) {
            System.out.println("Retain message denied for non-admin client: " + clientId);
            return false;
        }

        // 示例：管理员拥有所有权限
        if ("admin".equals(userName)) {
            return true;
        }

        // 默认允许普通发布
        return true;
    }
}
