package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolusagePojo;
import inks.service.std.eam.domain.pojo.DmToolusageitemdetailPojo;

/**
 * 工装具使用记录表(DmToolusage)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:20
 */
public interface DmToolusageService {

    DmToolusagePojo getEntity(String key,String tid);

    PageInfo<DmToolusageitemdetailPojo> getPageList(QueryParam queryParam);

    DmToolusagePojo getBillEntity(String key,String tid);

    PageInfo<DmToolusagePojo> getBillList(QueryParam queryParam);

    PageInfo<DmToolusagePojo> getPageTh(QueryParam queryParam);

    DmToolusagePojo insert(DmToolusagePojo dmToolusagePojo,Integer warn);

    DmToolusagePojo update(DmToolusagePojo dmToolusagepojo,Integer warn);

    int delete(String key,String tid);

}
