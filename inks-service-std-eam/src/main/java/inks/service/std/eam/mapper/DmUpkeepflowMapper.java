package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmUpkeepflowEntity;
import inks.service.std.eam.domain.pojo.DmUpkeepflowPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepflowitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 保养方案(DmUpkeepflow)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-06 14:35:36
 */
@Mapper
public interface DmUpkeepflowMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeepflowPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmUpkeepflowitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmUpkeepflowPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmUpkeepflowEntity 实例对象
     * @return 影响行数
     */
    int insert(DmUpkeepflowEntity dmUpkeepflowEntity);


    /**
     * 修改数据
     *
     * @param dmUpkeepflowEntity 实例对象
     * @return 影响行数
     */
    int update(DmUpkeepflowEntity dmUpkeepflowEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param dmUpkeepflowPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(DmUpkeepflowPojo dmUpkeepflowPojo);

    /**
     * 修改数据
     *
     * @param dmUpkeepflowEntity 实例对象
     * @return 影响行数
     */
    int approval(DmUpkeepflowEntity dmUpkeepflowEntity);
}

