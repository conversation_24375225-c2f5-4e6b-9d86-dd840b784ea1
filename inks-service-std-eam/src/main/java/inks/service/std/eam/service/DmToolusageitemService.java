package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolusageitemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 工装具使用记录子表(DmToolusageitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:35
 */
public interface DmToolusageitemService {

    DmToolusageitemPojo getEntity(String key,String tid);

    PageInfo<DmToolusageitemPojo> getPageList(QueryParam queryParam);

    List<DmToolusageitemPojo> getList(String Pid,String tid);  

    DmToolusageitemPojo insert(DmToolusageitemPojo dmToolusageitemPojo);

    DmToolusageitemPojo update(DmToolusageitemPojo dmToolusageitempojo);

    int delete(String key,String tid);

    DmToolusageitemPojo clearNull(DmToolusageitemPojo dmToolusageitempojo);
}
