package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolinfoPojo;
import com.github.pagehelper.PageInfo;

/**
 * 工装具基础信息(Dm_ToolInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-09 16:30:37
 */
public interface DmToolinfoService {

    DmToolinfoPojo getEntity(String key,String tid);

    PageInfo<DmToolinfoPojo> getPageList(QueryParam queryParam);

    DmToolinfoPojo insert(DmToolinfoPojo dmToolinfoPojo);

    DmToolinfoPojo update(DmToolinfoPojo dmToolinfopojo);

    int delete(String key,String tid);
}
