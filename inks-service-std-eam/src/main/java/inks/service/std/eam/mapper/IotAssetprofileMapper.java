package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotAssetprofilePojo;
import inks.service.std.eam.domain.IotAssetprofileEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 资产配置(Iot_AssetProfile)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-14 13:01:05
 */
@Mapper
public interface IotAssetprofileMapper {

    IotAssetprofilePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotAssetprofilePojo> getPageList(QueryParam queryParam);

    int insert(IotAssetprofileEntity iotAssetprofileEntity);

    int update(IotAssetprofileEntity iotAssetprofileEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

