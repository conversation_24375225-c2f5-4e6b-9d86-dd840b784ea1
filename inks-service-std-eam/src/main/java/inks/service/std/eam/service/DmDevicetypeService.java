package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmDevicetypePojo;

/**
 * 设备分类(DmDevicetype)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-06 12:52:02
 */
public interface DmDevicetypeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmDevicetypePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmDevicetypePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmDevicetypePojo 实例对象
     * @return 实例对象
     */
    DmDevicetypePojo insert(DmDevicetypePojo dmDevicetypePojo);

    /**
     * 修改数据
     *
     * @param dmDevicetypepojo 实例对象
     * @return 实例对象
     */
    DmDevicetypePojo update(DmDevicetypePojo dmDevicetypepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
