package inks.service.std.eam.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 出入库项目(DmSpareaccessitem)Entity
 *
 * <AUTHOR>
 * @since 2023-06-10 10:09:41
 */
public class DmSpareaccessitemEntity implements Serializable {
    private static final long serialVersionUID = -59995479888833974L;
    // ID
    private String id;
    // Pid
    private String pid;
    // 备件id
    private String spareid;
    // 数量
    private Double quantity;
    // 单价
    private Double price;
    // 金额
    private Double amount;
    // 备注
    private String remark;
    // 行号
    private Integer rownum;
    // 库位编码
    private String location;
    // 批号
    private String batchno;
    // 限用日期
    private Date expirydate;
    // 含税单价
    private Double taxprice;
    // 含税金额
    private Double taxamount;
    // 记录税率
    private Double itemtaxrate;
    // 应用单号
    private String citeuid;
    // 引用子项id
    private String citeitemid;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 租户id
    private String tenantid;
    // 乐观锁
    private Integer revision;

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 备件id
    public String getSpareid() {
        return spareid;
    }

    public void setSpareid(String spareid) {
        this.spareid = spareid;
    }

    // 数量
    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    // 单价
    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    // 金额
    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 库位编码
    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCiteuid() {
        return citeuid;
    }

    public void setCiteuid(String citeuid) {
        this.citeuid = citeuid;
    }

    public String getCiteitemid() {
        return citeitemid;
    }

    public void setCiteitemid(String citeitemid) {
        this.citeitemid = citeitemid;
    }

    // 批号
    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    // 限用日期
    public Date getExpirydate() {
        return expirydate;
    }

    public void setExpirydate(Date expirydate) {
        this.expirydate = expirydate;
    }

    // 含税单价
    public Double getTaxprice() {
        return taxprice;
    }

    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }

    // 含税金额
    public Double getTaxamount() {
        return taxamount;
    }

    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }

    // 记录税率
    public Double getItemtaxrate() {
        return itemtaxrate;
    }

    public void setItemtaxrate(Double itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

