package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmPatrolrecitemPojo;

import java.util.List;

/**
 * 检检点明细(DmPatrolrecitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 13:10:08
 */
public interface DmPatrolrecitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolrecitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolrecitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmPatrolrecitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param dmPatrolrecitemPojo 实例对象
     * @return 实例对象
     */
    DmPatrolrecitemPojo insert(DmPatrolrecitemPojo dmPatrolrecitemPojo);

    /**
     * 修改数据
     *
     * @param dmPatrolrecitempojo 实例对象
     * @return 实例对象
     */
    DmPatrolrecitemPojo update(DmPatrolrecitemPojo dmPatrolrecitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param dmPatrolrecitempojo 实例对象
     * @return 实例对象
     */
    DmPatrolrecitemPojo clearNull(DmPatrolrecitemPojo dmPatrolrecitempojo);
}
