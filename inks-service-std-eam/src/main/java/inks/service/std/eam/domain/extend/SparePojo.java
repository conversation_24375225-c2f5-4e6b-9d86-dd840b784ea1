package inks.service.std.eam.domain.extend;

import java.io.Serializable;

/**
 * 备件实体类 用于各个Item继承备件的以下字段
 *
 * <AUTHOR>
 * @since 2023-07-07 12:59:48
 */
public class SparePojo implements Serializable {
    private static final long serialVersionUID = -97119849723758555L;

    // 备件ID
    private String spareid;
    // 备件编码
    private String sparecode;
    // 备件name Guid
    private String sparename;
    // 备件规格
    private String sparespec;
    // 备件单位
    private String spareunit;

    public String getSpareid() {
        return spareid;
    }

    public void setSpareid(String spareid) {
        this.spareid = spareid;
    }

    public String getSparecode() {
        return sparecode;
    }

    public void setSparecode(String sparecode) {
        this.sparecode = sparecode;
    }

    public String getSparename() {
        return sparename;
    }

    public void setSparename(String sparename) {
        this.sparename = sparename;
    }

    public String getSparespec() {
        return sparespec;
    }

    public void setSparespec(String sparespec) {
        this.sparespec = sparespec;
    }

    public String getSpareunit() {
        return spareunit;
    }

    public void setSpareunit(String spareunit) {
        this.spareunit = spareunit;
    }
}
