package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmUpkeepplanEntity;
import inks.service.std.eam.domain.pojo.DmUpkeepplanPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepplanitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 保养计划(DmUpkeepplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-07 17:16:51
 */
@Mapper
public interface DmUpkeepplanMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeepplanPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmUpkeepplanitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmUpkeepplanPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmUpkeepplanEntity 实例对象
     * @return 影响行数
     */
    int insert(DmUpkeepplanEntity dmUpkeepplanEntity);


    /**
     * 修改数据
     *
     * @param dmUpkeepplanEntity 实例对象
     * @return 影响行数
     */
    int update(DmUpkeepplanEntity dmUpkeepplanEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param dmUpkeepplanPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(DmUpkeepplanPojo dmUpkeepplanPojo);

    /**
     * 修改数据
     *
     * @param dmUpkeepplanEntity 实例对象
     * @return 影响行数
     */
    int approval(DmUpkeepplanEntity dmUpkeepplanEntity);

    List<DmUpkeepplanPojo> getAllList(String tid);

    List<String> getAllDevId(@Param("planid") String planid, @Param("tid") String tid);

    String getPlanName(@Param("planid") String planid, @Param("tid") String tid);
}

