package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.IotAlarmPojo;
import inks.service.std.eam.service.IotAlarmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 警报(Iot_Alarm)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-08 13:01:06
 */
@RestController
@RequestMapping("D09M36B1")
@Api(tags = "D09M36B1:警报")
public class D09M36B1Controller extends IotAlarmController {


    @Resource
    private IotAlarmService iotAlarmService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "确认警报", notes = "确认警报", produces = "application/json")
    @RequestMapping(value = "/acknowledge", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_Alarm.Edit")
    public R<IotAlarmPojo> acknowledgeAlarm(@RequestParam String alarmId) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.iotAlarmService.acknowledgeAlarm(alarmId, loginUser.getUserid(),
                    loginUser.getRealName(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "清除警报", notes = "清除警报", produces = "application/json")
    @RequestMapping(value = "/clear", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_Alarm.Edit")
    public R<IotAlarmPojo> clearAlarm(@RequestParam String alarmId) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.iotAlarmService.clearAlarm(alarmId, loginUser.getUserid(),
                    loginUser.getRealName(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "分配警报", notes = "分配警报", produces = "application/json")
    @RequestMapping(value = "/assign", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_Alarm.Edit")
    public R<IotAlarmPojo> assignAlarm(@RequestParam String alarmId,
                                       @RequestParam String assigneeId,
                                       @RequestParam String assigneeName) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.iotAlarmService.assignAlarm(alarmId, assigneeId, assigneeName,
                    loginUser.getUserid(), loginUser.getRealName(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "取消确认警报", notes = "取消确认警报", produces = "application/json")
    @RequestMapping(value = "/unacknowledge", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_Alarm.Edit")
    public R<IotAlarmPojo> unacknowledgeAlarm(@RequestParam String alarmId) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.iotAlarmService.unacknowledgeAlarm(alarmId, loginUser.getUserid(),
                    loginUser.getRealName(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
