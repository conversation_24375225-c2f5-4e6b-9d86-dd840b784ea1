package inks.service.std.eam.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.eam.domain.pojo.DmToolreturnitemPojo;
import inks.service.std.eam.domain.DmToolreturnitemEntity;
import inks.service.std.eam.mapper.DmToolreturnitemMapper;
import inks.service.std.eam.service.DmToolreturnitemService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 工装具归还子表-工装具(DmToolreturnitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-19 10:16:52
 */
@Service("dmToolreturnitemService")
public class DmToolreturnitemServiceImpl implements DmToolreturnitemService {
    @Resource
    private DmToolreturnitemMapper dmToolreturnitemMapper;

    @Override
    public DmToolreturnitemPojo getEntity(String key,String tid) {
        return this.dmToolreturnitemMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<DmToolreturnitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolreturnitemPojo> lst = dmToolreturnitemMapper.getPageList(queryParam);
            PageInfo<DmToolreturnitemPojo> pageInfo = new PageInfo<DmToolreturnitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<DmToolreturnitemPojo> getList(String Pid,String tid) { 
        try {
            List<DmToolreturnitemPojo> lst = dmToolreturnitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public DmToolreturnitemPojo insert(DmToolreturnitemPojo dmToolreturnitemPojo) {
        //初始化item的NULL
        DmToolreturnitemPojo itempojo =this.clearNull(dmToolreturnitemPojo);
        DmToolreturnitemEntity dmToolreturnitemEntity = new DmToolreturnitemEntity(); 
        BeanUtils.copyProperties(itempojo,dmToolreturnitemEntity);
          //生成雪花id
          dmToolreturnitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          dmToolreturnitemEntity.setRevision(1);  //乐观锁      
          this.dmToolreturnitemMapper.insert(dmToolreturnitemEntity);
        return this.getEntity(dmToolreturnitemEntity.getId(),dmToolreturnitemEntity.getTenantid());
  
    }

    @Override
    public DmToolreturnitemPojo update(DmToolreturnitemPojo dmToolreturnitemPojo) {
        DmToolreturnitemEntity dmToolreturnitemEntity = new DmToolreturnitemEntity(); 
        BeanUtils.copyProperties(dmToolreturnitemPojo,dmToolreturnitemEntity);
        this.dmToolreturnitemMapper.update(dmToolreturnitemEntity);
        return this.getEntity(dmToolreturnitemEntity.getId(),dmToolreturnitemEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.dmToolreturnitemMapper.delete(key,tid) ;
    }

     @Override
     public DmToolreturnitemPojo clearNull(DmToolreturnitemPojo dmToolreturnitemPojo){
     //初始化NULL字段
     if(dmToolreturnitemPojo.getPid()==null) dmToolreturnitemPojo.setPid("");
     if(dmToolreturnitemPojo.getBorrowitemid()==null) dmToolreturnitemPojo.setBorrowitemid("");
     if(dmToolreturnitemPojo.getToolid()==null) dmToolreturnitemPojo.setToolid("");
     if(dmToolreturnitemPojo.getReturnqty()==null) dmToolreturnitemPojo.setReturnqty(0);
     if(dmToolreturnitemPojo.getUsageqty()==null) dmToolreturnitemPojo.setUsageqty(0);
     if(dmToolreturnitemPojo.getRownum()==null) dmToolreturnitemPojo.setRownum(0);
     if(dmToolreturnitemPojo.getRemark()==null) dmToolreturnitemPojo.setRemark("");
     if(dmToolreturnitemPojo.getCustom1()==null) dmToolreturnitemPojo.setCustom1("");
     if(dmToolreturnitemPojo.getCustom2()==null) dmToolreturnitemPojo.setCustom2("");
     if(dmToolreturnitemPojo.getCustom3()==null) dmToolreturnitemPojo.setCustom3("");
     if(dmToolreturnitemPojo.getCustom4()==null) dmToolreturnitemPojo.setCustom4("");
     if(dmToolreturnitemPojo.getCustom5()==null) dmToolreturnitemPojo.setCustom5("");
     if(dmToolreturnitemPojo.getCustom6()==null) dmToolreturnitemPojo.setCustom6("");
     if(dmToolreturnitemPojo.getCustom7()==null) dmToolreturnitemPojo.setCustom7("");
     if(dmToolreturnitemPojo.getCustom8()==null) dmToolreturnitemPojo.setCustom8("");
     if(dmToolreturnitemPojo.getCustom9()==null) dmToolreturnitemPojo.setCustom9("");
     if(dmToolreturnitemPojo.getCustom10()==null) dmToolreturnitemPojo.setCustom10("");
     if(dmToolreturnitemPojo.getTenantid()==null) dmToolreturnitemPojo.setTenantid("");
     if(dmToolreturnitemPojo.getRevision()==null) dmToolreturnitemPojo.setRevision(0);
     return dmToolreturnitemPojo;
     }
}
