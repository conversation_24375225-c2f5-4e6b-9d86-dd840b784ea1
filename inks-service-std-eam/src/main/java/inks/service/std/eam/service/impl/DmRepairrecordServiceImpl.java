package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmRepairrecordEntity;
import inks.service.std.eam.domain.DmRepairrecorditemEntity;
import inks.service.std.eam.domain.pojo.DmRepairrecordPojo;
import inks.service.std.eam.domain.pojo.DmRepairrecorditemPojo;
import inks.service.std.eam.domain.pojo.DmRepairrecorditemdetailPojo;
import inks.service.std.eam.mapper.DmRepairrecordMapper;
import inks.service.std.eam.mapper.DmRepairrecorditemMapper;
import inks.service.std.eam.mapper.DmRepairserviceMapper;
import inks.service.std.eam.service.DmRepairrecordService;
import inks.service.std.eam.service.DmRepairrecorditemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 维护记录(DmRepairrecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-06 14:54:55
 */
@Service("dmRepairrecordService")
public class DmRepairrecordServiceImpl implements DmRepairrecordService {
    @Resource
    private DmRepairrecordMapper dmRepairrecordMapper;

    @Resource
    private DmRepairrecorditemMapper dmRepairrecorditemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private DmRepairrecorditemService dmRepairrecorditemService;
    @Resource
    private DmRepairserviceMapper dmRepairserviceMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmRepairrecordPojo getEntity(String key, String tid) {
        return this.dmRepairrecordMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmRepairrecorditemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmRepairrecorditemdetailPojo> lst = dmRepairrecordMapper.getPageList(queryParam);
            PageInfo<DmRepairrecorditemdetailPojo> pageInfo = new PageInfo<DmRepairrecorditemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmRepairrecordPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            DmRepairrecordPojo dmRepairrecordPojo = this.dmRepairrecordMapper.getEntity(key, tid);
            //读取子表
            dmRepairrecordPojo.setItem(dmRepairrecorditemMapper.getList(dmRepairrecordPojo.getId(), dmRepairrecordPojo.getTenantid()));
            return dmRepairrecordPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmRepairrecordPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmRepairrecordPojo> lst = dmRepairrecordMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(dmRepairrecorditemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<DmRepairrecordPojo> pageInfo = new PageInfo<DmRepairrecordPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmRepairrecordPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmRepairrecordPojo> lst = dmRepairrecordMapper.getPageTh(queryParam);
            PageInfo<DmRepairrecordPojo> pageInfo = new PageInfo<DmRepairrecordPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmRepairrecordPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmRepairrecordPojo insert(DmRepairrecordPojo dmRepairrecordPojo) {
        String tid = dmRepairrecordPojo.getTenantid();
        // 检查传入报修单id是否已经生成了维修记录
        int count = this.dmRepairrecordMapper.checkRepair(dmRepairrecordPojo.getRepairid(), null, tid);
        if (count > 0) {
            throw new BaseBusinessException("该报修单号已经生成了维修记录，请勿重复生成！");
        }
        //初始化NULL字段
        if (dmRepairrecordPojo.getRefno() == null) dmRepairrecordPojo.setRefno("");
        if (dmRepairrecordPojo.getBilldate() == null) dmRepairrecordPojo.setBilldate(new Date());
        if (dmRepairrecordPojo.getBilltype() == null) dmRepairrecordPojo.setBilltype("");
        if (dmRepairrecordPojo.getBilltitle() == null) dmRepairrecordPojo.setBilltitle("");
        if (dmRepairrecordPojo.getDeviceid() == null) dmRepairrecordPojo.setDeviceid("");
        if (dmRepairrecordPojo.getRepairid() == null) dmRepairrecordPojo.setRepairid("");
        if (dmRepairrecordPojo.getFailurecause() == null) dmRepairrecordPojo.setFailurecause("");
        if (dmRepairrecordPojo.getSolution() == null) dmRepairrecordPojo.setSolution("");
        if (dmRepairrecordPojo.getWorktime() == null) dmRepairrecordPojo.setWorktime(0D);
        if (dmRepairrecordPojo.getSignimage() == null) dmRepairrecordPojo.setSignimage("");
        if (dmRepairrecordPojo.getOperator() == null) dmRepairrecordPojo.setOperator("");
        if (dmRepairrecordPojo.getSummary() == null) dmRepairrecordPojo.setSummary("");
        if (dmRepairrecordPojo.getCustom1() == null) dmRepairrecordPojo.setCustom1("");
        if (dmRepairrecordPojo.getCustom2() == null) dmRepairrecordPojo.setCustom2("");
        if (dmRepairrecordPojo.getCustom3() == null) dmRepairrecordPojo.setCustom3("");
        if (dmRepairrecordPojo.getCustom4() == null) dmRepairrecordPojo.setCustom4("");
        if (dmRepairrecordPojo.getCustom5() == null) dmRepairrecordPojo.setCustom5("");
        if (dmRepairrecordPojo.getCustom6() == null) dmRepairrecordPojo.setCustom6("");
        if (dmRepairrecordPojo.getCustom7() == null) dmRepairrecordPojo.setCustom7("");
        if (dmRepairrecordPojo.getCustom8() == null) dmRepairrecordPojo.setCustom8("");
        if (dmRepairrecordPojo.getLister() == null) dmRepairrecordPojo.setLister("");
        if (dmRepairrecordPojo.getListerid() == null) dmRepairrecordPojo.setListerid("");
        if (dmRepairrecordPojo.getCreateby() == null) dmRepairrecordPojo.setCreateby("");
        if (dmRepairrecordPojo.getCreatebyid() == null) dmRepairrecordPojo.setCreatebyid("");
        if (dmRepairrecordPojo.getCreatedate() == null) dmRepairrecordPojo.setCreatedate(new Date());
        if (dmRepairrecordPojo.getModifydate() == null) dmRepairrecordPojo.setModifydate(new Date());
        if (dmRepairrecordPojo.getAssessorid() == null) dmRepairrecordPojo.setAssessorid("");
        if (dmRepairrecordPojo.getAssessdate() == null) dmRepairrecordPojo.setAssessdate(new Date());
        if (tid == null) dmRepairrecordPojo.setTenantid("");
        if (dmRepairrecordPojo.getRevision() == null) dmRepairrecordPojo.setRevision(0);
        // 同步[保修单]状态为“完成”; 同步[设备]状态为“正常”
        dmRepairrecordMapper.updateRepairStatus(dmRepairrecordPojo.getRepairid(), tid, "完成");
        dmRepairserviceMapper.updateDeviceState(dmRepairrecordPojo.getDeviceid(), "正常", tid);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmRepairrecordEntity dmRepairrecordEntity = new DmRepairrecordEntity();
        BeanUtils.copyProperties(dmRepairrecordPojo, dmRepairrecordEntity);

        //设置id和新建日期
        dmRepairrecordEntity.setId(id);
        dmRepairrecordEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmRepairrecordMapper.insert(dmRepairrecordEntity);
        //Item子表处理
        List<DmRepairrecorditemPojo> lst = dmRepairrecordPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                DmRepairrecorditemPojo itemPojo = this.dmRepairrecorditemService.clearNull(lst.get(i));
                DmRepairrecorditemEntity dmRepairrecorditemEntity = new DmRepairrecorditemEntity();
                BeanUtils.copyProperties(itemPojo, dmRepairrecorditemEntity);
                //设置id和Pid
                dmRepairrecorditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmRepairrecorditemEntity.setPid(id);
                dmRepairrecorditemEntity.setTenantid(tid);
                dmRepairrecorditemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmRepairrecorditemMapper.insert(dmRepairrecorditemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmRepairrecordEntity.getId(), dmRepairrecordEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmRepairrecordPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmRepairrecordPojo update(DmRepairrecordPojo dmRepairrecordPojo) {
        String tid = dmRepairrecordPojo.getTenantid();
        // 检查传入报修单id是否已经生成了维修记录
        int count = this.dmRepairrecordMapper.checkRepair(dmRepairrecordPojo.getRepairid(), dmRepairrecordPojo.getId(), tid);
        if (count > 0) {
            throw new BaseBusinessException("该报修单号已经生成了维修记录，请勿重复生成！");
        }
        //主表更改
        DmRepairrecordEntity dmRepairrecordEntity = new DmRepairrecordEntity();
        BeanUtils.copyProperties(dmRepairrecordPojo, dmRepairrecordEntity);
        this.dmRepairrecordMapper.update(dmRepairrecordEntity);
        if (dmRepairrecordPojo.getItem() != null) {
            //Item子表处理
            List<DmRepairrecorditemPojo> lst = dmRepairrecordPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = dmRepairrecordMapper.getDelItemIds(dmRepairrecordPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.dmRepairrecorditemMapper.delete(lstDelIds.get(i), tid);
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    DmRepairrecorditemEntity dmRepairrecorditemEntity = new DmRepairrecorditemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        DmRepairrecorditemPojo itemPojo = this.dmRepairrecorditemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, dmRepairrecorditemEntity);
                        //设置id和Pid
                        dmRepairrecorditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        dmRepairrecorditemEntity.setPid(dmRepairrecordEntity.getId());  // 主表 id
                        dmRepairrecorditemEntity.setTenantid(tid);   // 租户id
                        dmRepairrecorditemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.dmRepairrecorditemMapper.insert(dmRepairrecorditemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), dmRepairrecorditemEntity);
                        dmRepairrecorditemEntity.setTenantid(tid);
                        this.dmRepairrecorditemMapper.update(dmRepairrecorditemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmRepairrecordEntity.getId(), tid);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        DmRepairrecordPojo dmRepairrecordPojo = this.getBillEntity(key, tid);
        // 同步[保修单]状态为“”; 同步[设备]状态为“报修”
        dmRepairrecordMapper.updateRepairStatus(dmRepairrecordPojo.getRepairid(), tid, "");
        dmRepairserviceMapper.updateDeviceState(dmRepairrecordPojo.getDeviceid(), "报修", tid);
        //Item子表处理
        List<DmRepairrecorditemPojo> lst = dmRepairrecordPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.dmRepairrecorditemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.dmRepairrecordMapper.delete(key, tid);
    }


}
