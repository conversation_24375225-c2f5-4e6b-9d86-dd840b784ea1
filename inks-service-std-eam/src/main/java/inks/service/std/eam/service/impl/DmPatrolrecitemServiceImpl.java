package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmPatrolrecitemEntity;
import inks.service.std.eam.domain.pojo.DmPatrolrecitemPojo;
import inks.service.std.eam.mapper.DmPatrolrecitemMapper;
import inks.service.std.eam.service.DmPatrolrecitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 检检点明细(DmPatrolrecitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 13:18:01
 */
@Service("dmPatrolrecitemService")
public class DmPatrolrecitemServiceImpl implements DmPatrolrecitemService {
    @Resource
    private DmPatrolrecitemMapper dmPatrolrecitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmPatrolrecitemPojo getEntity(String key, String tid) {
        return this.dmPatrolrecitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolrecitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolrecitemPojo> lst = dmPatrolrecitemMapper.getPageList(queryParam);
            PageInfo<DmPatrolrecitemPojo> pageInfo = new PageInfo<DmPatrolrecitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DmPatrolrecitemPojo> getList(String Pid, String tid) {
        try {
            List<DmPatrolrecitemPojo> lst = dmPatrolrecitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param dmPatrolrecitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolrecitemPojo insert(DmPatrolrecitemPojo dmPatrolrecitemPojo) {
        //初始化item的NULL
        DmPatrolrecitemPojo itempojo = this.clearNull(dmPatrolrecitemPojo);
        DmPatrolrecitemEntity dmPatrolrecitemEntity = new DmPatrolrecitemEntity();
        BeanUtils.copyProperties(itempojo, dmPatrolrecitemEntity);
        //生成雪花id
        dmPatrolrecitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmPatrolrecitemEntity.setRevision(1);  //乐观锁
        this.dmPatrolrecitemMapper.insert(dmPatrolrecitemEntity);
        return this.getEntity(dmPatrolrecitemEntity.getId(), dmPatrolrecitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmPatrolrecitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolrecitemPojo update(DmPatrolrecitemPojo dmPatrolrecitemPojo) {
        DmPatrolrecitemEntity dmPatrolrecitemEntity = new DmPatrolrecitemEntity();
        BeanUtils.copyProperties(dmPatrolrecitemPojo, dmPatrolrecitemEntity);
        this.dmPatrolrecitemMapper.update(dmPatrolrecitemEntity);
        return this.getEntity(dmPatrolrecitemEntity.getId(), dmPatrolrecitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmPatrolrecitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param dmPatrolrecitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolrecitemPojo clearNull(DmPatrolrecitemPojo dmPatrolrecitemPojo) {
        //初始化NULL字段
        if (dmPatrolrecitemPojo.getPid() == null) dmPatrolrecitemPojo.setPid("");
        if (dmPatrolrecitemPojo.getPointid() == null) dmPatrolrecitemPojo.setPointid("");
        if (dmPatrolrecitemPojo.getPointcode() == null) dmPatrolrecitemPojo.setPointcode("");
        if (dmPatrolrecitemPojo.getPointname() == null) dmPatrolrecitemPojo.setPointname("");
        if (dmPatrolrecitemPojo.getStdid() == null) dmPatrolrecitemPojo.setStdid("");
        if (dmPatrolrecitemPojo.getStdcode() == null) dmPatrolrecitemPojo.setStdcode("");
        if (dmPatrolrecitemPojo.getStdname() == null) dmPatrolrecitemPojo.setStdname("");
        if (dmPatrolrecitemPojo.getEvidence() == null) dmPatrolrecitemPojo.setEvidence("");
        if (dmPatrolrecitemPojo.getConforms() == null) dmPatrolrecitemPojo.setConforms(0);
        if (dmPatrolrecitemPojo.getRownum() == null) dmPatrolrecitemPojo.setRownum(0);
        if (dmPatrolrecitemPojo.getClausedetail() == null) dmPatrolrecitemPojo.setClausedetail("");
        if (dmPatrolrecitemPojo.getRemark() == null) dmPatrolrecitemPojo.setRemark("");
        if (dmPatrolrecitemPojo.getCustom1() == null) dmPatrolrecitemPojo.setCustom1("");
        if (dmPatrolrecitemPojo.getCustom2() == null) dmPatrolrecitemPojo.setCustom2("");
        if (dmPatrolrecitemPojo.getCustom3() == null) dmPatrolrecitemPojo.setCustom3("");
        if (dmPatrolrecitemPojo.getCustom4() == null) dmPatrolrecitemPojo.setCustom4("");
        if (dmPatrolrecitemPojo.getCustom5() == null) dmPatrolrecitemPojo.setCustom5("");
        if (dmPatrolrecitemPojo.getCustom6() == null) dmPatrolrecitemPojo.setCustom6("");
        if (dmPatrolrecitemPojo.getCustom7() == null) dmPatrolrecitemPojo.setCustom7("");
        if (dmPatrolrecitemPojo.getCustom8() == null) dmPatrolrecitemPojo.setCustom8("");
        if (dmPatrolrecitemPojo.getCustom9() == null) dmPatrolrecitemPojo.setCustom9("");
        if (dmPatrolrecitemPojo.getCustom10() == null) dmPatrolrecitemPojo.setCustom10("");
        if (dmPatrolrecitemPojo.getTenantid() == null) dmPatrolrecitemPojo.setTenantid("");
        if (dmPatrolrecitemPojo.getRevision() == null) dmPatrolrecitemPojo.setRevision(0);
        return dmPatrolrecitemPojo;
    }
}
