package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmSpareaccessPojo;
import inks.service.std.eam.domain.pojo.DmSpareaccessitemdetailPojo;

/**
 * 备件出入(DmSpareaccess)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 10:09:25
 */
public interface DmSpareaccessService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmSpareaccessPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmSpareaccessitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmSpareaccessPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmSpareaccessPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmSpareaccessPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmSpareaccessPojo 实例对象
     * @return 实例对象
     */
    DmSpareaccessPojo insert(DmSpareaccessPojo dmSpareaccessPojo);

    /**
     * 修改数据
     *
     * @param dmSpareaccesspojo 实例对象
     * @return 实例对象
     */
    DmSpareaccessPojo update(DmSpareaccessPojo dmSpareaccesspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param dmSpareaccessPojo 实例对象
     * @return 实例对象
     */
    DmSpareaccessPojo approval(DmSpareaccessPojo dmSpareaccessPojo);
}
