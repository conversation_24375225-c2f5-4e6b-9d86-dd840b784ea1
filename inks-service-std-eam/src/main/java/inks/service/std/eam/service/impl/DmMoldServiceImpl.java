package inks.service.std.eam.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.eam.domain.pojo.DmMoldPojo;
import inks.service.std.eam.domain.DmMoldEntity;
import inks.service.std.eam.mapper.DmMoldMapper;
import inks.service.std.eam.service.DmMoldService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 模具管理(DmMold)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-13 14:12:19
 */
@Service("dmMoldService")
public class DmMoldServiceImpl implements DmMoldService {
    @Resource
    private DmMoldMapper dmMoldMapper;

    @Override
    public DmMoldPojo getEntity(String key, String tid) {
        return this.dmMoldMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<DmMoldPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmMoldPojo> lst = dmMoldMapper.getPageList(queryParam);
            PageInfo<DmMoldPojo> pageInfo = new PageInfo<DmMoldPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public DmMoldPojo insert(DmMoldPojo dmMoldPojo) {
        //初始化NULL字段
        cleanNull(dmMoldPojo);
        DmMoldEntity dmMoldEntity = new DmMoldEntity(); 
        BeanUtils.copyProperties(dmMoldPojo,dmMoldEntity);
          //生成雪花id
          dmMoldEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          dmMoldEntity.setRevision(1);  //乐观锁
          this.dmMoldMapper.insert(dmMoldEntity);
        return this.getEntity(dmMoldEntity.getId(),dmMoldEntity.getTenantid());
    }


    @Override
    public DmMoldPojo update(DmMoldPojo dmMoldPojo) {
        DmMoldEntity dmMoldEntity = new DmMoldEntity(); 
        BeanUtils.copyProperties(dmMoldPojo,dmMoldEntity);
        this.dmMoldMapper.update(dmMoldEntity);
        return this.getEntity(dmMoldEntity.getId(),dmMoldEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.dmMoldMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(DmMoldPojo dmMoldPojo) {
        if(dmMoldPojo.getMoldcode()==null) dmMoldPojo.setMoldcode("");
        if(dmMoldPojo.getMoldname()==null) dmMoldPojo.setMoldname("");
        if(dmMoldPojo.getDescription()==null) dmMoldPojo.setDescription("");
        if(dmMoldPojo.getGroupid()==null) dmMoldPojo.setGroupid("");
        if(dmMoldPojo.getDrawingnumber()==null) dmMoldPojo.setDrawingnumber("");
        if(dmMoldPojo.getQuantity()==null) dmMoldPojo.setQuantity(0D);
        if(dmMoldPojo.getAbnormalqty()==null) dmMoldPojo.setAbnormalqty(0D);
        if(dmMoldPojo.getLastuseddate()==null) dmMoldPojo.setLastuseddate(new Date());
        if(dmMoldPojo.getMaxprocessing()==null) dmMoldPojo.setMaxprocessing(0);
        if(dmMoldPojo.getCurrentprocessing()==null) dmMoldPojo.setCurrentprocessing(0);
        if(dmMoldPojo.getStatuscode()==null) dmMoldPojo.setStatuscode("");
        if(dmMoldPojo.getRownum()==null) dmMoldPojo.setRownum(0);
        if(dmMoldPojo.getRemark()==null) dmMoldPojo.setRemark("");
        if(dmMoldPojo.getCreateby()==null) dmMoldPojo.setCreateby("");
        if(dmMoldPojo.getCreatebyid()==null) dmMoldPojo.setCreatebyid("");
        if(dmMoldPojo.getCreatedate()==null) dmMoldPojo.setCreatedate(new Date());
        if(dmMoldPojo.getLister()==null) dmMoldPojo.setLister("");
        if(dmMoldPojo.getListerid()==null) dmMoldPojo.setListerid("");
        if(dmMoldPojo.getModifydate()==null) dmMoldPojo.setModifydate(new Date());
        if(dmMoldPojo.getCustom1()==null) dmMoldPojo.setCustom1("");
        if(dmMoldPojo.getCustom2()==null) dmMoldPojo.setCustom2("");
        if(dmMoldPojo.getCustom3()==null) dmMoldPojo.setCustom3("");
        if(dmMoldPojo.getCustom4()==null) dmMoldPojo.setCustom4("");
        if(dmMoldPojo.getCustom5()==null) dmMoldPojo.setCustom5("");
        if(dmMoldPojo.getCustom6()==null) dmMoldPojo.setCustom6("");
        if(dmMoldPojo.getCustom7()==null) dmMoldPojo.setCustom7("");
        if(dmMoldPojo.getCustom8()==null) dmMoldPojo.setCustom8("");
        if(dmMoldPojo.getCustom9()==null) dmMoldPojo.setCustom9("");
        if(dmMoldPojo.getCustom10()==null) dmMoldPojo.setCustom10("");
        if(dmMoldPojo.getTenantid()==null) dmMoldPojo.setTenantid("");
        if(dmMoldPojo.getTenantname()==null) dmMoldPojo.setTenantname("");
        if(dmMoldPojo.getRevision()==null) dmMoldPojo.setRevision(0);
   }

}
