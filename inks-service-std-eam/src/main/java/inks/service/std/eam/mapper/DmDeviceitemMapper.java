package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmDeviceitemEntity;
import inks.service.std.eam.domain.pojo.DmDeviceitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备参数(DmDeviceitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-06 12:59:33
 */
@Mapper
public interface DmDeviceitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmDeviceitemPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmDeviceitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmDeviceitemPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);


    /**
     * 新增数据
     *
     * @param dmDeviceitemEntity 实例对象
     * @return 影响行数
     */
    int insert(DmDeviceitemEntity dmDeviceitemEntity);


    /**
     * 修改数据
     *
     * @param dmDeviceitemEntity 实例对象
     * @return 影响行数
     */
    int update(DmDeviceitemEntity dmDeviceitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

}

