package inks.service.std.eam.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.eam.domain.pojo.IotAssetPojo;
import inks.service.std.eam.domain.IotAssetEntity;
import inks.service.std.eam.mapper.IotAssetMapper;
import inks.service.std.eam.service.IotAssetService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 资产(IotAsset)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-14 13:01:05
 */
@Service("iotAssetService")
public class IotAssetServiceImpl implements IotAssetService {
    @Resource
    private IotAssetMapper iotAssetMapper;

    @Override
    public IotAssetPojo getEntity(String key, String tid) {
        return this.iotAssetMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotAssetPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotAssetPojo> lst = iotAssetMapper.getPageList(queryParam);
            PageInfo<IotAssetPojo> pageInfo = new PageInfo<IotAssetPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotAssetPojo insert(IotAssetPojo iotAssetPojo) {
        //初始化NULL字段
        cleanNull(iotAssetPojo);
        IotAssetEntity iotAssetEntity = new IotAssetEntity(); 
        BeanUtils.copyProperties(iotAssetPojo,iotAssetEntity);
          //生成雪花id
          iotAssetEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotAssetEntity.setRevision(1);  //乐观锁
          this.iotAssetMapper.insert(iotAssetEntity);
        return this.getEntity(iotAssetEntity.getId(),iotAssetEntity.getTenantid());
    }


    @Override
    public IotAssetPojo update(IotAssetPojo iotAssetPojo) {
        IotAssetEntity iotAssetEntity = new IotAssetEntity(); 
        BeanUtils.copyProperties(iotAssetPojo,iotAssetEntity);
        this.iotAssetMapper.update(iotAssetEntity);
        return this.getEntity(iotAssetEntity.getId(),iotAssetEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotAssetMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotAssetPojo iotAssetPojo) {
        if(iotAssetPojo.getAdditionalinfo()==null) iotAssetPojo.setAdditionalinfo("");
        if(iotAssetPojo.getCustomerid()==null) iotAssetPojo.setCustomerid("");
        if(iotAssetPojo.getAssetprofileid()==null) iotAssetPojo.setAssetprofileid("");
        if(iotAssetPojo.getAssetname()==null) iotAssetPojo.setAssetname("");
        if(iotAssetPojo.getAssetlabel()==null) iotAssetPojo.setAssetlabel("");
        if(iotAssetPojo.getAssettype()==null) iotAssetPojo.setAssettype("");
        if(iotAssetPojo.getExternalid()==null) iotAssetPojo.setExternalid("");
        if(iotAssetPojo.getRemark()==null) iotAssetPojo.setRemark("");
        if(iotAssetPojo.getRownum()==null) iotAssetPojo.setRownum(0);
        if(iotAssetPojo.getCreateby()==null) iotAssetPojo.setCreateby("");
        if(iotAssetPojo.getCreatebyid()==null) iotAssetPojo.setCreatebyid("");
        if(iotAssetPojo.getCreatedate()==null) iotAssetPojo.setCreatedate(new Date());
        if(iotAssetPojo.getLister()==null) iotAssetPojo.setLister("");
        if(iotAssetPojo.getListerid()==null) iotAssetPojo.setListerid("");
        if(iotAssetPojo.getModifydate()==null) iotAssetPojo.setModifydate(new Date());
        if(iotAssetPojo.getCustom1()==null) iotAssetPojo.setCustom1("");
        if(iotAssetPojo.getCustom2()==null) iotAssetPojo.setCustom2("");
        if(iotAssetPojo.getCustom3()==null) iotAssetPojo.setCustom3("");
        if(iotAssetPojo.getCustom4()==null) iotAssetPojo.setCustom4("");
        if(iotAssetPojo.getCustom5()==null) iotAssetPojo.setCustom5("");
        if(iotAssetPojo.getCustom6()==null) iotAssetPojo.setCustom6("");
        if(iotAssetPojo.getCustom7()==null) iotAssetPojo.setCustom7("");
        if(iotAssetPojo.getCustom8()==null) iotAssetPojo.setCustom8("");
        if(iotAssetPojo.getCustom9()==null) iotAssetPojo.setCustom9("");
        if(iotAssetPojo.getCustom10()==null) iotAssetPojo.setCustom10("");
        if(iotAssetPojo.getTenantid()==null) iotAssetPojo.setTenantid("");
        if(iotAssetPojo.getTenantname()==null) iotAssetPojo.setTenantname("");
        if(iotAssetPojo.getRevision()==null) iotAssetPojo.setRevision(0);
   }

}
