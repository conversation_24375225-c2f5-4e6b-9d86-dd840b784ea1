package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolreturnitemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 工装具归还子表-工装具(DmToolreturnitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-19 10:16:52
 */
public interface DmToolreturnitemService {

    DmToolreturnitemPojo getEntity(String key,String tid);

    PageInfo<DmToolreturnitemPojo> getPageList(QueryParam queryParam);

    List<DmToolreturnitemPojo> getList(String Pid,String tid);  

    DmToolreturnitemPojo insert(DmToolreturnitemPojo dmToolreturnitemPojo);

    DmToolreturnitemPojo update(DmToolreturnitemPojo dmToolreturnitempojo);

    int delete(String key,String tid);

    DmToolreturnitemPojo clearNull(DmToolreturnitemPojo dmToolreturnitempojo);
}
