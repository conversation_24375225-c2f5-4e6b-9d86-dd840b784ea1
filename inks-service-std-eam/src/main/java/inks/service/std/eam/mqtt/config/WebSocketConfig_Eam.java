package inks.service.std.eam.mqtt.config;

import inks.service.std.eam.mqtt.websocket.MqttWebSocketHandler;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

import javax.annotation.Resource;

/**
 * WebSocket配置类
 * 用于配置MQTT管理系统的WebSocket通信
 * 
 * <AUTHOR>
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig_Eam implements WebSocketConfigurer {
    
    @Resource
    private MqttWebSocketHandler mqttWebSocketHandler;
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册MQTT管理WebSocket处理器
        registry.addHandler(mqttWebSocketHandler, "/ws/mqtt-management")
                .setAllowedOrigins("*") // 允许所有来源，生产环境应该限制具体域名
                .withSockJS(); // 启用SockJS支持，提供WebSocket的降级方案

        // 注册MQTT WebSocket处理器（兼容旧路径）
        registry.addHandler(mqttWebSocketHandler, "/ws/mqtt")
                .setAllowedOrigins("*")
                .withSockJS();
        
        // 注册MQTT日志WebSocket处理器
        registry.addHandler(mqttWebSocketHandler, "/ws/mqtt-logs")
                .setAllowedOrigins("*")
                .withSockJS();
        
        // 注册MQTT消息WebSocket处理器
        registry.addHandler(mqttWebSocketHandler, "/ws/mqtt-messages")
                .setAllowedOrigins("*")
                .withSockJS();
    }
}
