package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.controller.schedule.PlanReminder;
import inks.service.std.eam.domain.extend.DevicePojo;
import inks.service.std.eam.domain.pojo.DmUpkeepplanPojo;
import inks.service.std.eam.mapper.DmDeviceMapper;
import inks.service.std.eam.mapper.DmUpkeepplanMapper;
import inks.service.std.eam.service.DmUpkeepplanService;
import inks.service.std.eam.service.DmUpkeeprecordService;
import inks.service.std.eam.utils.PrintColor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 保养计划(Dm_UpkeepPlan)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-07 17:16:49
 */
@RestController
@RequestMapping("D09M04B2")
@Api(tags = "D09M04B2:保养计划")
public class D09M04B2Controller extends DmUpkeepplanController {
    @Resource
    private DmUpkeepplanService dmUpkeepplanService;
    @Resource
    private DmUpkeepplanMapper dmUpkeepplanMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;
    @Resource
    private PlanReminder planReminderController;
    @Resource
    private DmUpkeeprecordService dmUpkeeprecordService;
    @Resource
    private DmDeviceMapper dmDeviceMapper;
    @Resource
    private DmDeviceMapper deviceMapper;
    @Resource
    private ems_MailController emsMailController;

    public static void main(String[] args) {
        // 测试每周3和周4,5的日期
        LocalDate startDate = LocalDate.of(2023, 6, 8);
        LocalDate endDate = LocalDate.of(2023, 9, 30);
//        String daysOfWeek = "WEDNESDAY,FRIDAY";
        String daysOfWeek = "3,4";
        List<LocalDate> weeklyDates = getDatesInRange(startDate, endDate, "WEEKLY", daysOfWeek);
        System.out.println("每周3和周4的日期:");
        for (LocalDate date : weeklyDates) {
            System.out.println(date);
        }
        String daysOfWeekString = "WEDNESDAY,THURSDAY,FRIDAY";

        // 测试每月4号和11,17号的日期
        String daysOfMonth = "4,11,17";
        List<LocalDate> monthlyDates = getDatesInRange(startDate, endDate, "MONTHLY", daysOfMonth);
        System.out.println("每月4号和11号的日期:");
        for (LocalDate date : monthlyDates) {
            System.out.println(date);
        }

        // 测试每隔11天的日期
        List<LocalDate> intervalDates = getDatesInRange(startDate, endDate, "DAY", "11");
        System.out.println("每隔11天的日期:");
        for (LocalDate date : intervalDates) {
            System.out.println(date);
        }
        List<String> devidsPlan = Arrays.asList("A", "B", "C", "D");
        List<String> devidsRecord = Arrays.asList("B", "D", "E");
        List<String> differentValues = devidsPlan.stream()
                .filter(value -> !devidsRecord.contains(value))
                .collect(Collectors.toList());
        differentValues.forEach(System.out::println);

    }

    /**
     * @return DayOfWeek[]
     * @Description String转DayOfWeek[]  传入的字符串格式为"1,4"，输出的为DayOfWeek[]数组“MONDAY,THURSDAY”
     * <AUTHOR>
     * @param[1] daysOfWeekString
     * @time 2023/6/8 14:03
     */
    private static DayOfWeek[] stringToDayOfWeek(String daysOfWeekString) {
        String[] daysOfWeekArray = daysOfWeekString.split(",");
        DayOfWeek[] daysOfWeek = new DayOfWeek[daysOfWeekArray.length];
        for (int i = 0; i < daysOfWeekArray.length; i++) {
            int dayIndex = Integer.parseInt(daysOfWeekArray[i]);
            DayOfWeek dayOfWeek = DayOfWeek.of(dayIndex);
            daysOfWeek[i] = dayOfWeek;
        }
        return daysOfWeek;
    }

    /**
     * @return int[]
     * @Description String转int[]  传入的字符串格式为每月几号"1,11,22"，输出的为int[]数组
     * <AUTHOR>
     * @param[1] str
     * @time 2023/6/8 14:04
     */
    public static int[] StringToIntArray(String str) {
        String[] strArray = str.split(",");
        int[] intArray = new int[strArray.length];
        for (int i = 0; i < strArray.length; i++) {
            intArray[i] = Integer.parseInt(strArray[i]);
        }
        return intArray;
    }

    /**
     * @return List<LocalDate>
     * @Description 根据起始日期、结束日期、周期类型和周期值返回日期列表(每周周3周4,每月几号,每隔几天)
     * <AUTHOR>
     * @param[1] startDate 开始日期
     * @param[2] endDate 结束日期
     * @param[3] cycleType  周期类型
     * @param[4] cycleValue  Object类型,可以是DayOfWeek[]、int[]、Integer
     * @time 2023/6/8 13:05
     */
    public static List<LocalDate> getDatesInRange(LocalDate startDate, LocalDate endDate, String cycleType, String cycleValue) {
        List<LocalDate> dates = new ArrayList<>();
        switch (cycleType) {
            case "WEEKLY":  //每周几,可以是多个
                DayOfWeek[] daysOfWeek = stringToDayOfWeek(cycleValue);
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    DayOfWeek dayOfWeek = date.getDayOfWeek();
                    for (DayOfWeek targetDayOfWeek : daysOfWeek) {
                        if (dayOfWeek == targetDayOfWeek) {
                            dates.add(date);
                            break;
                        }
                    }
                }
                break;
            case "MONTHLY":  //每月几号,可以是多个
                int[] daysOfMonth = StringToIntArray(cycleValue);
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    int dayOfMonth = date.getDayOfMonth();
                    for (int targetDayOfMonth : daysOfMonth) {
                        if (dayOfMonth == targetDayOfMonth) {
                            dates.add(date);
                            break;
                        }
                    }

                }
                break;
            case "DAY":   //每隔几天,只能是一个
                // 将 String 类型的 cycleValue 转换为 int 类型
                int intervalDays = Integer.parseInt(cycleValue);
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(intervalDays)) {
                    dates.add(date);
                }
                break;
            default:
                System.out.println("无效的周期类型");
        }
        return dates;
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取(一个保养计划)日历:", notes = "获取保养计划详细信息", produces = "application/json")
    @RequestMapping(value = "/getPlan", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List")
    public R<List<LocalDate>> getPlan(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            DmUpkeepplanPojo dmUpkeepplanPojo = this.dmUpkeepplanService.getEntity(key, loginUser.getTenantid());
            // Date转LocalDate
            LocalDate localDateStart = dmUpkeepplanPojo.getStartdate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate localDateEnd = dmUpkeepplanPojo.getEnddate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            List<LocalDate> datesInRange = getDatesInRange(localDateStart, localDateEnd, dmUpkeepplanPojo.getCycletype(), dmUpkeepplanPojo.getCyclevalue());
            return R.ok(datesInRange);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取(所有保养计划)日历：", notes = "获取保养计划详细信息", produces = "application/json")
    @RequestMapping(value = "/getALLPlan", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List") //自调用不能有权限
    public R<List<Map<String, String>>> getALLPlan() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 不传日期则默认获取所有的保养计划,传入日期范围则获取该日期范围的保养计划
        List<Map<String, String>> allPlanList = dmUpkeepplanService.getALLPlan(loginUser.getTenantid(), null, null);
        return R.ok(allPlanList);
    }

    @ApiOperation(value = "获取(某年某月所有保养计划)日历：", notes = "获取保养计划详细信息", produces = "application/json")
    @RequestMapping(value = "/getALLPlanByMonth", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List") //自调用不能有权限
    public R<List<Map<String, String>>> getALLPlanByMonth(Integer year, Integer month) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        List<Map<String, String>> allPlanList = dmUpkeepplanService.getALLPlanByMonth(year, month, loginUser.getTenantid());
        return R.ok(allPlanList);
    }

    @ApiOperation(value = "获取[今天的]所有保养计划日历：", notes = "获取[今天的]所有保养计划日历：", produces = "application/json")
    @RequestMapping(value = "/getALLPlanToday", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List") //自调用不能有权限
    public R<List<Map<String, String>>> getALLPlanToday() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        List<Map<String, String>> allPlanListToday = dmUpkeepplanService.getALLPlanToday(loginUser.getTenantid());
        return R.ok(allPlanListToday);
    }

    //    @Scheduled(cron = "0 0 23 * * ?") //每天23点执行  改为由utils服务的定时任务Uts_CronPlan执行 接口不能有权限
    @ApiOperation(value = "/getALLPlanToday基础上:再找到未完成保养实施的所有设备id,再更新设备状态为'未保养'", notes = "获取[今天的]所有保养计划日历：", produces = "application/json")
    @RequestMapping(value = "/syncUnDoneDevsToday", method = RequestMethod.GET)
    public R<String> syncUnDoneDevsToday(String tid) {
        if (StringUtils.isBlank(tid)) {
            tid = tokenService.getLoginUser(ServletUtils.getRequest()).getTenantid();
        }
        // 获取[今天的]所有保养计划日历：
        List<Map<String, String>> allPlanListToday = dmUpkeepplanService.getALLPlanToday(tid);
        // 过滤出未完成的保养计划: 只要finishcount小于itemcount的
        List<Map<String, String>> unFinishPlanList = allPlanListToday.stream()
                .filter(plan -> Integer.parseInt(plan.get("finishcount")) < Integer.parseInt(plan.get("itemcount")))
                .collect(Collectors.toList());
        // 构建HashSet,拿到unFinishPlanList里面所有未完成的设备id(已勾选完成的设备不用更新状态)
        HashSet<String> unFinishDevIdsTodaySet = new HashSet<>();
        for (Map<String, String> planMap : unFinishPlanList) {
            // 通过planid,plandata获取计划下未转为实施单的所有设备,附加已转为实施单但未勾选完成的
            List<String> unFinishDevsInPlan = getRemUnFinishDevice(planMap.get("planid"), planMap.get("plandate"), tid).getData();
            unFinishDevIdsTodaySet.addAll(unFinishDevsInPlan);
        }
        // 更新unFinishDevsTodaySet的所有设备状态为"未保养"
        int i = 0;
        if (CollectionUtils.isNotEmpty(unFinishDevIdsTodaySet)) {
            i = deviceMapper.updateUseState(unFinishDevIdsTodaySet, "未保养", tid);
        }
        return R.ok("更新了" + i + "条设备状态为'未保养',更新的设备id集合为:" + unFinishDevIdsTodaySet);
    }

    //改为由utils服务的定时任务Uts_CronPlan执行 接口不能有权限
    @ApiOperation(value = "(发送邮件)保养计划提醒,读取Dm_UpkeepPlan.CycleNotice:提前几天提醒", notes = "开启提醒(所有保养计划)", produces = "application/json")
    @PostMapping(value = "/remindPlan")
    public R<List<Map<String, String>>> remindPlan(@RequestBody String json, String tid) throws MessagingException {
        if (StringUtils.isBlank(tid)) {
            tid = tokenService.getLoginUser(ServletUtils.getRequest()).getTenantid();
        }
        // 从json中获取收件人和抄送人: toEmail,ccEmails
        JSONObject jsonObject = JSONObject.parseObject(json, Feature.OrderedField);
        String toEmail = jsonObject.getString("toEmail");
        String ccEmails = jsonObject.getString("ccEmails");
        // 获取所有保养计划 (只查今天到3个月后)
        // LocalDate构建时间范围今天-今天的后3个月
        LocalDate localDateStart = LocalDate.now();
        LocalDate localDateEnd = localDateStart.plusMonths(3);
        List<Map<String, String>> allPlanList = dmUpkeepplanService.getALLPlan(tid, localDateStart, localDateEnd);
        // 过滤时间即将到期的保养计划(提前几天提醒:Dm_UpkeepPlan.CycleNotice天)
        List<Map<String, String>> filterNoticeDayPlanList = filterPlanList(allPlanList);
        // 过滤出未完成的保养计划: 只要finishcount小于itemcount的
        List<Map<String, String>> unFinishPlanList = filterNoticeDayPlanList.stream()
                .filter(plan -> Integer.parseInt(plan.get("finishcount")) < Integer.parseInt(plan.get("itemcount")))
                .collect(Collectors.toList());
        // 构建HashSet,拿到今天所有未完成的设备id
        List<Map<String, String>> unFinishDevPlanList = new ArrayList<>();
        for (Map<String, String> planMap : unFinishPlanList) {
            // 通过planid,plandata获取计划下未转为实施单的所有设备,附加已转为实施单但未勾选完成的
            List<String> unFinishDevsInPlan = getRemUnFinishDevice(planMap.get("planid"), planMap.get("plandate"), tid).getData();
            HashMap<String, String> map = new HashMap<>();
            map.put("planid", planMap.get("planid"));
            map.put("planname", dmUpkeepplanMapper.getPlanName(planMap.get("planid"), tid));//查询计划名称
            map.put("plandate", planMap.get("plandate"));
            map.put("unfinishdevids", String.join(",", unFinishDevsInPlan));
            List<String> devNames = dmDeviceMapper.getDevNamesByDevids(unFinishDevsInPlan, tid);//查询需提醒的设备名称List
            map.put("unfinishdevnames", String.join(",", devNames));
            unFinishDevPlanList.add(map);
        }

        //
        String emailContent = ems_MailController.listMapToHtmlDevPlan(unFinishDevPlanList);
        //今日日期字符串 2023-09-08
        String todayString = LocalDate.now().toString();
        String subject = todayString + "保养计划提醒";

        emsMailController.sendEmailCC(toEmail, ccEmails, subject, emailContent);
//        mailController.sendEmail("<EMAIL>", "附带发送的 "+subject, emailContent);

        PrintColor.red("保养计划提醒邮件发送成功");
        return R.ok(unFinishDevPlanList);
    }

    @ApiOperation(value = "开启提醒(所有保养计划):", notes = "开启提醒(所有保养计划)", produces = "application/json")
    @RequestMapping(value = "/startPlan", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List")
    public R<String> startPlan(int day) throws SchedulerException {
//        // 拿到所有保养计划
//        List<Map<String, String>> planList = getALLPlan().getData();
        // 拿到所有保养计划过滤到提前几天提醒,再创建定时任务发送保养提醒
        planReminderController.scheduleReminders(day);
        return R.ok("开启提醒成功");
    }

    @ApiOperation(value = "关闭提醒(所有保养计划):", notes = "开启提醒(所有保养计划)", produces = "application/json")
    @RequestMapping(value = "/stopPlan", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List")
    public R<String> stopPlan() throws SchedulerException {
        planReminderController.stopScheduler();
        return R.ok("关闭提醒成功");
    }

    @ApiOperation(value = "获取【日期+计划id下的所有实施单中】结余的【设备id集合】", notes = "结余的,即排除掉以下-->设备已生成了实施单,不论是否勾选完成", produces = "application/json")
    @RequestMapping(value = "/getRemDevice", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List") //自调用不能有权限
    public R<List<String>> getRemDevice(String planid, String date) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        // 获取计划下所有设备
        List<String> devidsPlan = dmUpkeepplanService.getAllDevId(planid, tid);
        // 获取计划已转为实施单的所有设备
        List<String> devidsRecord = dmUpkeeprecordService.getAllDevId(planid, date, tid);
        // 获取计划下未转为实施单的所有设备
        List<String> differentValues = devidsPlan.stream()
                .filter(value -> !devidsRecord.contains(value))
                .collect(Collectors.toList());
        return R.ok(differentValues);
    }

    @ApiOperation(value = "获取【日期+计划id下的所有实施单中】结余的【设备Pojo集合】", notes = "/getRemDevice基础上附加设备信息", produces = "application/json")
    @RequestMapping(value = "/getRemDevice2", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List") //自调用不能有权限
    public R<List<DevicePojo>> getRemDevice2(String planid, String date) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        // 获取计划下所有设备
        List<String> devidsPlan = dmUpkeepplanService.getAllDevId(planid, tid);
        // 获取计划已转为实施单的所有设备
        List<String> devidsRecord = dmUpkeeprecordService.getAllDevId(planid, date, tid);
        // 获取计划下未转为实施单的所有设备
        List<String> devIdList = devidsPlan.stream()
                .filter(value -> !devidsRecord.contains(value))
                .collect(Collectors.toList());
        List<DevicePojo> devicePojos = dmDeviceMapper.getDeviceByDevids(devIdList, tid);
        return R.ok(devicePojos);
    }

    @ApiOperation(value = "获取【日期+计划id下的所有实施单中】结余的[附加未勾选完成的]【设备id集合】", notes = "/getRemDevice基础上再过滤掉勾选了完成的设备", produces = "application/json")
    @RequestMapping(value = "/getRemUnFinishDevice", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List") //自调用不能有权限
    public R<List<String>> getRemUnFinishDevice(String planid, String date, String tid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 获取计划下所有设备
        List<String> devidsPlan = dmUpkeepplanService.getAllDevId(planid, tid);
        // 获取计划已转为实施单的所有设备,并且是勾选了完成的
        List<String> devidsRecord = dmUpkeeprecordService.getAllFinishDevId(planid, date, tid);
        // 获取计划下未转为实施单的所有设备,附加已转为实施单但未勾选完成的
        List<String> differentValues = devidsPlan.stream()
                .filter(value -> !devidsRecord.contains(value))
                .collect(Collectors.toList());
        return R.ok(differentValues);
    }

    /**
     * @return List<Map < String>>
     * @Description 过滤出指定时间内即将到期的保养计划 （填入提前几天提醒）
     * <AUTHOR>
     * @param[1] planList
     * @time 2023/9/7 15:17
     */
    private List<Map<String, String>> filterPlanList(List<Map<String, String>> planList) {
        List<Map<String, String>> filteredList = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();

        for (Map<String, String> plan : planList) {
            String planDateStr = plan.get("plandate");
            // 提前几天进行检查提醒
            int cycleNoticeDay = Integer.parseInt(plan.get("cyclenotice"));
            // 若今天是6.10，days=3，拿不到13号的计划，所以要+1
            LocalDate maxDate = currentDate.plusDays(cycleNoticeDay + 1);
            LocalDate planDate = LocalDate.parse(planDateStr);
            //currentDate减一天,因为今天也要提醒
            if (planDate.isAfter(currentDate.minusDays(1)) && planDate.isBefore(maxDate)) {
                filteredList.add(plan);
            }
        }
        return filteredList;
    }
}
