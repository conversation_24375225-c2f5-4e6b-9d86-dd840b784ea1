package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.IotAlarmcommentPojo;
import inks.service.std.eam.service.IotAlarmcommentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 告警评论表(Iot_AlarmComment)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-24 15:27:08
 */
@RestController
@RequestMapping("D09M36B2")
@Api(tags = "D09M36B2:告警评论")
public class D09M36B2Controller extends IotAlarmcommentController{
    @Resource
    private IotAlarmcommentService iotAlarmcommentService;
    @Resource
    private TokenService tokenService;
    @ApiOperation(value="根据警报ID查询评论列表", notes="根据警报ID查询评论列表", produces="application/json")
    @RequestMapping(value="/getCommentsByAlarmId",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_AlarmComment.List")
    public R<List<IotAlarmcommentPojo>> getCommentsByAlarmId(@RequestParam String alarmId) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.iotAlarmcommentService.getCommentsByAlarmId(alarmId, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value="添加用户评论", notes="添加用户评论", produces="application/json")
    @RequestMapping(value="/addUserComment",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_AlarmComment.Add")
    public R<IotAlarmcommentPojo> addUserComment(@RequestParam String alarmId,
                                                 @RequestParam String commentText) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.iotAlarmcommentService.addUserComment(alarmId, loginUser.getUserid(),
                    loginUser.getRealName(), commentText, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
