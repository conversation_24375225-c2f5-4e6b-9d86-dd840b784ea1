package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotKeydictionaryPojo;
import com.github.pagehelper.PageInfo;

/**
 * 键值映射字典表(Iot_KeyDictionary)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:32
 */
public interface IotKeydictionaryService {

    IotKeydictionaryPojo getEntity(String key,String tid);

    PageInfo<IotKeydictionaryPojo> getPageList(QueryParam queryParam);

    IotKeydictionaryPojo insert(IotKeydictionaryPojo iotKeydictionaryPojo);

    IotKeydictionaryPojo update(IotKeydictionaryPojo iotKeydictionarypojo);

    int delete(String key,String tid);
}
