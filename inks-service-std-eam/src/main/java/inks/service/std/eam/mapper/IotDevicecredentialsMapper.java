package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotDevicecredentialsPojo;
import inks.service.std.eam.domain.IotDevicecredentialsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 设备鉴权信息表(Iot_DeviceCredentials)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:17
 */
@Mapper
public interface IotDevicecredentialsMapper {

    IotDevicecredentialsPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotDevicecredentialsPojo> getPageList(QueryParam queryParam);

    int insert(IotDevicecredentialsEntity iotDevicecredentialsEntity);

    int update(IotDevicecredentialsEntity iotDevicecredentialsEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

