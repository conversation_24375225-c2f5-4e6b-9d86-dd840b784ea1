package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotAssetprofilePojo;
import com.github.pagehelper.PageInfo;

/**
 * 资产配置(Iot_AssetProfile)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-14 13:01:05
 */
public interface IotAssetprofileService {

    IotAssetprofilePojo getEntity(String key,String tid);

    PageInfo<IotAssetprofilePojo> getPageList(QueryParam queryParam);

    IotAssetprofilePojo insert(IotAssetprofilePojo iotAssetprofilePojo);

    IotAssetprofilePojo update(IotAssetprofilePojo iotAssetprofilepojo);

    int delete(String key,String tid);
}
