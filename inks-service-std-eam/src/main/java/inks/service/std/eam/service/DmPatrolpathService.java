package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmPatrolpathPojo;
import inks.service.std.eam.domain.pojo.DmPatrolpathitemdetailPojo;

/**
 * 巡检路线(DmPatrolpath)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 12:57:53
 */
public interface DmPatrolpathService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolpathPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolpathitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolpathPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolpathPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolpathPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmPatrolpathPojo 实例对象
     * @return 实例对象
     */
    DmPatrolpathPojo insert(DmPatrolpathPojo dmPatrolpathPojo);

    /**
     * 修改数据
     *
     * @param dmPatrolpathpojo 实例对象
     * @return 实例对象
     */
    DmPatrolpathPojo update(DmPatrolpathPojo dmPatrolpathpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param dmPatrolpathPojo 实例对象
     * @return 实例对象
     */
    DmPatrolpathPojo approval(DmPatrolpathPojo dmPatrolpathPojo);
}
