package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmMoldPojo;
import inks.service.std.eam.domain.DmMoldEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 模具管理(Dm_Mold)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-13 14:12:19
 */
@Mapper
public interface DmMoldMapper {

    DmMoldPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmMoldPojo> getPageList(QueryParam queryParam);

    int insert(DmMoldEntity dmMoldEntity);

    int update(DmMoldEntity dmMoldEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

