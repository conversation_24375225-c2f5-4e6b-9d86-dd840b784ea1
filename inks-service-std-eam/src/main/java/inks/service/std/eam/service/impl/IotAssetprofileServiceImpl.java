package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.IotAssetprofileEntity;
import inks.service.std.eam.domain.pojo.IotAssetprofilePojo;
import inks.service.std.eam.mapper.IotAssetprofileMapper;
import inks.service.std.eam.service.IotAssetprofileService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 资产配置(IotAssetprofile)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-14 13:01:05
 */
@Service("iotAssetprofileService")
public class IotAssetprofileServiceImpl implements IotAssetprofileService {
    @Resource
    private IotAssetprofileMapper iotAssetprofileMapper;

    @Override
    public IotAssetprofilePojo getEntity(String key, String tid) {
        return this.iotAssetprofileMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotAssetprofilePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotAssetprofilePojo> lst = iotAssetprofileMapper.getPageList(queryParam);
            PageInfo<IotAssetprofilePojo> pageInfo = new PageInfo<IotAssetprofilePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotAssetprofilePojo insert(IotAssetprofilePojo iotAssetprofilePojo) {
        //初始化NULL字段
        cleanNull(iotAssetprofilePojo);
        IotAssetprofileEntity iotAssetprofileEntity = new IotAssetprofileEntity(); 
        BeanUtils.copyProperties(iotAssetprofilePojo,iotAssetprofileEntity);
          //生成雪花id
          iotAssetprofileEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotAssetprofileEntity.setRevision(1);  //乐观锁
          this.iotAssetprofileMapper.insert(iotAssetprofileEntity);
        return this.getEntity(iotAssetprofileEntity.getId(),iotAssetprofileEntity.getTenantid());
    }


    @Override
    public IotAssetprofilePojo update(IotAssetprofilePojo iotAssetprofilePojo) {
        IotAssetprofileEntity iotAssetprofileEntity = new IotAssetprofileEntity(); 
        BeanUtils.copyProperties(iotAssetprofilePojo,iotAssetprofileEntity);
        this.iotAssetprofileMapper.update(iotAssetprofileEntity);
        return this.getEntity(iotAssetprofileEntity.getId(),iotAssetprofileEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotAssetprofileMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotAssetprofilePojo iotAssetprofilePojo) {
        if(iotAssetprofilePojo.getProfname()==null) iotAssetprofilePojo.setProfname("");
        if(iotAssetprofilePojo.getImage()==null) iotAssetprofilePojo.setImage("");
        if(iotAssetprofilePojo.getDescription()==null) iotAssetprofilePojo.setDescription("");
        if(iotAssetprofilePojo.getIsdefault()==null) iotAssetprofilePojo.setIsdefault(0);
        if(iotAssetprofilePojo.getDefaultrulechainid()==null) iotAssetprofilePojo.setDefaultrulechainid("");
        if(iotAssetprofilePojo.getDefaultdashboardid()==null) iotAssetprofilePojo.setDefaultdashboardid("");
        if(iotAssetprofilePojo.getDefaultqueuename()==null) iotAssetprofilePojo.setDefaultqueuename("");
        if(iotAssetprofilePojo.getDefaultedgerulechainid()==null) iotAssetprofilePojo.setDefaultedgerulechainid("");
        if(iotAssetprofilePojo.getExternalid()==null) iotAssetprofilePojo.setExternalid("");
        if(iotAssetprofilePojo.getRemark()==null) iotAssetprofilePojo.setRemark("");
        if(iotAssetprofilePojo.getRownum()==null) iotAssetprofilePojo.setRownum(0);
        if(iotAssetprofilePojo.getCreateby()==null) iotAssetprofilePojo.setCreateby("");
        if(iotAssetprofilePojo.getCreatebyid()==null) iotAssetprofilePojo.setCreatebyid("");
        if(iotAssetprofilePojo.getCreatedate()==null) iotAssetprofilePojo.setCreatedate(new Date());
        if(iotAssetprofilePojo.getLister()==null) iotAssetprofilePojo.setLister("");
        if(iotAssetprofilePojo.getListerid()==null) iotAssetprofilePojo.setListerid("");
        if(iotAssetprofilePojo.getModifydate()==null) iotAssetprofilePojo.setModifydate(new Date());
        if(iotAssetprofilePojo.getCustom1()==null) iotAssetprofilePojo.setCustom1("");
        if(iotAssetprofilePojo.getCustom2()==null) iotAssetprofilePojo.setCustom2("");
        if(iotAssetprofilePojo.getCustom3()==null) iotAssetprofilePojo.setCustom3("");
        if(iotAssetprofilePojo.getCustom4()==null) iotAssetprofilePojo.setCustom4("");
        if(iotAssetprofilePojo.getCustom5()==null) iotAssetprofilePojo.setCustom5("");
        if(iotAssetprofilePojo.getCustom6()==null) iotAssetprofilePojo.setCustom6("");
        if(iotAssetprofilePojo.getCustom7()==null) iotAssetprofilePojo.setCustom7("");
        if(iotAssetprofilePojo.getCustom8()==null) iotAssetprofilePojo.setCustom8("");
        if(iotAssetprofilePojo.getCustom9()==null) iotAssetprofilePojo.setCustom9("");
        if(iotAssetprofilePojo.getCustom10()==null) iotAssetprofilePojo.setCustom10("");
        if(iotAssetprofilePojo.getTenantid()==null) iotAssetprofilePojo.setTenantid("");
        if(iotAssetprofilePojo.getTenantname()==null) iotAssetprofilePojo.setTenantname("");
        if(iotAssetprofilePojo.getRevision()==null) iotAssetprofilePojo.setRevision(0);
   }

}
