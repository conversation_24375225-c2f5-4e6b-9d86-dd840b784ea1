package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmSpareinventoryEntity;
import inks.service.std.eam.domain.pojo.DmSpareinventoryPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备件库存(DmSpareinventory)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 10:10:17
 */
@Mapper
public interface DmSpareinventoryMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmSpareinventoryPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmSpareinventoryPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param dmSpareinventoryEntity 实例对象
     * @return 影响行数
     */
    int insert(DmSpareinventoryEntity dmSpareinventoryEntity);


    /**
     * 修改数据
     *
     * @param dmSpareinventoryEntity 实例对象
     * @return 影响行数
     */
    int update(DmSpareinventoryEntity dmSpareinventoryEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    DmSpareinventoryPojo getEntityBySpareid(@Param("spareid") String spareid, @Param("location") String location, @Param("batchno") String batchno, @Param("tid") String tid);
}

