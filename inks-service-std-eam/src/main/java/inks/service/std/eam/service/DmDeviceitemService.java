package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmDeviceitemPojo;

import java.util.List;

/**
 * 设备参数(DmDeviceitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-06 12:59:33
 */
public interface DmDeviceitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmDeviceitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmDeviceitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmDeviceitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param dmDeviceitemPojo 实例对象
     * @return 实例对象
     */
    DmDeviceitemPojo insert(DmDeviceitemPojo dmDeviceitemPojo);

    /**
     * 修改数据
     *
     * @param dmDeviceitempojo 实例对象
     * @return 实例对象
     */
    DmDeviceitemPojo update(DmDeviceitemPojo dmDeviceitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param dmDeviceitempojo 实例对象
     * @return 实例对象
     */
    DmDeviceitemPojo clearNull(DmDeviceitemPojo dmDeviceitempojo);
}
