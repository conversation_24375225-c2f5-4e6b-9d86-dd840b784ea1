package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.IotDeviceEntity;
import inks.service.std.eam.domain.pojo.IotDevicePojo;
import inks.service.std.eam.domain.vo.DeviceTokenVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备基本信息表(Iot_Device)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:10
 */
@Mapper
public interface IotDeviceMapper {

    IotDevicePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotDevicePojo> getPageList(QueryParam queryParam);

    int insert(IotDeviceEntity iotDeviceEntity);

    int update(IotDeviceEntity iotDeviceEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    IotDevicePojo getEntitybySn(String sn);

    /**
     * 通过token查询设备信息
     * @param token 设备token
     * @return 设备信息，如果不存在返回null
     */
    IotDevicePojo getEntityByToken(String token);

    List<DeviceTokenVO> getAllToken(String tid);

    /**
     * 获取所有有token的设备信息（不为空的token）
     * @return 设备token信息列表
     */
    List<DeviceTokenVO> getAllDevicesWithToken();
}

