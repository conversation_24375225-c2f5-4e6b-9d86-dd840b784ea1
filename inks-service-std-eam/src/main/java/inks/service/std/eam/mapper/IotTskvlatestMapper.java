package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotTskvlatestPojo;
import inks.service.std.eam.domain.IotTskvlatestEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 最新时间序列遥测数据快照表(Iot_TsKvLatest)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:59
 */
@Mapper
public interface IotTskvlatestMapper {

    IotTskvlatestPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotTskvlatestPojo> getPageList(QueryParam queryParam);

    int insert(IotTskvlatestEntity iotTskvlatestEntity);

    int update(IotTskvlatestEntity iotTskvlatestEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    int batchLatest(@Param("list") List<IotTskvlatestPojo> list);

    List<IotTskvlatestPojo> getList(String entityid, String tid);
}

