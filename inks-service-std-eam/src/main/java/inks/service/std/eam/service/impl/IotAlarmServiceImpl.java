package inks.service.std.eam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.IotAlarmEntity;
import inks.service.std.eam.domain.pojo.IotAlarmPojo;
import inks.service.std.eam.domain.pojo.IotAlarmcommentPojo;
import inks.service.std.eam.mapper.IotAlarmMapper;
import inks.service.std.eam.service.IotAlarmService;
import inks.service.std.eam.service.IotAlarmcommentService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 告警信息记录表(IotAlarm)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-18 17:10:17
 */
@Service("iotAlarmService")
public class IotAlarmServiceImpl implements IotAlarmService {
    @Resource
    private IotAlarmMapper iotAlarmMapper;

    @Resource
    private IotAlarmcommentService iotAlarmcommentService;

    @Override
    public IotAlarmPojo getEntity(String key, String tid) {
        return this.iotAlarmMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotAlarmPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotAlarmPojo> lst = iotAlarmMapper.getPageList(queryParam);
            PageInfo<IotAlarmPojo> pageInfo = new PageInfo<IotAlarmPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotAlarmPojo insert(IotAlarmPojo iotAlarmPojo) {
        //初始化NULL字段
        cleanNull(iotAlarmPojo);
        IotAlarmEntity iotAlarmEntity = new IotAlarmEntity(); 
        BeanUtils.copyProperties(iotAlarmPojo,iotAlarmEntity);
          //生成雪花id
          iotAlarmEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotAlarmEntity.setRevision(1);  //乐观锁
          this.iotAlarmMapper.insert(iotAlarmEntity);
        return this.getEntity(iotAlarmEntity.getId(),iotAlarmEntity.getTenantid());
    }


    @Override
    public IotAlarmPojo update(IotAlarmPojo iotAlarmPojo) {
        IotAlarmEntity iotAlarmEntity = new IotAlarmEntity(); 
        BeanUtils.copyProperties(iotAlarmPojo,iotAlarmEntity);
        this.iotAlarmMapper.update(iotAlarmEntity);
        return this.getEntity(iotAlarmEntity.getId(),iotAlarmEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotAlarmMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotAlarmPojo iotAlarmPojo) {
        if(iotAlarmPojo.getAdditionalinfo()==null) iotAlarmPojo.setAdditionalinfo("");
        if(iotAlarmPojo.getOriginatorid()==null) iotAlarmPojo.setOriginatorid("");
        if(iotAlarmPojo.getOriginatortype()==null) iotAlarmPojo.setOriginatortype(0);
        if(iotAlarmPojo.getPropagate()==null) iotAlarmPojo.setPropagate(0);
        if(iotAlarmPojo.getSeverity()==null) iotAlarmPojo.setSeverity("");
        if(iotAlarmPojo.getAssigneeid()==null) iotAlarmPojo.setAssigneeid("");
        if(iotAlarmPojo.getCustomerid()==null) iotAlarmPojo.setCustomerid("");
        if(iotAlarmPojo.getPropagaterelationtypes()==null) iotAlarmPojo.setPropagaterelationtypes("");
        if(iotAlarmPojo.getType()==null) iotAlarmPojo.setType("");
        if(iotAlarmPojo.getPropagatetoowner()==null) iotAlarmPojo.setPropagatetoowner(0);
        if(iotAlarmPojo.getPropagatetotenant()==null) iotAlarmPojo.setPropagatetotenant(0);
        if(iotAlarmPojo.getAcknowledged()==null) iotAlarmPojo.setAcknowledged(0);
        if(iotAlarmPojo.getCleared()==null) iotAlarmPojo.setCleared(0);
        if(iotAlarmPojo.getRemark()==null) iotAlarmPojo.setRemark("");
        if(iotAlarmPojo.getRownum()==null) iotAlarmPojo.setRownum(0);
        if(iotAlarmPojo.getCreateby()==null) iotAlarmPojo.setCreateby("");
        if(iotAlarmPojo.getCreatebyid()==null) iotAlarmPojo.setCreatebyid("");
        if(iotAlarmPojo.getCreatedate()==null) iotAlarmPojo.setCreatedate(new Date());
        if(iotAlarmPojo.getLister()==null) iotAlarmPojo.setLister("");
        if(iotAlarmPojo.getListerid()==null) iotAlarmPojo.setListerid("");
        if(iotAlarmPojo.getModifydate()==null) iotAlarmPojo.setModifydate(new Date());
        if(iotAlarmPojo.getCustom1()==null) iotAlarmPojo.setCustom1("");
        if(iotAlarmPojo.getCustom2()==null) iotAlarmPojo.setCustom2("");
        if(iotAlarmPojo.getCustom3()==null) iotAlarmPojo.setCustom3("");
        if(iotAlarmPojo.getCustom4()==null) iotAlarmPojo.setCustom4("");
        if(iotAlarmPojo.getCustom5()==null) iotAlarmPojo.setCustom5("");
        if(iotAlarmPojo.getCustom6()==null) iotAlarmPojo.setCustom6("");
        if(iotAlarmPojo.getCustom7()==null) iotAlarmPojo.setCustom7("");
        if(iotAlarmPojo.getCustom8()==null) iotAlarmPojo.setCustom8("");
        if(iotAlarmPojo.getCustom9()==null) iotAlarmPojo.setCustom9("");
        if(iotAlarmPojo.getCustom10()==null) iotAlarmPojo.setCustom10("");
        if(iotAlarmPojo.getTenantid()==null) iotAlarmPojo.setTenantid("");
        if(iotAlarmPojo.getTenantname()==null) iotAlarmPojo.setTenantname("");
        if(iotAlarmPojo.getRevision()==null) iotAlarmPojo.setRevision(0);
   }

    @Override
    public IotAlarmPojo acknowledgeAlarm(String alarmId, String userId, String userName, String tenantId) {
        // 获取当前警报信息
        IotAlarmPojo alarm = this.getEntity(alarmId, tenantId);
        if (alarm == null) {
            throw new BaseBusinessException("警报不存在");
        }

        // 检查警报是否已经确认
        if (alarm.getAcknowledged() != null && alarm.getAcknowledged() == 1) {
            throw new BaseBusinessException("警报已经确认");
        }

        // 更新警报状态
        long currentTime = System.currentTimeMillis();
        alarm.setAcknowledged(1);
        alarm.setAckts(currentTime);
        alarm.setModifydate(new Date());

        // 保存更新
        IotAlarmPojo updatedAlarm = this.update(alarm);

        // 创建系统评论记录确认操作
        createSystemComment(alarmId, userId, userName, tenantId, "ACK",
            "Alarm was acknowledged by user " + userName);

        return updatedAlarm;
    }

    @Override
    public IotAlarmPojo clearAlarm(String alarmId, String userId, String userName, String tenantId) {
        // 获取当前警报信息
        IotAlarmPojo alarm = this.getEntity(alarmId, tenantId);
        if (alarm == null) {
            throw new BaseBusinessException("警报不存在");
        }

        // 检查警报是否已经清除
        if (alarm.getCleared() != null && alarm.getCleared() == 1) {
            throw new BaseBusinessException("警报已经清除");
        }

        // 更新警报状态
        long currentTime = System.currentTimeMillis();
        alarm.setCleared(1);
        alarm.setClearts(currentTime);
        alarm.setEndts(currentTime);
        alarm.setModifydate(new Date());

        // 保存更新
        IotAlarmPojo updatedAlarm = this.update(alarm);

        // 创建系统评论记录清除操作
        createSystemComment(alarmId, userId, userName, tenantId, "CLEAR",
            "Alarm was cleared by user " + userName);

        return updatedAlarm;
    }

    @Override
    public IotAlarmPojo assignAlarm(String alarmId, String assigneeId, String assigneeName,
                                   String operatorId, String operatorName, String tenantId) {
        // 获取当前警报信息
        IotAlarmPojo alarm = this.getEntity(alarmId, tenantId);
        if (alarm == null) {
            throw new BaseBusinessException("警报不存在");
        }

        // 更新警报分配信息
        long currentTime = System.currentTimeMillis();
        alarm.setAssigneeid(assigneeId);
        alarm.setAssignts(currentTime);
        alarm.setModifydate(new Date());

        // 保存更新
        IotAlarmPojo updatedAlarm = this.update(alarm);

        // 创建系统评论记录分配操作
        JSONObject commentJson = new JSONObject();
        commentJson.put("text", "Alarm was assigned by user " + operatorName + " to user " + assigneeName);
        commentJson.put("subtype", "ASSIGN");
        commentJson.put("userId", operatorId);
        commentJson.put("assigneeId", assigneeId);

        createSystemCommentWithJson(alarmId, operatorId, operatorName, tenantId, commentJson.toJSONString());

        return updatedAlarm;
    }

    @Override
    public IotAlarmPojo unacknowledgeAlarm(String alarmId, String userId, String userName, String tenantId) {
        // 获取当前警报信息
        IotAlarmPojo alarm = this.getEntity(alarmId, tenantId);
        if (alarm == null) {
            throw new BaseBusinessException("警报不存在");
        }

        // 检查警报是否已确认
        if (alarm.getAcknowledged() == null || alarm.getAcknowledged() == 0) {
            throw new BaseBusinessException("警报未确认，无法取消确认");
        }

        // 更新警报状态
        alarm.setAcknowledged(0);
        alarm.setAckts(0L);
        alarm.setModifydate(new Date());

        // 保存更新
        IotAlarmPojo updatedAlarm = this.update(alarm);

        // 创建系统评论记录取消确认操作
        createSystemComment(alarmId, userId, userName, tenantId, "UNACK",
            "Alarm was unacknowledged by user " + userName);

        return updatedAlarm;
    }

    /**
     * 创建系统评论
     */
    private void createSystemComment(String alarmId, String userId, String userName, String tenantId,
                                   String subtype, String text) {
        JSONObject commentJson = new JSONObject();
        commentJson.put("text", text);
        commentJson.put("subtype", subtype);
        commentJson.put("userId", userId);

        createSystemCommentWithJson(alarmId, userId, userName, tenantId, commentJson.toJSONString());
    }

    /**
     * 创建系统评论（使用JSON内容）
     */
    private void createSystemCommentWithJson(String alarmId, String userId, String userName,
                                           String tenantId, String commentJson) {
        IotAlarmcommentPojo comment = new IotAlarmcommentPojo();
        comment.setAlarmid(alarmId);
        comment.setUserid(userId);
        comment.setType("0"); // 系统评论
        comment.setComment(commentJson);
        comment.setCreateby(userName);
        comment.setCreatebyid(userId);
        comment.setCreatedate(new Date());
        comment.setLister(userName);
        comment.setListerid(userId);
        comment.setModifydate(new Date());
        comment.setTenantid(tenantId);

        iotAlarmcommentService.insert(comment);
    }

}
