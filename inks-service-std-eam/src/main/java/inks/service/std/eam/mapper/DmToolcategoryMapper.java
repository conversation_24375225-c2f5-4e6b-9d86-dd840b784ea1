package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolcategoryPojo;
import inks.service.std.eam.domain.DmToolcategoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具类别表(Dm_ToolCategory)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-09 14:05:55
 */
@Mapper
public interface DmToolcategoryMapper {

    DmToolcategoryPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolcategoryPojo> getPageList(QueryParam queryParam);

    int insert(DmToolcategoryEntity dmToolcategoryEntity);

    int update(DmToolcategoryEntity dmToolcategoryEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

