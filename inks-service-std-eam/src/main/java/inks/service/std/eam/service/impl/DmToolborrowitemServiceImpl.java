package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmToolborrowitemEntity;
import inks.service.std.eam.domain.pojo.DmToolborrowitemPojo;
import inks.service.std.eam.mapper.DmToolborrowitemMapper;
import inks.service.std.eam.service.DmToolborrowitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 工装具借还子表-工装具(DmToolborrowitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-13 09:52:08
 */
@Service("dmToolborrowitemService")
public class DmToolborrowitemServiceImpl implements DmToolborrowitemService {
    @Resource
    private DmToolborrowitemMapper dmToolborrowitemMapper;

    @Override
    public DmToolborrowitemPojo getEntity(String key,String tid) {
        return this.dmToolborrowitemMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<DmToolborrowitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolborrowitemPojo> lst = dmToolborrowitemMapper.getPageList(queryParam);
            PageInfo<DmToolborrowitemPojo> pageInfo = new PageInfo<DmToolborrowitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<DmToolborrowitemPojo> getList(String Pid,String tid) { 
        try {
            List<DmToolborrowitemPojo> lst = dmToolborrowitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public DmToolborrowitemPojo insert(DmToolborrowitemPojo dmToolborrowitemPojo) {
        //初始化item的NULL
        DmToolborrowitemPojo itempojo =this.clearNull(dmToolborrowitemPojo);
        DmToolborrowitemEntity dmToolborrowitemEntity = new DmToolborrowitemEntity(); 
        BeanUtils.copyProperties(itempojo,dmToolborrowitemEntity);
          //生成雪花id
          dmToolborrowitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          dmToolborrowitemEntity.setRevision(1);  //乐观锁      
          this.dmToolborrowitemMapper.insert(dmToolborrowitemEntity);
        return this.getEntity(dmToolborrowitemEntity.getId(),dmToolborrowitemEntity.getTenantid());
  
    }

    @Override
    public DmToolborrowitemPojo update(DmToolborrowitemPojo dmToolborrowitemPojo) {
        DmToolborrowitemEntity dmToolborrowitemEntity = new DmToolborrowitemEntity(); 
        BeanUtils.copyProperties(dmToolborrowitemPojo,dmToolborrowitemEntity);
        this.dmToolborrowitemMapper.update(dmToolborrowitemEntity);
        return this.getEntity(dmToolborrowitemEntity.getId(),dmToolborrowitemEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.dmToolborrowitemMapper.delete(key,tid) ;
    }

     @Override
     public DmToolborrowitemPojo clearNull(DmToolborrowitemPojo dmToolborrowitemPojo){
     //初始化NULL字段
     if(dmToolborrowitemPojo.getPid()==null) dmToolborrowitemPojo.setPid("");
     if(dmToolborrowitemPojo.getToolid()==null) dmToolborrowitemPojo.setToolid("");
     if(dmToolborrowitemPojo.getBorrowqty()==null) dmToolborrowitemPojo.setBorrowqty(0);
     if(dmToolborrowitemPojo.getReturnedqty()==null) dmToolborrowitemPojo.setReturnedqty(0);
     if(dmToolborrowitemPojo.getStatus()==null) dmToolborrowitemPojo.setStatus(0);
     if(dmToolborrowitemPojo.getRownum()==null) dmToolborrowitemPojo.setRownum(0);
     if(dmToolborrowitemPojo.getRemark()==null) dmToolborrowitemPojo.setRemark("");
     if(dmToolborrowitemPojo.getCustom1()==null) dmToolborrowitemPojo.setCustom1("");
     if(dmToolborrowitemPojo.getCustom2()==null) dmToolborrowitemPojo.setCustom2("");
     if(dmToolborrowitemPojo.getCustom3()==null) dmToolborrowitemPojo.setCustom3("");
     if(dmToolborrowitemPojo.getCustom4()==null) dmToolborrowitemPojo.setCustom4("");
     if(dmToolborrowitemPojo.getCustom5()==null) dmToolborrowitemPojo.setCustom5("");
     if(dmToolborrowitemPojo.getCustom6()==null) dmToolborrowitemPojo.setCustom6("");
     if(dmToolborrowitemPojo.getCustom7()==null) dmToolborrowitemPojo.setCustom7("");
     if(dmToolborrowitemPojo.getCustom8()==null) dmToolborrowitemPojo.setCustom8("");
     if(dmToolborrowitemPojo.getCustom9()==null) dmToolborrowitemPojo.setCustom9("");
     if(dmToolborrowitemPojo.getCustom10()==null) dmToolborrowitemPojo.setCustom10("");
     if(dmToolborrowitemPojo.getTenantid()==null) dmToolborrowitemPojo.setTenantid("");
     if(dmToolborrowitemPojo.getRevision()==null) dmToolborrowitemPojo.setRevision(0);
     return dmToolborrowitemPojo;
     }
}
