package inks.service.std.eam.controller.schedule;

import inks.common.core.domain.LoginUser;
import inks.common.security.service.TokenService;
import inks.service.std.eam.controller.D09M04B2Controller;
import inks.service.std.eam.service.DmUpkeepplanService;
import inks.service.std.eam.service.DmUpkeeprecordService;
import inks.service.std.eam.utils.PrintColor;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;


@Controller
public class PlanReminder implements CommandLineRunner {
    // 提前几天提醒
    public static final int DAYS = 5;
    @Resource
    private Scheduler scheduler;
    @Resource
    private D09M04B2Controller d09M04B2Controller;
    @Resource
    private DmUpkeeprecordService dmUpkeeprecordService;
    @Resource
    private DmUpkeepplanService dmUpkeepplanService;
    @Resource
    private TokenService tokenService;
    @Autowired
    private ApplicationContext applicationContext;

    /**
     * @Description 打印所有触发器和任务标识
     * <AUTHOR>
     * @param[1] scheduler
     * @time 2023/6/9 11:11
     */
    private static void printAllTheTriggersAndTaskIdentity(Scheduler scheduler) throws SchedulerException {
        // 获取调度器中的任务标识列表
        Set<JobKey> jobKeys = scheduler.getJobKeys(GroupMatcher.anyGroup());
        List<String> jobIdentifiers = new ArrayList<>();
        for (JobKey jobKey : jobKeys) {
            jobIdentifiers.add(jobKey.getName());
        }
        PrintColor.color("zi", "所有任务标识列表: " + jobIdentifiers);
        // 获取调度器中的所有触发器标识
        Set<TriggerKey> triggerKeys = scheduler.getTriggerKeys(GroupMatcher.anyTriggerGroup());
        List<String> triggerIdentifiers = new ArrayList<>();
        for (TriggerKey triggerKey : triggerKeys) {
            triggerIdentifiers.add(triggerKey.getName());
        }
        PrintColor.color("lv", "所有触发器标识列表: " + triggerIdentifiers);
    }

    /**
     * @Description 实现CommandLineRunner接口, 项目启动时自动执行run方法, 创建定时任务发送保养提醒
     * <AUTHOR> TODO Springboot启动自动执行此方法，但是拿不到token
     * @param[1] args
     * @time 2023/6/9 14:09
     */
    @Override
    public void run(String... args) throws Exception {
//        // 自定义的授权信息
//        String authorization = "bcdb";  // 替换为您自己的授权信息
//        // 将授权信息存储在适当的位置，以供后续接口调用获取
//        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//        HttpServletRequest request = requestAttributes.getRequest();
//        // 将自定义的授权信息设置到请求头中
//        request.setAttribute("Authorization", authorization);
//        d09M04B2Controller.getALLPlan();
    }

    /**
     * @Description 拿到所有保养计划过滤到提前几天提醒, 再过滤到只保留未提前完成的保养计划，再创建定时任务发送保养提醒
     * <AUTHOR>
     * @param[1] planList 所有保养计划
     * @time 2023/6/9 11:14
     */
    public void scheduleReminders2(List<Map<String, String>> planList) throws SchedulerException {
        // TODO 通过tid加分布式锁？
        LoginUser loginUser = tokenService.getLoginUser();
        // 重启调度器,防止分布式重复创建定时任务
        restartScheduler();
        // 过滤时间即将到期的保养计划(提前3天提醒)
        List<Map<String, String>> filterPlanList = filterPlanList(planList, DAYS);
        // 只保留未提前完成的保养计划
        filterPlanList = checkPlan(planList, filterPlanList, loginUser.getTenantid());
        for (Map<String, String> plan : filterPlanList) {
            String planDate = plan.get("plandate");
            String planId = plan.get("planid");
            // 创建触发器
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity("ReminderTrigger-" + planId + "-" + UUID.randomUUID(), "ReminderGroup")
                    .startNow() // 立即开始触发器
                    .withSchedule(SimpleScheduleBuilder.repeatSecondlyForever(10)) // 每10秒执行一次
                    .build();
            // 创建任务
            JobDetail job = JobBuilder.newJob(ReminderJob.class)
                    .withIdentity("ReminderJob-" + planId + "-" + UUID.randomUUID(), "ReminderGroup")
                    .usingJobData("planId", planId) // 传递保养计划的ID到任务中
                    .usingJobData("planDate", planDate) // 将planDate传递给定时任务的执行逻辑
                    .usingJobData("day", DAYS)
                    .build();
            job.getJobDataMap().put("filterPlanList", filterPlanList);
            // 打印所有的触发器和任务
//            printAllTheTriggersAndTaskIdentity(scheduler);
            // 将任务和触发器加入调度器
            scheduler.scheduleJob(job, trigger);
        }
    }

    public void scheduleReminders(int day) throws SchedulerException {
        // TODO 通过tid加分布式锁？
        String tid = tokenService.getLoginUser().getTenantid();
        // 重启调度器,防止分布式重复创建定时任务
        restartScheduler();
        // 创建触发器
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("ReminderTrigger-" + UUID.randomUUID(), "ReminderGroup")
                .startNow() // 立即开始触发器
                .withSchedule(SimpleScheduleBuilder.repeatSecondlyForever(10)) // 每10秒执行一次
                .build();
        // 创建任务
        JobDetail job = JobBuilder.newJob(ReminderJob.class)
                .withIdentity("ReminderJob-" + UUID.randomUUID(), "ReminderGroup")
                .usingJobData("day", day)    // 过滤时间即将到期的保养计划(提前3天提醒)
                .usingJobData("tid", tid)     // 传递tid到任务中
                .build();
//        job.getJobDataMap().put("planList", planList);
        // 打印所有的触发器和任务
        printAllTheTriggersAndTaskIdentity(scheduler);
        // 将任务和触发器加入调度器
        scheduler.scheduleJob(job, trigger);
    }

    /**
     * @return List<Map < String>>
     * @Description 去掉保养实施单finishCount完成的保养计划, 只保留未提前完成的保养计划
     * <AUTHOR>
     * @param[1] filterPlanList
     * @param[2] tid
     * @time 2023/6/9 15:40
     */
    public List<Map<String, String>> checkPlan(List<Map<String, String>> planList, List<Map<String, String>> filterPlanList, String tid) {
        List<Map<String, String>> list = new ArrayList<>();
        for (Map<String, String> plan : filterPlanList) {
            String planId = plan.get("planid");
            String planDate = plan.get("plandate");
            // 获取保养计划itemcount
            int itemCount = dmUpkeepplanService.getEntity(planId, tid).getItemcount();
            // 获取保养实施单完成的finishcount(累加)
            int finishCount = dmUpkeeprecordService.getSumFinishCount(planId, planDate, tid);
            // 如果finishCount<itemCount,说明保养实施单还没有完成,需要提醒;否则不需要提醒
            if (finishCount < itemCount) {
                list.add(plan);
            }
        }
        return list;
    }

    // 启动调度器
    public void startScheduler() throws SchedulerException {
        if (scheduler == null || scheduler.isShutdown()) {
            SchedulerFactory schedulerFactory = new StdSchedulerFactory();
            scheduler = schedulerFactory.getScheduler();
        }
        scheduler.start();
    }

    // 停止调度器
    public void stopScheduler() throws SchedulerException {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown(true);
        }
    }

    // 重启调度器
    public void restartScheduler() throws SchedulerException {
        stopScheduler();
        startScheduler();
    }

    //    /**
//     * @return List<Map < String>>
//     * @Description 过滤出指定时间内即将到期的保养计划 （这里暂时过滤到一周内的保养计划）
//     * <AUTHOR>
//     * @param[1] planList
//     * @time 2023/6/9 10:12
//     */
//    public List<Map<String, String>> filterPlanList(List<Map<String, String>> planList) {
//        List<Map<String, String>> filteredList = new ArrayList<>();
//        LocalDate currentDate = LocalDate.now();
//        LocalDate maxDate = currentDate.plusWeeks(1);
//        for (Map<String, String> plan : planList) {
//            String planDateStr = plan.get("plandate");
//            LocalDate planDate = LocalDate.parse(planDateStr);
//
//            if (planDate.isAfter(currentDate) && planDate.isBefore(maxDate)) {
//                filteredList.add(plan);
//            }
//        }
//        return filteredList;
//    }

    /**
     * @return List<Map < String>>
     * @Description 过滤出指定时间内即将到期的保养计划 （填入提前几天提醒）
     * <AUTHOR>
     * @param[1] planList
     * @param[2] days
     * @time 2023/6/9 10:47
     */
    public List<Map<String, String>> filterPlanList(List<Map<String, String>> planList, int days) {
        List<Map<String, String>> filteredList = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();
        // 若今天是6.10，days=3，拿不到13号的计划，所以要+1
        LocalDate maxDate = currentDate.plusDays(days + 1);
        for (Map<String, String> plan : planList) {
            String planDateStr = plan.get("plandate");
            LocalDate planDate = LocalDate.parse(planDateStr);
            //currentDate减一天,因为今天也要提醒
            if (planDate.isAfter(currentDate.minusDays(1)) && planDate.isBefore(maxDate)) {
                filteredList.add(plan);
            }
        }
        return filteredList;
    }


}
