package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmUpkeepplanPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepplanitemdetailPojo;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 保养计划(DmUpkeepplan)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-07 17:16:52
 */
public interface DmUpkeepplanService {


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeepplanPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeepplanitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeepplanPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeepplanPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeepplanPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmUpkeepplanPojo 实例对象
     * @return 实例对象
     */
    DmUpkeepplanPojo insert(DmUpkeepplanPojo dmUpkeepplanPojo);

    /**
     * 修改数据
     *
     * @param dmUpkeepplanpojo 实例对象
     * @return 实例对象
     */
    DmUpkeepplanPojo update(DmUpkeepplanPojo dmUpkeepplanpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param dmUpkeepplanPojo 实例对象
     * @return 实例对象
     */
    DmUpkeepplanPojo approval(DmUpkeepplanPojo dmUpkeepplanPojo);

    List<DmUpkeepplanPojo> getAllList(String tid);

    // 不传日期则默认获取所有的保养计划,传入日期范围则获取该日期范围的保养计划
    List<Map<String, String>> getALLPlan(String tenantid, LocalDate localDateStart, LocalDate localDateEnd);

    List<Map<String, String>> getALLPlanToday(String tenantid);

    List<Map<String, String>> getALLPlanByMonth(Integer year, Integer month, String tenantid);

    List<String> getAllDevId(String planid, String tid);
}
