package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotRulechainPojo;
import inks.service.std.eam.domain.IotRulechainEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 规则链定义表(Iot_RuleChain)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:38
 */
@Mapper
public interface IotRulechainMapper {

    IotRulechainPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotRulechainPojo> getPageList(QueryParam queryParam);

    int insert(IotRulechainEntity iotRulechainEntity);

    int update(IotRulechainEntity iotRulechainEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    IotRulechainPojo getEntityByDeviceProfile(String deviceprofileid);

    int setRoot(String key, String tid);
}

