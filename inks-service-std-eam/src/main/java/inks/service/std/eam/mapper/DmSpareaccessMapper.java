package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmSpareaccessEntity;
import inks.service.std.eam.domain.pojo.DmSpareaccessPojo;
import inks.service.std.eam.domain.pojo.DmSpareaccessitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备件出入(DmSpareaccess)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 10:09:25
 */
@Mapper
public interface DmSpareaccessMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmSpareaccessPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmSpareaccessitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmSpareaccessPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmSpareaccessEntity 实例对象
     * @return 影响行数
     */
    int insert(DmSpareaccessEntity dmSpareaccessEntity);


    /**
     * 修改数据
     *
     * @param dmSpareaccessEntity 实例对象
     * @return 影响行数
     */
    int update(DmSpareaccessEntity dmSpareaccessEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param dmSpareaccessPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(DmSpareaccessPojo dmSpareaccessPojo);

    /**
     * 修改数据
     *
     * @param dmSpareaccessEntity 实例对象
     * @return 影响行数
     */
    int approval(DmSpareaccessEntity dmSpareaccessEntity);
}

