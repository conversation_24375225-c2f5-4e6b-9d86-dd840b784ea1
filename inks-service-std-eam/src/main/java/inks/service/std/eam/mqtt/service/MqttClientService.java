package inks.service.std.eam.mqtt.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * MQTT客户端业务服务
 * 提供客户端和订阅相关的业务逻辑，避免HTTP调用
 * 
 * <AUTHOR>
 */
@Service
public class MqttClientService {

    private static final Logger logger = LoggerFactory.getLogger(MqttClientService.class);

    @Autowired
    private MqttStatsService mqttStatsService;

    /**
     * 获取客户端列表（分页）
     * 
     * @param page 页码
     * @param limit 每页数量
     * @return 分页的客户端列表数据
     */
    public Map<String, Object> getClientsList(int page, int limit) {
        try {
            Map<String, Object> result = new HashMap<>();
            Map<String, Object> data = new HashMap<>();

            // 获取真实客户端数据
            Map<String, MqttStatsService.ClientInfo> connectedClients = mqttStatsService.getConnectedClients();
            List<Map<String, Object>> clients = new ArrayList<>();

            for (MqttStatsService.ClientInfo clientInfo : connectedClients.values()) {
                Map<String, Object> client = new HashMap<>();
                client.put("clientId", clientInfo.clientId);
                client.put("username", clientInfo.username != null ? clientInfo.username : "");
                client.put("connected", clientInfo.connected);
                client.put("protoName", clientInfo.protoName);
                client.put("protoVer", clientInfo.protoVer);
                client.put("ipAddress", clientInfo.ipAddress);
                client.put("port", clientInfo.getPort());
                client.put("connectedAt", clientInfo.connectedAt);
                client.put("createdAt", clientInfo.connectedAt);
                clients.add(client);
            }

            // 简单分页处理
            int totalRow = clients.size();
            int startIndex = (page - 1) * limit;
            int endIndex = Math.min(startIndex + limit, totalRow);

            List<Map<String, Object>> pagedClients = new ArrayList<>();
            if (startIndex < totalRow) {
                pagedClients = clients.subList(startIndex, endIndex);
            }

            data.put("list", pagedClients);
            data.put("pageNumber", page);
            data.put("pageSize", limit);
            data.put("totalRow", totalRow);

            result.put("code", 1);
            result.put("data", data);

            logger.info("成功获取客户端列表，共 {} 个客户端", totalRow);
            return result;
        } catch (Exception e) {
            logger.error("获取客户端列表失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", 105);
            errorResult.put("message", "获取客户端列表失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 获取客户端详情
     * 
     * @param clientId 客户端ID
     * @return 客户端详情数据
     */
    public Map<String, Object> getClientInfo(String clientId) {
        Map<String, Object> result = new HashMap<>();

        // 获取真实客户端详情数据
        Map<String, MqttStatsService.ClientInfo> connectedClients = mqttStatsService.getConnectedClients();
        MqttStatsService.ClientInfo clientInfo = connectedClients.get(clientId);

        if (clientInfo != null) {
            Map<String, Object> clientData = new HashMap<>();
            clientData.put("clientId", clientInfo.clientId);
            clientData.put("username", clientInfo.username != null ? clientInfo.username : "");
            clientData.put("connected", clientInfo.connected);
            clientData.put("protoName", clientInfo.protoName);
            clientData.put("protoVer", clientInfo.protoVer);
            clientData.put("ipAddress", clientInfo.ipAddress);
            clientData.put("port", clientInfo.getPort());
            clientData.put("connectedAt", clientInfo.connectedAt);
            clientData.put("createdAt", clientInfo.connectedAt);

            result.put("code", 1);
            result.put("data", clientData);
        } else {
            result.put("code", 104);
            result.put("message", "Client not found: " + clientId);
        }

        return result;
    }

    /**
     * 获取客户端订阅信息
     * 
     * @param clientId 客户端ID
     * @return 客户端订阅信息
     */
    public Map<String, Object> getClientSubscriptions(String clientId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取真实订阅数据（带QoS）
            Map<String, Integer> clientSubscriptionsWithQos = mqttStatsService.getClientSubscriptionsWithQos(clientId);
            List<Map<String, Object>> subscriptions = new ArrayList<>();

            for (Map.Entry<String, Integer> entry : clientSubscriptionsWithQos.entrySet()) {
                Map<String, Object> subscription = new HashMap<>();
                subscription.put("clientId", clientId);
                subscription.put("topicFilter", entry.getKey());
                subscription.put("mqttQoS", entry.getValue());
                subscriptions.add(subscription);
            }

            result.put("code", 1);
            result.put("data", subscriptions);
            logger.info("获取客户端订阅信息 - ClientId: {}, 订阅数量: {}", clientId, subscriptions.size());
        } catch (Exception e) {
            logger.error("获取客户端订阅信息失败", e);
            result.put("code", 105);
            result.put("message", "获取客户端订阅信息失败: " + e.getMessage());
            result.put("data", new ArrayList<>());
        }

        return result;
    }

    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getStats() {
        Map<String, Object> result = new HashMap<>();

        // 更新订阅统计
        mqttStatsService.updateSubscriptionStats();

        // 获取真实统计数据
        Map<String, Object> stats = mqttStatsService.getStats();

        result.put("code", 1);
        result.put("data", stats);
        return result;
    }
}
