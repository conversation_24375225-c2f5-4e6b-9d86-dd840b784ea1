package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmPatrolstdEntity;
import inks.service.std.eam.domain.pojo.DmPatrolstdPojo;
import inks.service.std.eam.domain.pojo.DmPatrolstditemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检查标准(DmPatrolstd)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 13:10:30
 */
@Mapper
public interface DmPatrolstdMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolstdPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmPatrolstditemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmPatrolstdPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmPatrolstdEntity 实例对象
     * @return 影响行数
     */
    int insert(DmPatrolstdEntity dmPatrolstdEntity);


    /**
     * 修改数据
     *
     * @param dmPatrolstdEntity 实例对象
     * @return 影响行数
     */
    int update(DmPatrolstdEntity dmPatrolstdEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param dmPatrolstdPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(DmPatrolstdPojo dmPatrolstdPojo);

    /**
     * 修改数据
     *
     * @param dmPatrolstdEntity 实例对象
     * @return 影响行数
     */
    int approval(DmPatrolstdEntity dmPatrolstdEntity);
}

