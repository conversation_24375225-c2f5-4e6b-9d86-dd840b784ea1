package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmUpkeeprecordPojo;
import inks.service.std.eam.domain.pojo.DmUpkeeprecorditemdetailPojo;

import java.util.List;

/**
 * 保养实施(DmUpkeeprecord)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-06 14:35:43
 */
public interface DmUpkeeprecordService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeeprecordPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeeprecorditemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeeprecordPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeeprecordPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeeprecordPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmUpkeeprecordPojo 实例对象
     * @return 实例对象
     */
    DmUpkeeprecordPojo insert(DmUpkeeprecordPojo dmUpkeeprecordPojo);

    /**
     * 修改数据
     *
     * @param dmUpkeeprecordpojo 实例对象
     * @return 实例对象
     */
    DmUpkeeprecordPojo update(DmUpkeeprecordPojo dmUpkeeprecordpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param dmUpkeeprecordPojo 实例对象
     * @return 实例对象
     */
    DmUpkeeprecordPojo approval(DmUpkeeprecordPojo dmUpkeeprecordPojo);

    int getSumFinishCount(String planid, String plandate, String tid);

    List<String> getAllDevId(String planid, String plandate, String tid);

    List<String> getAllFinishDevId(String planid, String date, String tid);


    List<DmUpkeeprecordPojo> getListByPlanidAndDate(String planid, String plandate, String tid);

}
