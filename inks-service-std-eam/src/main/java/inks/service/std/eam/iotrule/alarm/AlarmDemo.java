package inks.service.std.eam.iotrule.alarm;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 示例使用
class AlarmDemo {
    public static void main(String[] args) {
        // 创建告警服务
        AlarmService alarmService = new AlarmService();
        
        // 创建设备ID
        String deviceId = "device-001";
        
        // 创建规则："温度大于50且电压等于220V"
        SimpleCondition tempCondition = new SimpleCondition("temperature", "GREATER", 50);
        SimpleCondition voltageCondition = new SimpleCondition("voltage", "EQUALS", "220V");
        AndCondition complexCondition = new AndCondition(Arrays.asList(tempCondition, voltageCondition));
        
        // 注册规则
        AlarmRule rule = new AlarmRule("高温高压告警", "SIMPLE", complexCondition);
        alarmService.registerDeviceRule(deviceId, rule);
        
        // 测试数据1: 应该触发告警
        Map<String, Object> telemetry1 = new HashMap<>();
        telemetry1.put("temperature", 170);
        telemetry1.put("voltage", "220V");
        
        // 测试数据2: 不应该触发告警(电压不符合)
        Map<String, Object> telemetry2 = new HashMap<>();
        telemetry2.put("temperature", 170);
        telemetry2.put("voltage", "221V");
        
        // 处理遥测数据
        List<Alarm> triggeredAlarms1 = alarmService.processDeviceTelemetry(deviceId, telemetry1);
        System.out.println("遥测数据1触发告警数: " + triggeredAlarms1.size());
        for (Alarm alarm : triggeredAlarms1) {
            System.out.println("  - " + alarm);
        }
        
        List<Alarm> triggeredAlarms2 = alarmService.processDeviceTelemetry(deviceId, telemetry2);
        System.out.println("遥测数据2触发告警数: " + triggeredAlarms2.size());
    }
}