package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmPatrolrecEntity;
import inks.service.std.eam.domain.DmPatrolrecitemEntity;
import inks.service.std.eam.domain.pojo.DmPatrolrecPojo;
import inks.service.std.eam.domain.pojo.DmPatrolrecitemPojo;
import inks.service.std.eam.domain.pojo.DmPatrolrecitemdetailPojo;
import inks.service.std.eam.mapper.DmPatrolrecMapper;
import inks.service.std.eam.mapper.DmPatrolrecitemMapper;
import inks.service.std.eam.service.DmPatrolrecService;
import inks.service.std.eam.service.DmPatrolrecitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 巡检记录(DmPatrolrec)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 13:17:13
 */
@Service("dmPatrolrecService")
public class DmPatrolrecServiceImpl implements DmPatrolrecService {
    @Resource
    private DmPatrolrecMapper dmPatrolrecMapper;

    @Resource
    private DmPatrolrecitemMapper dmPatrolrecitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private DmPatrolrecitemService dmPatrolrecitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmPatrolrecPojo getEntity(String key, String tid) {
        return this.dmPatrolrecMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolrecitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolrecitemdetailPojo> lst = dmPatrolrecMapper.getPageList(queryParam);
            PageInfo<DmPatrolrecitemdetailPojo> pageInfo = new PageInfo<DmPatrolrecitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmPatrolrecPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            DmPatrolrecPojo dmPatrolrecPojo = this.dmPatrolrecMapper.getEntity(key, tid);
            //读取子表
            dmPatrolrecPojo.setItem(dmPatrolrecitemMapper.getList(dmPatrolrecPojo.getId(), dmPatrolrecPojo.getTenantid()));
            return dmPatrolrecPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolrecPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolrecPojo> lst = dmPatrolrecMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(dmPatrolrecitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<DmPatrolrecPojo> pageInfo = new PageInfo<DmPatrolrecPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolrecPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolrecPojo> lst = dmPatrolrecMapper.getPageTh(queryParam);
            PageInfo<DmPatrolrecPojo> pageInfo = new PageInfo<DmPatrolrecPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmPatrolrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmPatrolrecPojo insert(DmPatrolrecPojo dmPatrolrecPojo) {
//初始化NULL字段
        if (dmPatrolrecPojo.getRefno() == null) dmPatrolrecPojo.setRefno("");
        if (dmPatrolrecPojo.getBilldate() == null) dmPatrolrecPojo.setBilldate(new Date());
        if (dmPatrolrecPojo.getBilltype() == null) dmPatrolrecPojo.setBilltype("");
        if (dmPatrolrecPojo.getPlanid() == null) dmPatrolrecPojo.setPlanid("");
        if (dmPatrolrecPojo.getPlancode() == null) dmPatrolrecPojo.setPlancode("");
        if (dmPatrolrecPojo.getPlanname() == null) dmPatrolrecPojo.setPlanname("");
        if (dmPatrolrecPojo.getPathid() == null) dmPatrolrecPojo.setPathid("");
        if (dmPatrolrecPojo.getPathcode() == null) dmPatrolrecPojo.setPathcode("");
        if (dmPatrolrecPojo.getPathname() == null) dmPatrolrecPojo.setPathname("");
        if (dmPatrolrecPojo.getOperator() == null) dmPatrolrecPojo.setOperator("");
        if (dmPatrolrecPojo.getSummary() == null) dmPatrolrecPojo.setSummary("");
        if (dmPatrolrecPojo.getLister() == null) dmPatrolrecPojo.setLister("");
        if (dmPatrolrecPojo.getListerid() == null) dmPatrolrecPojo.setListerid("");
        if (dmPatrolrecPojo.getCreatedate() == null) dmPatrolrecPojo.setCreatedate(new Date());
        if (dmPatrolrecPojo.getCreateby() == null) dmPatrolrecPojo.setCreateby("");
        if (dmPatrolrecPojo.getCreatebyid() == null) dmPatrolrecPojo.setCreatebyid("");
        if (dmPatrolrecPojo.getModifydate() == null) dmPatrolrecPojo.setModifydate(new Date());
        if (dmPatrolrecPojo.getAssessor() == null) dmPatrolrecPojo.setAssessor("");
        if (dmPatrolrecPojo.getAssessorid() == null) dmPatrolrecPojo.setAssessorid("");
        if (dmPatrolrecPojo.getAssessdate() == null) dmPatrolrecPojo.setAssessdate(new Date());
        if (dmPatrolrecPojo.getEnabledmark() == null) dmPatrolrecPojo.setEnabledmark(0);
        if (dmPatrolrecPojo.getCustom1() == null) dmPatrolrecPojo.setCustom1("");
        if (dmPatrolrecPojo.getCustom2() == null) dmPatrolrecPojo.setCustom2("");
        if (dmPatrolrecPojo.getCustom3() == null) dmPatrolrecPojo.setCustom3("");
        if (dmPatrolrecPojo.getCustom4() == null) dmPatrolrecPojo.setCustom4("");
        if (dmPatrolrecPojo.getCustom5() == null) dmPatrolrecPojo.setCustom5("");
        if (dmPatrolrecPojo.getCustom6() == null) dmPatrolrecPojo.setCustom6("");
        if (dmPatrolrecPojo.getCustom7() == null) dmPatrolrecPojo.setCustom7("");
        if (dmPatrolrecPojo.getCustom8() == null) dmPatrolrecPojo.setCustom8("");
        if (dmPatrolrecPojo.getCustom9() == null) dmPatrolrecPojo.setCustom9("");
        if (dmPatrolrecPojo.getCustom10() == null) dmPatrolrecPojo.setCustom10("");
        if (dmPatrolrecPojo.getTenantid() == null) dmPatrolrecPojo.setTenantid("");
        if (dmPatrolrecPojo.getRevision() == null) dmPatrolrecPojo.setRevision(0);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmPatrolrecEntity dmPatrolrecEntity = new DmPatrolrecEntity();
        BeanUtils.copyProperties(dmPatrolrecPojo, dmPatrolrecEntity);

        //设置id和新建日期
        dmPatrolrecEntity.setId(id);
        dmPatrolrecEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmPatrolrecMapper.insert(dmPatrolrecEntity);
        //Item子表处理
        List<DmPatrolrecitemPojo> lst = dmPatrolrecPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                DmPatrolrecitemPojo itemPojo = this.dmPatrolrecitemService.clearNull(lst.get(i));
                DmPatrolrecitemEntity dmPatrolrecitemEntity = new DmPatrolrecitemEntity();
                BeanUtils.copyProperties(itemPojo, dmPatrolrecitemEntity);
                //设置id和Pid
                dmPatrolrecitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmPatrolrecitemEntity.setPid(id);
                dmPatrolrecitemEntity.setTenantid(dmPatrolrecPojo.getTenantid());
                dmPatrolrecitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmPatrolrecitemMapper.insert(dmPatrolrecitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmPatrolrecEntity.getId(), dmPatrolrecEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmPatrolrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmPatrolrecPojo update(DmPatrolrecPojo dmPatrolrecPojo) {
        //主表更改
        DmPatrolrecEntity dmPatrolrecEntity = new DmPatrolrecEntity();
        BeanUtils.copyProperties(dmPatrolrecPojo, dmPatrolrecEntity);
        this.dmPatrolrecMapper.update(dmPatrolrecEntity);
        if (dmPatrolrecPojo.getItem() != null) {
            //Item子表处理
            List<DmPatrolrecitemPojo> lst = dmPatrolrecPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = dmPatrolrecMapper.getDelItemIds(dmPatrolrecPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.dmPatrolrecitemMapper.delete(lstDelIds.get(i), dmPatrolrecEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    DmPatrolrecitemEntity dmPatrolrecitemEntity = new DmPatrolrecitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        DmPatrolrecitemPojo itemPojo = this.dmPatrolrecitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, dmPatrolrecitemEntity);
                        //设置id和Pid
                        dmPatrolrecitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        dmPatrolrecitemEntity.setPid(dmPatrolrecEntity.getId());  // 主表 id
                        dmPatrolrecitemEntity.setTenantid(dmPatrolrecPojo.getTenantid());   // 租户id
                        dmPatrolrecitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.dmPatrolrecitemMapper.insert(dmPatrolrecitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), dmPatrolrecitemEntity);
                        dmPatrolrecitemEntity.setTenantid(dmPatrolrecPojo.getTenantid());
                        this.dmPatrolrecitemMapper.update(dmPatrolrecitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmPatrolrecEntity.getId(), dmPatrolrecEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        DmPatrolrecPojo dmPatrolrecPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<DmPatrolrecitemPojo> lst = dmPatrolrecPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.dmPatrolrecitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.dmPatrolrecMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param dmPatrolrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmPatrolrecPojo approval(DmPatrolrecPojo dmPatrolrecPojo) {
        //主表更改
        DmPatrolrecEntity dmPatrolrecEntity = new DmPatrolrecEntity();
        BeanUtils.copyProperties(dmPatrolrecPojo, dmPatrolrecEntity);
        this.dmPatrolrecMapper.approval(dmPatrolrecEntity);
        //返回Bill实例
        return this.getBillEntity(dmPatrolrecEntity.getId(), dmPatrolrecEntity.getTenantid());
    }

}
