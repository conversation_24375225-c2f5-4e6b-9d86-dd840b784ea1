package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmSpareaccessitemEntity;
import inks.service.std.eam.domain.pojo.DmSpareaccessitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 出入库项目(DmSpareaccessitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 10:09:41
 */
@Mapper
public interface DmSpareaccessitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmSpareaccessitemPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmSpareaccessitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmSpareaccessitemPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);


    /**
     * 新增数据
     *
     * @param dmSpareaccessitemEntity 实例对象
     * @return 影响行数
     */
    int insert(DmSpareaccessitemEntity dmSpareaccessitemEntity);


    /**
     * 修改数据
     *
     * @param dmSpareaccessitemEntity 实例对象
     * @return 影响行数
     */
    int update(DmSpareaccessitemEntity dmSpareaccessitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

}

