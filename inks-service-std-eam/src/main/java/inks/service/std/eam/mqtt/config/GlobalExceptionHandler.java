//package inks.service.std.eam.mqtt.config;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 全局异常处理器
// *
// * <AUTHOR>
// */
//@ControllerAdvice
//public class GlobalExceptionHandler {
//
//    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
//
//    @ExceptionHandler(Exception.class)
//    @ResponseBody
//    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
//        logger.error("API调用异常", e);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("code", 105);
//        result.put("message", "服务器内部错误: " + e.getMessage());
//        result.put("timestamp", System.currentTimeMillis());
//
//        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
//    }
//
//    @ExceptionHandler(IllegalArgumentException.class)
//    @ResponseBody
//    public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(IllegalArgumentException e) {
//        logger.error("参数错误", e);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("code", 102);
//        result.put("message", "参数错误: " + e.getMessage());
//        result.put("timestamp", System.currentTimeMillis());
//
//        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
//    }
//}
