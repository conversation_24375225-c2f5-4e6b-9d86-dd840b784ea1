package inks.service.std.eam.iotrule.rule;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * AdvancedAlarmRuleEvaluator 告警生命周期控制功能的单元测试.
 */
public class AdvancedAlarmRuleEvaluatorLifecycleTest {

    private static ObjectMapper objectMapper;

    @BeforeClass
    public static void setUpClass() {
        objectMapper = new ObjectMapper();
    }

    @Before
    public void setUp() {
        // 通常在每个测试前清理特定设备的缓存以隔离状态
    }

    /**
     * 测试场景: 去抖动 (Debounce) 功能。
     * 规则: count > 5, 去抖动配置 {count: 3, timeWindow: 10s}
     * 预期: 前两次满足条件时被去抖动，第三次才真正触发告警。
     */
    @Test
    public void testDebounceFunction() throws JsonProcessingException {
        String ruleId = "debounce-func-rule";
        String deviceId = "dev-debounce-func";

        // 清理此测试相关的状态
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId);

        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\"type\": \"SIMPLE\", \"key\": \"count\", \"operation\": \"GT\", \"value\": 5},\n" +
                "  \"debounce\": { \"count\": 3, \"timeWindow\": 10 } \n" + // 10秒内连续3次满足才触发
                "}", ruleId);

        long t0 = System.currentTimeMillis();
        String telemetry1 = String.format("{\"deviceId\": \"%s\", \"count\": 6, \"timestamp\": %d}", deviceId, t0);       // 满足条件 (第1次)
        String telemetry2 = String.format("{\"deviceId\": \"%s\", \"count\": 7, \"timestamp\": %d}", deviceId, t0 + 1000); // 满足条件 (第2次)
        String telemetry3 = String.format("{\"deviceId\": \"%s\", \"count\": 8, \"timestamp\": %d}", deviceId, t0 + 2000); // 满足条件 (第3次)
        String telemetry4 = String.format("{\"deviceId\": \"%s\", \"count\": 4, \"timestamp\": %d}", deviceId, t0 + 3000); // 不满足条件 (中断)
        String telemetry5 = String.format("{\"deviceId\": \"%s\", \"count\": 9, \"timestamp\": %d}", deviceId, t0 + 4000); // 再次满足条件 (重新计数)

        // 第一次评估
        AlarmEvaluationResult r1 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry1, ruleJson);
        assertFalse("第1次触发，应被去抖动", r1.isTriggered());
        assertTrue("Debounced 标志应为 true", r1.isDebounced());
        assertEquals("计数 1/3 (窗口 10s)", r1.getDebounceInfo());

        // 第二次评估
        AlarmEvaluationResult r2 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry2, ruleJson);
        assertFalse("第2次触发，应被去抖动", r2.isTriggered());
        assertTrue("Debounced 标志应为 true", r2.isDebounced());
        assertEquals("计数 2/3 (窗口 10s)", r2.getDebounceInfo());

        // 第三次评估
        AlarmEvaluationResult r3 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry3, ruleJson);
        assertTrue("第3次触发，应解除去抖动并触发", r3.isTriggered());
        assertFalse("Debounced 标志应为 false", r3.isDebounced());
        assertNull("触发后不应有 DebounceInfo", r3.getDebounceInfo());

        // 第四次评估 (条件不满足)
        AlarmEvaluationResult r4 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry4, ruleJson);
        assertFalse("条件不满足，不应触发", r4.isTriggered());
        assertFalse("条件不满足，不应是 Debounced 状态", r4.isDebounced()); // 计数器是否重置取决于实现，但结果不应是 Debounced

        // 第五次评估 (中断后重新开始)
        AlarmEvaluationResult r5 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry5, ruleJson);
        assertFalse("中断后第1次触发，应被去抖动", r5.isTriggered());
        assertTrue("Debounced 标志应为 true", r5.isDebounced());
        assertEquals("计数 1/3 (窗口 10s)", r5.getDebounceInfo()); // 计数器重置
    }


    /**
     * 测试场景: 频率抑制 (Frequency Suppression) 功能。
     * 规则: status == "ERROR", 频率抑制 {maxCount: 2, timeWindow: 10s}
     * 预期: 前两次触发正常，第三次在10秒内触发时被抑制。第4次在远超10秒后触发时应正常。
     */
    @Test
    public void testFrequencySuppressionFunction() throws JsonProcessingException {
        String ruleId = "freq-suppress-rule";
        String deviceId = "dev-freq-suppress";

        // 清理此测试相关的状态
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId);

        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\"type\": \"SIMPLE\", \"key\": \"status\", \"operation\": \"EQ\", \"value\": \"ERROR\"},\n" +
                "  \"frequencySuppression\": { \"maxCount\": 2, \"timeWindow\": 10 } \n" + // 10秒内最多触发2次
                "}", ruleId);

        long t0 = System.currentTimeMillis();
        String telemetry1 = String.format("{\"deviceId\": \"%s\", \"status\": \"ERROR\", \"timestamp\": %d}", deviceId, t0);       // 第1次触发
        String telemetry2 = String.format("{\"deviceId\": \"%s\", \"status\": \"ERROR\", \"timestamp\": %d}", deviceId, t0 + 1000);  // 第2次触发
        String telemetry3 = String.format("{\"deviceId\": \"%s\", \"status\": \"ERROR\", \"timestamp\": %d}", deviceId, t0 + 2000);  // 第3次触发 (应被抑制)
        // --- 修正：增大时间间隔，确保明确超出窗口 ---
        String telemetry4 = String.format("{\"deviceId\": \"%s\", \"status\": \"ERROR\", \"timestamp\": %d}", deviceId, t0 + 20000); // 20秒后触发 (应正常)

        // 第一次评估
        AlarmEvaluationResult r1 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry1, ruleJson);
        assertTrue("第1次触发，应正常触发", r1.isTriggered());
        assertFalse("不应被抑制", r1.isSuppressed());

        // 第二次评估
        AlarmEvaluationResult r2 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry2, ruleJson);
        assertTrue("第2次触发，应正常触发", r2.isTriggered());
        assertFalse("不应被抑制", r2.isSuppressed());

        // 第三次评估
        AlarmEvaluationResult r3 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry3, ruleJson);
        assertFalse("第3次触发，应被频率抑制", r3.isTriggered());
        assertTrue("Suppressed 标志应为 true", r3.isSuppressed());
        assertTrue("应包含频率抑制原因", r3.getSuppressionReason().contains("频率超限"));

        // 第四次评估 (20秒后)
        AlarmEvaluationResult r4 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry4, ruleJson);
        assertTrue("超出窗口后再次触发，应正常触发", r4.isTriggered()); // 断言不变，但增加了时间间隔
        assertFalse("不应被抑制", r4.isSuppressed());
    }


    /**
     * 测试场景: 重复抑制 (Repeat Suppression) 功能。
     * 规则: value > 10, 重复抑制策略 "SUPPRESS_UNTIL_CLEAR"
     * 预期: 第一次触发后，在告警未清除或确认前，后续满足条件的评估被抑制。
     */
    @Test
    public void testRepeatSuppressionFunction() throws JsonProcessingException {
        String ruleId = "repeat-suppress-rule";
        String deviceId = "dev-repeat-suppress";

        // 清理此测试相关的状态
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId);

        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\"type\": \"SIMPLE\", \"key\": \"value\", \"operation\": \"GT\", \"value\": 10},\n" +
                "  \"repeatSuppression\": \"SUPPRESS_UNTIL_CLEAR\" \n" +
                "}", ruleId);

        long t0 = System.currentTimeMillis();
        String telemetry1 = String.format("{\"deviceId\": \"%s\", \"value\": 15, \"timestamp\": %d}", deviceId, t0); // 第一次触发
        String telemetry2 = String.format("{\"deviceId\": \"%s\", \"value\": 16, \"timestamp\": %d}", deviceId, t0 + 1000); // 第二次满足条件 (应被抑制)
        String telemetry3 = String.format("{\"deviceId\": \"%s\", \"value\": 5, \"timestamp\": %d}", deviceId, t0 + 2000); // 条件不满足 (不触发)
        String telemetry4 = String.format("{\"deviceId\": \"%s\", \"value\": 12, \"timestamp\": %d}", deviceId, t0 + 3000); // 告警仍激活，再次满足条件 (应被抑制)

        // 第一次评估
        AlarmEvaluationResult r1 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry1, ruleJson);
        assertTrue("第1次触发，应正常触发", r1.isTriggered());
        assertFalse("不应被抑制", r1.isSuppressed());

        // 第二次评估
        AlarmEvaluationResult r2 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry2, ruleJson);
        assertFalse("第2次触发，应被重复抑制", r2.isTriggered());
        assertTrue("Suppressed 标志应为 true", r2.isSuppressed());
        assertTrue("应包含重复抑制原因", r2.getSuppressionReason().contains("告警已激活"));

        // 第三次评估
        AlarmEvaluationResult r3 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry3, ruleJson);
        assertFalse("条件不满足，不应触发", r3.isTriggered());
        assertFalse("不应被抑制", r3.isSuppressed());

        // 第四次评估
        AlarmEvaluationResult r4 = AdvancedAlarmRuleEvaluator.evaluateRule(telemetry4, ruleJson);
        assertFalse("告警仍激活，再次触发应被重复抑制", r4.isTriggered());
        assertTrue("Suppressed 标志应为 true", r4.isSuppressed());
        assertTrue("应包含重复抑制原因", r4.getSuppressionReason().contains("告警已激活"));
    }

    /**
     * 测试场景: 自动清除条件 (Clear Condition) 功能。
     * 规则: 触发 temp > 50, 清除 temp <= 40
     * 预期: 告警触发后，当数据满足清除条件时，告警状态变为非激活，结果中 Cleared 为 true。
     */
    @Test
    public void testClearConditionFunction() throws JsonProcessingException {
        String ruleId = "clear-cond-func-rule";
        String deviceId = "dev-clear-cond-func";

        // 清理此测试相关的状态
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId);

        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\"type\": \"SIMPLE\", \"key\": \"temp\", \"operation\": \"GT\", \"value\": 50},\n" +
                "  \"clearCondition\": {\"type\": \"SIMPLE\", \"key\": \"temp\", \"operation\": \"LE\", \"value\": 40}\n" +
                "}", ruleId);

        long t0 = System.currentTimeMillis();
        String triggerTelemetry = String.format("{\"deviceId\": \"%s\", \"temp\": 55, \"timestamp\": %d}", deviceId, t0);       // 触发
        String stillHighTelemetry = String.format("{\"deviceId\": \"%s\", \"temp\": 52, \"timestamp\": %d}", deviceId, t0 + 1000); // 维持激活
        String clearTelemetry = String.format("{\"deviceId\": \"%s\", \"temp\": 38, \"timestamp\": %d}", deviceId, t0 + 2000);     // 清除
        String afterClearTelemetry = String.format("{\"deviceId\": \"%s\", \"temp\": 39, \"timestamp\": %d}", deviceId, t0 + 3000);   // 清除后

        // 1. 触发告警
        AlarmEvaluationResult r1 = AdvancedAlarmRuleEvaluator.evaluateRule(triggerTelemetry, ruleJson);
        assertTrue("告警应触发", r1.isTriggered());
        assertFalse("告警不应清除", r1.isCleared());
        // 验证内部状态 (需要访问内部状态的方法，或者依赖后续行为)
        // AlarmState state1 = AdvancedAlarmRuleEvaluator.getActiveAlarms().get(deviceId).get(ruleId);
        // assertTrue(state1.isActive());

        // 2. 告警持续 (触发条件仍满足，清除条件不满足)
        AlarmEvaluationResult r2 = AdvancedAlarmRuleEvaluator.evaluateRule(stillHighTelemetry, ruleJson);
        assertTrue("告警应持续触发 (无抑制)", r2.isTriggered());
        assertFalse("告警仍未清除", r2.isCleared());
        // AlarmState state2 = AdvancedAlarmRuleEvaluator.getActiveAlarms().get(deviceId).get(ruleId);
        // assertTrue(state2.isActive());

        // 3. 满足清除条件
        AlarmEvaluationResult r3 = AdvancedAlarmRuleEvaluator.evaluateRule(clearTelemetry, ruleJson);
        assertFalse("触发条件不满足，不应触发", r3.isTriggered());
        assertTrue("告警应被清除", r3.isCleared());
        assertNotNull("应有清除条件结果", r3.getClearConditionResults());
        assertTrue("清除条件评估结果应为 true", r3.getClearConditionResults().get(0).isResult());
        // 验证内部状态是否变为 inactive
        // AlarmState state3 = AdvancedAlarmRuleEvaluator.getActiveAlarms().get(deviceId).get(ruleId);
        // assertNotNull("告警状态不应为null", state3); // 状态对象可能还会保留一段时间
        // assertFalse("告警状态应变为 inactive", state3.isActive());
        // assertTrue("清除时间应被记录", state3.getClearTime() > 0);

        // 4. 告警已清除后，再次发送满足清除条件的数据
        AlarmEvaluationResult r4 = AdvancedAlarmRuleEvaluator.evaluateRule(afterClearTelemetry, ruleJson);
        assertFalse("告警已清除，不应触发", r4.isTriggered());
        assertFalse("告警已清除，不应再次清除", r4.isCleared()); // 清除条件只在告警激活时评估其效果
    }

    /**
     * 测试场景: 手动控制 API (acknowledge 和 clear)。
     * 规则: pressure > 100
     * 预期: 触发告警 -> 手动确认 -> 手动清除。
     */
    @Test
    public void testManualControlFunction() throws JsonProcessingException {
        String ruleId = "manual-ctrl-rule";
        String deviceId = "dev-manual-ctrl";

        // 清理此测试相关的状态
        AdvancedAlarmRuleEvaluator.clearDeviceCacheForTesting(deviceId);

        String ruleJson = String.format("{\n" +
                "  \"id\": \"%s\",\n" +
                "  \"condition\": {\"type\": \"SIMPLE\", \"key\": \"pressure\", \"operation\": \"GT\", \"value\": 100}\n" +
                "}", ruleId);

        long t0 = System.currentTimeMillis();
        String triggerTelemetry = String.format("{\"deviceId\": \"%s\", \"pressure\": 110, \"timestamp\": %d}", deviceId, t0);

        // 1. 触发告警
        AlarmEvaluationResult r1 = AdvancedAlarmRuleEvaluator.evaluateRule(triggerTelemetry, ruleJson);
        assertTrue("告警应触发", r1.isTriggered());
        // 验证内部状态：激活，未确认
        // AlarmState state1 = AdvancedAlarmRuleEvaluator.getActiveAlarms().get(deviceId).get(ruleId);
        // assertNotNull(state1);
        // assertTrue(state1.isActive());
        // assertFalse(state1.isAcknowledged());

        // 2. 手动确认告警
        boolean ackResult = AdvancedAlarmRuleEvaluator.acknowledgeAlarm(deviceId, ruleId);
        assertTrue("手动确认应成功", ackResult);
        // 验证内部状态：激活，已确认
        // AlarmState state2 = AdvancedAlarmRuleEvaluator.getActiveAlarms().get(deviceId).get(ruleId);
        // assertNotNull(state2);
        // assertTrue(state2.isActive()); // 确认不改变激活状态
        // assertTrue(state2.isAcknowledged());
        // assertTrue(state2.getAcknowledgeTime() > 0);

        // 再次发送触发数据，检查是否被抑制（如果配置了 repeatSuppression）
        // 如果 repeatSuppression = SUPPRESS_UNTIL_CLEAR，确认后不会抑制
        // AlarmEvaluationResult r2 = AdvancedAlarmRuleEvaluator.evaluateRule(triggerTelemetry, ruleJson);
        // assertTrue("确认后，再次触发应不再被抑制(取决于repeat策略)", r2.isTriggered());

        // 3. 手动清除告警
        boolean clearResult = AdvancedAlarmRuleEvaluator.clearAlarm(deviceId, ruleId);
        assertTrue("手动清除应成功", clearResult);
        // 验证内部状态：非激活
        // AlarmState state3 = AdvancedAlarmRuleEvaluator.getActiveAlarms().get(deviceId).get(ruleId);
        // assertNotNull(state3);
        // assertFalse("告警状态应变为 inactive", state3.isActive());
        // assertTrue("清除时间应被记录", state3.getClearTime() > 0);
        // assertFalse("清除后确认状态应重置", state3.isAcknowledged());

        // 尝试清除已清除的告警
        boolean clearResultAgain = AdvancedAlarmRuleEvaluator.clearAlarm(deviceId, ruleId);
        assertFalse("尝试清除已清除的告警应失败", clearResultAgain);
    }
}
