package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.DmUpkeepplanPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepplanitemPojo;
import inks.service.std.eam.domain.pojo.DmUpkeepplanitemdetailPojo;
import inks.service.std.eam.service.DmUpkeepplanService;
import inks.service.std.eam.service.DmUpkeepplanitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 保养计划(DmUpkeepplan)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-07 17:16:49
 */

public class DmUpkeepplanController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(DmUpkeepplanController.class);
    /**
     * 服务对象
     */
    @Resource
    private DmUpkeepplanService dmUpkeepplanService;
    /**
     * 服务对象Item
     */
    @Resource
    private DmUpkeepplanitemService dmUpkeepplanitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取保养计划详细信息", notes = "获取保养计划详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List")
    public R<DmUpkeepplanPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmUpkeepplanService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List")
    public R<PageInfo<DmUpkeepplanitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_UpkeepPlan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.dmUpkeepplanService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取保养计划详细信息", notes = "获取保养计划详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List")
    public R<DmUpkeepplanPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmUpkeepplanService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List")
    public R<PageInfo<DmUpkeepplanPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_UpkeepPlan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.dmUpkeepplanService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.List")
    public R<PageInfo<DmUpkeepplanPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_UpkeepPlan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.dmUpkeepplanService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增保养计划", notes = "新增保养计划", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.Add")
    public R<DmUpkeepplanPojo> create(@RequestBody String json) {
        try {
            DmUpkeepplanPojo dmUpkeepplanPojo = JSONArray.parseObject(json, DmUpkeepplanPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            R r = systemFeignService.getBillCode("DxxMxxB1", loginUser.getToken());
            if (r.getCode() == 200)
                dmUpkeepplanPojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            dmUpkeepplanPojo.setCreateby(loginUser.getRealName());   // 创建者
            dmUpkeepplanPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            dmUpkeepplanPojo.setCreatedate(new Date());   // 创建时间
            dmUpkeepplanPojo.setLister(loginUser.getRealname());   // 制表
            dmUpkeepplanPojo.setListerid(loginUser.getUserid());    // 制表id            
            dmUpkeepplanPojo.setModifydate(new Date());   //修改时间
            dmUpkeepplanPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.dmUpkeepplanService.insert(dmUpkeepplanPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改保养计划", notes = "修改保养计划", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.Edit")
    public R<DmUpkeepplanPojo> update(@RequestBody String json) {
        try {
            DmUpkeepplanPojo dmUpkeepplanPojo = JSONArray.parseObject(json, DmUpkeepplanPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmUpkeepplanPojo.setLister(loginUser.getRealname());   // 制表
            dmUpkeepplanPojo.setListerid(loginUser.getUserid());    // 制表id   
            dmUpkeepplanPojo.setModifydate(new Date());   //修改时间
            dmUpkeepplanPojo.setAssessor(""); //审核员
            dmUpkeepplanPojo.setAssessorid(""); //审核员
            dmUpkeepplanPojo.setAssessdate(new Date()); //审核时间
            dmUpkeepplanPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.dmUpkeepplanService.update(dmUpkeepplanPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除保养计划", notes = "删除保养计划", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmUpkeepplanService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增保养计划Item", notes = "新增保养计划Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.Add")
    public R<DmUpkeepplanitemPojo> createItem(@RequestBody String json) {
        try {
            DmUpkeepplanitemPojo dmUpkeepplanitemPojo = JSONArray.parseObject(json, DmUpkeepplanitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmUpkeepplanitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.dmUpkeepplanitemService.insert(dmUpkeepplanitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除保养计划Item", notes = "删除保养计划Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmUpkeepplanitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核保养计划", notes = "审核保养计划", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.Approval")
    public R<DmUpkeepplanPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            DmUpkeepplanPojo dmUpkeepplanPojo = this.dmUpkeepplanService.getEntity(key, loginUser.getTenantid());
            if (dmUpkeepplanPojo.getAssessor().equals("")) {
                dmUpkeepplanPojo.setAssessor(loginUser.getRealname()); //审核员
                dmUpkeepplanPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                dmUpkeepplanPojo.setAssessor(""); //审核员
                dmUpkeepplanPojo.setAssessorid(""); //审核员
            }
            dmUpkeepplanPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.dmUpkeepplanService.approval(dmUpkeepplanPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_UpkeepPlan.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        DmUpkeepplanPojo dmUpkeepplanPojo = this.dmUpkeepplanService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(dmUpkeepplanPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = dmUpkeepplanPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    DmUpkeepplanitemPojo dmUpkeepplanitemPojo = new DmUpkeepplanitemPojo();
                    dmUpkeepplanPojo.getItem().add(dmUpkeepplanitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(dmUpkeepplanPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

