package inks.service.std.eam.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;

/**
 * 工装具借还主表(DmToolborrow)实体类
 *
 * <AUTHOR>
 * @since 2025-06-13 09:51:56
 */
public class DmToolborrowPojo implements Serializable {
    private static final long serialVersionUID = 303657647941084796L;
     // id
     @Excel(name = "id")
    private String id;
     // 流水号
     @Excel(name = "流水号")
    private String refno;
     // 单据日期
     @Excel(name = "单据日期")
    private Date billdate;
     // 类型
     @Excel(name = "类型")
    private String billtype;
     // 标题
     @Excel(name = "标题")
    private String billtitle;
     // 单据状态 (1:待归还, 2:部分归还, 3:已归还)
     @Excel(name = "单据状态 (1:待归还, 2:部分归还, 3:已归还)")
    private Integer status;
     // 借用人id
     @Excel(name = "借用人id")
    private String borrowerid;
     // 借用人姓名
     @Excel(name = "借用人姓名")
    private String borrowername;
     // 借用部门id
     @Excel(name = "借用部门id")
    private String deptid;
     // 预计归还日期
     @Excel(name = "预计归还日期")
    private Date expreturndate;
     // 实际完全归还日期
     @Excel(name = "实际完全归还日期")
    private Date actreturndate;
     // 借用item行数
     @Excel(name = "借用item行数")
    private Integer itemcount;
     // 完成归还的行数
     @Excel(name = "完成归还的行数")
    private Integer finishcount;
     // 逾期天数
     @Excel(name = "逾期天数")
    private Integer overduedays;
     // 摘要
     @Excel(name = "摘要")
    private String summary;
     // 创建人
     @Excel(name = "创建人")
    private String createby;
     // 创建人id
     @Excel(name = "创建人id")
    private String createbyid;
     // 创建时间
     @Excel(name = "创建时间")
    private Date createdate;
     // 制表
     @Excel(name = "制表")
    private String lister;
     // 制表id
     @Excel(name = "制表id")
    private String listerid;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // 自定义1
     @Excel(name = "自定义1")
    private String custom1;
     // 自定义2
     @Excel(name = "自定义2")
    private String custom2;
     // 自定义3
     @Excel(name = "自定义3")
    private String custom3;
     // 自定义4
     @Excel(name = "自定义4")
    private String custom4;
     // 自定义5
     @Excel(name = "自定义5")
    private String custom5;
     // 自定义6
     @Excel(name = "自定义6")
    private String custom6;
     // 自定义7
     @Excel(name = "自定义7")
    private String custom7;
     // 自定义8
     @Excel(name = "自定义8")
    private String custom8;
     // 自定义9
     @Excel(name = "自定义9")
    private String custom9;
     // 自定义10
     @Excel(name = "自定义10")
    private String custom10;
     // 租户id
     @Excel(name = "租户id")
    private String tenantid;
     // 租户名称
     @Excel(name = "租户名称")
    private String tenantname;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<DmToolborrowitemPojo> item;
    // ws子表
    private List<DmToolborrowwsPojo> ws;
    
  // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public List<DmToolborrowwsPojo> getWs() {
        return ws;
    }

    public void setWs(List<DmToolborrowwsPojo> ws) {
        this.ws = ws;
    }

    // 借用单流水号
    public String getRefno() {
        return refno;
    }
    
    public void setRefno(String refno) {
        this.refno = refno;
    }
        
  // 单据日期
    public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
  // 类型
    public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
  // 标题
    public String getBilltitle() {
        return billtitle;
    }
    
    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
        
  // 单据状态 (1:待归还, 2:部分归还, 3:已归还)
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
        
  // 借用人id
    public String getBorrowerid() {
        return borrowerid;
    }
    
    public void setBorrowerid(String borrowerid) {
        this.borrowerid = borrowerid;
    }
        
  // 借用人姓名
    public String getBorrowername() {
        return borrowername;
    }
    
    public void setBorrowername(String borrowername) {
        this.borrowername = borrowername;
    }
        
  // 借用部门id
    public String getDeptid() {
        return deptid;
    }
    
    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
        
  // 预计归还日期
    public Date getExpreturndate() {
        return expreturndate;
    }
    
    public void setExpreturndate(Date expreturndate) {
        this.expreturndate = expreturndate;
    }
        
  // 实际完全归还日期
    public Date getActreturndate() {
        return actreturndate;
    }
    
    public void setActreturndate(Date actreturndate) {
        this.actreturndate = actreturndate;
    }
        
  // 借用item行数
    public Integer getItemcount() {
        return itemcount;
    }
    
    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }
        
  // 完成归还的行数
    public Integer getFinishcount() {
        return finishcount;
    }
    
    public void setFinishcount(Integer finishcount) {
        this.finishcount = finishcount;
    }
        
  // 逾期天数
    public Integer getOverduedays() {
        return overduedays;
    }
    
    public void setOverduedays(Integer overduedays) {
        this.overduedays = overduedays;
    }

  // 摘要
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
  // 创建人
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
  // 创建人id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
  // 创建时间
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
  // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
  // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
  // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
  // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
  // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
  // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
  // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
  // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
  // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
  // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
  // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
  // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
  // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
  // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
  // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
  // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

    public List<DmToolborrowitemPojo> getItem() {
        return item;
    }

    public void setItem(List<DmToolborrowitemPojo> item) {
        this.item = item;
    }


}

