package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolreturnitemPojo;
import inks.service.std.eam.domain.DmToolreturnitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具归还子表-工装具(DmToolreturnitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-19 10:16:52
 */
 @Mapper
public interface DmToolreturnitemMapper {

    DmToolreturnitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolreturnitemPojo> getPageList(QueryParam queryParam);

    List<DmToolreturnitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(DmToolreturnitemEntity dmToolreturnitemEntity);

    int update(DmToolreturnitemEntity dmToolreturnitemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

}

