package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmDevicetypeEntity;
import inks.service.std.eam.domain.pojo.DmDevicetypePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备分类(DmDevicetype)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-06 12:52:02
 */
@Mapper
public interface DmDevicetypeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmDevicetypePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmDevicetypePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param dmDevicetypeEntity 实例对象
     * @return 影响行数
     */
    int insert(DmDevicetypeEntity dmDevicetypeEntity);


    /**
     * 修改数据
     *
     * @param dmDevicetypeEntity 实例对象
     * @return 影响行数
     */
    int update(DmDevicetypeEntity dmDevicetypeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

}

