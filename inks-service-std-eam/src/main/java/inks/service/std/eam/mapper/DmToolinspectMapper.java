package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolinspectPojo;
import inks.service.std.eam.domain.pojo.DmToolinspectitemdetailPojo;
import inks.service.std.eam.domain.DmToolinspectEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具校验主表(DmToolinspect)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-19 10:17:23
 */
@Mapper
public interface DmToolinspectMapper {

    DmToolinspectPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolinspectitemdetailPojo> getPageList(QueryParam queryParam);

    List<DmToolinspectPojo> getPageTh(QueryParam queryParam);

    int insert(DmToolinspectEntity dmToolinspectEntity);

    int update(DmToolinspectEntity dmToolinspectEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(DmToolinspectPojo dmToolinspectPojo);
}

