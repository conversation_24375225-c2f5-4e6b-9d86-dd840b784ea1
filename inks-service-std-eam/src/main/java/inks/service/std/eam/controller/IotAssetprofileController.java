package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotAssetprofilePojo;
import inks.service.std.eam.service.IotAssetprofileService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 资产配置(Iot_AssetProfile)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-14 13:01:05
 */
//@RestController
//@RequestMapping("iotAssetprofile")
public class IotAssetprofileController {

    @Resource
    private IotAssetprofileService iotAssetprofileService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(IotAssetprofileController.class);


    @ApiOperation(value=" 获取资产配置详细信息", notes="获取资产配置详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_AssetProfile.List")
    public R<IotAssetprofilePojo> getEntity(String key) {
    try {
           // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.iotAssetprofileService.getEntity(key, loginUser.getTenantid()));
         }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_AssetProfile.List")
    public R<PageInfo<IotAssetprofilePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Iot_AssetProfile.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.iotAssetprofileService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增资产配置", notes="新增资产配置", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_AssetProfile.Add")
    public R<IotAssetprofilePojo> create(@RequestBody String json) {
       try {
       IotAssetprofilePojo iotAssetprofilePojo = JSONArray.parseObject(json,IotAssetprofilePojo.class);       
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            iotAssetprofilePojo.setCreateby(loginUser.getRealName());   // 创建者
            iotAssetprofilePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            iotAssetprofilePojo.setCreatedate(new Date());   // 创建时间
            iotAssetprofilePojo.setLister(loginUser.getRealname());   // 制表
            iotAssetprofilePojo.setListerid(loginUser.getUserid());    // 制表id  
            iotAssetprofilePojo.setModifydate(new Date());   //修改时间
            iotAssetprofilePojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.iotAssetprofileService.insert(iotAssetprofilePojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改资产配置", notes="修改资产配置", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_AssetProfile.Edit")
    public R<IotAssetprofilePojo> update(@RequestBody String json) {
       try {
         IotAssetprofilePojo iotAssetprofilePojo = JSONArray.parseObject(json,IotAssetprofilePojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            iotAssetprofilePojo.setLister(loginUser.getRealname());   // 制表
            iotAssetprofilePojo.setListerid(loginUser.getUserid());    // 制表id  
            iotAssetprofilePojo.setTenantid(loginUser.getTenantid());   //租户id
            iotAssetprofilePojo.setModifydate(new Date());   //修改时间
//            iotAssetprofilePojo.setAssessor(""); // 审核员
//            iotAssetprofilePojo.setAssessorid(""); // 审核员id
//            iotAssetprofilePojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.iotAssetprofileService.update(iotAssetprofilePojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除资产配置", notes="删除资产配置", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_AssetProfile.Delete")
    public R<Integer> delete(String key) {
    try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.iotAssetprofileService.delete(key, loginUser.getTenantid()));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_AssetProfile.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        IotAssetprofilePojo iotAssetprofilePojo = this.iotAssetprofileService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(iotAssetprofilePojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

