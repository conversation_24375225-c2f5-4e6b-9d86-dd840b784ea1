package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolinspectitemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 工装具校验记录子表(DmToolinspectitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-19 10:17:17
 */
public interface DmToolinspectitemService {

    DmToolinspectitemPojo getEntity(String key,String tid);

    PageInfo<DmToolinspectitemPojo> getPageList(QueryParam queryParam);

    List<DmToolinspectitemPojo> getList(String Pid,String tid);  

    DmToolinspectitemPojo insert(DmToolinspectitemPojo dmToolinspectitemPojo);

    DmToolinspectitemPojo update(DmToolinspectitemPojo dmToolinspectitempojo);

    int delete(String key,String tid);

    DmToolinspectitemPojo clearNull(DmToolinspectitemPojo dmToolinspectitempojo);
}
