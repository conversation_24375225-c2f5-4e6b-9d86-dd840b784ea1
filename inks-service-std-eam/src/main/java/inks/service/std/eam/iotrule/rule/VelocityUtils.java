package inks.service.std.eam.iotrule.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.StringResourceLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;

public class VelocityUtils {

    private static final Logger logger = LoggerFactory.getLogger(VelocityUtils.class);
    private static VelocityEngine VE;

    static {
        try {
            VE = new VelocityEngine();

            // 配置 Velocity 2.x 属性
            Properties props = new Properties();
            props.setProperty(RuntimeConstants.RESOURCE_LOADERS, "string");
            props.setProperty("resource.loader.string.class", StringResourceLoader.class.getName());
            props.setProperty(RuntimeConstants.RUNTIME_LOG_NAME, "VelocityUtils");
            props.setProperty("runtime.log.logsystem.class", "org.apache.velocity.runtime.log.NullLogChute");

            VE.init(props);
            logger.info("VelocityEngine 初始化成功");
        } catch (Exception e) {
            logger.error("VelocityEngine 初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("VelocityEngine 初始化失败", e);
        }
    }


    /**
     * 根据模板和 JSON 文本或对象生成渲染结果，
     * 在 Velocity 模板中直接用 $sn、$ts 等根级字段来获取信息
     *
     * @param template 模板字符串，支持 ${} 占位符
     * @param msg      要渲染的数据，既可以是字符串；也可以是 JSONObject
     * @return 渲染后的字符串结果
     */
    public static String renderTemplate(String template, Object msg) {
        if (VE == null) {
            logger.error("VelocityEngine 未初始化");
            return template; // 返回原模板
        }

        if (template == null || template.trim().isEmpty()) {
            logger.warn("模板为空，返回空字符串");
            return "";
        }

        try {
            JSONObject jsonData;
            if (msg instanceof String) {
                jsonData = JSON.parseObject((String) msg);
            } else if (msg instanceof JSONObject) {
                jsonData = (JSONObject) msg;
            } else {
                logger.error("msg 必须是 JSON 字符串或 JSONObject，实际类型: {}",
                           msg != null ? msg.getClass().getSimpleName() : "null");
                return template; // 返回原模板
            }

            VelocityContext context = new VelocityContext();

            // 安全地添加数据到上下文
            if (jsonData != null) {
                jsonData.forEach((key, value) -> {
                    if (key != null && value != null) {
                        context.put(key, value);
                    }
                });

                // 格式化 ts 时间戳
                if (jsonData.containsKey("ts")) {
                    try {
                        long ts = jsonData.getLongValue("ts");
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String formatTs = sdf.format(new Date(ts));
                        context.put("ts", formatTs);
                        logger.debug("时间戳格式化: {} -> {}", ts, formatTs);
                    } catch (Exception e) {
                        logger.warn("时间戳格式化失败: {}", e.getMessage());
                    }
                }
            }

            StringWriter writer = new StringWriter();
            boolean success = VE.evaluate(context, writer, "VelocityUtils", template);

            if (success) {
                String result = writer.toString();
                logger.debug("模板渲染成功 - 原模板: {}, 渲染结果: {}", template, result);
                return result;
            } else {
                logger.error("Velocity 模板评估失败");
                return template; // 返回原模板
            }

        } catch (Exception e) {
            logger.error("模板渲染异常 - 模板: {}, 数据: {}, 错误: {}",
                        template, msg, e.getMessage(), e);
            return template; // 发生异常时返回原模板
        }
    }

    public static void main(String[] args) {
        // 模板里直接使用 $sn、$ts、$temp、$dianya
        String template = "设备[$sn]在[$ts]时间点温度为[$temp]℃，电压为[$dianya]，请注意！";

        String msgtext = "{"
                + "\"sn\":\"ABC12138\","
                + "\"ts\":1744878995023,"
                + "\"temp\":39,"
                + "\"dianya\":\"221V\""
                + "}";

        String rendered = renderTemplate(template, msgtext);

        System.out.println("渲染后的内容：");
        System.out.println(rendered);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        System.out.println(sdf.format(new Date(-11111)));
    }
}
