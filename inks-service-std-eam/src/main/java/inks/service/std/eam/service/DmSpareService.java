package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmSparePojo;

/**
 * 备件信息(DmSpare)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-07 16:26:36
 */
public interface DmSpareService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmSparePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmSparePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmSparePojo 实例对象
     * @return 实例对象
     */
    DmSparePojo insert(DmSparePojo dmSparePojo);

    /**
     * 修改数据
     *
     * @param dmSparepojo 实例对象
     * @return 实例对象
     */
    DmSparePojo update(DmSparePojo dmSparepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
