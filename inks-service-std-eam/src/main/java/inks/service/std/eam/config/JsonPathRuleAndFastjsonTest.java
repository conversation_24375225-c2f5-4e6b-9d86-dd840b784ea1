package inks.service.std.eam.config;

import com.alibaba.fastjson.JSONObject;
import inks.service.std.eam.iotrule.rule.JsonPathRuleExtractor;
import org.junit.Test;

public class JsonPathRuleAndFastjsonTest {

    private final String json = "{\n" +
            "  \"msg\": {\n" +
            "    \"info\": {\n" +
            "      \"code\": \"ds01\",\n" +
            "      \"date\": \"2024-06-08 13:42:00\",\n" +
            "      \"content\": [\n" +
            "        {\"key\": \"temp\",   \"value\": 37},\n" +
            "        {\"key\": \"dianya\", \"value\": \"220V\"}\n" +
            "      ]\n" +
            "    }\n" +
            "  },\n" +
            "  \"modulecode\": \"saiot\"\n" +
            "}";

    /** 规则字段：alias 为输出字段名，path 为标准 JSONPath 表达式 */
    private final String ruleJson = "[\n" +
            "  {\"alias\":\"temp\",   \"path\":\"msg.info.content[?(@.key=='temp')].value\"},\n" +
            "  {\"alias\":\"dianya\", \"path\":\"msg.info.content[?(@.key=='dianya')].value\"}\n" +
            "]";

    @Test
    public void testExtractByRuleField() {
        JSONObject jsonObject = JsonPathRuleExtractor.extractByRule(json, ruleJson);
        System.out.println("Extracted JSON: " + jsonObject);
    }
}
