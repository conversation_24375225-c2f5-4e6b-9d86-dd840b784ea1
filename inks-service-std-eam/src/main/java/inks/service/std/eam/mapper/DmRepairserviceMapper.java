package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmRepairserviceEntity;
import inks.service.std.eam.domain.pojo.DmRepairservicePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备报修(DmRepairservice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-06 14:55:25
 */
@Mapper
public interface DmRepairserviceMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmRepairservicePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmRepairservicePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param dmRepairserviceEntity 实例对象
     * @return 影响行数
     */
    int insert(DmRepairserviceEntity dmRepairserviceEntity);


    /**
     * 修改数据
     *
     * @param dmRepairserviceEntity 实例对象
     * @return 影响行数
     */
    int update(DmRepairserviceEntity dmRepairserviceEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改数据
     *
     * @param dmRepairserviceEntity 实例对象
     * @return 影响行数
     */
    int approval(DmRepairserviceEntity dmRepairserviceEntity);

    int updateDeviceState(@Param("deviceid") String deviceid, @Param("usestate") String usestate, @Param("tid") String tenantid);

    int checkDevice(@Param("deviceid") String deviceid, @Param("id") String id, @Param("tenantid") String tenantid);
}

