package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolborrowPojo;
import inks.service.std.eam.domain.pojo.DmToolborrowitemdetailPojo;
import com.github.pagehelper.PageInfo;

/**
 * 工装具借还主表(DmToolborrow)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-13 09:51:56
 */
public interface DmToolborrowService {

    DmToolborrowPojo getEntity(String key,String tid);

    PageInfo<DmToolborrowitemdetailPojo> getPageList(QueryParam queryParam);

    DmToolborrowPojo getBillEntity(String key,String tid);

    PageInfo<DmToolborrowPojo> getBillList(QueryParam queryParam);

    PageInfo<DmToolborrowPojo> getPageTh(QueryParam queryParam);

    DmToolborrowPojo insert(DmToolborrowPojo dmToolborrowPojo);

    DmToolborrowPojo update(DmToolborrowPojo dmToolborrowpojo);

    int delete(String key,String tid);

}
