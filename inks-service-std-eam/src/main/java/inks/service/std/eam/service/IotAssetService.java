package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotAssetPojo;
import com.github.pagehelper.PageInfo;

/**
 * 资产(Iot_Asset)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-14 13:01:05
 */
public interface IotAssetService {

    IotAssetPojo getEntity(String key,String tid);

    PageInfo<IotAssetPojo> getPageList(QueryParam queryParam);

    IotAssetPojo insert(IotAssetPojo iotAssetPojo);

    IotAssetPojo update(IotAssetPojo iotAssetpojo);

    int delete(String key,String tid);
}
