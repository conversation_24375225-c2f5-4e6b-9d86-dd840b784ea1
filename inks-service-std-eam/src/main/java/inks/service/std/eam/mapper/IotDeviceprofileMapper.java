package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotDeviceprofilePojo;
import inks.service.std.eam.domain.IotDeviceprofileEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 设备配置模板表(Iot_DeviceProfile)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:23
 */
@Mapper
public interface IotDeviceprofileMapper {

    IotDeviceprofilePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotDeviceprofilePojo> getPageList(QueryParam queryParam);

    int insert(IotDeviceprofileEntity iotDeviceprofileEntity);

    int update(IotDeviceprofileEntity iotDeviceprofileEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

