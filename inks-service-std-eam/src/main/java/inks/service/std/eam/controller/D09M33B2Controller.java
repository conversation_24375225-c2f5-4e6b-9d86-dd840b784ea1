package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.IotTskvlatestPojo;
import inks.service.std.eam.service.IotTskvlatestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 最新遥测数据(Iot_TsKvLatest)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-08 13:01:06
 */
@RestController
@RequestMapping("D09M33B2")
@Api(tags = "D09M33B2:最新遥测数据")
public class D09M33B2Controller extends IotTskvlatestController {
    @Resource
    private IotTskvlatestService iotTskvlatestService;
    @Resource
    private TokenService tokenService;



    @ApiOperation(value=" 获取最新时间序列遥测数据", notes="", produces="application/json")
    @RequestMapping(value="/getList",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_TsKvLatest.List")
    public R<List<IotTskvlatestPojo>> getList(String entityid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotTskvlatestService.getList(entityid, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


}
