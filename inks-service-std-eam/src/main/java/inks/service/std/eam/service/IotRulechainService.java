package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotRulechainPojo;
import com.github.pagehelper.PageInfo;

/**
 * 规则链定义表(<PERSON><PERSON>_RuleChain)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:40
 */
public interface IotRulechainService {

    IotRulechainPojo getEntity(String key,String tid);

    PageInfo<IotRulechainPojo> getPageList(QueryParam queryParam);

    IotRulechainPojo insert(IotRulechainPojo iotRulechainPojo);

    IotRulechainPojo update(IotRulechainPojo iotRulechainpojo);

    int delete(String key,String tid);

    IotRulechainPojo getRuleBillByDeviceProfile(String deviceid);

    int setRoot(String key, String tid);
}
