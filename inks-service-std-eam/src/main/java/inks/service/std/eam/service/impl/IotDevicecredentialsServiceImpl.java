package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.IotDevicecredentialsEntity;
import inks.service.std.eam.domain.pojo.IotDevicecredentialsPojo;
import inks.service.std.eam.mapper.IotDevicecredentialsMapper;
import inks.service.std.eam.service.IotDevicecredentialsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 设备鉴权信息表(IotDevicecredentials)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:19
 */
@Service("iotDevicecredentialsService")
public class IotDevicecredentialsServiceImpl implements IotDevicecredentialsService {
    @Resource
    private IotDevicecredentialsMapper iotDevicecredentialsMapper;

    @Override
    public IotDevicecredentialsPojo getEntity(String key, String tid) {
        return this.iotDevicecredentialsMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotDevicecredentialsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotDevicecredentialsPojo> lst = iotDevicecredentialsMapper.getPageList(queryParam);
            PageInfo<IotDevicecredentialsPojo> pageInfo = new PageInfo<IotDevicecredentialsPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotDevicecredentialsPojo insert(IotDevicecredentialsPojo iotDevicecredentialsPojo) {
        //初始化NULL字段
        cleanNull(iotDevicecredentialsPojo);
        IotDevicecredentialsEntity iotDevicecredentialsEntity = new IotDevicecredentialsEntity(); 
        BeanUtils.copyProperties(iotDevicecredentialsPojo,iotDevicecredentialsEntity);
          //生成雪花id
          iotDevicecredentialsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotDevicecredentialsEntity.setRevision(1);  //乐观锁
          this.iotDevicecredentialsMapper.insert(iotDevicecredentialsEntity);
        return this.getEntity(iotDevicecredentialsEntity.getId(),iotDevicecredentialsEntity.getTenantid());
    }


    @Override
    public IotDevicecredentialsPojo update(IotDevicecredentialsPojo iotDevicecredentialsPojo) {
        IotDevicecredentialsEntity iotDevicecredentialsEntity = new IotDevicecredentialsEntity(); 
        BeanUtils.copyProperties(iotDevicecredentialsPojo,iotDevicecredentialsEntity);
        this.iotDevicecredentialsMapper.update(iotDevicecredentialsEntity);
        return this.getEntity(iotDevicecredentialsEntity.getId(),iotDevicecredentialsEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotDevicecredentialsMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotDevicecredentialsPojo iotDevicecredentialsPojo) {
        if(iotDevicecredentialsPojo.getCredcode()==null) iotDevicecredentialsPojo.setCredcode("");
        if(iotDevicecredentialsPojo.getCredtype()==null) iotDevicecredentialsPojo.setCredtype("");
        if(iotDevicecredentialsPojo.getCredvalue()==null) iotDevicecredentialsPojo.setCredvalue("");
        if(iotDevicecredentialsPojo.getDeviceid()==null) iotDevicecredentialsPojo.setDeviceid("");
        if(iotDevicecredentialsPojo.getRemark()==null) iotDevicecredentialsPojo.setRemark("");
        if(iotDevicecredentialsPojo.getRownum()==null) iotDevicecredentialsPojo.setRownum(0);
        if(iotDevicecredentialsPojo.getCreateby()==null) iotDevicecredentialsPojo.setCreateby("");
        if(iotDevicecredentialsPojo.getCreatebyid()==null) iotDevicecredentialsPojo.setCreatebyid("");
        if(iotDevicecredentialsPojo.getCreatedate()==null) iotDevicecredentialsPojo.setCreatedate(new Date());
        if(iotDevicecredentialsPojo.getLister()==null) iotDevicecredentialsPojo.setLister("");
        if(iotDevicecredentialsPojo.getListerid()==null) iotDevicecredentialsPojo.setListerid("");
        if(iotDevicecredentialsPojo.getModifydate()==null) iotDevicecredentialsPojo.setModifydate(new Date());
        if(iotDevicecredentialsPojo.getCustom1()==null) iotDevicecredentialsPojo.setCustom1("");
        if(iotDevicecredentialsPojo.getCustom2()==null) iotDevicecredentialsPojo.setCustom2("");
        if(iotDevicecredentialsPojo.getCustom3()==null) iotDevicecredentialsPojo.setCustom3("");
        if(iotDevicecredentialsPojo.getCustom4()==null) iotDevicecredentialsPojo.setCustom4("");
        if(iotDevicecredentialsPojo.getCustom5()==null) iotDevicecredentialsPojo.setCustom5("");
        if(iotDevicecredentialsPojo.getCustom6()==null) iotDevicecredentialsPojo.setCustom6("");
        if(iotDevicecredentialsPojo.getCustom7()==null) iotDevicecredentialsPojo.setCustom7("");
        if(iotDevicecredentialsPojo.getCustom8()==null) iotDevicecredentialsPojo.setCustom8("");
        if(iotDevicecredentialsPojo.getCustom9()==null) iotDevicecredentialsPojo.setCustom9("");
        if(iotDevicecredentialsPojo.getCustom10()==null) iotDevicecredentialsPojo.setCustom10("");
        if(iotDevicecredentialsPojo.getTenantid()==null) iotDevicecredentialsPojo.setTenantid("");
        if(iotDevicecredentialsPojo.getTenantname()==null) iotDevicecredentialsPojo.setTenantname("");
        if(iotDevicecredentialsPojo.getRevision()==null) iotDevicecredentialsPojo.setRevision(0);
   }

}
