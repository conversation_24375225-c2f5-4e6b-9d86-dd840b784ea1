package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolborrowwsPojo;
import inks.service.std.eam.domain.DmToolborrowwsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具借还子表-加工单(DmToolborrowws)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-13 09:52:08
 */
 @Mapper
public interface DmToolborrowwsMapper {

    DmToolborrowwsPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolborrowwsPojo> getPageList(QueryParam queryParam);

    List<DmToolborrowwsPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(DmToolborrowwsEntity dmToolborrowwsEntity);

    int update(DmToolborrowwsEntity dmToolborrowwsEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

}

