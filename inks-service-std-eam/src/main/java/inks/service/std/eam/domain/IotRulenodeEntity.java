package inks.service.std.eam.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 规则链节点定义表(IotRulenode)实体类
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:43
 */
public class IotRulenodeEntity implements Serializable {
    private static final long serialVersionUID = 776271034107780113L;
     // 规则节点ID
    private String id;
     // 所属规则链ID
    private String rulechainid;
     // 附加信息
    private String additionalinfo;
     // 配置版本
    private Integer configurationversion;
     // 配置（JSON格式）
    private String configuration;
     // 节点类型
    private String rulenodetype;
     // 节点名称
    private String rulenodename;
     // 调试设置
    private String debugsettings;
     // 是否单例模式
    private Integer singletonmode;
     // 队列名称
    private String queuename;
     // 外部系统标识
    private String externalid;
     // 备注
    private String remark;
     // 顺序
    private Integer rownum;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

// 规则节点ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 所属规则链ID
    public String getRulechainid() {
        return rulechainid;
    }
    
    public void setRulechainid(String rulechainid) {
        this.rulechainid = rulechainid;
    }
        
// 附加信息
    public String getAdditionalinfo() {
        return additionalinfo;
    }
    
    public void setAdditionalinfo(String additionalinfo) {
        this.additionalinfo = additionalinfo;
    }
        
// 配置版本
    public Integer getConfigurationversion() {
        return configurationversion;
    }
    
    public void setConfigurationversion(Integer configurationversion) {
        this.configurationversion = configurationversion;
    }
        
// 配置（JSON格式）
    public String getConfiguration() {
        return configuration;
    }
    
    public void setConfiguration(String configuration) {
        this.configuration = configuration;
    }
        
// 节点类型
    public String getRulenodetype() {
        return rulenodetype;
    }
    
    public void setRulenodetype(String rulenodetype) {
        this.rulenodetype = rulenodetype;
    }
        
// 节点名称
    public String getRulenodename() {
        return rulenodename;
    }
    
    public void setRulenodename(String rulenodename) {
        this.rulenodename = rulenodename;
    }
        
// 调试设置
    public String getDebugsettings() {
        return debugsettings;
    }
    
    public void setDebugsettings(String debugsettings) {
        this.debugsettings = debugsettings;
    }
        
// 是否单例模式
    public Integer getSingletonmode() {
        return singletonmode;
    }
    
    public void setSingletonmode(Integer singletonmode) {
        this.singletonmode = singletonmode;
    }
        
// 队列名称
    public String getQueuename() {
        return queuename;
    }
    
    public void setQueuename(String queuename) {
        this.queuename = queuename;
    }
        
// 外部系统标识
    public String getExternalid() {
        return externalid;
    }
    
    public void setExternalid(String externalid) {
        this.externalid = externalid;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 顺序
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

