package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmPatrolplanPojo;

/**
 * 巡检计划(DmPatrolplan)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 13:08:47
 */
public interface DmPatrolplanService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolplanPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolplanPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmPatrolplanPojo 实例对象
     * @return 实例对象
     */
    DmPatrolplanPojo insert(DmPatrolplanPojo dmPatrolplanPojo);

    /**
     * 修改数据
     *
     * @param dmPatrolplanpojo 实例对象
     * @return 实例对象
     */
    DmPatrolplanPojo update(DmPatrolplanPojo dmPatrolplanpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param dmPatrolplanPojo 实例对象
     * @return 实例对象
     */
    DmPatrolplanPojo approval(DmPatrolplanPojo dmPatrolplanPojo);
}
