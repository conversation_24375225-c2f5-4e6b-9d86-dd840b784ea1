package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmToolusageitemEntity;
import inks.service.std.eam.domain.pojo.DmToolusageitemPojo;
import inks.service.std.eam.mapper.DmToolusageitemMapper;
import inks.service.std.eam.service.DmToolusageitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 工装具使用记录子表(DmToolusageitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:35
 */
@Service("dmToolusageitemService")
public class DmToolusageitemServiceImpl implements DmToolusageitemService {
    @Resource
    private DmToolusageitemMapper dmToolusageitemMapper;

    @Override
    public DmToolusageitemPojo getEntity(String key,String tid) {
        return this.dmToolusageitemMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<DmToolusageitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolusageitemPojo> lst = dmToolusageitemMapper.getPageList(queryParam);
            PageInfo<DmToolusageitemPojo> pageInfo = new PageInfo<DmToolusageitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<DmToolusageitemPojo> getList(String Pid,String tid) { 
        try {
            List<DmToolusageitemPojo> lst = dmToolusageitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public DmToolusageitemPojo insert(DmToolusageitemPojo dmToolusageitemPojo) {
        //初始化item的NULL
        DmToolusageitemPojo itempojo =this.clearNull(dmToolusageitemPojo);
        DmToolusageitemEntity dmToolusageitemEntity = new DmToolusageitemEntity(); 
        BeanUtils.copyProperties(itempojo,dmToolusageitemEntity);
          //生成雪花id
          dmToolusageitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          dmToolusageitemEntity.setRevision(1);  //乐观锁      
          this.dmToolusageitemMapper.insert(dmToolusageitemEntity);
        return this.getEntity(dmToolusageitemEntity.getId(),dmToolusageitemEntity.getTenantid());
  
    }

    @Override
    public DmToolusageitemPojo update(DmToolusageitemPojo dmToolusageitemPojo) {
        DmToolusageitemEntity dmToolusageitemEntity = new DmToolusageitemEntity(); 
        BeanUtils.copyProperties(dmToolusageitemPojo,dmToolusageitemEntity);
        this.dmToolusageitemMapper.update(dmToolusageitemEntity);
        return this.getEntity(dmToolusageitemEntity.getId(),dmToolusageitemEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.dmToolusageitemMapper.delete(key,tid) ;
    }

     @Override
     public DmToolusageitemPojo clearNull(DmToolusageitemPojo dmToolusageitemPojo){
     //初始化NULL字段
     if(dmToolusageitemPojo.getPid()==null) dmToolusageitemPojo.setPid("");
     if(dmToolusageitemPojo.getToolid()==null) dmToolusageitemPojo.setToolid("");
     if(dmToolusageitemPojo.getQuantity()==null) dmToolusageitemPojo.setQuantity(0);
     if(dmToolusageitemPojo.getWorkid()==null) dmToolusageitemPojo.setWorkid("");
     if(dmToolusageitemPojo.getWorkuid()==null) dmToolusageitemPojo.setWorkuid("");
     if(dmToolusageitemPojo.getUssgedate()==null) dmToolusageitemPojo.setUssgedate(new Date());
     if(dmToolusageitemPojo.getRownum()==null) dmToolusageitemPojo.setRownum(0);
     if(dmToolusageitemPojo.getRemark()==null) dmToolusageitemPojo.setRemark("");
     if(dmToolusageitemPojo.getCustom1()==null) dmToolusageitemPojo.setCustom1("");
     if(dmToolusageitemPojo.getCustom2()==null) dmToolusageitemPojo.setCustom2("");
     if(dmToolusageitemPojo.getCustom3()==null) dmToolusageitemPojo.setCustom3("");
     if(dmToolusageitemPojo.getCustom4()==null) dmToolusageitemPojo.setCustom4("");
     if(dmToolusageitemPojo.getCustom5()==null) dmToolusageitemPojo.setCustom5("");
     if(dmToolusageitemPojo.getCustom6()==null) dmToolusageitemPojo.setCustom6("");
     if(dmToolusageitemPojo.getCustom7()==null) dmToolusageitemPojo.setCustom7("");
     if(dmToolusageitemPojo.getCustom8()==null) dmToolusageitemPojo.setCustom8("");
     if(dmToolusageitemPojo.getCustom9()==null) dmToolusageitemPojo.setCustom9("");
     if(dmToolusageitemPojo.getCustom10()==null) dmToolusageitemPojo.setCustom10("");
     if(dmToolusageitemPojo.getDeptid()==null) dmToolusageitemPojo.setDeptid("");
     if(dmToolusageitemPojo.getTenantid()==null) dmToolusageitemPojo.setTenantid("");
     if(dmToolusageitemPojo.getTenantname()==null) dmToolusageitemPojo.setTenantname("");
     if(dmToolusageitemPojo.getRevision()==null) dmToolusageitemPojo.setRevision(0);
     return dmToolusageitemPojo;
     }
}
