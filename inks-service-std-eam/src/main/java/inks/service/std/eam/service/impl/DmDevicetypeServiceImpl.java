package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmDevicetypeEntity;
import inks.service.std.eam.domain.pojo.DmDevicetypePojo;
import inks.service.std.eam.mapper.DmDevicetypeMapper;
import inks.service.std.eam.service.DmDevicetypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 设备分类(DmDevicetype)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-28 09:32:53
 */
@Service("dmDevicetypeService")
public class DmDevicetypeServiceImpl implements DmDevicetypeService {
    @Resource
    private DmDevicetypeMapper dmDevicetypeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmDevicetypePojo getEntity(String key, String tid) {
        return this.dmDevicetypeMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmDevicetypePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmDevicetypePojo> lst = dmDevicetypeMapper.getPageList(queryParam);
            PageInfo<DmDevicetypePojo> pageInfo = new PageInfo<DmDevicetypePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmDevicetypePojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmDevicetypePojo insert(DmDevicetypePojo dmDevicetypePojo) {
        //初始化NULL字段
        if (dmDevicetypePojo.getTypecode() == null) dmDevicetypePojo.setTypecode("");
        if (dmDevicetypePojo.getTypename() == null) dmDevicetypePojo.setTypename("");
        if (dmDevicetypePojo.getParentid() == null) dmDevicetypePojo.setParentid("");
        if (dmDevicetypePojo.getAncestors() == null) dmDevicetypePojo.setAncestors("");
        if (dmDevicetypePojo.getEnabledmark() == null) dmDevicetypePojo.setEnabledmark(0);
        if (dmDevicetypePojo.getRownum() == null) dmDevicetypePojo.setRownum(0);
        if (dmDevicetypePojo.getRemark() == null) dmDevicetypePojo.setRemark("");
        if (dmDevicetypePojo.getPrefix() == null) dmDevicetypePojo.setPrefix("");
        if (dmDevicetypePojo.getSuffix() == null) dmDevicetypePojo.setSuffix("");
        if (dmDevicetypePojo.getSncode() == null) dmDevicetypePojo.setSncode("");
        if (dmDevicetypePojo.getAllowitem() == null) dmDevicetypePojo.setAllowitem(0);
        if (dmDevicetypePojo.getStatecode() == null) dmDevicetypePojo.setStatecode("");
        if (dmDevicetypePojo.getGroupsvg() == null) dmDevicetypePojo.setGroupsvg("");
        if (dmDevicetypePojo.getCustom1() == null) dmDevicetypePojo.setCustom1("");
        if (dmDevicetypePojo.getCustom2() == null) dmDevicetypePojo.setCustom2("");
        if (dmDevicetypePojo.getCustom3() == null) dmDevicetypePojo.setCustom3("");
        if (dmDevicetypePojo.getCustom4() == null) dmDevicetypePojo.setCustom4("");
        if (dmDevicetypePojo.getCustom5() == null) dmDevicetypePojo.setCustom5("");
        if (dmDevicetypePojo.getCustom6() == null) dmDevicetypePojo.setCustom6("");
        if (dmDevicetypePojo.getCustom7() == null) dmDevicetypePojo.setCustom7("");
        if (dmDevicetypePojo.getCustom8() == null) dmDevicetypePojo.setCustom8("");
        if (dmDevicetypePojo.getLister() == null) dmDevicetypePojo.setLister("");
        if (dmDevicetypePojo.getListerid() == null) dmDevicetypePojo.setListerid("");
        if (dmDevicetypePojo.getCreatedate() == null) dmDevicetypePojo.setCreatedate(new Date());
        if (dmDevicetypePojo.getCreateby() == null) dmDevicetypePojo.setCreateby("");
        if (dmDevicetypePojo.getCreatebyid() == null) dmDevicetypePojo.setCreatebyid("");
        if (dmDevicetypePojo.getModifydate() == null) dmDevicetypePojo.setModifydate(new Date());
        if (dmDevicetypePojo.getTenantid() == null) dmDevicetypePojo.setTenantid("");
        if (dmDevicetypePojo.getRevision() == null) dmDevicetypePojo.setRevision(0);
        if (dmDevicetypePojo.getChildcount() == null) dmDevicetypePojo.setChildcount(0);
        DmDevicetypeEntity dmDevicetypeEntity = new DmDevicetypeEntity();
        BeanUtils.copyProperties(dmDevicetypePojo, dmDevicetypeEntity);
        //生成雪花id
        dmDevicetypeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmDevicetypeEntity.setRevision(1);  //乐观锁
        this.dmDevicetypeMapper.insert(dmDevicetypeEntity);
        return this.getEntity(dmDevicetypeEntity.getId(), dmDevicetypeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmDevicetypePojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmDevicetypePojo update(DmDevicetypePojo dmDevicetypePojo) {
        DmDevicetypeEntity dmDevicetypeEntity = new DmDevicetypeEntity();
        BeanUtils.copyProperties(dmDevicetypePojo, dmDevicetypeEntity);
        this.dmDevicetypeMapper.update(dmDevicetypeEntity);
        return this.getEntity(dmDevicetypeEntity.getId(), dmDevicetypeEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmDevicetypeMapper.delete(key, tid);
    }


}
