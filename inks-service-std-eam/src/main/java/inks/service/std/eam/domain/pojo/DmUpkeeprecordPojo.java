package inks.service.std.eam.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 保养实施(DmUpkeeprecord)实体类
 *
 * <AUTHOR>
 * @since 2023-06-09 15:29:20
 */
public class DmUpkeeprecordPojo implements Serializable {
    private static final long serialVersionUID = 974436383625999360L;
    // id
    @Excel(name = "id")
    private String id;
    // 流水号
    @Excel(name = "流水号")
    private String refno;
    // 单据日期
    @Excel(name = "单据日期")
    private Date billdate;
    // 类型
    @Excel(name = "类型")
    private String billtype;
    // 报修主题
    @Excel(name = "报修主题")
    private String billtitle;
    // 计划id
    @Excel(name = "计划id")
    private String planid;
    // 计划时间
    @Excel(name = "计划时间")
    private Date plandate;
    // 保养级别
    @Excel(name = "保养级别")
    private String itemlevel;
    // 保养名称
    @Excel(name = "保养名称")
    private String itemname;
    // 用时
    @Excel(name = "用时")
    private Double worktime;
    // 操作员
    @Excel(name = "操作员")
    private String operator;
    // 过程描述
    @Excel(name = "过程描述")
    private String solution;
    // 子表数
    @Excel(name = "子表数")
    private Integer itemcount;
    // 完成数
    @Excel(name = "完成数")
    private Integer finishcount;
    // 简述
    @Excel(name = "简述")
    private String summary;
    // 产线确认人
    @Excel(name = "产线确认人")
    private String confirmer;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 审核员id
    @Excel(name = "审核员id")
    private String assessorid;
    // 审核员
    @Excel(name = "审核员")
    private String assessor;
    // 审核日期
    @Excel(name = "审核日期")
    private Date assessdate;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<DmUpkeeprecorditemPojo> item;
    // 备件子表
    private List<DmUpkeeprecordsparePojo> spare;
    // 这里的planid关联的保养方案的子表List
    private List<DmUpkeepflowitemPojo> flowitem;

    // flowname 方案名称
    private String flowname;
    // flowid 方案id
    private String flowid;

    public List<DmUpkeepflowitemPojo> getFlowitem() {
        return flowitem;
    }

    public void setFlowitem(List<DmUpkeepflowitemPojo> flowitem) {
        this.flowitem = flowitem;
    }

    public String getFlowid() {
        return flowid;
    }

    public void setFlowid(String flowid) {
        this.flowid = flowid;
    }

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 流水号
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 单据日期
    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }

    // 类型
    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }

    // 报修主题
    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }

    public String getConfirmer() {
        return confirmer;
    }

    public void setConfirmer(String confirmer) {
        this.confirmer = confirmer;
    }

    // 计划id
    public String getPlanid() {
        return planid;
    }

    public void setPlanid(String planid) {
        this.planid = planid;
    }

    // 计划时间
    public Date getPlandate() {
        return plandate;
    }

    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }

    // 保养级别
    public String getItemlevel() {
        return itemlevel;
    }

    public void setItemlevel(String itemlevel) {
        this.itemlevel = itemlevel;
    }

    public String getFlowname() {
        return flowname;
    }

    public void setFlowname(String flowname) {
        this.flowname = flowname;
    }

    // 保养名称
    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    // 用时
    public Double getWorktime() {
        return worktime;
    }

    public void setWorktime(Double worktime) {
        this.worktime = worktime;
    }

    // 操作员
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    // 过程描述
    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    // 子表数
    public Integer getItemcount() {
        return itemcount;
    }

    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }

    // 完成数
    public Integer getFinishcount() {
        return finishcount;
    }

    public void setFinishcount(Integer finishcount) {
        this.finishcount = finishcount;
    }

    // 简述
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 审核员id
    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }

    // 审核员
    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }

    // 审核日期
    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


    public List<DmUpkeeprecorditemPojo> getItem() {
        return item;
    }

    public void setItem(List<DmUpkeeprecorditemPojo> item) {
        this.item = item;
    }

    public List<DmUpkeeprecordsparePojo> getSpare() {
        return spare;
    }

    public void setSpare(List<DmUpkeeprecordsparePojo> spare) {
        this.spare = spare;
    }
}

