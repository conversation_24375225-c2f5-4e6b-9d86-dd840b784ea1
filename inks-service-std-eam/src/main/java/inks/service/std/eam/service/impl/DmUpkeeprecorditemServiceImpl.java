package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmUpkeeprecorditemEntity;
import inks.service.std.eam.domain.pojo.DmUpkeeprecorditemPojo;
import inks.service.std.eam.mapper.DmUpkeeprecorditemMapper;
import inks.service.std.eam.service.DmUpkeeprecorditemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 实施备件(DmUpkeeprecorditem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-06 15:43:03
 */
@Service("dmUpkeeprecorditemService")
public class DmUpkeeprecorditemServiceImpl implements DmUpkeeprecorditemService {
    @Resource
    private DmUpkeeprecorditemMapper dmUpkeeprecorditemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmUpkeeprecorditemPojo getEntity(String key, String tid) {
        return this.dmUpkeeprecorditemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeeprecorditemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeeprecorditemPojo> lst = dmUpkeeprecorditemMapper.getPageList(queryParam);
            PageInfo<DmUpkeeprecorditemPojo> pageInfo = new PageInfo<DmUpkeeprecorditemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DmUpkeeprecorditemPojo> getList(String Pid, String tid) {
        try {
            List<DmUpkeeprecorditemPojo> lst = dmUpkeeprecorditemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param dmUpkeeprecorditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeeprecorditemPojo insert(DmUpkeeprecorditemPojo dmUpkeeprecorditemPojo) {
        //初始化item的NULL
        DmUpkeeprecorditemPojo itempojo = this.clearNull(dmUpkeeprecorditemPojo);
        DmUpkeeprecorditemEntity dmUpkeeprecorditemEntity = new DmUpkeeprecorditemEntity();
        BeanUtils.copyProperties(itempojo, dmUpkeeprecorditemEntity);
        //生成雪花id
        dmUpkeeprecorditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmUpkeeprecorditemEntity.setRevision(1);  //乐观锁
        this.dmUpkeeprecorditemMapper.insert(dmUpkeeprecorditemEntity);
        return this.getEntity(dmUpkeeprecorditemEntity.getId(), dmUpkeeprecorditemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmUpkeeprecorditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeeprecorditemPojo update(DmUpkeeprecorditemPojo dmUpkeeprecorditemPojo) {
        DmUpkeeprecorditemEntity dmUpkeeprecorditemEntity = new DmUpkeeprecorditemEntity();
        BeanUtils.copyProperties(dmUpkeeprecorditemPojo, dmUpkeeprecorditemEntity);
        this.dmUpkeeprecorditemMapper.update(dmUpkeeprecorditemEntity);
        return this.getEntity(dmUpkeeprecorditemEntity.getId(), dmUpkeeprecorditemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmUpkeeprecorditemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param dmUpkeeprecorditemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeeprecorditemPojo clearNull(DmUpkeeprecorditemPojo dmUpkeeprecorditemPojo) {
        //初始化NULL字段
        if (dmUpkeeprecorditemPojo.getPid() == null) dmUpkeeprecorditemPojo.setPid("");
        if (dmUpkeeprecorditemPojo.getDeviceid() == null) dmUpkeeprecorditemPojo.setDeviceid("");
        if (dmUpkeeprecorditemPojo.getRemark() == null) dmUpkeeprecorditemPojo.setRemark("");
        if (dmUpkeeprecorditemPojo.getRownum() == null) dmUpkeeprecorditemPojo.setRownum(0);
        if (dmUpkeeprecorditemPojo.getExpirydate() == null) dmUpkeeprecorditemPojo.setExpirydate(new Date());
        if (dmUpkeeprecorditemPojo.getFinishmark() == null) dmUpkeeprecorditemPojo.setFinishmark(0);
        if (dmUpkeeprecorditemPojo.getRecordid() == null) dmUpkeeprecorditemPojo.setRecordid("");
        if (dmUpkeeprecorditemPojo.getRecordby() == null) dmUpkeeprecorditemPojo.setRecordby("");
        if (dmUpkeeprecorditemPojo.getRecorddate() == null) dmUpkeeprecorditemPojo.setRecorddate(new Date());
        if (dmUpkeeprecorditemPojo.getCustom1() == null) dmUpkeeprecorditemPojo.setCustom1("");
        if (dmUpkeeprecorditemPojo.getCustom2() == null) dmUpkeeprecorditemPojo.setCustom2("");
        if (dmUpkeeprecorditemPojo.getCustom3() == null) dmUpkeeprecorditemPojo.setCustom3("");
        if (dmUpkeeprecorditemPojo.getCustom4() == null) dmUpkeeprecorditemPojo.setCustom4("");
        if (dmUpkeeprecorditemPojo.getCustom5() == null) dmUpkeeprecorditemPojo.setCustom5("");
        if (dmUpkeeprecorditemPojo.getCustom6() == null) dmUpkeeprecorditemPojo.setCustom6("");
        if (dmUpkeeprecorditemPojo.getCustom7() == null) dmUpkeeprecorditemPojo.setCustom7("");
        if (dmUpkeeprecorditemPojo.getCustom8() == null) dmUpkeeprecorditemPojo.setCustom8("");
        if (dmUpkeeprecorditemPojo.getTenantid() == null) dmUpkeeprecorditemPojo.setTenantid("");
        if (dmUpkeeprecorditemPojo.getRevision() == null) dmUpkeeprecorditemPojo.setRevision(0);
        return dmUpkeeprecorditemPojo;
    }
}
