package inks.service.std.eam.mqtt.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * MQTT WebSocket处理器 - 用于实时推送消息到前端
 * 
 * <AUTHOR>
 */
@Component
public class MqttWebSocketHandler implements WebSocketHandler {
    
    private static final CopyOnWriteArraySet<WebSocketSession> sessions = new CopyOnWriteArraySet<>();
    private static final Map<String, WebSocketSession> sessionMap = new ConcurrentHashMap<>();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        sessions.add(session);
        sessionMap.put(session.getId(), session);

        System.out.println("=== WebSocket连接建立 ===");
        System.out.println("Session ID: " + session.getId());
        System.out.println("Remote Address: " + session.getRemoteAddress());
        System.out.println("URI: " + session.getUri());
        System.out.println("当前连接数: " + sessions.size());
        System.out.println("========================");

        // 发送连接成功消息
        Map<String, Object> message = new HashMap<>();
        message.put("type", "connection");
        message.put("status", "connected");
        message.put("sessionId", session.getId());
        message.put("timestamp", System.currentTimeMillis());
        message.put("message", "WebSocket连接已建立");
        sendMessage(session, message);
    }
    
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String payload = message.getPayload().toString();
        System.out.println("收到WebSocket消息: " + payload);
        
        try {
            // 解析客户端消息
            Map<String, Object> messageData = objectMapper.readValue(payload, Map.class);
            String type = (String) messageData.get("type");
            
            switch (type) {
                case "subscribe":
                    handleSubscribeMessage(session, messageData);
                    break;
                case "unsubscribe":
                    handleUnsubscribeMessage(session, messageData);
                    break;
                case "ping":
                    handlePingMessage(session);
                    break;
                default:
                    sendErrorMessage(session, "Unknown message type: " + type);
            }
        } catch (Exception e) {
            System.err.println("处理WebSocket消息失败: " + e.getMessage());
            sendErrorMessage(session, "Invalid message format");
        }
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        System.err.println("WebSocket传输错误: " + exception.getMessage());
        sessions.remove(session);
        sessionMap.remove(session.getId());
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        sessions.remove(session);
        sessionMap.remove(session.getId());

        System.out.println("=== WebSocket连接关闭 ===");
        System.out.println("Session ID: " + session.getId());
        System.out.println("关闭状态: " + closeStatus);
        System.out.println("剩余连接数: " + sessions.size());
        System.out.println("========================");
    }
    
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
    
    /**
     * 处理订阅消息
     */
    private void handleSubscribeMessage(WebSocketSession session, Map<String, Object> messageData) {
        String topic = (String) messageData.get("topic");
        if (topic != null) {
            // 这里可以记录客户端订阅的主题
            session.getAttributes().put("subscribedTopic", topic);
            
            Map<String, Object> message = new HashMap<>();
            message.put("type", "subscribe_response");
            message.put("status", "success");
            message.put("topic", topic);
            message.put("message", "已订阅主题: " + topic);
            message.put("timestamp", System.currentTimeMillis());
            sendMessage(session, message);
        }
    }
    
    /**
     * 处理取消订阅消息
     */
    private void handleUnsubscribeMessage(WebSocketSession session, Map<String, Object> messageData) {
        String topic = (String) messageData.get("topic");
        if (topic != null) {
            session.getAttributes().remove("subscribedTopic");
            
            Map<String, Object> message = new HashMap<>();
            message.put("type", "unsubscribe_response");
            message.put("status", "success");
            message.put("topic", topic);
            message.put("message", "已取消订阅主题: " + topic);
            message.put("timestamp", System.currentTimeMillis());
            sendMessage(session, message);
        }
    }
    
    /**
     * 处理心跳消息
     */
    private void handlePingMessage(WebSocketSession session) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "pong");
        message.put("timestamp", System.currentTimeMillis());
        sendMessage(session, message);
    }
    
    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String error) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "error");
        message.put("message", error);
        message.put("timestamp", System.currentTimeMillis());
        sendMessage(session, message);
    }
    
    /**
     * 发送消息到指定会话
     */
    private void sendMessage(WebSocketSession session, Object message) {
        try {
            if (session.isOpen()) {
                String jsonMessage = objectMapper.writeValueAsString(message);
                session.sendMessage(new TextMessage(jsonMessage));
            }
        } catch (IOException e) {
            System.err.println("发送WebSocket消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 广播MQTT消息到所有WebSocket客户端
     */
    public void broadcastMqttMessage(String clientId, String topic, String payload) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "mqtt_message");
        message.put("clientId", clientId);
        message.put("topic", topic);
        message.put("payload", payload);
        message.put("timestamp", System.currentTimeMillis());
        
        sessions.forEach(session -> {
            if (session.isOpen()) {
                // 检查客户端是否订阅了这个主题
                String subscribedTopic = (String) session.getAttributes().get("subscribedTopic");
                if (subscribedTopic == null || topicMatches(topic, subscribedTopic)) {
                    sendMessage(session, message);
                }
            }
        });
    }
    
    /**
     * 广播统计信息更新
     */
    public void broadcastStatsUpdate(Map<String, Object> stats) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "stats_update");
        message.put("data", stats);
        message.put("timestamp", System.currentTimeMillis());
        
        sessions.forEach(session -> {
            if (session.isOpen()) {
                sendMessage(session, message);
            }
        });
    }
    
    /**
     * 简单的主题匹配（支持通配符）
     */
    private boolean topicMatches(String actualTopic, String subscribedTopic) {
        if (subscribedTopic.equals("#")) {
            return true;
        }
        
        if (subscribedTopic.contains("+") || subscribedTopic.contains("#")) {
            // 简化的通配符匹配
            String pattern = subscribedTopic
                .replace("+", "[^/]+")
                .replace("#", ".*");
            return actualTopic.matches(pattern);
        }
        
        return actualTopic.equals(subscribedTopic);
    }
    
    /**
     * 获取活跃连接数
     */
    public int getActiveConnectionCount() {
        return sessions.size();
    }
}
