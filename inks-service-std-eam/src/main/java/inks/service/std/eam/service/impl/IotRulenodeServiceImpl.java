package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.IotRulenodeEntity;
import inks.service.std.eam.domain.pojo.IotRulenodePojo;
import inks.service.std.eam.mapper.IotRulenodeMapper;
import inks.service.std.eam.service.IotRulenodeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 规则链节点定义表(IotRulenode)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:47
 */
@Service("iotRulenodeService")
public class IotRulenodeServiceImpl implements IotRulenodeService {
    @Resource
    private IotRulenodeMapper iotRulenodeMapper;

    @Override
    public IotRulenodePojo getEntity(String key, String tid) {
        return this.iotRulenodeMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotRulenodePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotRulenodePojo> lst = iotRulenodeMapper.getPageList(queryParam);
            PageInfo<IotRulenodePojo> pageInfo = new PageInfo<IotRulenodePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotRulenodePojo insert(IotRulenodePojo iotRulenodePojo) {
        //初始化NULL字段
        cleanNull(iotRulenodePojo);
        IotRulenodeEntity iotRulenodeEntity = new IotRulenodeEntity(); 
        BeanUtils.copyProperties(iotRulenodePojo,iotRulenodeEntity);
          //生成雪花id
          iotRulenodeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotRulenodeEntity.setRevision(1);  //乐观锁
          this.iotRulenodeMapper.insert(iotRulenodeEntity);
        return this.getEntity(iotRulenodeEntity.getId(),iotRulenodeEntity.getTenantid());
    }


    @Override
    public IotRulenodePojo update(IotRulenodePojo iotRulenodePojo) {
        IotRulenodeEntity iotRulenodeEntity = new IotRulenodeEntity(); 
        BeanUtils.copyProperties(iotRulenodePojo,iotRulenodeEntity);
        this.iotRulenodeMapper.update(iotRulenodeEntity);
        return this.getEntity(iotRulenodeEntity.getId(),iotRulenodeEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotRulenodeMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotRulenodePojo iotRulenodePojo) {
        if(iotRulenodePojo.getRulechainid()==null) iotRulenodePojo.setRulechainid("");
        if(iotRulenodePojo.getAdditionalinfo()==null) iotRulenodePojo.setAdditionalinfo("");
        if(iotRulenodePojo.getConfigurationversion()==null) iotRulenodePojo.setConfigurationversion(0);
        if(iotRulenodePojo.getConfiguration()==null) iotRulenodePojo.setConfiguration("");
        if(iotRulenodePojo.getRulenodetype()==null) iotRulenodePojo.setRulenodetype("");
        if(iotRulenodePojo.getRulenodename()==null) iotRulenodePojo.setRulenodename("");
        if(iotRulenodePojo.getDebugsettings()==null) iotRulenodePojo.setDebugsettings("");
        if(iotRulenodePojo.getSingletonmode()==null) iotRulenodePojo.setSingletonmode(0);
        if(iotRulenodePojo.getQueuename()==null) iotRulenodePojo.setQueuename("");
        if(iotRulenodePojo.getExternalid()==null) iotRulenodePojo.setExternalid("");
        if(iotRulenodePojo.getRemark()==null) iotRulenodePojo.setRemark("");
        if(iotRulenodePojo.getRownum()==null) iotRulenodePojo.setRownum(0);
        if(iotRulenodePojo.getCreateby()==null) iotRulenodePojo.setCreateby("");
        if(iotRulenodePojo.getCreatebyid()==null) iotRulenodePojo.setCreatebyid("");
        if(iotRulenodePojo.getCreatedate()==null) iotRulenodePojo.setCreatedate(new Date());
        if(iotRulenodePojo.getLister()==null) iotRulenodePojo.setLister("");
        if(iotRulenodePojo.getListerid()==null) iotRulenodePojo.setListerid("");
        if(iotRulenodePojo.getModifydate()==null) iotRulenodePojo.setModifydate(new Date());
        if(iotRulenodePojo.getCustom1()==null) iotRulenodePojo.setCustom1("");
        if(iotRulenodePojo.getCustom2()==null) iotRulenodePojo.setCustom2("");
        if(iotRulenodePojo.getCustom3()==null) iotRulenodePojo.setCustom3("");
        if(iotRulenodePojo.getCustom4()==null) iotRulenodePojo.setCustom4("");
        if(iotRulenodePojo.getCustom5()==null) iotRulenodePojo.setCustom5("");
        if(iotRulenodePojo.getCustom6()==null) iotRulenodePojo.setCustom6("");
        if(iotRulenodePojo.getCustom7()==null) iotRulenodePojo.setCustom7("");
        if(iotRulenodePojo.getCustom8()==null) iotRulenodePojo.setCustom8("");
        if(iotRulenodePojo.getCustom9()==null) iotRulenodePojo.setCustom9("");
        if(iotRulenodePojo.getCustom10()==null) iotRulenodePojo.setCustom10("");
        if(iotRulenodePojo.getTenantid()==null) iotRulenodePojo.setTenantid("");
        if(iotRulenodePojo.getTenantname()==null) iotRulenodePojo.setTenantname("");
        if(iotRulenodePojo.getRevision()==null) iotRulenodePojo.setRevision(0);
   }


    @Override
    public List<IotRulenodePojo> getListByChainid(String chainid, String tenantid) {
        return iotRulenodeMapper.getListByRulechainId(chainid);
    }
}
