package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmToolinspectitemEntity;
import inks.service.std.eam.domain.pojo.DmToolinspectitemPojo;
import inks.service.std.eam.mapper.DmToolinspectitemMapper;
import inks.service.std.eam.service.DmToolinspectitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 工装具校验记录子表(DmToolinspectitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-19 10:17:17
 */
@Service("dmToolinspectitemService")
public class DmToolinspectitemServiceImpl implements DmToolinspectitemService {
    @Resource
    private DmToolinspectitemMapper dmToolinspectitemMapper;

    @Override
    public DmToolinspectitemPojo getEntity(String key,String tid) {
        return this.dmToolinspectitemMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<DmToolinspectitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolinspectitemPojo> lst = dmToolinspectitemMapper.getPageList(queryParam);
            PageInfo<DmToolinspectitemPojo> pageInfo = new PageInfo<DmToolinspectitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<DmToolinspectitemPojo> getList(String Pid,String tid) { 
        try {
            List<DmToolinspectitemPojo> lst = dmToolinspectitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public DmToolinspectitemPojo insert(DmToolinspectitemPojo dmToolinspectitemPojo) {
        //初始化item的NULL
        DmToolinspectitemPojo itempojo =this.clearNull(dmToolinspectitemPojo);
        DmToolinspectitemEntity dmToolinspectitemEntity = new DmToolinspectitemEntity(); 
        BeanUtils.copyProperties(itempojo,dmToolinspectitemEntity);
          //生成雪花id
          dmToolinspectitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          dmToolinspectitemEntity.setRevision(1);  //乐观锁      
          this.dmToolinspectitemMapper.insert(dmToolinspectitemEntity);
        return this.getEntity(dmToolinspectitemEntity.getId(),dmToolinspectitemEntity.getTenantid());
  
    }

    @Override
    public DmToolinspectitemPojo update(DmToolinspectitemPojo dmToolinspectitemPojo) {
        DmToolinspectitemEntity dmToolinspectitemEntity = new DmToolinspectitemEntity(); 
        BeanUtils.copyProperties(dmToolinspectitemPojo,dmToolinspectitemEntity);
        this.dmToolinspectitemMapper.update(dmToolinspectitemEntity);
        return this.getEntity(dmToolinspectitemEntity.getId(),dmToolinspectitemEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.dmToolinspectitemMapper.delete(key,tid) ;
    }

     @Override
     public DmToolinspectitemPojo clearNull(DmToolinspectitemPojo dmToolinspectitemPojo){
     //初始化NULL字段
     if(dmToolinspectitemPojo.getPid()==null) dmToolinspectitemPojo.setPid("");
     if(dmToolinspectitemPojo.getToolid()==null) dmToolinspectitemPojo.setToolid("");
     if(dmToolinspectitemPojo.getResult()==null) dmToolinspectitemPojo.setResult("");
     if(dmToolinspectitemPojo.getMeasvalue()==null) dmToolinspectitemPojo.setMeasvalue(0D);
     //if(dmToolinspectitemPojo.getCompdate()==null) dmToolinspectitemPojo.setCompdate(new Date());
     if(dmToolinspectitemPojo.getEquipused()==null) dmToolinspectitemPojo.setEquipused("");
     if(dmToolinspectitemPojo.getProcref()==null) dmToolinspectitemPojo.setProcref("");
     if(dmToolinspectitemPojo.getNchandling()==null) dmToolinspectitemPojo.setNchandling("");
     if(dmToolinspectitemPojo.getRownum()==null) dmToolinspectitemPojo.setRownum(0);
     if(dmToolinspectitemPojo.getRemark()==null) dmToolinspectitemPojo.setRemark("");
     if(dmToolinspectitemPojo.getCustom1()==null) dmToolinspectitemPojo.setCustom1("");
     if(dmToolinspectitemPojo.getCustom2()==null) dmToolinspectitemPojo.setCustom2("");
     if(dmToolinspectitemPojo.getCustom3()==null) dmToolinspectitemPojo.setCustom3("");
     if(dmToolinspectitemPojo.getCustom4()==null) dmToolinspectitemPojo.setCustom4("");
     if(dmToolinspectitemPojo.getCustom5()==null) dmToolinspectitemPojo.setCustom5("");
     if(dmToolinspectitemPojo.getCustom6()==null) dmToolinspectitemPojo.setCustom6("");
     if(dmToolinspectitemPojo.getCustom7()==null) dmToolinspectitemPojo.setCustom7("");
     if(dmToolinspectitemPojo.getCustom8()==null) dmToolinspectitemPojo.setCustom8("");
     if(dmToolinspectitemPojo.getCustom9()==null) dmToolinspectitemPojo.setCustom9("");
     if(dmToolinspectitemPojo.getCustom10()==null) dmToolinspectitemPojo.setCustom10("");
     if(dmToolinspectitemPojo.getTenantid()==null) dmToolinspectitemPojo.setTenantid("");
     if(dmToolinspectitemPojo.getTenantname()==null) dmToolinspectitemPojo.setTenantname("");
     if(dmToolinspectitemPojo.getRevision()==null) dmToolinspectitemPojo.setRevision(0);
     return dmToolinspectitemPojo;
     }
}
