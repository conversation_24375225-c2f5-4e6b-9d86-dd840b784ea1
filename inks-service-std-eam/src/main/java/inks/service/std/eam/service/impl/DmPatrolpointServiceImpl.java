package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmPatrolpointEntity;
import inks.service.std.eam.domain.pojo.DmPatrolpointPojo;
import inks.service.std.eam.mapper.DmPatrolpointMapper;
import inks.service.std.eam.service.DmPatrolpointService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 巡检点(DmPatrolpoint)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 13:18:38
 */
@Service("dmPatrolpointService")
public class DmPatrolpointServiceImpl implements DmPatrolpointService {
    @Resource
    private DmPatrolpointMapper dmPatrolpointMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmPatrolpointPojo getEntity(String key, String tid) {
        return this.dmPatrolpointMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmPatrolpointPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmPatrolpointPojo> lst = dmPatrolpointMapper.getPageList(queryParam);
            PageInfo<DmPatrolpointPojo> pageInfo = new PageInfo<DmPatrolpointPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmPatrolpointPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolpointPojo insert(DmPatrolpointPojo dmPatrolpointPojo) {
        //初始化NULL字段
        if (dmPatrolpointPojo.getPointgroupid() == null) dmPatrolpointPojo.setPointgroupid("");
        if (dmPatrolpointPojo.getPointcode() == null) dmPatrolpointPojo.setPointcode("");
        if (dmPatrolpointPojo.getPointname() == null) dmPatrolpointPojo.setPointname("");
        if (dmPatrolpointPojo.getStdid() == null) dmPatrolpointPojo.setStdid("");
        if (dmPatrolpointPojo.getStdcode() == null) dmPatrolpointPojo.setStdcode("");
        if (dmPatrolpointPojo.getStdname() == null) dmPatrolpointPojo.setStdname("");
        if (dmPatrolpointPojo.getRownum() == null) dmPatrolpointPojo.setRownum(0);
        if (dmPatrolpointPojo.getEnabledmark() == null) dmPatrolpointPojo.setEnabledmark(0);
        if (dmPatrolpointPojo.getDeletemark() == null) dmPatrolpointPojo.setDeletemark(0);
        if (dmPatrolpointPojo.getDeletelister() == null) dmPatrolpointPojo.setDeletelister("");
        if (dmPatrolpointPojo.getDeletedate() == null) dmPatrolpointPojo.setDeletedate(new Date());
        if (dmPatrolpointPojo.getRemark() == null) dmPatrolpointPojo.setRemark("");
        if (dmPatrolpointPojo.getStatecode() == null) dmPatrolpointPojo.setStatecode("");
        if (dmPatrolpointPojo.getStatedate() == null) dmPatrolpointPojo.setStatedate(new Date());
        if (dmPatrolpointPojo.getLister() == null) dmPatrolpointPojo.setLister("");
        if (dmPatrolpointPojo.getListerid() == null) dmPatrolpointPojo.setListerid("");
        if (dmPatrolpointPojo.getCreateby() == null) dmPatrolpointPojo.setCreateby("");
        if (dmPatrolpointPojo.getCreatebyid() == null) dmPatrolpointPojo.setCreatebyid("");
        if (dmPatrolpointPojo.getCreatedate() == null) dmPatrolpointPojo.setCreatedate(new Date());
        if (dmPatrolpointPojo.getModifydate() == null) dmPatrolpointPojo.setModifydate(new Date());
        if (dmPatrolpointPojo.getCustom1() == null) dmPatrolpointPojo.setCustom1("");
        if (dmPatrolpointPojo.getCustom2() == null) dmPatrolpointPojo.setCustom2("");
        if (dmPatrolpointPojo.getCustom3() == null) dmPatrolpointPojo.setCustom3("");
        if (dmPatrolpointPojo.getCustom4() == null) dmPatrolpointPojo.setCustom4("");
        if (dmPatrolpointPojo.getCustom5() == null) dmPatrolpointPojo.setCustom5("");
        if (dmPatrolpointPojo.getCustom6() == null) dmPatrolpointPojo.setCustom6("");
        if (dmPatrolpointPojo.getCustom7() == null) dmPatrolpointPojo.setCustom7("");
        if (dmPatrolpointPojo.getCustom8() == null) dmPatrolpointPojo.setCustom8("");
        if (dmPatrolpointPojo.getCustom9() == null) dmPatrolpointPojo.setCustom9("");
        if (dmPatrolpointPojo.getCustom10() == null) dmPatrolpointPojo.setCustom10("");
        if (dmPatrolpointPojo.getTenantid() == null) dmPatrolpointPojo.setTenantid("");
        if (dmPatrolpointPojo.getRevision() == null) dmPatrolpointPojo.setRevision(0);
        DmPatrolpointEntity dmPatrolpointEntity = new DmPatrolpointEntity();
        BeanUtils.copyProperties(dmPatrolpointPojo, dmPatrolpointEntity);
        //生成雪花id
        dmPatrolpointEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmPatrolpointEntity.setRevision(1);  //乐观锁
        this.dmPatrolpointMapper.insert(dmPatrolpointEntity);
        return this.getEntity(dmPatrolpointEntity.getId(), dmPatrolpointEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmPatrolpointPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmPatrolpointPojo update(DmPatrolpointPojo dmPatrolpointPojo) {
        DmPatrolpointEntity dmPatrolpointEntity = new DmPatrolpointEntity();
        BeanUtils.copyProperties(dmPatrolpointPojo, dmPatrolpointEntity);
        this.dmPatrolpointMapper.update(dmPatrolpointEntity);
        return this.getEntity(dmPatrolpointEntity.getId(), dmPatrolpointEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmPatrolpointMapper.delete(key, tid);
    }


}
