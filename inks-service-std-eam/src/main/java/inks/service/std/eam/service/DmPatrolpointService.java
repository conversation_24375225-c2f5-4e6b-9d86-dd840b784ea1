package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmPatrolpointPojo;

/**
 * 巡检点(DmPatrolpoint)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 13:09:02
 */
public interface DmPatrolpointService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolpointPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolpointPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmPatrolpointPojo 实例对象
     * @return 实例对象
     */
    DmPatrolpointPojo insert(DmPatrolpointPojo dmPatrolpointPojo);

    /**
     * 修改数据
     *
     * @param dmPatrolpointpojo 实例对象
     * @return 实例对象
     */
    DmPatrolpointPojo update(DmPatrolpointPojo dmPatrolpointpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
