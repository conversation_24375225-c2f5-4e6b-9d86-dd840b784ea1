package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolmaintenanceitemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 工装具保养记录子表(DmToolmaintenanceitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:35
 */
public interface DmToolmaintenanceitemService {

    DmToolmaintenanceitemPojo getEntity(String key,String tid);

    PageInfo<DmToolmaintenanceitemPojo> getPageList(QueryParam queryParam);

    List<DmToolmaintenanceitemPojo> getList(String Pid,String tid);  

    DmToolmaintenanceitemPojo insert(DmToolmaintenanceitemPojo dmToolmaintenanceitemPojo);

    DmToolmaintenanceitemPojo update(DmToolmaintenanceitemPojo dmToolmaintenanceitempojo);

    int delete(String key,String tid);

    DmToolmaintenanceitemPojo clearNull(DmToolmaintenanceitemPojo dmToolmaintenanceitempojo);
}
