package inks.service.std.eam.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 实体属性存储表(Iot_AttributeKv)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-07 10:06:34
 */
@RestController
@RequestMapping("D09M34B1")
@Api(tags = "D09M34B1:实体属性存储")
public class D09M34B1Controller extends IotAttributekvController{


}
