package inks.service.std.eam.controller.mqtt;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Spring上下文工具类，用于静态方式获取Spring管理的Bean对象。 继承了ApplicationContextAware
 */
@Component
public final class Eam_SpringUtilsMQTT implements BeanFactoryPostProcessor, ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(Eam_SpringUtilsMQTT.class);

    // Spring Bean 工厂，用于获取Bean
    private static volatile ConfigurableListableBeanFactory beanFactory;

    // Spring应用上下文
    private static volatile ApplicationContext applicationContext;

    /**
     * 实现 BeanFactoryPostProcessor 接口，用于在Spring初始化时获取 BeanFactory
     */
    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) {
        if (Eam_SpringUtilsMQTT.beanFactory == null) {
            Eam_SpringUtilsMQTT.beanFactory = beanFactory;
            log.info("SpringUtilsMQTT初始化完成，BeanFactory已注入");
        }
    }

    /**
     * 实现 ApplicationContextAware 接口，用于获取 ApplicationContext
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        if (Eam_SpringUtilsMQTT.applicationContext == null) {
            Eam_SpringUtilsMQTT.applicationContext = applicationContext;
            log.info("ApplicationContext已注入");
        }
    }

    /**
     * 根据 Bean 名称获取 Bean 实例
     * @param name Bean 名称
     * @throws NoSuchBeanDefinitionException 如果 Bean 不存在
     */
    @SuppressWarnings("unchecked")
    public static <T> T getBean(String name) {
        try {
            return (T) beanFactory.getBean(name);
        } catch (NoSuchBeanDefinitionException e) {
            log.error("Bean [{}] 未找到", name, e);
            throw e;
        }
    }

    /**
     * 根据 Bean 类型获取 Bean 实例
     * @param clz Bean 类型
     * @throws NoSuchBeanDefinitionException 如果 Bean 不存在
     */
    public static <T> T getBean(Class<T> clz) {
        try {
            return beanFactory.getBean(clz);
        } catch (NoSuchBeanDefinitionException e) {
            log.error("Bean [{}] 未找到", clz.getName(), e);
            throw e;
        }
    }

    /**
     * 判断容器中是否包含指定名称的 Bean
     * @param name Bean 名称
     */
    public static boolean containsBean(String name) {
        return beanFactory.containsBean(name);
    }

    /**
     * 获取指定名称 Bean 的类型
     * @param name Bean 名称
     * @throws NoSuchBeanDefinitionException 如果 Bean 不存在
     */
    public static Class<?> getType(String name) {
        return beanFactory.getType(name);
    }

    /**
     * 获取指定名称 Bean 的所有别名
     * @param name Bean 名称
     * @throws NoSuchBeanDefinitionException 如果 Bean 不存在
     */
    public static String[] getAliases(String name) {
        return beanFactory.getAliases(name);
    }

    /**
     * 获取 AOP 代理对象（只能在当前对象方法内部调用）
     * @param invoker 当前调用对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T getAopProxy(T invoker) {
        return (T) AopContext.currentProxy();
    }

    /**
     * 获取当前激活的所有配置环境（如 dev、prod 等）
     */
    public static String[] getActiveProfiles() {
        return applicationContext.getEnvironment().getActiveProfiles();
    }

    /**
     * 获取当前激活的第一个配置环境
     */
    public static String getActiveProfile() {
        final String[] activeProfiles = getActiveProfiles();
        return activeProfiles.length > 0 ? activeProfiles[0] : null;
    }
}
