package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotDashboardPojo;
import inks.service.std.eam.domain.IotDashboardEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 仪表盘配置表(Iot_Dashboard)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-07 10:06:44
 */
@Mapper
public interface IotDashboardMapper {

    IotDashboardPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotDashboardPojo> getPageList(QueryParam queryParam);

    int insert(IotDashboardEntity iotDashboardEntity);

    int update(IotDashboardEntity iotDashboardEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

