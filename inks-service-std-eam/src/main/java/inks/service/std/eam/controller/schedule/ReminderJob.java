package inks.service.std.eam.controller.schedule;

import inks.service.std.eam.service.DmUpkeepplanService;
import inks.service.std.eam.utils.AppCtxUtils;
import inks.service.std.eam.utils.PrintColor;
import org.quartz.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;


@DisallowConcurrentExecution//注解用于确保同一时间只有一个任务实例在运行。当多个触发器同时触发同一个任务时，该注解将阻止并发执行，确保任务实例按顺序执行，避免可能的并发问题。
public class ReminderJob implements Job {

    //  实现即时Quartz Job注入Bean(Job不能被Spring管理，所以不能注入Bean，需要手动获取Bean)
    private final PlanReminder planReminder = AppCtxUtils.getBean(PlanReminder.class);

    private final DmUpkeepplanService dmUpkeepplanService = AppCtxUtils.getBean(DmUpkeepplanService.class);

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        // jobDataMap:获取传递过来的所有参数(Job拿不到tid，所以需要传递tid)
        JobDataMap jobDataMap = context.getJobDetail().getJobDataMap();
        String tid = jobDataMap.getString("tid");
        Integer day = jobDataMap.getInt("day");
        // 获取所有保养计划
        List<Map<String, String>> allPlanList = dmUpkeepplanService.getALLPlan(tid, null, null);
        // 过滤时间即将到期的保养计划(提前3天提醒)
        List<Map<String, String>> filterPlanList = planReminder.filterPlanList(allPlanList, day);
        // 只保留未提前完成的保养计划
        filterPlanList = planReminder.checkPlan(allPlanList, filterPlanList, tid);
        for (Map<String, String> plan : filterPlanList) {
            String planDate = plan.get("plandate");
            String planId = plan.get("planid");
            PrintColor.color("lan", "for保养计划ID：" + planId + "，  保养日期：" + planDate + "，  该保养计划即将在" + day + "天内到期，请及时处理！  当前时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
        }
        // 在这里编写提醒的具体逻辑，例如发送邮件通知
        // 可以使用保养计划的ID（planId）来获取相关信息并进行相应操作
    }


    //    @Override
//    public void execute(JobExecutionContext context) throws JobExecutionException {
//        String planId = context.getJobDetail().getJobDataMap().getString("planId");
//        String planDate = context.getJobDetail().getJobDataMap().getString("planDate");
//
//        Integer day = context.getJobDetail().getJobDataMap().getInt("day");
//        PrintColor.color("保养计划ID：" + planId + "，  保养日期：" + planDate + "，  该保养计划即将在"+day+"天内到期，请及时处理！  当前时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
//
//        // 在这里编写提醒的具体逻辑，例如发送邮件通知
//        // 可以使用保养计划的ID（planId）来获取相关信息并进行相应操作
//
//    }
}
