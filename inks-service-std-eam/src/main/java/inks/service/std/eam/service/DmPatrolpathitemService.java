package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmPatrolpathitemPojo;

import java.util.List;

/**
 * 路线巡检点(DmPatrolpathitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 13:00:01
 */
public interface DmPatrolpathitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolpathitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolpathitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmPatrolpathitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param dmPatrolpathitemPojo 实例对象
     * @return 实例对象
     */
    DmPatrolpathitemPojo insert(DmPatrolpathitemPojo dmPatrolpathitemPojo);

    /**
     * 修改数据
     *
     * @param dmPatrolpathitempojo 实例对象
     * @return 实例对象
     */
    DmPatrolpathitemPojo update(DmPatrolpathitemPojo dmPatrolpathitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param dmPatrolpathitempojo 实例对象
     * @return 实例对象
     */
    DmPatrolpathitemPojo clearNull(DmPatrolpathitemPojo dmPatrolpathitempojo);
}
