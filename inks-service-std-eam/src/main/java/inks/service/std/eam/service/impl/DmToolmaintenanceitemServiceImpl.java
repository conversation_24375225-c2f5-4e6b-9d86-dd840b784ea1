package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmToolmaintenanceitemEntity;
import inks.service.std.eam.domain.pojo.DmToolmaintenanceitemPojo;
import inks.service.std.eam.mapper.DmToolmaintenanceitemMapper;
import inks.service.std.eam.service.DmToolmaintenanceitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 工装具保养记录子表(DmToolmaintenanceitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:35
 */
@Service("dmToolmaintenanceitemService")
public class DmToolmaintenanceitemServiceImpl implements DmToolmaintenanceitemService {
    @Resource
    private DmToolmaintenanceitemMapper dmToolmaintenanceitemMapper;

    @Override
    public DmToolmaintenanceitemPojo getEntity(String key, String tid) {
        return this.dmToolmaintenanceitemMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<DmToolmaintenanceitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolmaintenanceitemPojo> lst = dmToolmaintenanceitemMapper.getPageList(queryParam);
            PageInfo<DmToolmaintenanceitemPojo> pageInfo = new PageInfo<DmToolmaintenanceitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<DmToolmaintenanceitemPojo> getList(String Pid, String tid) {
        try {
            List<DmToolmaintenanceitemPojo> lst = dmToolmaintenanceitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public DmToolmaintenanceitemPojo insert(DmToolmaintenanceitemPojo dmToolmaintenanceitemPojo) {
        //初始化item的NULL
        DmToolmaintenanceitemPojo itempojo = this.clearNull(dmToolmaintenanceitemPojo);
        DmToolmaintenanceitemEntity dmToolmaintenanceitemEntity = new DmToolmaintenanceitemEntity();
        BeanUtils.copyProperties(itempojo, dmToolmaintenanceitemEntity);
        //生成雪花id
        dmToolmaintenanceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmToolmaintenanceitemEntity.setRevision(1);  //乐观锁
        this.dmToolmaintenanceitemMapper.insert(dmToolmaintenanceitemEntity);
        return this.getEntity(dmToolmaintenanceitemEntity.getId(), dmToolmaintenanceitemEntity.getTenantid());

    }

    @Override
    public DmToolmaintenanceitemPojo update(DmToolmaintenanceitemPojo dmToolmaintenanceitemPojo) {
        DmToolmaintenanceitemEntity dmToolmaintenanceitemEntity = new DmToolmaintenanceitemEntity();
        BeanUtils.copyProperties(dmToolmaintenanceitemPojo, dmToolmaintenanceitemEntity);
        this.dmToolmaintenanceitemMapper.update(dmToolmaintenanceitemEntity);
        return this.getEntity(dmToolmaintenanceitemEntity.getId(), dmToolmaintenanceitemEntity.getTenantid());
    }

    @Override
    public int delete(String key, String tid) {
        return this.dmToolmaintenanceitemMapper.delete(key, tid);
    }

    @Override
    public DmToolmaintenanceitemPojo clearNull(DmToolmaintenanceitemPojo dmToolmaintenanceitemPojo) {
        //初始化NULL字段
        if (dmToolmaintenanceitemPojo.getPid() == null) dmToolmaintenanceitemPojo.setPid("");
        if (dmToolmaintenanceitemPojo.getToolid() == null) dmToolmaintenanceitemPojo.setToolid("");
        if (dmToolmaintenanceitemPojo.getMaintdate() == null) dmToolmaintenanceitemPojo.setMaintdate(new Date());
        if (dmToolmaintenanceitemPojo.getRownum() == null) dmToolmaintenanceitemPojo.setRownum(0);
        if (dmToolmaintenanceitemPojo.getRemark() == null) dmToolmaintenanceitemPojo.setRemark("");
        if (dmToolmaintenanceitemPojo.getCustom1() == null) dmToolmaintenanceitemPojo.setCustom1("");
        if (dmToolmaintenanceitemPojo.getCustom2() == null) dmToolmaintenanceitemPojo.setCustom2("");
        if (dmToolmaintenanceitemPojo.getCustom3() == null) dmToolmaintenanceitemPojo.setCustom3("");
        if (dmToolmaintenanceitemPojo.getCustom4() == null) dmToolmaintenanceitemPojo.setCustom4("");
        if (dmToolmaintenanceitemPojo.getCustom5() == null) dmToolmaintenanceitemPojo.setCustom5("");
        if (dmToolmaintenanceitemPojo.getCustom6() == null) dmToolmaintenanceitemPojo.setCustom6("");
        if (dmToolmaintenanceitemPojo.getCustom7() == null) dmToolmaintenanceitemPojo.setCustom7("");
        if (dmToolmaintenanceitemPojo.getCustom8() == null) dmToolmaintenanceitemPojo.setCustom8("");
        if (dmToolmaintenanceitemPojo.getCustom9() == null) dmToolmaintenanceitemPojo.setCustom9("");
        if (dmToolmaintenanceitemPojo.getCustom10() == null) dmToolmaintenanceitemPojo.setCustom10("");
        if (dmToolmaintenanceitemPojo.getTenantid() == null) dmToolmaintenanceitemPojo.setTenantid("");
        if (dmToolmaintenanceitemPojo.getTenantname() == null) dmToolmaintenanceitemPojo.setTenantname("");
        if (dmToolmaintenanceitemPojo.getRevision() == null) dmToolmaintenanceitemPojo.setRevision(0);
        return dmToolmaintenanceitemPojo;
    }
}
