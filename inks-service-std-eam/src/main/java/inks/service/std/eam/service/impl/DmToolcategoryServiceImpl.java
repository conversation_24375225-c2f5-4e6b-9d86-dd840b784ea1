package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmToolcategoryEntity;
import inks.service.std.eam.domain.pojo.DmToolcategoryPojo;
import inks.service.std.eam.mapper.DmToolcategoryMapper;
import inks.service.std.eam.service.DmToolcategoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 工装具类别表(DmToolcategory)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-24 09:16:37
 */
@Service("dmToolcategoryService")
public class DmToolcategoryServiceImpl implements DmToolcategoryService {
    @Resource
    private DmToolcategoryMapper dmToolcategoryMapper;

    @Override
    public DmToolcategoryPojo getEntity(String key, String tid) {
        return this.dmToolcategoryMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<DmToolcategoryPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolcategoryPojo> lst = dmToolcategoryMapper.getPageList(queryParam);
            PageInfo<DmToolcategoryPojo> pageInfo = new PageInfo<DmToolcategoryPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public DmToolcategoryPojo insert(DmToolcategoryPojo dmToolcategoryPojo) {
        //初始化NULL字段
        cleanNull(dmToolcategoryPojo);
        DmToolcategoryEntity dmToolcategoryEntity = new DmToolcategoryEntity(); 
        BeanUtils.copyProperties(dmToolcategoryPojo,dmToolcategoryEntity);
          //生成雪花id
          dmToolcategoryEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          dmToolcategoryEntity.setRevision(1);  //乐观锁
          this.dmToolcategoryMapper.insert(dmToolcategoryEntity);
        return this.getEntity(dmToolcategoryEntity.getId(),dmToolcategoryEntity.getTenantid());
    }


    @Override
    public DmToolcategoryPojo update(DmToolcategoryPojo dmToolcategoryPojo) {
        DmToolcategoryEntity dmToolcategoryEntity = new DmToolcategoryEntity(); 
        BeanUtils.copyProperties(dmToolcategoryPojo,dmToolcategoryEntity);
        this.dmToolcategoryMapper.update(dmToolcategoryEntity);
        return this.getEntity(dmToolcategoryEntity.getId(),dmToolcategoryEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.dmToolcategoryMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(DmToolcategoryPojo dmToolcategoryPojo) {
        if(dmToolcategoryPojo.getParentid()==null) dmToolcategoryPojo.setParentid("");
        if(dmToolcategoryPojo.getCatecode()==null) dmToolcategoryPojo.setCatecode("");
        if(dmToolcategoryPojo.getCatename()==null) dmToolcategoryPojo.setCatename("");
        if(dmToolcategoryPojo.getMaintdays()==null) dmToolcategoryPojo.setMaintdays(0);
        if(dmToolcategoryPojo.getMaintwarnqty()==null) dmToolcategoryPojo.setMaintwarnqty(0);
        if(dmToolcategoryPojo.getMaintlimitqty()==null) dmToolcategoryPojo.setMaintlimitqty(0);
        if(dmToolcategoryPojo.getMaintlimitcount()==null) dmToolcategoryPojo.setMaintlimitcount(0);
        if(dmToolcategoryPojo.getScrapdays()==null) dmToolcategoryPojo.setScrapdays(0);
        if(dmToolcategoryPojo.getScrapwarnqty()==null) dmToolcategoryPojo.setScrapwarnqty(0);
        if(dmToolcategoryPojo.getScraplimitqty()==null) dmToolcategoryPojo.setScraplimitqty(0);
        if(dmToolcategoryPojo.getInspdays()==null) dmToolcategoryPojo.setInspdays(0);
        if(dmToolcategoryPojo.getInsptype()==null) dmToolcategoryPojo.setInsptype(0);
        if(dmToolcategoryPojo.getEnabledmark()==null) dmToolcategoryPojo.setEnabledmark(0);
        if(dmToolcategoryPojo.getRownum()==null) dmToolcategoryPojo.setRownum(0);
        if(dmToolcategoryPojo.getRemark()==null) dmToolcategoryPojo.setRemark("");
        if(dmToolcategoryPojo.getCreateby()==null) dmToolcategoryPojo.setCreateby("");
        if(dmToolcategoryPojo.getCreatebyid()==null) dmToolcategoryPojo.setCreatebyid("");
        if(dmToolcategoryPojo.getCreatedate()==null) dmToolcategoryPojo.setCreatedate(new Date());
        if(dmToolcategoryPojo.getLister()==null) dmToolcategoryPojo.setLister("");
        if(dmToolcategoryPojo.getListerid()==null) dmToolcategoryPojo.setListerid("");
        if(dmToolcategoryPojo.getModifydate()==null) dmToolcategoryPojo.setModifydate(new Date());
        if(dmToolcategoryPojo.getCustom1()==null) dmToolcategoryPojo.setCustom1("");
        if(dmToolcategoryPojo.getCustom2()==null) dmToolcategoryPojo.setCustom2("");
        if(dmToolcategoryPojo.getCustom3()==null) dmToolcategoryPojo.setCustom3("");
        if(dmToolcategoryPojo.getCustom4()==null) dmToolcategoryPojo.setCustom4("");
        if(dmToolcategoryPojo.getCustom5()==null) dmToolcategoryPojo.setCustom5("");
        if(dmToolcategoryPojo.getCustom6()==null) dmToolcategoryPojo.setCustom6("");
        if(dmToolcategoryPojo.getCustom7()==null) dmToolcategoryPojo.setCustom7("");
        if(dmToolcategoryPojo.getCustom8()==null) dmToolcategoryPojo.setCustom8("");
        if(dmToolcategoryPojo.getCustom9()==null) dmToolcategoryPojo.setCustom9("");
        if(dmToolcategoryPojo.getCustom10()==null) dmToolcategoryPojo.setCustom10("");
        if(dmToolcategoryPojo.getTenantid()==null) dmToolcategoryPojo.setTenantid("");
        if(dmToolcategoryPojo.getTenantname()==null) dmToolcategoryPojo.setTenantname("");
        if(dmToolcategoryPojo.getRevision()==null) dmToolcategoryPojo.setRevision(0);
   }

}
