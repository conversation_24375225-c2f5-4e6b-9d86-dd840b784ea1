package inks.service.std.eam.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.eam.domain.pojo.IotDashboardPojo;
import inks.service.std.eam.domain.IotDashboardEntity;
import inks.service.std.eam.mapper.IotDashboardMapper;
import inks.service.std.eam.service.IotDashboardService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 仪表盘配置表(IotDashboard)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-07 10:06:47
 */
@Service("iotDashboardService")
public class IotDashboardServiceImpl implements IotDashboardService {
    @Resource
    private IotDashboardMapper iotDashboardMapper;

    @Override
    public IotDashboardPojo getEntity(String key, String tid) {
        return this.iotDashboardMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotDashboardPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotDashboardPojo> lst = iotDashboardMapper.getPageList(queryParam);
            PageInfo<IotDashboardPojo> pageInfo = new PageInfo<IotDashboardPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotDashboardPojo insert(IotDashboardPojo iotDashboardPojo) {
        //初始化NULL字段
        cleanNull(iotDashboardPojo);
        IotDashboardEntity iotDashboardEntity = new IotDashboardEntity(); 
        BeanUtils.copyProperties(iotDashboardPojo,iotDashboardEntity);
          //生成雪花id
          iotDashboardEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotDashboardEntity.setRevision(1);  //乐观锁
          this.iotDashboardMapper.insert(iotDashboardEntity);
        return this.getEntity(iotDashboardEntity.getId(),iotDashboardEntity.getTenantid());
    }


    @Override
    public IotDashboardPojo update(IotDashboardPojo iotDashboardPojo) {
        IotDashboardEntity iotDashboardEntity = new IotDashboardEntity(); 
        BeanUtils.copyProperties(iotDashboardPojo,iotDashboardEntity);
        this.iotDashboardMapper.update(iotDashboardEntity);
        return this.getEntity(iotDashboardEntity.getId(),iotDashboardEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotDashboardMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotDashboardPojo iotDashboardPojo) {
        if(iotDashboardPojo.getConfiguration()==null) iotDashboardPojo.setConfiguration("");
        if(iotDashboardPojo.getAssignedcustomers()==null) iotDashboardPojo.setAssignedcustomers("");
        if(iotDashboardPojo.getTitle()==null) iotDashboardPojo.setTitle("");
        if(iotDashboardPojo.getMobilehide()==null) iotDashboardPojo.setMobilehide(0);
        if(iotDashboardPojo.getMobileorder()==null) iotDashboardPojo.setMobileorder(0);
        if(iotDashboardPojo.getImage()==null) iotDashboardPojo.setImage("");
        if(iotDashboardPojo.getExternalid()==null) iotDashboardPojo.setExternalid("");
        if(iotDashboardPojo.getRemark()==null) iotDashboardPojo.setRemark("");
        if(iotDashboardPojo.getRownum()==null) iotDashboardPojo.setRownum(0);
        if(iotDashboardPojo.getCreateby()==null) iotDashboardPojo.setCreateby("");
        if(iotDashboardPojo.getCreatebyid()==null) iotDashboardPojo.setCreatebyid("");
        if(iotDashboardPojo.getCreatedate()==null) iotDashboardPojo.setCreatedate(new Date());
        if(iotDashboardPojo.getLister()==null) iotDashboardPojo.setLister("");
        if(iotDashboardPojo.getListerid()==null) iotDashboardPojo.setListerid("");
        if(iotDashboardPojo.getModifydate()==null) iotDashboardPojo.setModifydate(new Date());
        if(iotDashboardPojo.getCustom1()==null) iotDashboardPojo.setCustom1("");
        if(iotDashboardPojo.getCustom2()==null) iotDashboardPojo.setCustom2("");
        if(iotDashboardPojo.getCustom3()==null) iotDashboardPojo.setCustom3("");
        if(iotDashboardPojo.getCustom4()==null) iotDashboardPojo.setCustom4("");
        if(iotDashboardPojo.getCustom5()==null) iotDashboardPojo.setCustom5("");
        if(iotDashboardPojo.getCustom6()==null) iotDashboardPojo.setCustom6("");
        if(iotDashboardPojo.getCustom7()==null) iotDashboardPojo.setCustom7("");
        if(iotDashboardPojo.getCustom8()==null) iotDashboardPojo.setCustom8("");
        if(iotDashboardPojo.getCustom9()==null) iotDashboardPojo.setCustom9("");
        if(iotDashboardPojo.getCustom10()==null) iotDashboardPojo.setCustom10("");
        if(iotDashboardPojo.getTenantid()==null) iotDashboardPojo.setTenantid("");
        if(iotDashboardPojo.getTenantname()==null) iotDashboardPojo.setTenantname("");
        if(iotDashboardPojo.getRevision()==null) iotDashboardPojo.setRevision(0);
   }

}
