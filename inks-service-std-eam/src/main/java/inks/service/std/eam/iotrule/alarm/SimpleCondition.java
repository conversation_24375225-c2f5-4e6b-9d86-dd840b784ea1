package inks.service.std.eam.iotrule.alarm;

import java.util.Map;

// 简单条件
public class SimpleCondition implements AlarmCondition {
    private String key;           // 遥测项名称，如"temperature"
    private String operation;     // 操作符: EQUALS, NOT_EQUALS, GREATER, LESS, GREATER_OR_EQUAL, LESS_OR_EQUAL
    private Object value;         // 阈值
    
    public SimpleCondition(String key, String operation, Object value) {
        this.key = key;
        this.operation = operation;
        this.value = value;
    }
    
    @Override
    public boolean evaluate(Map<String, Object> telemetry) {
        if (!telemetry.containsKey(key)) {
            return false;
        }
        
        Object telemetryValue = telemetry.get(key);
        
        // 将值转换为可比较的格式
        if (telemetryValue instanceof String && value instanceof String) {
            return compareStrings((String)telemetryValue, (String)value);
        } else if (telemetryValue instanceof Number && value instanceof Number) {
            return compareNumbers((Number)telemetryValue, (Number)value);
        } else {
            // 尝试根据值的格式进行转换
            try {
                if (telemetryValue instanceof String && isNumeric((String)telemetryValue)) {
                    if (((String)telemetryValue).contains(".")) {
                        return compareNumbers(Double.parseDouble((String)telemetryValue), 
                                         (value instanceof Number) ? (Number)value : Double.parseDouble(value.toString()));
                    } else {
                        return compareNumbers(Long.parseLong((String)telemetryValue), 
                                         (value instanceof Number) ? (Number)value : Long.parseLong(value.toString()));
                    }
                }
                // 其他情况暂不处理
                return false;
            } catch (Exception e) {
                return false;
            }
        }
    }
    
    private boolean isNumeric(String str) {
        return str.matches("-?\\d+(\\.\\d+)?");
    }
    
    private boolean compareStrings(String telemetryValue, String threshold) {
        if (operation.equals("EQUALS")) {
            return telemetryValue.equals(threshold);
        } else if (operation.equals("NOT_EQUALS")) {
            return !telemetryValue.equals(threshold);
        }
        
        // 尝试提取数字部分进行比较
        String telValueNum = telemetryValue.replaceAll("[^0-9.]", "");
        String thresholdNum = threshold.replaceAll("[^0-9.]", "");
        
        try {
            double telVal = Double.parseDouble(telValueNum);
            double thVal = Double.parseDouble(thresholdNum);
            return compareDoubles(telVal, thVal);
        } catch (NumberFormatException e) {
            // 如果无法转为数字，按字符串字典序比较
            int compResult = telemetryValue.compareTo(threshold);
            if (operation.equals("GREATER")) {
                return compResult > 0;
            } else if (operation.equals("LESS")) {
                return compResult < 0;
            } else if (operation.equals("GREATER_OR_EQUAL")) {
                return compResult >= 0;
            } else if (operation.equals("LESS_OR_EQUAL")) {
                return compResult <= 0;
            }
            return false;
        }
    }
    
    private boolean compareNumbers(Number telemetryValue, Number threshold) {
        double telVal = telemetryValue.doubleValue();
        double thVal = threshold.doubleValue();
        return compareDoubles(telVal, thVal);
    }
    
    private boolean compareDoubles(double telVal, double thVal) {
        if (operation.equals("EQUALS")) {
            return Math.abs(telVal - thVal) < 0.000001; // 浮点数相等比较
        } else if (operation.equals("NOT_EQUALS")) {
            return Math.abs(telVal - thVal) >= 0.000001;
        } else if (operation.equals("GREATER")) {
            return telVal > thVal;
        } else if (operation.equals("LESS")) {
            return telVal < thVal;
        } else if (operation.equals("GREATER_OR_EQUAL")) {
            return telVal >= thVal;
        } else if (operation.equals("LESS_OR_EQUAL")) {
            return telVal <= thVal;
        }
        return false;
    }
}
