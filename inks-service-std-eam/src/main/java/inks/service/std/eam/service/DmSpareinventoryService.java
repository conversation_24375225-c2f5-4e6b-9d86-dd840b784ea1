package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmSpareinventoryPojo;

/**
 * 备件库存(DmSpareinventory)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 10:10:17
 */
public interface DmSpareinventoryService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmSpareinventoryPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmSpareinventoryPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmSpareinventoryPojo 实例对象
     * @return 实例对象
     */
    DmSpareinventoryPojo insert(DmSpareinventoryPojo dmSpareinventoryPojo);

    /**
     * 修改数据
     *
     * @param dmSpareinventorypojo 实例对象
     * @return 实例对象
     */
    DmSpareinventoryPojo update(DmSpareinventoryPojo dmSpareinventorypojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    DmSpareinventoryPojo getEntityBySpareid(String spareid, String location, String batchno, String tid);
}
