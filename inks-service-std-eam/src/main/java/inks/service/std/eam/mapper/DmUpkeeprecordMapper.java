package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmUpkeeprecordEntity;
import inks.service.std.eam.domain.pojo.DmUpkeeprecordPojo;
import inks.service.std.eam.domain.pojo.DmUpkeeprecorditemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 保养实施(DmUpkeeprecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-06 14:35:42
 */
@Mapper
public interface DmUpkeeprecordMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeeprecordPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmUpkeeprecorditemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmUpkeeprecordPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmUpkeeprecordEntity 实例对象
     * @return 影响行数
     */
    int insert(DmUpkeeprecordEntity dmUpkeeprecordEntity);


    /**
     * 修改数据
     *
     * @param dmUpkeeprecordEntity 实例对象
     * @return 影响行数
     */
    int update(DmUpkeeprecordEntity dmUpkeeprecordEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param dmUpkeeprecordPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(DmUpkeeprecordPojo dmUpkeeprecordPojo);

    List<String> getDelSpareIds(DmUpkeeprecordPojo dmUpkeeprecordPojo);

    /**
     * 修改数据
     *
     * @param dmUpkeeprecordEntity 实例对象
     * @return 影响行数
     */
    int approval(DmUpkeeprecordEntity dmUpkeeprecordEntity);

    int getSumFinishCount(@Param("planid") String planid, @Param("plandate") String plandate, @Param("tid") String tid);

    List<String> getAllDevId(@Param("planid") String planid, @Param("plandate") String plandate, @Param("tid") String tid);

    List<String> getAllFinishDevId(@Param("planid") String planid, @Param("plandate") String plandate, @Param("tid") String tid);

    List<DmUpkeeprecordPojo> getListByPlanidAndDate(@Param("planid") String planid, @Param("plandate") String plandate, @Param("tid") String tid);

}

