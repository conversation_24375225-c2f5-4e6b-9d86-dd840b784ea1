package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotRulenodePojo;
import inks.service.std.eam.service.IotRulenodeService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 规则链节点定义表(Iot_RuleNode)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:42
 */
//@RestController
//@RequestMapping("iotRulenode")
public class IotRulenodeController {

    @Resource
    private IotRulenodeService iotRulenodeService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(IotRulenodeController.class);


    @ApiOperation(value=" 获取规则链节点定义表详细信息", notes="获取规则链节点定义表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_RuleNode.List")
    public R<IotRulenodePojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotRulenodeService.getEntity(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_RuleNode.List")
    public R<PageInfo<IotRulenodePojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Iot_RuleNode.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.iotRulenodeService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增规则链节点定义表", notes="新增规则链节点定义表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_RuleNode.Add")
    public R<IotRulenodePojo> create(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
       IotRulenodePojo iotRulenodePojo = JSONArray.parseObject(json,IotRulenodePojo.class);       
            iotRulenodePojo.setCreateby(loginUser.getRealName());   // 创建者
            iotRulenodePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            iotRulenodePojo.setCreatedate(new Date());   // 创建时间
            iotRulenodePojo.setLister(loginUser.getRealname());   // 制表
            iotRulenodePojo.setListerid(loginUser.getUserid());    // 制表id  
            iotRulenodePojo.setModifydate(new Date());   //修改时间
            iotRulenodePojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.iotRulenodeService.insert(iotRulenodePojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改规则链节点定义表", notes="修改规则链节点定义表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_RuleNode.Edit")
    public R<IotRulenodePojo> update(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
         IotRulenodePojo iotRulenodePojo = JSONArray.parseObject(json,IotRulenodePojo.class);
            iotRulenodePojo.setLister(loginUser.getRealname());   // 制表
            iotRulenodePojo.setListerid(loginUser.getUserid());    // 制表id  
            iotRulenodePojo.setTenantid(loginUser.getTenantid());   //租户id
            iotRulenodePojo.setModifydate(new Date());   //修改时间
//            iotRulenodePojo.setAssessor(""); // 审核员
//            iotRulenodePojo.setAssessorid(""); // 审核员id
//            iotRulenodePojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.iotRulenodeService.update(iotRulenodePojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除规则链节点定义表", notes="删除规则链节点定义表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_RuleNode.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotRulenodeService.delete(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_RuleNode.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        IotRulenodePojo iotRulenodePojo = this.iotRulenodeService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(iotRulenodePojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

