package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmDeviceEntity;
import inks.service.std.eam.domain.DmDeviceitemEntity;
import inks.service.std.eam.domain.pojo.DmDevicePojo;
import inks.service.std.eam.domain.pojo.DmDeviceitemPojo;
import inks.service.std.eam.domain.pojo.DmDeviceitemdetailPojo;
import inks.service.std.eam.mapper.DmDeviceMapper;
import inks.service.std.eam.mapper.DmDeviceitemMapper;
import inks.service.std.eam.service.DmDeviceService;
import inks.service.std.eam.service.DmDeviceitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 设备台账(DmDevice)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-06 12:59:49
 */
@Service("dmDeviceService")
public class DmDeviceServiceImpl implements DmDeviceService {
    @Resource
    private DmDeviceMapper dmDeviceMapper;

    @Resource
    private DmDeviceitemMapper dmDeviceitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private DmDeviceitemService dmDeviceitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmDevicePojo getEntity(String key, String tid) {
        return this.dmDeviceMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmDeviceitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmDeviceitemdetailPojo> lst = dmDeviceMapper.getPageList(queryParam);
            PageInfo<DmDeviceitemdetailPojo> pageInfo = new PageInfo<DmDeviceitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmDevicePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            DmDevicePojo dmDevicePojo = this.dmDeviceMapper.getEntity(key, tid);
            //读取子表
            dmDevicePojo.setItem(dmDeviceitemMapper.getList(dmDevicePojo.getId(), dmDevicePojo.getTenantid()));
            return dmDevicePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmDevicePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmDevicePojo> lst = dmDeviceMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(dmDeviceitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<DmDevicePojo> pageInfo = new PageInfo<DmDevicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmDevicePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmDevicePojo> lst = dmDeviceMapper.getPageTh(queryParam);
            PageInfo<DmDevicePojo> pageInfo = new PageInfo<DmDevicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmDevicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmDevicePojo insert(DmDevicePojo dmDevicePojo) {
//初始化NULL字段
        if (dmDevicePojo.getDevgroupid() == null) dmDevicePojo.setDevgroupid("");
        if (dmDevicePojo.getDevcode() == null) dmDevicePojo.setDevcode("");
        if (dmDevicePojo.getDevname() == null) dmDevicePojo.setDevname("");
        if (dmDevicePojo.getDevspec() == null) dmDevicePojo.setDevspec("");
        if (dmDevicePojo.getDevunit() == null) dmDevicePojo.setDevunit("");
        if (dmDevicePojo.getDevpinyin() == null) dmDevicePojo.setDevpinyin("");
        if (dmDevicePojo.getGroupcode() == null) dmDevicePojo.setGroupcode("");
        if (dmDevicePojo.getGroupname() == null) dmDevicePojo.setGroupname("");
        if (dmDevicePojo.getGroupno() == null) dmDevicePojo.setGroupno(0);
        if (dmDevicePojo.getDevphoto1() == null) dmDevicePojo.setDevphoto1("");
        if (dmDevicePojo.getDevphoto2() == null) dmDevicePojo.setDevphoto2("");
        if (dmDevicePojo.getMakedate() == null) dmDevicePojo.setMakedate(new Date());
        if (dmDevicePojo.getStartdate() == null) dmDevicePojo.setStartdate(new Date());
        if (dmDevicePojo.getInprice() == null) dmDevicePojo.setInprice(0D);
        if (dmDevicePojo.getNowprice() == null) dmDevicePojo.setNowprice(0D);
        if (dmDevicePojo.getDepreciation() == null) dmDevicePojo.setDepreciation(0);
        if (dmDevicePojo.getUsestate() == null) dmDevicePojo.setUsestate("");
        if (dmDevicePojo.getUselocation() == null) dmDevicePojo.setUselocation("");
        if (dmDevicePojo.getUsebranch() == null) dmDevicePojo.setUsebranch("");
        if (dmDevicePojo.getUsestaff() == null) dmDevicePojo.setUsestaff("");
        if (dmDevicePojo.getRemark() == null) dmDevicePojo.setRemark("");
        if (dmDevicePojo.getCustom1() == null) dmDevicePojo.setCustom1("");
        if (dmDevicePojo.getCustom2() == null) dmDevicePojo.setCustom2("");
        if (dmDevicePojo.getCustom3() == null) dmDevicePojo.setCustom3("");
        if (dmDevicePojo.getCustom4() == null) dmDevicePojo.setCustom4("");
        if (dmDevicePojo.getCustom5() == null) dmDevicePojo.setCustom5("");
        if (dmDevicePojo.getCustom6() == null) dmDevicePojo.setCustom6("");
        if (dmDevicePojo.getCustom7() == null) dmDevicePojo.setCustom7("");
        if (dmDevicePojo.getCustom8() == null) dmDevicePojo.setCustom8("");
        if (dmDevicePojo.getLister() == null) dmDevicePojo.setLister("");
        if (dmDevicePojo.getListerid() == null) dmDevicePojo.setListerid("");
        if (dmDevicePojo.getCreatedate() == null) dmDevicePojo.setCreatedate(new Date());
        if (dmDevicePojo.getCreateby() == null) dmDevicePojo.setCreateby("");
        if (dmDevicePojo.getCreatebyid() == null) dmDevicePojo.setCreatebyid("");
        if (dmDevicePojo.getModifydate() == null) dmDevicePojo.setModifydate(new Date());
        if (dmDevicePojo.getEnabledmark() == null) dmDevicePojo.setEnabledmark(0);
        if (dmDevicePojo.getDeletemark() == null) dmDevicePojo.setDeletemark(0);
        if (dmDevicePojo.getDeletelister() == null) dmDevicePojo.setDeletelister("");
        if (dmDevicePojo.getDeletedate() == null) dmDevicePojo.setDeletedate(new Date());
        if (dmDevicePojo.getDisannulmark() == null) dmDevicePojo.setDisannulmark(0);
        if (dmDevicePojo.getDisannuldate() == null) dmDevicePojo.setDisannuldate(new Date());
        if (dmDevicePojo.getTenantid() == null) dmDevicePojo.setTenantid("");
        if (dmDevicePojo.getRevision() == null) dmDevicePojo.setRevision(0);
        //设备编码唯一性检查
        if (this.dmDeviceMapper.checkDevcode(dmDevicePojo.getDevcode(), null, dmDevicePojo.getTenantid()) > 0) {
            throw new BaseBusinessException(dmDevicePojo.getDevcode() + "设备编码已存在");
        }
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmDeviceEntity dmDeviceEntity = new DmDeviceEntity();
        BeanUtils.copyProperties(dmDevicePojo, dmDeviceEntity);

        //设置id和新建日期
        dmDeviceEntity.setId(id);
        dmDeviceEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmDeviceMapper.insert(dmDeviceEntity);
        //Item子表处理
        List<DmDeviceitemPojo> lst = dmDevicePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                DmDeviceitemPojo itemPojo = this.dmDeviceitemService.clearNull(lst.get(i));
                DmDeviceitemEntity dmDeviceitemEntity = new DmDeviceitemEntity();
                BeanUtils.copyProperties(itemPojo, dmDeviceitemEntity);
                //设置id和Pid
                dmDeviceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmDeviceitemEntity.setPid(id);
                dmDeviceitemEntity.setTenantid(dmDevicePojo.getTenantid());
                dmDeviceitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmDeviceitemMapper.insert(dmDeviceitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmDeviceEntity.getId(), dmDeviceEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmDevicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmDevicePojo update(DmDevicePojo dmDevicePojo) {
        //设备编码唯一性检查
        if (this.dmDeviceMapper.checkDevcode(dmDevicePojo.getDevcode(), dmDevicePojo.getId(), dmDevicePojo.getTenantid()) > 0) {
            throw new BaseBusinessException(dmDevicePojo.getDevcode() + "设备编码已存在");
        }
        //主表更改
        DmDeviceEntity dmDeviceEntity = new DmDeviceEntity();
        BeanUtils.copyProperties(dmDevicePojo, dmDeviceEntity);
        this.dmDeviceMapper.update(dmDeviceEntity);
        if (dmDevicePojo.getItem() != null) {
            //Item子表处理
            List<DmDeviceitemPojo> lst = dmDevicePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = dmDeviceMapper.getDelItemIds(dmDevicePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.dmDeviceitemMapper.delete(lstDelIds.get(i), dmDeviceEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    DmDeviceitemEntity dmDeviceitemEntity = new DmDeviceitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        DmDeviceitemPojo itemPojo = this.dmDeviceitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, dmDeviceitemEntity);
                        //设置id和Pid
                        dmDeviceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        dmDeviceitemEntity.setPid(dmDeviceEntity.getId());  // 主表 id
                        dmDeviceitemEntity.setTenantid(dmDevicePojo.getTenantid());   // 租户id
                        dmDeviceitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.dmDeviceitemMapper.insert(dmDeviceitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), dmDeviceitemEntity);
                        dmDeviceitemEntity.setTenantid(dmDevicePojo.getTenantid());
                        this.dmDeviceitemMapper.update(dmDeviceitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmDeviceEntity.getId(), dmDeviceEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        DmDevicePojo dmDevicePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<DmDeviceitemPojo> lst = dmDevicePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (DmDeviceitemPojo dmDeviceitemPojo : lst) {
                this.dmDeviceitemMapper.delete(dmDeviceitemPojo.getId(), tid);
            }
        }
        return this.dmDeviceMapper.delete(key, tid);
    }

    @Override
    public String getMaxDevCode(String tenantid) {
        return this.dmDeviceMapper.getMaxDevCode(tenantid);
    }
}
