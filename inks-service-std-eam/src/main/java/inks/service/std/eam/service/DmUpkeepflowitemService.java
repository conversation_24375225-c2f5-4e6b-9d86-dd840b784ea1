package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmUpkeepflowitemPojo;

import java.util.List;

/**
 * 方案流程(DmUpkeepflowitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 11:14:37
 */
public interface DmUpkeepflowitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeepflowitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeepflowitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmUpkeepflowitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param dmUpkeepflowitemPojo 实例对象
     * @return 实例对象
     */
    DmUpkeepflowitemPojo insert(DmUpkeepflowitemPojo dmUpkeepflowitemPojo);

    /**
     * 修改数据
     *
     * @param dmUpkeepflowitempojo 实例对象
     * @return 实例对象
     */
    DmUpkeepflowitemPojo update(DmUpkeepflowitemPojo dmUpkeepflowitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param dmUpkeepflowitempojo 实例对象
     * @return 实例对象
     */
    DmUpkeepflowitemPojo clearNull(DmUpkeepflowitemPojo dmUpkeepflowitempojo);
}
