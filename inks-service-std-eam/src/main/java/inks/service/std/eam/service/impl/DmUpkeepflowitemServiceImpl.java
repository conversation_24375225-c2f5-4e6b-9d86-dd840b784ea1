package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmUpkeepflowitemEntity;
import inks.service.std.eam.domain.pojo.DmUpkeepflowitemPojo;
import inks.service.std.eam.mapper.DmUpkeepflowitemMapper;
import inks.service.std.eam.service.DmUpkeepflowitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 方案流程(DmUpkeepflowitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 11:14:37
 */
@Service("dmUpkeepflowitemService")
public class DmUpkeepflowitemServiceImpl implements DmUpkeepflowitemService {
    @Resource
    private DmUpkeepflowitemMapper dmUpkeepflowitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmUpkeepflowitemPojo getEntity(String key, String tid) {
        return this.dmUpkeepflowitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeepflowitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeepflowitemPojo> lst = dmUpkeepflowitemMapper.getPageList(queryParam);
            PageInfo<DmUpkeepflowitemPojo> pageInfo = new PageInfo<DmUpkeepflowitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DmUpkeepflowitemPojo> getList(String Pid, String tid) {
        try {
            List<DmUpkeepflowitemPojo> lst = dmUpkeepflowitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param dmUpkeepflowitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeepflowitemPojo insert(DmUpkeepflowitemPojo dmUpkeepflowitemPojo) {
        //初始化item的NULL
        DmUpkeepflowitemPojo itempojo = this.clearNull(dmUpkeepflowitemPojo);
        DmUpkeepflowitemEntity dmUpkeepflowitemEntity = new DmUpkeepflowitemEntity();
        BeanUtils.copyProperties(itempojo, dmUpkeepflowitemEntity);
        //生成雪花id
        dmUpkeepflowitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmUpkeepflowitemEntity.setRevision(1);  //乐观锁
        this.dmUpkeepflowitemMapper.insert(dmUpkeepflowitemEntity);
        return this.getEntity(dmUpkeepflowitemEntity.getId(), dmUpkeepflowitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmUpkeepflowitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeepflowitemPojo update(DmUpkeepflowitemPojo dmUpkeepflowitemPojo) {
        DmUpkeepflowitemEntity dmUpkeepflowitemEntity = new DmUpkeepflowitemEntity();
        BeanUtils.copyProperties(dmUpkeepflowitemPojo, dmUpkeepflowitemEntity);
        this.dmUpkeepflowitemMapper.update(dmUpkeepflowitemEntity);
        return this.getEntity(dmUpkeepflowitemEntity.getId(), dmUpkeepflowitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmUpkeepflowitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param dmUpkeepflowitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeepflowitemPojo clearNull(DmUpkeepflowitemPojo dmUpkeepflowitemPojo) {
        //初始化NULL字段
        if (dmUpkeepflowitemPojo.getPid() == null) dmUpkeepflowitemPojo.setPid("");
        if (dmUpkeepflowitemPojo.getLevel() == null) dmUpkeepflowitemPojo.setLevel("");
        if (dmUpkeepflowitemPojo.getField() == null) dmUpkeepflowitemPojo.setField("");
        if (dmUpkeepflowitemPojo.getFieldtype() == null) dmUpkeepflowitemPojo.setFieldtype("");
        if (dmUpkeepflowitemPojo.getRequired() == null) dmUpkeepflowitemPojo.setRequired(0);
        if (dmUpkeepflowitemPojo.getStartdate() == null) dmUpkeepflowitemPojo.setStartdate(new Date());
        if (dmUpkeepflowitemPojo.getEnddate() == null) dmUpkeepflowitemPojo.setEnddate(new Date());
        if (dmUpkeepflowitemPojo.getDuty() == null) dmUpkeepflowitemPojo.setDuty("");
        if (dmUpkeepflowitemPojo.getTracker() == null) dmUpkeepflowitemPojo.setTracker("");
        if (dmUpkeepflowitemPojo.getRemark() == null) dmUpkeepflowitemPojo.setRemark("");
        if (dmUpkeepflowitemPojo.getRownum() == null) dmUpkeepflowitemPojo.setRownum(0);
        if (dmUpkeepflowitemPojo.getCustom1() == null) dmUpkeepflowitemPojo.setCustom1("");
        if (dmUpkeepflowitemPojo.getCustom2() == null) dmUpkeepflowitemPojo.setCustom2("");
        if (dmUpkeepflowitemPojo.getCustom3() == null) dmUpkeepflowitemPojo.setCustom3("");
        if (dmUpkeepflowitemPojo.getCustom4() == null) dmUpkeepflowitemPojo.setCustom4("");
        if (dmUpkeepflowitemPojo.getCustom5() == null) dmUpkeepflowitemPojo.setCustom5("");
        if (dmUpkeepflowitemPojo.getCustom6() == null) dmUpkeepflowitemPojo.setCustom6("");
        if (dmUpkeepflowitemPojo.getCustom7() == null) dmUpkeepflowitemPojo.setCustom7("");
        if (dmUpkeepflowitemPojo.getCustom8() == null) dmUpkeepflowitemPojo.setCustom8("");
        if (dmUpkeepflowitemPojo.getTenantid() == null) dmUpkeepflowitemPojo.setTenantid("");
        if (dmUpkeepflowitemPojo.getRevision() == null) dmUpkeepflowitemPojo.setRevision(0);
        return dmUpkeepflowitemPojo;
    }
}
