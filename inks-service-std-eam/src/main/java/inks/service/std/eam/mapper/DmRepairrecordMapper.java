package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmRepairrecordEntity;
import inks.service.std.eam.domain.pojo.DmRepairrecordPojo;
import inks.service.std.eam.domain.pojo.DmRepairrecorditemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 维护记录(DmRepairrecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-06 14:54:54
 */
@Mapper
public interface DmRepairrecordMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmRepairrecordPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmRepairrecorditemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmRepairrecordPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmRepairrecordEntity 实例对象
     * @return 影响行数
     */
    int insert(DmRepairrecordEntity dmRepairrecordEntity);


    /**
     * 修改数据
     *
     * @param dmRepairrecordEntity 实例对象
     * @return 影响行数
     */
    int update(DmRepairrecordEntity dmRepairrecordEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param dmRepairrecordPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(DmRepairrecordPojo dmRepairrecordPojo);

    void updateRepairStatus(@Param("repairid") String repairid, @Param("tid") String tenantid, @Param("billresult") String billresult);

    int checkRepair(@Param("repairid") String repairid, @Param("id") String id, @Param("tid") String tid);
}

