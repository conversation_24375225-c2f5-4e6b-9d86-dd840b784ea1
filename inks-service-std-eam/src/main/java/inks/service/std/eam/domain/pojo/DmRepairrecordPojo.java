package inks.service.std.eam.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.service.std.eam.domain.extend.DevicePojo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 维护记录(DmRepairrecord)实体类
 *
 * <AUTHOR>
 * @since 2023-06-06 14:54:54
 */
public class DmRepairrecordPojo extends DevicePojo implements Serializable {
    private static final long serialVersionUID = -38845064720654117L;
    // id
    @Excel(name = "id")
    private String id;
    // 流水号
    @Excel(name = "流水号")
    private String refno;
    // 单据日期
    @Excel(name = "单据日期")
    private Date billdate;
    // 类型
    @Excel(name = "类型")
    private String billtype;
    // 报修主题
    @Excel(name = "报修主题")
    private String billtitle;
    // 设备id
    @Excel(name = "设备id")
    private String deviceid;
    // 报修id
    @Excel(name = "报修id")
    private String repairid;
    // 故障原因
    @Excel(name = "故障原因")
    private String failurecause;
    // 过程描述
    @Excel(name = "过程描述")
    private String solution;
    // 用时
    @Excel(name = "用时")
    private Double worktime;
    // 签字图片
    @Excel(name = "签字图片")
    private String signimage;

    // 经办人
    @Excel(name = "经办人")
    private String operator;
    // 简述
    @Excel(name = "简述")
    private String summary;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 审核员id
    @Excel(name = "审核员id")
    private String assessorid;
    // 审核日期
    @Excel(name = "审核日期")
    private Date assessdate;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 报修单RefNo
    private String repairrefno;
    // 报修单主题
    private String repairtitle;
    // 报修单经办人
    private String repairoperator;
    // 报修单内容
    private String repairreason;
    // 报修时间
    private Date repairdate;
    // 子表
    private List<DmRepairrecorditemPojo> item;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 流水号
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 单据日期
    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }

    public String getRepairrefno() {
        return repairrefno;
    }

    public void setRepairrefno(String repairrefno) {
        this.repairrefno = repairrefno;
    }

    // 类型
    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }

    // 报修主题
    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }

    public String getRepairtitle() {
        return repairtitle;
    }

    public void setRepairtitle(String repairtitle) {
        this.repairtitle = repairtitle;
    }

    public String getRepairoperator() {
        return repairoperator;
    }

    public void setRepairoperator(String repairoperator) {
        this.repairoperator = repairoperator;
    }

    public String getRepairreason() {
        return repairreason;
    }

    public void setRepairreason(String repairreason) {
        this.repairreason = repairreason;
    }

    public String getSignimage() {
        return signimage;
    }

    public void setSignimage(String signimage) {
        this.signimage = signimage;
    }

    // 设备id
    public String getDeviceid() {
        return deviceid;
    }

    public void setDeviceid(String deviceid) {
        this.deviceid = deviceid;
    }

    // 报修id
    public String getRepairid() {
        return repairid;
    }

    public void setRepairid(String repairid) {
        this.repairid = repairid;
    }

    // 故障原因
    public String getFailurecause() {
        return failurecause;
    }

    public void setFailurecause(String failurecause) {
        this.failurecause = failurecause;
    }

    // 过程描述
    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    // 用时
    public Double getWorktime() {
        return worktime;
    }

    public void setWorktime(Double worktime) {
        this.worktime = worktime;
    }

    // 经办人
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    // 简述
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    public Date getRepairdate() {
        return repairdate;
    }

    public void setRepairdate(Date repairdate) {
        this.repairdate = repairdate;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 审核员id
    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }

    // 审核日期
    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


    public List<DmRepairrecorditemPojo> getItem() {
        return item;
    }

    public void setItem(List<DmRepairrecorditemPojo> item) {
        this.item = item;
    }


}

