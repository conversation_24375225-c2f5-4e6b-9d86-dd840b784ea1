package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmIubricationEntity;
import inks.service.std.eam.domain.pojo.DmIubricationPojo;
import inks.service.std.eam.mapper.DmIubricationMapper;
import inks.service.std.eam.service.DmIubricationService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 设备润滑(DmIubrication)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 12:40:14
 */
@Service("dmIubricationService")
public class DmIubricationServiceImpl implements DmIubricationService {
    @Resource
    private DmIubricationMapper dmIubricationMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmIubricationPojo getEntity(String key, String tid) {
        return this.dmIubricationMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmIubricationPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmIubricationPojo> lst = dmIubricationMapper.getPageList(queryParam);
            PageInfo<DmIubricationPojo> pageInfo = new PageInfo<DmIubricationPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmIubricationPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmIubricationPojo insert(DmIubricationPojo dmIubricationPojo) {
        //初始化NULL字段
        if (dmIubricationPojo.getId() == null) dmIubricationPojo.setId("");
        if (dmIubricationPojo.getRefno() == null) dmIubricationPojo.setRefno("");
        if (dmIubricationPojo.getBilltype() == null) dmIubricationPojo.setBilltype("");
        if (dmIubricationPojo.getOperator() == null) dmIubricationPojo.setOperator("");
        if (dmIubricationPojo.getDeviceid() == null) dmIubricationPojo.setDeviceid("");
        if (dmIubricationPojo.getDevicename() == null) dmIubricationPojo.setDevicename("");
        if (dmIubricationPojo.getUseid() == null) dmIubricationPojo.setUseid("");
        if (dmIubricationPojo.getUsestaff() == null) dmIubricationPojo.setUsestaff("");
        if (dmIubricationPojo.getIubdetails() == null) dmIubricationPojo.setIubdetails("");
        if (dmIubricationPojo.getIubplan() == null) dmIubricationPojo.setIubplan("");
        if (dmIubricationPojo.getSafetyrules() == null) dmIubricationPojo.setSafetyrules("");
        if (dmIubricationPojo.getAssessorid() == null) dmIubricationPojo.setAssessorid("");
        if (dmIubricationPojo.getAssessdate() == null) dmIubricationPojo.setAssessdate(new Date());
        if (dmIubricationPojo.getAssessor() == null) dmIubricationPojo.setAssessor("");
        if (dmIubricationPojo.getBilldate() == null) dmIubricationPojo.setBilldate(new Date());
        if (dmIubricationPojo.getOpedetails() == null) dmIubricationPojo.setOpedetails("");
        if (dmIubricationPojo.getRemark() == null) dmIubricationPojo.setRemark("");
        if (dmIubricationPojo.getCustom1() == null) dmIubricationPojo.setCustom1("");
        if (dmIubricationPojo.getCustom2() == null) dmIubricationPojo.setCustom2("");
        if (dmIubricationPojo.getCustom3() == null) dmIubricationPojo.setCustom3("");
        if (dmIubricationPojo.getCustom4() == null) dmIubricationPojo.setCustom4("");
        if (dmIubricationPojo.getCustom5() == null) dmIubricationPojo.setCustom5("");
        if (dmIubricationPojo.getCustom6() == null) dmIubricationPojo.setCustom6("");
        if (dmIubricationPojo.getCustom7() == null) dmIubricationPojo.setCustom7("");
        if (dmIubricationPojo.getCustom8() == null) dmIubricationPojo.setCustom8("");
        if (dmIubricationPojo.getLister() == null) dmIubricationPojo.setLister("");
        if (dmIubricationPojo.getListerid() == null) dmIubricationPojo.setListerid("");
        if (dmIubricationPojo.getCreatedate() == null) dmIubricationPojo.setCreatedate(new Date());
        if (dmIubricationPojo.getCreateby() == null) dmIubricationPojo.setCreateby("");
        if (dmIubricationPojo.getCreatebyid() == null) dmIubricationPojo.setCreatebyid("");
        if (dmIubricationPojo.getModifydate() == null) dmIubricationPojo.setModifydate(new Date());
        if (dmIubricationPojo.getEnabledmark() == null) dmIubricationPojo.setEnabledmark(0);
        if (dmIubricationPojo.getDeletemark() == null) dmIubricationPojo.setDeletemark(0);
        if (dmIubricationPojo.getDeletelister() == null) dmIubricationPojo.setDeletelister("");
        if (dmIubricationPojo.getDeletedate() == null) dmIubricationPojo.setDeletedate(new Date());
        if (dmIubricationPojo.getTenantid() == null) dmIubricationPojo.setTenantid("");
        if (dmIubricationPojo.getRevision() == null) dmIubricationPojo.setRevision(0);
        DmIubricationEntity dmIubricationEntity = new DmIubricationEntity();
        BeanUtils.copyProperties(dmIubricationPojo, dmIubricationEntity);
        //生成雪花id
        dmIubricationEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmIubricationEntity.setRevision(1);  //乐观锁
        this.dmIubricationMapper.insert(dmIubricationEntity);
        return this.getEntity(dmIubricationEntity.getId(), dmIubricationEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmIubricationPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmIubricationPojo update(DmIubricationPojo dmIubricationPojo) {
        DmIubricationEntity dmIubricationEntity = new DmIubricationEntity();
        BeanUtils.copyProperties(dmIubricationPojo, dmIubricationEntity);
        this.dmIubricationMapper.update(dmIubricationEntity);
        return this.getEntity(dmIubricationEntity.getId(), dmIubricationEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmIubricationMapper.delete(key, tid);
    }

    /**
     * 审核数据
     *
     * @param dmIubricationPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public DmIubricationPojo approval(DmIubricationPojo dmIubricationPojo) {
        //主表更改
        DmIubricationEntity dmIubricationEntity = new DmIubricationEntity();
        BeanUtils.copyProperties(dmIubricationPojo, dmIubricationEntity);
        this.dmIubricationMapper.approval(dmIubricationEntity);
        //返回Bill实例
        return this.getEntity(dmIubricationEntity.getId(), dmIubricationEntity.getTenantid());
    }

}
