package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.IotRelationEntity;
import inks.service.std.eam.domain.pojo.IotRelationPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 实体关系映射表(Iot_Relation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 11:02:52
 */
@Mapper
public interface IotRelationMapper {

    IotRelationPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotRelationPojo> getPageList(QueryParam queryParam);

    int insert(IotRelationEntity iotRelationEntity);

    int update(IotRelationEntity iotRelationEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    List<IotRelationPojo> getList(String fromid, String fromtype, String toid, String totype, String tenantid);


    List<Map<String,String>> getDeviceNamesByIds(@Param("ids") Set<String> ids, @Param("tenantid") String tenantid);

    List<Map<String,String>> getAssetNamesByIds(@Param("ids") Set<String> ids, @Param("tenantid") String tenantid);
}

