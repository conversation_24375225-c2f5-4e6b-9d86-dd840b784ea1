package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotAlarmPojo;
import inks.service.std.eam.domain.IotAlarmEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 警报(Iot_Alarm)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-14 13:01:05
 */
@Mapper
public interface IotAlarmMapper {

    IotAlarmPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotAlarmPojo> getPageList(QueryParam queryParam);

    int insert(IotAlarmEntity iotAlarmEntity);

    int update(IotAlarmEntity iotAlarmEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

