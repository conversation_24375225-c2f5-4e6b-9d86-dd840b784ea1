package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmPatrolpathitemEntity;
import inks.service.std.eam.domain.pojo.DmPatrolpathitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 路线巡检点(DmPatrolpathitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 13:00:01
 */
@Mapper
public interface DmPatrolpathitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolpathitemPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmPatrolpathitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmPatrolpathitemPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);


    /**
     * 新增数据
     *
     * @param dmPatrolpathitemEntity 实例对象
     * @return 影响行数
     */
    int insert(DmPatrolpathitemEntity dmPatrolpathitemEntity);


    /**
     * 修改数据
     *
     * @param dmPatrolpathitemEntity 实例对象
     * @return 影响行数
     */
    int update(DmPatrolpathitemEntity dmPatrolpathitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

}

