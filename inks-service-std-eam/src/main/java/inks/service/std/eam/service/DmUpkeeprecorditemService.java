package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmUpkeeprecorditemPojo;

import java.util.List;

/**
 * 实施备件(DmUpkeeprecorditem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-06 15:43:02
 */
public interface DmUpkeeprecorditemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmUpkeeprecorditemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmUpkeeprecorditemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<DmUpkeeprecorditemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param dmUpkeeprecorditemPojo 实例对象
     * @return 实例对象
     */
    DmUpkeeprecorditemPojo insert(DmUpkeeprecorditemPojo dmUpkeeprecorditemPojo);

    /**
     * 修改数据
     *
     * @param dmUpkeeprecorditempojo 实例对象
     * @return 实例对象
     */
    DmUpkeeprecorditemPojo update(DmUpkeeprecorditemPojo dmUpkeeprecorditempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param dmUpkeeprecorditempojo 实例对象
     * @return 实例对象
     */
    DmUpkeeprecorditemPojo clearNull(DmUpkeeprecorditemPojo dmUpkeeprecorditempojo);
}
