package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotAlarmcommentPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;

/**
 * 告警评论表(Iot_AlarmComment)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-24 15:27:08
 */
public interface IotAlarmcommentService {

    IotAlarmcommentPojo getEntity(String key,String tid);

    PageInfo<IotAlarmcommentPojo> getPageList(QueryParam queryParam);

    IotAlarmcommentPojo insert(IotAlarmcommentPojo iotAlarmcommentPojo);

    IotAlarmcommentPojo update(IotAlarmcommentPojo iotAlarmcommentpojo);

    int delete(String key,String tid);

    /**
     * 根据警报ID查询评论列表
     * @param alarmId 警报ID
     * @param tenantId 租户ID
     * @return 评论列表
     */
    List<IotAlarmcommentPojo> getCommentsByAlarmId(String alarmId, String tenantId);

    /**
     * 添加用户评论
     * @param alarmId 警报ID
     * @param userId 用户ID
     * @param userName 用户名
     * @param commentText 评论内容
     * @param tenantId 租户ID
     * @return 创建的评论
     */
    IotAlarmcommentPojo addUserComment(String alarmId, String userId, String userName, String commentText, String tenantId);
}
