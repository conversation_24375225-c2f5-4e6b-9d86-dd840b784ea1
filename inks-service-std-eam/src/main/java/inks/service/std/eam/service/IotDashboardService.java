package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotDashboardPojo;
import com.github.pagehelper.PageInfo;

/**
 * 仪表盘配置表(Iot_Dashboard)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-07 10:06:46
 */
public interface IotDashboardService {

    IotDashboardPojo getEntity(String key,String tid);

    PageInfo<IotDashboardPojo> getPageList(QueryParam queryParam);

    IotDashboardPojo insert(IotDashboardPojo iotDashboardPojo);

    IotDashboardPojo update(IotDashboardPojo iotDashboardpojo);

    int delete(String key,String tid);
}
