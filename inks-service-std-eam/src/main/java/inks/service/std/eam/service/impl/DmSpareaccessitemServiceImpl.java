package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmSpareaccessitemEntity;
import inks.service.std.eam.domain.pojo.DmSpareaccessitemPojo;
import inks.service.std.eam.mapper.DmSpareaccessitemMapper;
import inks.service.std.eam.service.DmSpareaccessitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 出入库项目(DmSpareaccessitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-10 10:09:41
 */
@Service("dmSpareaccessitemService")
public class DmSpareaccessitemServiceImpl implements DmSpareaccessitemService {
    @Resource
    private DmSpareaccessitemMapper dmSpareaccessitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmSpareaccessitemPojo getEntity(String key, String tid) {
        return this.dmSpareaccessitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmSpareaccessitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmSpareaccessitemPojo> lst = dmSpareaccessitemMapper.getPageList(queryParam);
            PageInfo<DmSpareaccessitemPojo> pageInfo = new PageInfo<DmSpareaccessitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DmSpareaccessitemPojo> getList(String Pid, String tid) {
        try {
            List<DmSpareaccessitemPojo> lst = dmSpareaccessitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param dmSpareaccessitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmSpareaccessitemPojo insert(DmSpareaccessitemPojo dmSpareaccessitemPojo) {
        //初始化item的NULL
        DmSpareaccessitemPojo itempojo = this.clearNull(dmSpareaccessitemPojo);
        DmSpareaccessitemEntity dmSpareaccessitemEntity = new DmSpareaccessitemEntity();
        BeanUtils.copyProperties(itempojo, dmSpareaccessitemEntity);
        //生成雪花id
        dmSpareaccessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmSpareaccessitemEntity.setRevision(1);  //乐观锁
        this.dmSpareaccessitemMapper.insert(dmSpareaccessitemEntity);
        return this.getEntity(dmSpareaccessitemEntity.getId(), dmSpareaccessitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmSpareaccessitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmSpareaccessitemPojo update(DmSpareaccessitemPojo dmSpareaccessitemPojo) {
        DmSpareaccessitemEntity dmSpareaccessitemEntity = new DmSpareaccessitemEntity();
        BeanUtils.copyProperties(dmSpareaccessitemPojo, dmSpareaccessitemEntity);
        this.dmSpareaccessitemMapper.update(dmSpareaccessitemEntity);
        return this.getEntity(dmSpareaccessitemEntity.getId(), dmSpareaccessitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmSpareaccessitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param dmSpareaccessitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmSpareaccessitemPojo clearNull(DmSpareaccessitemPojo dmSpareaccessitemPojo) {
        //初始化NULL字段
        if (dmSpareaccessitemPojo.getPid() == null) dmSpareaccessitemPojo.setPid("");
        if (dmSpareaccessitemPojo.getSpareid() == null) dmSpareaccessitemPojo.setSpareid("");
        if (dmSpareaccessitemPojo.getQuantity() == null) dmSpareaccessitemPojo.setQuantity(0D);
        if (dmSpareaccessitemPojo.getPrice() == null) dmSpareaccessitemPojo.setPrice(0D);
        if (dmSpareaccessitemPojo.getAmount() == null) dmSpareaccessitemPojo.setAmount(0D);
        if (dmSpareaccessitemPojo.getRemark() == null) dmSpareaccessitemPojo.setRemark("");
        if (dmSpareaccessitemPojo.getRownum() == null) dmSpareaccessitemPojo.setRownum(0);
        if (dmSpareaccessitemPojo.getLocation() == null) dmSpareaccessitemPojo.setLocation("");
        if (dmSpareaccessitemPojo.getBatchno() == null) dmSpareaccessitemPojo.setBatchno("");
        if (dmSpareaccessitemPojo.getExpirydate() == null) dmSpareaccessitemPojo.setExpirydate(new Date());
        if (dmSpareaccessitemPojo.getTaxprice() == null) dmSpareaccessitemPojo.setTaxprice(0D);
        if (dmSpareaccessitemPojo.getTaxamount() == null) dmSpareaccessitemPojo.setTaxamount(0D);
        if (dmSpareaccessitemPojo.getItemtaxrate() == null) dmSpareaccessitemPojo.setItemtaxrate(0D);
        if (dmSpareaccessitemPojo.getCiteuid() == null) dmSpareaccessitemPojo.setCiteuid("");
        if (dmSpareaccessitemPojo.getCiteitemid() == null) dmSpareaccessitemPojo.setCiteitemid("");
        if (dmSpareaccessitemPojo.getCustom1() == null) dmSpareaccessitemPojo.setCustom1("");
        if (dmSpareaccessitemPojo.getCustom2() == null) dmSpareaccessitemPojo.setCustom2("");
        if (dmSpareaccessitemPojo.getCustom3() == null) dmSpareaccessitemPojo.setCustom3("");
        if (dmSpareaccessitemPojo.getCustom4() == null) dmSpareaccessitemPojo.setCustom4("");
        if (dmSpareaccessitemPojo.getCustom5() == null) dmSpareaccessitemPojo.setCustom5("");
        if (dmSpareaccessitemPojo.getCustom6() == null) dmSpareaccessitemPojo.setCustom6("");
        if (dmSpareaccessitemPojo.getCustom7() == null) dmSpareaccessitemPojo.setCustom7("");
        if (dmSpareaccessitemPojo.getCustom8() == null) dmSpareaccessitemPojo.setCustom8("");
        if (dmSpareaccessitemPojo.getTenantid() == null) dmSpareaccessitemPojo.setTenantid("");
        if (dmSpareaccessitemPojo.getRevision() == null) dmSpareaccessitemPojo.setRevision(0);
        return dmSpareaccessitemPojo;
    }
}
