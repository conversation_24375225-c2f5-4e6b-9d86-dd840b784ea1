package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.DmIubricationPojo;
import inks.service.std.eam.service.DmIubricationService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 设备润滑(Dm_Iubrication)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-10 12:40:14
 */

public class DmIubricationController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(DmIubricationController.class);
    /**
     * 服务对象
     */
    @Resource
    private DmIubricationService dmIubricationService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取设备润滑详细信息", notes = "获取设备润滑详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Iubrication.List")
    public R<DmIubricationPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmIubricationService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Iubrication.List")
    public R<PageInfo<DmIubricationPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_Iubrication.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.dmIubricationService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增设备润滑", notes = "新增设备润滑", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Iubrication.Add")
    public R<DmIubricationPojo> create(@RequestBody String json) {
        try {
            DmIubricationPojo dmIubricationPojo = JSONArray.parseObject(json, DmIubricationPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            R r = systemFeignService.getBillCode("DxxMxxB1", loginUser.getToken());
            if (r.getCode() == 200)
                dmIubricationPojo.setRefno(r.getData().toString());
            else {
                return R.fail("单据编码读取出错" + r);
            }
            dmIubricationPojo.setCreateby(loginUser.getRealName());   // 创建者
            dmIubricationPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            dmIubricationPojo.setCreatedate(new Date());   // 创建时间
            dmIubricationPojo.setLister(loginUser.getRealname());   // 制表
            dmIubricationPojo.setListerid(loginUser.getUserid());    // 制表id  
            dmIubricationPojo.setModifydate(new Date());   //修改时间
            dmIubricationPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.dmIubricationService.insert(dmIubricationPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改设备润滑", notes = "修改设备润滑", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_Iubrication.Edit")
    public R<DmIubricationPojo> update(@RequestBody String json) {
        try {
            DmIubricationPojo dmIubricationPojo = JSONArray.parseObject(json, DmIubricationPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmIubricationPojo.setLister(loginUser.getRealname());   // 制表
            dmIubricationPojo.setListerid(loginUser.getUserid());    // 制表id  
            dmIubricationPojo.setTenantid(loginUser.getTenantid());   //租户id
            dmIubricationPojo.setModifydate(new Date());   //修改时间
//            dmIubricationPojo.setAssessor(""); // 审核员
//            dmIubricationPojo.setAssessorid(""); // 审核员id
//            dmIubricationPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.dmIubricationService.update(dmIubricationPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除设备润滑", notes = "删除设备润滑", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Iubrication.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmIubricationService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核设备润滑", notes = "审核设备润滑", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Iubrication.Approval")
    public R<DmIubricationPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            DmIubricationPojo dmIubricationPojo = this.dmIubricationService.getEntity(key, loginUser.getTenantid());
            if (dmIubricationPojo.getAssessor().equals("")) {
                dmIubricationPojo.setAssessor(loginUser.getRealname()); //审核员
                dmIubricationPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                dmIubricationPojo.setAssessor(""); //审核员
                dmIubricationPojo.setAssessorid(""); //审核员
            }
            dmIubricationPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.dmIubricationService.approval(dmIubricationPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_Iubrication.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        DmIubricationPojo dmIubricationPojo = this.dmIubricationService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(dmIubricationPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

