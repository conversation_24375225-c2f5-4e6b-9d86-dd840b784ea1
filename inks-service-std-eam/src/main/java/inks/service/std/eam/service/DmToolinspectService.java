package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolinspectPojo;
import inks.service.std.eam.domain.pojo.DmToolinspectitemdetailPojo;
import com.github.pagehelper.PageInfo;

/**
 * 工装具校验主表(DmToolinspect)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-19 10:17:23
 */
public interface DmToolinspectService {

    DmToolinspectPojo getEntity(String key,String tid);

    PageInfo<DmToolinspectitemdetailPojo> getPageList(QueryParam queryParam);

    DmToolinspectPojo getBillEntity(String key,String tid);

    PageInfo<DmToolinspectPojo> getBillList(QueryParam queryParam);

    PageInfo<DmToolinspectPojo> getPageTh(QueryParam queryParam);

    DmToolinspectPojo insert(DmToolinspectPojo dmToolinspectPojo);

    DmToolinspectPojo update(DmToolinspectPojo dmToolinspectpojo);

    int delete(String key,String tid);

}
