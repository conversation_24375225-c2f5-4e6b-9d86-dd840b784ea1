package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmPatrolstdPojo;
import inks.service.std.eam.domain.pojo.DmPatrolstditemdetailPojo;

/**
 * 检查标准(DmPatrolstd)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 13:10:31
 */
public interface DmPatrolstdService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolstdPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolstditemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolstdPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolstdPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmPatrolstdPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmPatrolstdPojo 实例对象
     * @return 实例对象
     */
    DmPatrolstdPojo insert(DmPatrolstdPojo dmPatrolstdPojo);

    /**
     * 修改数据
     *
     * @param dmPatrolstdpojo 实例对象
     * @return 实例对象
     */
    DmPatrolstdPojo update(DmPatrolstdPojo dmPatrolstdpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param dmPatrolstdPojo 实例对象
     * @return 实例对象
     */
    DmPatrolstdPojo approval(DmPatrolstdPojo dmPatrolstdPojo);
}
