package inks.service.std.eam.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.eam.domain.pojo.IotKeydictionaryPojo;
import inks.service.std.eam.domain.IotKeydictionaryEntity;
import inks.service.std.eam.mapper.IotKeydictionaryMapper;
import inks.service.std.eam.service.IotKeydictionaryService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 键值映射字典表(IotKeydictionary)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:32
 */
@Service("iotKeydictionaryService")
public class IotKeydictionaryServiceImpl implements IotKeydictionaryService {
    @Resource
    private IotKeydictionaryMapper iotKeydictionaryMapper;

    @Override
    public IotKeydictionaryPojo getEntity(String key, String tid) {
        return this.iotKeydictionaryMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotKeydictionaryPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotKeydictionaryPojo> lst = iotKeydictionaryMapper.getPageList(queryParam);
            PageInfo<IotKeydictionaryPojo> pageInfo = new PageInfo<IotKeydictionaryPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotKeydictionaryPojo insert(IotKeydictionaryPojo iotKeydictionaryPojo) {
        //初始化NULL字段
        cleanNull(iotKeydictionaryPojo);
        IotKeydictionaryEntity iotKeydictionaryEntity = new IotKeydictionaryEntity(); 
        BeanUtils.copyProperties(iotKeydictionaryPojo,iotKeydictionaryEntity);
          //生成雪花id
          iotKeydictionaryEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotKeydictionaryEntity.setRevision(1);  //乐观锁
          this.iotKeydictionaryMapper.insert(iotKeydictionaryEntity);
        return this.getEntity(iotKeydictionaryEntity.getId(),iotKeydictionaryEntity.getTenantid());
    }


    @Override
    public IotKeydictionaryPojo update(IotKeydictionaryPojo iotKeydictionaryPojo) {
        IotKeydictionaryEntity iotKeydictionaryEntity = new IotKeydictionaryEntity(); 
        BeanUtils.copyProperties(iotKeydictionaryPojo,iotKeydictionaryEntity);
        this.iotKeydictionaryMapper.update(iotKeydictionaryEntity);
        return this.getEntity(iotKeydictionaryEntity.getId(),iotKeydictionaryEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotKeydictionaryMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotKeydictionaryPojo iotKeydictionaryPojo) {
        if(iotKeydictionaryPojo.getKeyid()==null) iotKeydictionaryPojo.setKeyid(0);
        if(iotKeydictionaryPojo.getKeyvalue()==null) iotKeydictionaryPojo.setKeyvalue("");
        if(iotKeydictionaryPojo.getRemark()==null) iotKeydictionaryPojo.setRemark("");
        if(iotKeydictionaryPojo.getRownum()==null) iotKeydictionaryPojo.setRownum(0);
        if(iotKeydictionaryPojo.getCreateby()==null) iotKeydictionaryPojo.setCreateby("");
        if(iotKeydictionaryPojo.getCreatebyid()==null) iotKeydictionaryPojo.setCreatebyid("");
        if(iotKeydictionaryPojo.getCreatedate()==null) iotKeydictionaryPojo.setCreatedate(new Date());
        if(iotKeydictionaryPojo.getLister()==null) iotKeydictionaryPojo.setLister("");
        if(iotKeydictionaryPojo.getListerid()==null) iotKeydictionaryPojo.setListerid("");
        if(iotKeydictionaryPojo.getModifydate()==null) iotKeydictionaryPojo.setModifydate(new Date());
        if(iotKeydictionaryPojo.getCustom1()==null) iotKeydictionaryPojo.setCustom1("");
        if(iotKeydictionaryPojo.getCustom2()==null) iotKeydictionaryPojo.setCustom2("");
        if(iotKeydictionaryPojo.getCustom3()==null) iotKeydictionaryPojo.setCustom3("");
        if(iotKeydictionaryPojo.getCustom4()==null) iotKeydictionaryPojo.setCustom4("");
        if(iotKeydictionaryPojo.getCustom5()==null) iotKeydictionaryPojo.setCustom5("");
        if(iotKeydictionaryPojo.getCustom6()==null) iotKeydictionaryPojo.setCustom6("");
        if(iotKeydictionaryPojo.getCustom7()==null) iotKeydictionaryPojo.setCustom7("");
        if(iotKeydictionaryPojo.getCustom8()==null) iotKeydictionaryPojo.setCustom8("");
        if(iotKeydictionaryPojo.getCustom9()==null) iotKeydictionaryPojo.setCustom9("");
        if(iotKeydictionaryPojo.getCustom10()==null) iotKeydictionaryPojo.setCustom10("");
        if(iotKeydictionaryPojo.getTenantid()==null) iotKeydictionaryPojo.setTenantid("");
        if(iotKeydictionaryPojo.getTenantname()==null) iotKeydictionaryPojo.setTenantname("");
        if(iotKeydictionaryPojo.getRevision()==null) iotKeydictionaryPojo.setRevision(0);
   }

}
