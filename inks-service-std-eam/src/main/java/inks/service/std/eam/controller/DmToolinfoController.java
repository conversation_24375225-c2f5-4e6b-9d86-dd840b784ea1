package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolinfoPojo;
import inks.service.std.eam.service.DmToolinfoService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 工装具基础信息(Dm_ToolInfo)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-09 16:30:34
 */
//@RestController
//@RequestMapping("dmToolinfo")
public class DmToolinfoController {

    @Resource
    private DmToolinfoService dmToolinfoService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(DmToolinfoController.class);


    @ApiOperation(value=" 获取工装具基础信息详细信息", notes="获取工装具基础信息详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolInfo.List")
    public R<DmToolinfoPojo> getEntity(String key) {
    try {
           // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolinfoService.getEntity(key, loginUser.getTenantid()));
         }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolInfo.List")
    public R<PageInfo<DmToolinfoPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Dm_ToolInfo.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmToolinfoService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增工装具基础信息", notes="新增工装具基础信息", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolInfo.Add")
    public R<DmToolinfoPojo> create(@RequestBody String json) {
       try {
       DmToolinfoPojo dmToolinfoPojo = JSONArray.parseObject(json,DmToolinfoPojo.class);       
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolinfoPojo.setCreateby(loginUser.getRealName());   // 创建者
            dmToolinfoPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            dmToolinfoPojo.setCreatedate(new Date());   // 创建时间
            dmToolinfoPojo.setLister(loginUser.getRealname());   // 制表
            dmToolinfoPojo.setListerid(loginUser.getUserid());    // 制表id  
            dmToolinfoPojo.setModifydate(new Date());   //修改时间
            dmToolinfoPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.dmToolinfoService.insert(dmToolinfoPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改工装具基础信息", notes="修改工装具基础信息", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolInfo.Edit")
    public R<DmToolinfoPojo> update(@RequestBody String json) {
       try {
         DmToolinfoPojo dmToolinfoPojo = JSONArray.parseObject(json,DmToolinfoPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolinfoPojo.setLister(loginUser.getRealname());   // 制表
            dmToolinfoPojo.setListerid(loginUser.getUserid());    // 制表id  
            dmToolinfoPojo.setTenantid(loginUser.getTenantid());   //租户id
            dmToolinfoPojo.setModifydate(new Date());   //修改时间
//            dmToolinfoPojo.setAssessor(""); // 审核员
//            dmToolinfoPojo.setAssessorid(""); // 审核员id
//            dmToolinfoPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.dmToolinfoService.update(dmToolinfoPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除工装具基础信息", notes="删除工装具基础信息", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolInfo.Delete")
    public R<Integer> delete(String key) {
    try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolinfoService.delete(key, loginUser.getTenantid()));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolInfo.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        DmToolinfoPojo dmToolinfoPojo = this.dmToolinfoService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(dmToolinfoPojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

