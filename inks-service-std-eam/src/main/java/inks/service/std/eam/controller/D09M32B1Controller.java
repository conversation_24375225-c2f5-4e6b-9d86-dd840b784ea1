package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.service.IotRulechainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 规则链定义(Iot_RuleChain)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-08 13:01:06
 */
@RestController
@RequestMapping("D09M32B1")
@Api(tags = "D09M32B1:规则链定义")
public class D09M32B1Controller extends IotRulechainController {
    @Resource
    private IotRulechainService iotRulechainService;
    @Resource
    private TokenService tokenService;



    @ApiOperation(value=" 获取规则链定义表详细信息", notes="获取规则链定义表详细信息", produces="application/json")
    @RequestMapping(value="/setRoot",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_RuleChain.List")
    public R<Integer> setRoot(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotRulechainService.setRoot(key,loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


}
