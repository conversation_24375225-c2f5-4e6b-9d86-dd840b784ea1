package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.NoRepeatSubmit;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.DmToolusagePojo;
import inks.service.std.eam.domain.pojo.DmToolusageitemPojo;
import inks.service.std.eam.domain.pojo.DmToolusageitemdetailPojo;
import inks.service.std.eam.service.DmToolusageService;
import inks.service.std.eam.service.DmToolusageitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;
import java.util.Date;
import java.util.Map;


/**
 * 工装具使用记录表(Dm_ToolUsage)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:20
 */
//@RestController
//@RequestMapping("dmToolusage")
public class DmToolusageController {

    @Resource
    private DmToolusageService dmToolusageService;
    @Resource
    private DmToolusageitemService dmToolusageitemService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;
    @Resource
    private SystemFeignService systemFeignService;

    private final static Logger logger = LoggerFactory.getLogger(DmToolusageController.class);


    @ApiOperation(value = " 获取工装具使用记录表详细信息", notes = "获取工装具使用记录表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.List")
    public R<DmToolusagePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolusageService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.List")
    public R<PageInfo<DmToolusageitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_ToolUsage.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmToolusageService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取工装具使用记录表详细信息", notes = "获取工装具使用记录表详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.List")
    public R<DmToolusagePojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolusageService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.List")
    public R<PageInfo<DmToolusagePojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_ToolUsage.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmToolusageService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.List")
    public R<PageInfo<DmToolusagePojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Dm_ToolUsage.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmToolusageService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    private final String moduleCode = "D09M11B1";

    @ApiOperation(value = " 新增工装具使用记录表", notes = "新增工装具使用记录表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.Add")
    @NoRepeatSubmit
    public R<DmToolusagePojo> create(@RequestBody String json, @RequestParam(defaultValue = "1") Integer warn) throws SQLException {
        DmToolusagePojo dmToolusagePojo = JSONArray.parseObject(json, DmToolusagePojo.class);
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        // 生成单据编码RefNoUtils
        String refno = RefNoUtils.generateRefNo(moduleCode, "Dm_ToolUsage", "", tid);
        dmToolusagePojo.setRefno(refno);
        dmToolusagePojo.setCreateby(loginUser.getRealName());   // 创建者
        dmToolusagePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        dmToolusagePojo.setCreatedate(new Date());   // 创建时间
        dmToolusagePojo.setLister(loginUser.getRealname());   // 制表
        dmToolusagePojo.setListerid(loginUser.getUserid());    // 制表id
        dmToolusagePojo.setModifydate(new Date());   //修改时间
        dmToolusagePojo.setTenantid(tid);   //租户id
        DmToolusagePojo insertDB = this.dmToolusageService.insert(dmToolusagePojo,warn);
        RefNoUtils.saveRedisRefNo(refno, moduleCode, tid);// 保存单据编码RefNoUtils
        return R.ok(insertDB);
    }


    @ApiOperation(value = "修改工装具使用记录表", notes = "修改工装具使用记录表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.Edit")
    public R<DmToolusagePojo> update(@RequestBody String json, @RequestParam(defaultValue = "1") Integer warn) {
        DmToolusagePojo dmToolusagePojo = JSONArray.parseObject(json, DmToolusagePojo.class);
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        dmToolusagePojo.setLister(loginUser.getRealname());   // 制表
        dmToolusagePojo.setListerid(loginUser.getUserid());    // 制表id
        dmToolusagePojo.setModifydate(new Date());   //修改时间
        dmToolusagePojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.dmToolusageService.update(dmToolusagePojo,warn));
    }


    @ApiOperation(value = "删除工装具使用记录表", notes = "删除工装具使用记录表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.Delete")
    public R<Integer> delete(String key) throws SQLException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        this.dmToolusageService.delete(key, loginUser.getTenantid());
        RefNoUtils.deleteRedisRefNo(moduleCode, loginUser.getTenantid());
        return R.ok(1);
    }

    /*子表操作 */

    @ApiOperation(value = " 新增工装具使用记录表Item", notes = "新增工装具使用记录表Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.Add")
    public R<DmToolusageitemPojo> createItem(@RequestBody String json) {
        try {
            DmToolusageitemPojo dmToolusageitemPojo = JSONArray.parseObject(json, DmToolusageitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolusageitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.dmToolusageitemService.insert(dmToolusageitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改工装具使用记录表Item", notes = "修改工装具使用记录表Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.Edit")
    public R<DmToolusageitemPojo> updateItem(@RequestBody String json) {
        try {
            DmToolusageitemPojo dmToolusageitemPojo = JSONArray.parseObject(json, DmToolusageitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolusageitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.dmToolusageitemService.update(dmToolusageitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除工装具使用记录表Item", notes = "删除工装具使用记录表Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolusageitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolUsage.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        DmToolusagePojo dmToolusagePojo = this.dmToolusageService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(dmToolusagePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = dmToolusagePojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    DmToolusageitemPojo dmToolusageitemPojo = new DmToolusageitemPojo();
                    dmToolusagePojo.getItem().add(dmToolusageitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(dmToolusagePojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

