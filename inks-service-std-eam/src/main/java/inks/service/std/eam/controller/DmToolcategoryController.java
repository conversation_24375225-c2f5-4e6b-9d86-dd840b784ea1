package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolcategoryPojo;
import inks.service.std.eam.service.DmToolcategoryService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 工装具类别表(Dm_ToolCategory)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-09 14:05:53
 */
//@RestController
//@RequestMapping("dmToolcategory")
public class DmToolcategoryController {

    @Resource
    private DmToolcategoryService dmToolcategoryService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(DmToolcategoryController.class);


    @ApiOperation(value=" 获取工装具类别表详细信息", notes="获取工装具类别表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolCategory.List")
    public R<DmToolcategoryPojo> getEntity(String key) {
    try {
           // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolcategoryService.getEntity(key, loginUser.getTenantid()));
         }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolCategory.List")
    public R<PageInfo<DmToolcategoryPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Dm_ToolCategory.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.dmToolcategoryService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增工装具类别表", notes="新增工装具类别表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolCategory.Add")
    public R<DmToolcategoryPojo> create(@RequestBody String json) {
       try {
       DmToolcategoryPojo dmToolcategoryPojo = JSONArray.parseObject(json,DmToolcategoryPojo.class);       
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolcategoryPojo.setCreateby(loginUser.getRealName());   // 创建者
            dmToolcategoryPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            dmToolcategoryPojo.setCreatedate(new Date());   // 创建时间
            dmToolcategoryPojo.setLister(loginUser.getRealname());   // 制表
            dmToolcategoryPojo.setListerid(loginUser.getUserid());    // 制表id  
            dmToolcategoryPojo.setModifydate(new Date());   //修改时间
            dmToolcategoryPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.dmToolcategoryService.insert(dmToolcategoryPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改工装具类别表", notes="修改工装具类别表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Dm_ToolCategory.Edit")
    public R<DmToolcategoryPojo> update(@RequestBody String json) {
       try {
         DmToolcategoryPojo dmToolcategoryPojo = JSONArray.parseObject(json,DmToolcategoryPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            dmToolcategoryPojo.setLister(loginUser.getRealname());   // 制表
            dmToolcategoryPojo.setListerid(loginUser.getUserid());    // 制表id  
            dmToolcategoryPojo.setTenantid(loginUser.getTenantid());   //租户id
            dmToolcategoryPojo.setModifydate(new Date());   //修改时间
//            dmToolcategoryPojo.setAssessor(""); // 审核员
//            dmToolcategoryPojo.setAssessorid(""); // 审核员id
//            dmToolcategoryPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.dmToolcategoryService.update(dmToolcategoryPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除工装具类别表", notes="删除工装具类别表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolCategory.Delete")
    public R<Integer> delete(String key) {
    try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.dmToolcategoryService.delete(key, loginUser.getTenantid()));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Dm_ToolCategory.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        DmToolcategoryPojo dmToolcategoryPojo = this.dmToolcategoryService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(dmToolcategoryPojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

