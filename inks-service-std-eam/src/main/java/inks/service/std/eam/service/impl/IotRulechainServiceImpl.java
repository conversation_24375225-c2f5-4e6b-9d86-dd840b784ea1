package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.IotRulechainEntity;
import inks.service.std.eam.domain.pojo.IotRulechainPojo;
import inks.service.std.eam.domain.pojo.IotRulenodePojo;
import inks.service.std.eam.mapper.IotRulechainMapper;
import inks.service.std.eam.mapper.IotRulenodeMapper;
import inks.service.std.eam.service.IotRulechainService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 规则链定义表(IotRulechain)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:41
 */
@Service("iotRulechainService")
public class IotRulechainServiceImpl implements IotRulechainService {
    @Resource
    private IotRulechainMapper iotRulechainMapper;
    @Resource
    private IotRulenodeMapper iotRulenodeMapper;

    @Override
    public IotRulechainPojo getEntity(String key, String tid) {
        return this.iotRulechainMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotRulechainPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotRulechainPojo> lst = iotRulechainMapper.getPageList(queryParam);
            PageInfo<IotRulechainPojo> pageInfo = new PageInfo<IotRulechainPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotRulechainPojo insert(IotRulechainPojo iotRulechainPojo) {
        //初始化NULL字段
        cleanNull(iotRulechainPojo);
        IotRulechainEntity iotRulechainEntity = new IotRulechainEntity(); 
        BeanUtils.copyProperties(iotRulechainPojo,iotRulechainEntity);
          //生成雪花id
          iotRulechainEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotRulechainEntity.setRevision(1);  //乐观锁
          this.iotRulechainMapper.insert(iotRulechainEntity);
        return this.getEntity(iotRulechainEntity.getId(),iotRulechainEntity.getTenantid());
    }


    @Override
    public IotRulechainPojo update(IotRulechainPojo iotRulechainPojo) {
        IotRulechainEntity iotRulechainEntity = new IotRulechainEntity(); 
        BeanUtils.copyProperties(iotRulechainPojo,iotRulechainEntity);
        this.iotRulechainMapper.update(iotRulechainEntity);
        return this.getEntity(iotRulechainEntity.getId(),iotRulechainEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotRulechainMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotRulechainPojo iotRulechainPojo) {
        if(iotRulechainPojo.getAdditionalinfo()==null) iotRulechainPojo.setAdditionalinfo("");
        if(iotRulechainPojo.getConfiguration()==null) iotRulechainPojo.setConfiguration("");
        if(iotRulechainPojo.getRulechainname()==null) iotRulechainPojo.setRulechainname("");
        if(iotRulechainPojo.getRulechaintype()==null) iotRulechainPojo.setRulechaintype("");
        if(iotRulechainPojo.getFirstrulenodeid()==null) iotRulechainPojo.setFirstrulenodeid("");
        if(iotRulechainPojo.getRoot()==null) iotRulechainPojo.setRoot(0);
        if(iotRulechainPojo.getDebugmode()==null) iotRulechainPojo.setDebugmode(0);
        if(iotRulechainPojo.getExternalid()==null) iotRulechainPojo.setExternalid("");
        if(iotRulechainPojo.getRemark()==null) iotRulechainPojo.setRemark("");
        if(iotRulechainPojo.getRownum()==null) iotRulechainPojo.setRownum(0);
        if(iotRulechainPojo.getCreateby()==null) iotRulechainPojo.setCreateby("");
        if(iotRulechainPojo.getCreatebyid()==null) iotRulechainPojo.setCreatebyid("");
        if(iotRulechainPojo.getCreatedate()==null) iotRulechainPojo.setCreatedate(new Date());
        if(iotRulechainPojo.getLister()==null) iotRulechainPojo.setLister("");
        if(iotRulechainPojo.getListerid()==null) iotRulechainPojo.setListerid("");
        if(iotRulechainPojo.getModifydate()==null) iotRulechainPojo.setModifydate(new Date());
        if(iotRulechainPojo.getCustom1()==null) iotRulechainPojo.setCustom1("");
        if(iotRulechainPojo.getCustom2()==null) iotRulechainPojo.setCustom2("");
        if(iotRulechainPojo.getCustom3()==null) iotRulechainPojo.setCustom3("");
        if(iotRulechainPojo.getCustom4()==null) iotRulechainPojo.setCustom4("");
        if(iotRulechainPojo.getCustom5()==null) iotRulechainPojo.setCustom5("");
        if(iotRulechainPojo.getCustom6()==null) iotRulechainPojo.setCustom6("");
        if(iotRulechainPojo.getCustom7()==null) iotRulechainPojo.setCustom7("");
        if(iotRulechainPojo.getCustom8()==null) iotRulechainPojo.setCustom8("");
        if(iotRulechainPojo.getCustom9()==null) iotRulechainPojo.setCustom9("");
        if(iotRulechainPojo.getCustom10()==null) iotRulechainPojo.setCustom10("");
        if(iotRulechainPojo.getTenantid()==null) iotRulechainPojo.setTenantid("");
        if(iotRulechainPojo.getTenantname()==null) iotRulechainPojo.setTenantname("");
        if(iotRulechainPojo.getRevision()==null) iotRulechainPojo.setRevision(0);
   }

    @Override
    public IotRulechainPojo getRuleBillByDeviceProfile(String deviceprofileid) {
        IotRulechainPojo rulechainPojo = this.iotRulechainMapper.getEntityByDeviceProfile(deviceprofileid);
        List<IotRulenodePojo> rulenodes = iotRulenodeMapper.getListByRulechainId(rulechainPojo.getId());
        rulechainPojo.setRulenodes(rulenodes);
        return rulechainPojo;
    }

    @Override
    public int setRoot(String key, String tid) {
        // 保证只有一个根节点root
        return iotRulechainMapper.setRoot(key, tid);
    }
}
