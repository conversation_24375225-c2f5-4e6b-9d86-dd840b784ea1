package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotTskvPojo;
import inks.service.std.eam.service.IotTskvService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 时间序列遥测数据表(Iot_TsKv)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:48
 */
//@RestController
//@RequestMapping("iotTskv")
public class IotTskvController {

    @Resource
    private IotTskvService iotTskvService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(IotTskvController.class);


    @ApiOperation(value=" 获取时间序列遥测数据表详细信息", notes="获取时间序列遥测数据表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_TsKv.List")
    public R<IotTskvPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotTskvService.getEntity(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_TsKv.List")
    public R<PageInfo<IotTskvPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Iot_TsKv.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.iotTskvService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增时间序列遥测数据表", notes="新增时间序列遥测数据表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_TsKv.Add")
    public R<IotTskvPojo> create(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
       IotTskvPojo iotTskvPojo = JSONArray.parseObject(json,IotTskvPojo.class);       
            iotTskvPojo.setCreatedate(new Date());   // 创建时间
            iotTskvPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.iotTskvService.insert(iotTskvPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改时间序列遥测数据表", notes="修改时间序列遥测数据表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_TsKv.Edit")
    public R<IotTskvPojo> update(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
         IotTskvPojo iotTskvPojo = JSONArray.parseObject(json,IotTskvPojo.class);
            iotTskvPojo.setTenantid(loginUser.getTenantid());   //租户id
//            iotTskvPojo.setAssessor(""); // 审核员
//            iotTskvPojo.setAssessorid(""); // 审核员id
//            iotTskvPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.iotTskvService.update(iotTskvPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除时间序列遥测数据表", notes="删除时间序列遥测数据表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_TsKv.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotTskvService.delete(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_TsKv.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        IotTskvPojo iotTskvPojo = this.iotTskvService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(iotTskvPojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

