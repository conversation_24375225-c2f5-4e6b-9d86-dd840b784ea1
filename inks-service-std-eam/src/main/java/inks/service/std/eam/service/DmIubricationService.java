package inks.service.std.eam.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmIubricationPojo;

/**
 * 设备润滑(DmIubrication)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-10 12:40:14
 */
public interface DmIubricationService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmIubricationPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<DmIubricationPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmIubricationPojo 实例对象
     * @return 实例对象
     */
    DmIubricationPojo insert(DmIubricationPojo dmIubricationPojo);

    /**
     * 修改数据
     *
     * @param dmIubricationpojo 实例对象
     * @return 实例对象
     */
    DmIubricationPojo update(DmIubricationPojo dmIubricationpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param dmIubricationPojo 实例对象
     * @return 实例对象
     */
    DmIubricationPojo approval(DmIubricationPojo dmIubricationPojo);
}
