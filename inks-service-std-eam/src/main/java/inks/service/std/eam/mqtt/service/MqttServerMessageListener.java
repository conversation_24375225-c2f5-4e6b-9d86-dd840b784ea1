package inks.service.std.eam.mqtt.service;

import inks.service.std.eam.iotrule.A_IotMqttService;
import inks.service.std.eam.mqtt.websocket.MqttWebSocketHandler;
import org.dromara.mica.mqtt.codec.MqttPublishMessage;
import org.dromara.mica.mqtt.codec.MqttQoS;
import org.dromara.mica.mqtt.core.server.event.IMqttMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.tio.core.ChannelContext;

import java.nio.charset.StandardCharsets;

/**
 * MQTT服务器消息监听器
 *
 * <AUTHOR>
 */
@Service
public class MqttServerMessageListener implements IMqttMessageListener {

    private static final Logger logger = LoggerFactory.getLogger(MqttServerMessageListener.class);

    @Autowired
    private MqttStatsService mqttStatsService;

    @Autowired
    private MqttWebSocketHandler webSocketHandler;

    @Autowired
    private A_IotMqttService iotMqttService;


    @Override
    public void onMessage(ChannelContext context, String clientId, String topic, MqttQoS mqttQoS, MqttPublishMessage message) {
        // 从MqttPublishMessage对象中获取payload
        byte[] payload = message.getPayload();
        String payloadStr = new String(payload, StandardCharsets.UTF_8);

        // 从ChannelContext中获取username（在认证时存储的）
        String username = context.get("username");
        String deviceId = context.get("deviceId");

        logger.info("=== MQTT消息接收 === ClientId: {}, Username: {}, DeviceId: {}, Topic: {}, QoS: {}, PayloadLength: {}bytes",
                clientId,
                username != null ? username : "未知",
                deviceId != null ? deviceId : "未知",
                topic,
                mqttQoS,
                payload.length);
        logger.debug("MQTT消息内容: {}", payloadStr);

        // 更新统计数据
        mqttStatsService.onMessageReceived(clientId, topic, payload);

        // 通过WebSocket推送消息到前端
        webSocketHandler.broadcastMqttMessage(clientId, topic, payloadStr);

        // 调用规则引擎处理消息（不再在此处处理主题逻辑）
        if (deviceId != null && !deviceId.trim().isEmpty()) {
            try {
                iotMqttService.mqttToRule(topic, payloadStr, deviceId, username);
                logger.debug("规则引擎处理完成 - DeviceId: {}, Topic: {}", deviceId, topic);
            } catch (Exception e) {
                logger.error("规则引擎处理异常 - DeviceId: {}, Topic: {}, Error: {}",
                        deviceId, topic, e.getMessage(), e);
            }
        } else {
            logger.warn("设备ID为空，跳过规则处理 - ClientId: {}, Username: {}, Topic: {}",
                    clientId, username, topic);
        }
    }
}
