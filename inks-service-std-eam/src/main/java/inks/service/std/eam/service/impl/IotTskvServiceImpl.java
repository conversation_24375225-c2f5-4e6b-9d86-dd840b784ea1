package inks.service.std.eam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.IotTskvEntity;
import inks.service.std.eam.domain.pojo.IotTskvPojo;
import inks.service.std.eam.mapper.IotTskvMapper;
import inks.service.std.eam.service.IotTskvService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 时间序列遥测数据表(IotTskv)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:55
 */
@Service("iotTskvService")
public class IotTskvServiceImpl implements IotTskvService {
    @Resource
    private IotTskvMapper iotTskvMapper;

    @Override
    public IotTskvPojo getEntity(String key, String tid) {
        return this.iotTskvMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<IotTskvPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotTskvPojo> lst = iotTskvMapper.getPageList(queryParam);
            PageInfo<IotTskvPojo> pageInfo = new PageInfo<IotTskvPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public IotTskvPojo insert(IotTskvPojo iotTskvPojo) {
        //初始化NULL字段
        cleanNull(iotTskvPojo);
        IotTskvEntity iotTskvEntity = new IotTskvEntity();
        BeanUtils.copyProperties(iotTskvPojo, iotTskvEntity);
        //生成雪花id
        iotTskvEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.iotTskvMapper.insert(iotTskvEntity);
        return this.getEntity(iotTskvEntity.getId(), iotTskvEntity.getTenantid());
    }


    @Override
    public IotTskvPojo update(IotTskvPojo iotTskvPojo) {
        IotTskvEntity iotTskvEntity = new IotTskvEntity();
        BeanUtils.copyProperties(iotTskvPojo, iotTskvEntity);
        this.iotTskvMapper.update(iotTskvEntity);
        return this.getEntity(iotTskvEntity.getId(), iotTskvEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotTskvMapper.delete(key, tid);
    }


    private static void cleanNull(IotTskvPojo iotTskvPojo) {
        if (iotTskvPojo.getEntityid() == null) iotTskvPojo.setEntityid("");
        if (iotTskvPojo.getKeyid() == null) iotTskvPojo.setKeyid(0);
        if(iotTskvPojo.getKeyv()==null) iotTskvPojo.setKeyv("");
        if(iotTskvPojo.getBoolv()==null) iotTskvPojo.setBoolv(0);
        if (iotTskvPojo.getStrv() == null) iotTskvPojo.setStrv("");
        if (iotTskvPojo.getDblv() == null) iotTskvPojo.setDblv(0D);
        if (iotTskvPojo.getJsonv() == null) iotTskvPojo.setJsonv("{}");
        if (iotTskvPojo.getCreatedate() == null) iotTskvPojo.setCreatedate(new Date());
        if (iotTskvPojo.getCustom1() == null) iotTskvPojo.setCustom1("");
        if (iotTskvPojo.getCustom2() == null) iotTskvPojo.setCustom2("");
        if (iotTskvPojo.getCustom3() == null) iotTskvPojo.setCustom3("");
        if (iotTskvPojo.getCustom4() == null) iotTskvPojo.setCustom4("");
        if (iotTskvPojo.getCustom5() == null) iotTskvPojo.setCustom5("");
        if (iotTskvPojo.getCustom6() == null) iotTskvPojo.setCustom6("");
        if (iotTskvPojo.getCustom7() == null) iotTskvPojo.setCustom7("");
        if (iotTskvPojo.getCustom8() == null) iotTskvPojo.setCustom8("");
        if (iotTskvPojo.getCustom9() == null) iotTskvPojo.setCustom9("");
        if (iotTskvPojo.getCustom10() == null) iotTskvPojo.setCustom10("");
        if (iotTskvPojo.getTenantid() == null) iotTskvPojo.setTenantid("");
        if (iotTskvPojo.getTenantname() == null) iotTskvPojo.setTenantname("");
    }

    @Override
    public int batchInsert(JSONObject mqttJSON) {
        // 取出设备 SN 和时间戳
        String entityid = mqttJSON.getString("entityid");
        long ts = mqttJSON.getLongValue("ts");
        //mqttJSON.remove("entityid");
        //mqttJSON.remove("ts");

        List<IotTskvPojo> list = new ArrayList<>();
        Date now = new Date();

        for (String key : mqttJSON.keySet()) {
            // 排除元数据字段
            if (Arrays.asList("entityid", "ts", "sn").contains(key)) continue;
            Object val = mqttJSON.get(key);
            IotTskvPojo p = new IotTskvPojo();
            p.setId(inksSnowflake.getSnowflake().nextIdStr());
            p.setEntityid(entityid);
            p.setKeyid(0); // TODO: 根据 key 查字典设置 keyid
            p.setKeyv(key);
            p.setTs(ts);
            p.setCreatedate(now);
            p.setTenantid("");

            // 根据值类型填充不同列
            if (val instanceof Boolean) {
                p.setBoolv(((Boolean) val) ? 1 : 0);//转换为int类型
            } else if (val instanceof Integer || val instanceof Long) {
                p.setLongv(((Number) val).longValue());
            } else if (val instanceof Float || val instanceof Double) {
                p.setDblv(((Number) val).doubleValue());
            } else {
                String s = String.valueOf(val);
                if (s.startsWith("{") || s.startsWith("[")) {
                    p.setJsonv(s);
                } else {
                    p.setStrv(s);
                }
            }
            // 如有租户等自定义字段，可在此设置 p.setTenantid(...);

            list.add(p);
        }

        // 插入条数
        int count = 0;
        if (!list.isEmpty()) {
            count = iotTskvMapper.batchInsert(list);
        }
        return count;
    }

}
