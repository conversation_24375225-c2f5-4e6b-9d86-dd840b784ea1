package inks.service.std.eam.iotrule.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;

import java.util.List;

public class JsonPathRuleExtractor {

    /**
     * 根据规则字段提取并扁平化输出
     *
     * @param mqttJson     原始 JSON 字符串 例如：
     *                  {"msg":{"info":{"content":[{"key":"temp","value":37},{"key":"dianya","value":"220V"}]}}
     * @param ruleJson 规则 JSON 字符串，格式如下：
     *                 [
     *                   {"alias":"temp",   "path":"msg.info.content[?(@.key=='temp')].value"},
     *                   {"alias":"dianya", "path":"msg.info.content[?(@.key=='dianya')].value"}
     *                 ]
     * @return 扁平化后的 JSONObject，例如 {"temp":37,"dianya":"220V"}
     */
    public static JSONObject extractByRule(String mqttJson, String ruleJson) {
        // 解析规则
        JSONArray rules = JSON.parseArray(ruleJson);
        // 缓存解析后的 JSON 对象
        JSONObject input = JSON.parseObject(mqttJson);
        // 使用有序 JSONObject 保持插入顺序
        JSONObject result = new JSONObject(true);

        for (int i = 0; i < rules.size(); i++) {
            JSONObject rule = rules.getJSONObject(i);
            String alias = rule.getString("alias");
            String path  = rule.getString("path");
            try {
                // 执行 JSONPath 查询
                Object raw = JSONPath.eval(input, path);
                Object value;
                // 如果是 List，则取第一个元素
                if (raw instanceof List) {
                    List<?> list = (List<?>) raw;
                    value = list.isEmpty() ? null : list.get(0);
                } else {
                    value = raw;
                }
                // 过滤空值
                if (value != null) {
                    result.put(alias, value);
                }
            } catch (Exception ignore) {
                // 路径错误或执行失败时忽略
            }
        }
        return result;
    }
}
