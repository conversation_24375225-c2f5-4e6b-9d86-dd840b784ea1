package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmToolborrowwsEntity;
import inks.service.std.eam.domain.pojo.DmToolborrowwsPojo;
import inks.service.std.eam.mapper.DmToolborrowwsMapper;
import inks.service.std.eam.service.DmToolborrowwsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 工装具借还子表-加工单(DmToolborrowws)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-13 09:52:08
 */
@Service("dmToolborrowwsService")
public class DmToolborrowwsServiceImpl implements DmToolborrowwsService {
    @Resource
    private DmToolborrowwsMapper dmToolborrowwsMapper;

    @Override
    public DmToolborrowwsPojo getEntity(String key,String tid) {
        return this.dmToolborrowwsMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<DmToolborrowwsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolborrowwsPojo> lst = dmToolborrowwsMapper.getPageList(queryParam);
            PageInfo<DmToolborrowwsPojo> pageInfo = new PageInfo<DmToolborrowwsPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<DmToolborrowwsPojo> getList(String Pid,String tid) { 
        try {
            List<DmToolborrowwsPojo> lst = dmToolborrowwsMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public DmToolborrowwsPojo insert(DmToolborrowwsPojo dmToolborrowwsPojo) {
        //初始化item的NULL
        DmToolborrowwsPojo itempojo =this.clearNull(dmToolborrowwsPojo);
        DmToolborrowwsEntity dmToolborrowwsEntity = new DmToolborrowwsEntity(); 
        BeanUtils.copyProperties(itempojo,dmToolborrowwsEntity);
          //生成雪花id
          dmToolborrowwsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          dmToolborrowwsEntity.setRevision(1);  //乐观锁      
          this.dmToolborrowwsMapper.insert(dmToolborrowwsEntity);
        return this.getEntity(dmToolborrowwsEntity.getId(),dmToolborrowwsEntity.getTenantid());
  
    }

    @Override
    public DmToolborrowwsPojo update(DmToolborrowwsPojo dmToolborrowwsPojo) {
        DmToolborrowwsEntity dmToolborrowwsEntity = new DmToolborrowwsEntity(); 
        BeanUtils.copyProperties(dmToolborrowwsPojo,dmToolborrowwsEntity);
        this.dmToolborrowwsMapper.update(dmToolborrowwsEntity);
        return this.getEntity(dmToolborrowwsEntity.getId(),dmToolborrowwsEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.dmToolborrowwsMapper.delete(key,tid) ;
    }

     @Override
     public DmToolborrowwsPojo clearNull(DmToolborrowwsPojo dmToolborrowwsPojo){
     //初始化NULL字段
     if(dmToolborrowwsPojo.getPid()==null) dmToolborrowwsPojo.setPid("");
     if(dmToolborrowwsPojo.getWorkid()==null) dmToolborrowwsPojo.setWorkid("");
     if(dmToolborrowwsPojo.getWorkuid()==null) dmToolborrowwsPojo.setWorkuid("");
     if(dmToolborrowwsPojo.getRownum()==null) dmToolborrowwsPojo.setRownum(0);
     if(dmToolborrowwsPojo.getRemark()==null) dmToolborrowwsPojo.setRemark("");
     if(dmToolborrowwsPojo.getCustom1()==null) dmToolborrowwsPojo.setCustom1("");
     if(dmToolborrowwsPojo.getCustom2()==null) dmToolborrowwsPojo.setCustom2("");
     if(dmToolborrowwsPojo.getCustom3()==null) dmToolborrowwsPojo.setCustom3("");
     if(dmToolborrowwsPojo.getCustom4()==null) dmToolborrowwsPojo.setCustom4("");
     if(dmToolborrowwsPojo.getCustom5()==null) dmToolborrowwsPojo.setCustom5("");
     if(dmToolborrowwsPojo.getCustom6()==null) dmToolborrowwsPojo.setCustom6("");
     if(dmToolborrowwsPojo.getCustom7()==null) dmToolborrowwsPojo.setCustom7("");
     if(dmToolborrowwsPojo.getCustom8()==null) dmToolborrowwsPojo.setCustom8("");
     if(dmToolborrowwsPojo.getCustom9()==null) dmToolborrowwsPojo.setCustom9("");
     if(dmToolborrowwsPojo.getCustom10()==null) dmToolborrowwsPojo.setCustom10("");
     if(dmToolborrowwsPojo.getTenantid()==null) dmToolborrowwsPojo.setTenantid("");
     if(dmToolborrowwsPojo.getRevision()==null) dmToolborrowwsPojo.setRevision(0);
     return dmToolborrowwsPojo;
     }
}
