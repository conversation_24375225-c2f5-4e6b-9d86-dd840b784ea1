package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotDevicecredentialsPojo;
import com.github.pagehelper.PageInfo;

/**
 * 设备鉴权信息表(Iot_DeviceCredentials)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:18
 */
public interface IotDevicecredentialsService {

    IotDevicecredentialsPojo getEntity(String key,String tid);

    PageInfo<IotDevicecredentialsPojo> getPageList(QueryParam queryParam);

    IotDevicecredentialsPojo insert(IotDevicecredentialsPojo iotDevicecredentialsPojo);

    IotDevicecredentialsPojo update(IotDevicecredentialsPojo iotDevicecredentialspojo);

    int delete(String key,String tid);
}
