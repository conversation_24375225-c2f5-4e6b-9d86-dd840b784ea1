package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.DmPatrolrecEntity;
import inks.service.std.eam.domain.pojo.DmPatrolrecPojo;
import inks.service.std.eam.domain.pojo.DmPatrolrecitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡检记录(DmPatrolrec)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-10 13:09:30
 */
@Mapper
public interface DmPatrolrecMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    DmPatrolrecPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmPatrolrecitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<DmPatrolrecPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param dmPatrolrecEntity 实例对象
     * @return 影响行数
     */
    int insert(DmPatrolrecEntity dmPatrolrecEntity);


    /**
     * 修改数据
     *
     * @param dmPatrolrecEntity 实例对象
     * @return 影响行数
     */
    int update(DmPatrolrecEntity dmPatrolrecEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param dmPatrolrecPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(DmPatrolrecPojo dmPatrolrecPojo);

    /**
     * 修改数据
     *
     * @param dmPatrolrecEntity 实例对象
     * @return 影响行数
     */
    int approval(DmPatrolrecEntity dmPatrolrecEntity);
}

