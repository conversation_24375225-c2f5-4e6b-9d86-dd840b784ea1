package inks.service.std.eam.iotrule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import inks.service.std.eam.domain.pojo.IotAlarmPojo;
import inks.service.std.eam.domain.pojo.IotDevicePojo;
import inks.service.std.eam.domain.pojo.IotRulechainPojo;
import inks.service.std.eam.domain.pojo.IotRulenodePojo;
import inks.service.std.eam.iotrule.rule.AlarmRuleEvaluator;
import inks.service.std.eam.iotrule.rule.JsonPathRuleExtractor;
import inks.service.std.eam.iotrule.rule.VelocityUtils;
import inks.service.std.eam.mqtt.service.DeviceTokenService;
import inks.service.std.eam.service.*;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Mqtt监听到消息后所有处理逻辑
 */
@Service("iotMqttService")
public class A_IotMqttServiceImpl implements A_IotMqttService {
    private final static Logger log = LoggerFactory.getLogger(A_IotMqttServiceImpl.class);

    @Resource
    private IotDeviceService iotDeviceService;
    @Resource
    private IotDeviceprofileService iotDeviceprofileService;
    @Resource
    private IotRulechainService iotRulechainService;
    @Resource
    private IotTskvlatestService iotTskvlatestService;
    @Resource
    private IotTskvService iotTskvService;
    @Resource
    private IotAttributekvService iotAttributekvService;
    @Resource
    private IotAlarmService iotAlarmService;
    @Resource
    private DeviceTokenService deviceTokenService;

    /**
     * 主题：inks/001
     * {
     * "sn": "ABC12138","ts":1744878995023,
     * "msg":{"info":{"content":[{"key":"temp","value":39},{"key":"dianya","value":"221V"}]}}
     * }
     */

    //  接收MQTT消息：通过deviceId直接处理，通过规则引擎处理信息、存入遥测/属性、报警...
    @Override
    public void mqttToRule(String topic, String msg, String deviceId, String token) {

        try {
            if (iotDeviceService == null || iotRulechainService == null) {
                log.error("服务实例未正确初始化，无法处理规则链");
                return;
            }

            // 1. 参数验证
            if (deviceId == null || deviceId.trim().isEmpty()) {
                log.warn("设备ID为空，无法处理MQTT消息 - token:{}, topic:{}", token, topic);
                return;
            }

            // 2. 通过设备ID查询设备信息（忽略tid查询）
            IotDevicePojo devicePojo = iotDeviceService.getEntity(deviceId, "");
            if (devicePojo == null) {
                log.warn("未找到设备ID为{}的设备 - token:{}, topic:{}", deviceId, token, topic);
                return;
            }

            log.info("处理MQTT消息 - token:{}, deviceId:{}, deviceName:{}, topic:{}",
                    token, deviceId, devicePojo.getDevname(), topic);

            // 3. 解析MQTT消息
            JSONObject mqttJSON = JSON.parseObject(msg);
            // 设备固定信息；sn、ts、 attrtype(取值有 1.client 2.server 默认是client表示是设备上报的属性，server表示是服务端下发的属性)
            String sn = mqttJSON.getString("sn");
            Long ts = mqttJSON.getLong("ts");
            // 服务端下发必须加入 "attrtype":"server"
            Integer attrtype = "server".equals(mqttJSON.getString("attrtype")) ? 2 : 1;

            // 4. 调用通用规则处理逻辑
            processRuleChain(devicePojo, mqttJSON, topic, sn, ts, attrtype);

        } catch (Exception e) {
            log.error("MQTT规则处理异常 - deviceId:{}, token:{}, topic:{}, error:{}",
                     deviceId, token, topic, e.getMessage(), e);
        }
    }



    /**
     * 通用规则链处理逻辑
     * @param devicePojo 设备信息
     * @param mqttJSON MQTT消息JSON
     * @param topic MQTT主题
     * @param sn 设备SN（可能为空）
     * @param ts 时间戳
     * @param attrtype 属性类型
     */
    private void processRuleChain(IotDevicePojo devicePojo, JSONObject mqttJSON,
                                 String topic, String sn, Long ts, Integer attrtype) {
        try {
            // 查询设备配置的规则链
            IotRulechainPojo ruleChainPojo = iotRulechainService.getRuleBillByDeviceProfile(devicePojo.getDeviceprofileid());
            if (ruleChainPojo == null) {
                log.warn("未找到设备{}对应的规则链配置 - deviceId:{}", devicePojo.getId(), devicePojo.getId());
                return;
            }

            List<IotRulenodePojo> ruleNodes = ruleChainPojo.getRulenodes();
            if (ruleNodes == null || ruleNodes.isEmpty()) {
                log.warn("设备{}的规则链中没有规则节点 - deviceId:{}", devicePojo.getId(), devicePojo.getId());
                return;
            }

            // 解析MQTT消息主题：遥测or属性
            boolean isTelemetry = topic.endsWith("/telemetry");
            boolean isAttributes = topic.endsWith("/attributes");
            // 是否触发警告
            boolean isNeedAlarm = false;
            // 按RowNum顺序执行规则节点
            for (IotRulenodePojo ruleNode : ruleNodes) {
                String ruleNodeType = ruleNode.getRulenodetype();// 规则节点类型
                String configuration = ruleNode.getConfiguration();// 规则配置
                switch (ruleNodeType) {
                    case "JsonPath":
                        log.info("【JsonPath规则】开始处理");
                        mqttJSON = JsonPathRuleExtractor.extractByRule(mqttJSON.toJSONString(), configuration);
                        mqttJSON.put("ts", ts);
                        if (sn != null) {
                            mqttJSON.put("sn", sn);
                        }
                        mqttJSON.put("entityid", devicePojo.getId());
                        log.info("【JsonPath规则】处理结果: {}", mqttJSON);
                        break;

                    case "SaveTimeSeries":
                        if (!isTelemetry) break;// 只处理telemetry主题
                        log.info("【SaveTimeSeries规则】开始处理");
                        // 批量插入遥测历史表
                        int kv_count = iotTskvService.batchInsert(mqttJSON); //注意将mqttJSON删除了键entityid和ts
                        // 更新遥测最新表 同一设备KeyV保持最新值
                        int latest_count = iotTskvlatestService.batchLatest(mqttJSON);
                        log.info("【SaveTimeSeries规则】存入遥测：成功插入KV{}条，更新KVLatest{}条", kv_count, latest_count);
                        break;
                    case "SaveAttributes":
                        if (!isAttributes) break;// 只处理 attributes 主题
                        mqttJSON.put("attrtype", attrtype);// 带上属性类型：1.client设备上报的属性/2.server服务端下发的属性
                        iotAttributekvService.batchLatest(mqttJSON);
                        log.info("SaveAttributes 处理{}条", 1);
                        break;
                    case "Alarm":
                        log.info("【Alarm规则】开始处理");
                        AlarmRuleEvaluator.AlarmEvaluationResult alarmEvaluationResult = AlarmRuleEvaluator.evaluateRule(String.valueOf(mqttJSON), configuration);
                        log.info("【Alarm规则】告警校验结果: {}", alarmEvaluationResult);
                        // 触发告警
                        if (alarmEvaluationResult.isTriggered()) {
                            isNeedAlarm = true;
                            log.info("【Alarm规则】告警触发: {}", alarmEvaluationResult.getRuleName());

                            // 保存告警信息到表
                            IotAlarmPojo iotAlarmPojo = new IotAlarmPojo();

                            // 基本告警信息
                            iotAlarmPojo.setType(alarmEvaluationResult.getRuleType());
                            iotAlarmPojo.setAdditionalinfo(alarmEvaluationResult.getRuleName());
                            iotAlarmPojo.setSeverity(alarmEvaluationResult.getSeverity());

                            // 触发实体信息
                            iotAlarmPojo.setOriginatorid(devicePojo.getId()); // 设备ID作为触发实体
                            iotAlarmPojo.setOriginatortype(5); // 5表示设备类型（参考ThingsBoard）

                            // 时间戳信息
                            long currentTime = System.currentTimeMillis();
                            iotAlarmPojo.setStartts(ts != null ? ts : currentTime); // 使用MQTT消息时间戳或当前时间

                            // 初始状态设置
                            iotAlarmPojo.setAcknowledged(0); // 未确认
                            iotAlarmPojo.setCleared(0); // 未清除
                            iotAlarmPojo.setPropagate(0); // 不传播
                            iotAlarmPojo.setPropagatetoowner(0); // 不传播给所有者
                            iotAlarmPojo.setPropagatetotenant(0); // 不传播给租户

                            // 租户信息
                            iotAlarmPojo.setTenantid(devicePojo.getTenantid());
                            iotAlarmPojo.setTenantname(devicePojo.getTenantname());

                            // 系统信息
                            iotAlarmPojo.setCreateby("系统");
                            iotAlarmPojo.setCreatebyid("SYSTEM");
                            iotAlarmPojo.setLister("系统");
                            iotAlarmPojo.setListerid("SYSTEM");
                            iotAlarmPojo.setCreatedate(new java.util.Date());
                            iotAlarmPojo.setModifydate(new java.util.Date());

                            // 备注信息（可包含触发条件详情）
                            String remarkInfo = String.format("设备[%s]触发告警规则[%s]，严重程度[%s]",
                                devicePojo.getDevname(), alarmEvaluationResult.getRuleName(), alarmEvaluationResult.getSeverity());
                            iotAlarmPojo.setRemark(remarkInfo);

                            // 保存告警
                            IotAlarmPojo savedAlarm = iotAlarmService.insert(iotAlarmPojo);
                            log.info("【Alarm规则】告警已保存，ID: {}, 设备: {}, 规则: {}",
                                savedAlarm.getId(), devicePojo.getDevname(), alarmEvaluationResult.getRuleName());

                        } else {
                            log.info("【Alarm规则】告警未触发: {}", alarmEvaluationResult.getRuleName());
                        }
                        break;
                    case "Notifications":
                        log.info("【Notifications规则】开始处理");
                        // 警告标识
                        if (isNeedAlarm) {
                            //configuration格式：
                            //{
                            //    "agentcode": "ems001",
                            //    "template": "设备[$sn]在[$ts]时间点温度为[$temp]℃，电压为[$dianya]，请注意！"
                            //}
                            JSONObject configJson = JSON.parseObject(configuration);
                            String agentcode = configJson.getString("agentcode");
                            String template = configJson.getString("template");
                            if (StringUtils.isAnyBlank(agentcode, template)) {
                                log.warn("Notifications 配置缺少 agentcode 或 template，跳过发送");
                                break;
                            }
                            // mqttJSON替换模板变量 其中ts格式化为yyyy-MM-dd HH:mm:ss
                            String result_data = VelocityUtils.renderTemplate(template, mqttJSON);
                            sendAgentData(agentcode, result_data);   // 调用代理信息发送 utils/D96M30B1/sendAgent
                            log.info("【Notifications规则】告警通知发送成功！agentcode：{}, data：{}", agentcode, result_data);
                        } else {
                            log.info("【Notifications规则】未触发警告");
                        }
                        break;

                    // TODO: 增加其他规则类型处理，如保存属性
                    default:
                        log.warn("未识别的规则类型: {}", ruleNodeType);
                        break;
                }}

        } catch (Exception e) {
            log.error("规则链处理异常 - deviceId:{}, topic:{}, error:{}",
                     devicePojo.getId(), topic, e.getMessage(), e);
        }
    }

    // 传入的mqttJson格式为：固定字段sn、ts，+ JSONPath规则提取的字段temp、dianya等
    // 示例：{"sn":"ABC12138","ts":1744878995023,"temp":39,"dianya":"221V"}
    public static String sendAgentData(String agentcode, String data) {
        String url = "http://dev.inksyun.com:31080/utils/D96M30B1/sendAgent";

        // 用 JSONObject 构造 payload
        JSONObject payload = new JSONObject();
        payload.put("code", agentcode);
        // data 已经是JSON字符串，parse成JSONObject再放进去，保证输出是合法的嵌套 JSON
        payload.put("data", data);
        // 发送请求负载
        String json = payload.toJSONString();
        RequestBody body = RequestBody.create(
                MediaType.parse("application/json"),
                json
        );
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("accept", "*/*")
                .addHeader("Authorization", "b8")
                .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                .addHeader("Content-Type", "application/json")
                .addHeader("Host", "dev.inksyun.com:31080")
                .addHeader("Connection", "keep-alive")
                .build();

        try (Response response = new OkHttpClient().newCall(request).execute()) {
            return response.body().string();
        } catch (Exception e) {
            log.error("发送失败", e);
            return null;
        }
    }


}
