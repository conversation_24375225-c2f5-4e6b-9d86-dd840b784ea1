package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmSpareEntity;
import inks.service.std.eam.domain.pojo.DmSparePojo;
import inks.service.std.eam.mapper.DmSpareMapper;
import inks.service.std.eam.service.DmSpareService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 备件信息(DmSpare)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-07 16:26:36
 */
@Service("dmSpareService")
public class DmSpareServiceImpl implements DmSpareService {
    @Resource
    private DmSpareMapper dmSpareMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmSparePojo getEntity(String key, String tid) {
        return this.dmSpareMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmSparePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmSparePojo> lst = dmSpareMapper.getPageList(queryParam);
            PageInfo<DmSparePojo> pageInfo = new PageInfo<DmSparePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dmSparePojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmSparePojo insert(DmSparePojo dmSparePojo) {
        //初始化NULL字段
        if (dmSparePojo.getSparecode() == null) dmSparePojo.setSparecode("");
        if (dmSparePojo.getSparename() == null) dmSparePojo.setSparename("");
        if (dmSparePojo.getSparespec() == null) dmSparePojo.setSparespec("");
        if (dmSparePojo.getSpareunit() == null) dmSparePojo.setSpareunit("");
        if (dmSparePojo.getIvquantity() == null) dmSparePojo.setIvquantity(0D);
        if (dmSparePojo.getSafetystock() == null) dmSparePojo.setSafetystock(0D);
        if (dmSparePojo.getSuppid() == null) dmSparePojo.setSuppid("");
        if (dmSparePojo.getBuyprice() == null) dmSparePojo.setBuyprice(0D);
        if (dmSparePojo.getDeviceid() == null) dmSparePojo.setDeviceid("");
        if (dmSparePojo.getUsefullife() == null) dmSparePojo.setUsefullife(0);
        if (dmSparePojo.getRemark() == null) dmSparePojo.setRemark("");
        if (dmSparePojo.getCustom1() == null) dmSparePojo.setCustom1("");
        if (dmSparePojo.getCustom2() == null) dmSparePojo.setCustom2("");
        if (dmSparePojo.getCustom3() == null) dmSparePojo.setCustom3("");
        if (dmSparePojo.getCustom4() == null) dmSparePojo.setCustom4("");
        if (dmSparePojo.getCustom5() == null) dmSparePojo.setCustom5("");
        if (dmSparePojo.getCustom6() == null) dmSparePojo.setCustom6("");
        if (dmSparePojo.getCustom7() == null) dmSparePojo.setCustom7("");
        if (dmSparePojo.getCustom8() == null) dmSparePojo.setCustom8("");
        if (dmSparePojo.getLister() == null) dmSparePojo.setLister("");
        if (dmSparePojo.getListerid() == null) dmSparePojo.setListerid("");
        if (dmSparePojo.getCreateby() == null) dmSparePojo.setCreateby("");
        if (dmSparePojo.getCreatebyid() == null) dmSparePojo.setCreatebyid("");
        if (dmSparePojo.getCreatedate() == null) dmSparePojo.setCreatedate(new Date());
        if (dmSparePojo.getModifydate() == null) dmSparePojo.setModifydate(new Date());
        if (dmSparePojo.getEnabledmark() == null) dmSparePojo.setEnabledmark(0);
        if (dmSparePojo.getDeletemark() == null) dmSparePojo.setDeletemark(0);
        if (dmSparePojo.getDeletelister() == null) dmSparePojo.setDeletelister("");
        if (dmSparePojo.getDeletedate() == null) dmSparePojo.setDeletedate(new Date());
        if (dmSparePojo.getTenantid() == null) dmSparePojo.setTenantid("");
        if (dmSparePojo.getRevision() == null) dmSparePojo.setRevision(0);
        DmSpareEntity dmSpareEntity = new DmSpareEntity();
        BeanUtils.copyProperties(dmSparePojo, dmSpareEntity);
        //生成雪花id
        dmSpareEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmSpareEntity.setRevision(1);  //乐观锁
        this.dmSpareMapper.insert(dmSpareEntity);
        return this.getEntity(dmSpareEntity.getId(), dmSpareEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmSparePojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmSparePojo update(DmSparePojo dmSparePojo) {
        DmSpareEntity dmSpareEntity = new DmSpareEntity();
        BeanUtils.copyProperties(dmSparePojo, dmSpareEntity);
        this.dmSpareMapper.update(dmSpareEntity);
        return this.getEntity(dmSpareEntity.getId(), dmSpareEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmSpareMapper.delete(key, tid);
    }


}
