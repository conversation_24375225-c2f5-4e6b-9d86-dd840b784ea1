package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotKeydictionaryPojo;
import inks.service.std.eam.domain.IotKeydictionaryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 键值映射字典表(Iot_KeyDictionary)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:30
 */
@Mapper
public interface IotKeydictionaryMapper {

    IotKeydictionaryPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotKeydictionaryPojo> getPageList(QueryParam queryParam);

    int insert(IotKeydictionaryEntity iotKeydictionaryEntity);

    int update(IotKeydictionaryEntity iotKeydictionaryEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
}

