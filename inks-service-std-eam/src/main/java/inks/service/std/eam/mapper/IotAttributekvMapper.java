package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotAttributekvPojo;
import inks.service.std.eam.domain.IotAttributekvEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 实体属性存储表(Iot_AttributeKv)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-07 10:06:36
 */
@Mapper
public interface IotAttributekvMapper {

    IotAttributekvPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotAttributekvPojo> getPageList(QueryParam queryParam);

    int insert(IotAttributekvEntity iotAttributekvEntity);

    int update(IotAttributekvEntity iotAttributekvEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    int batchLatest(List<IotAttributekvPojo> list);
}

