package inks.service.std.eam.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 工装具校验记录子表(DmToolinspectitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-06-19 10:17:16
 */
public class DmToolinspectitemPojo implements Serializable {
    private static final long serialVersionUID = -14632614912125181L;
     // id
  @Excel(name = "id")    
  private String id;
     // 主表id
  @Excel(name = "主表id")    
  private String pid;
     // 工装具id
  @Excel(name = "工装具id")    
  private String toolid;
     // 检验结果
  @Excel(name = "检验结果")    
  private String result;
     // 实际测量值
  @Excel(name = "实际测量值")    
  private Double measvalue;
     // 本次检验完成日期
  @Excel(name = "本次检验完成日期")    
  private Date compdate;
     // 使用的检验设备
  @Excel(name = "使用的检验设备")    
  private String equipused;
     // 关联的检验规程
  @Excel(name = "关联的检验规程")    
  private String procref;
     // 不合格处理措施
  @Excel(name = "不合格处理措施")    
  private String nchandling;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
  @Excel(name = "")    
  private String custom1;
  @Excel(name = "")    
  private String custom2;
  @Excel(name = "")    
  private String custom3;
  @Excel(name = "")    
  private String custom4;
  @Excel(name = "")    
  private String custom5;
  @Excel(name = "")    
  private String custom6;
  @Excel(name = "")    
  private String custom7;
  @Excel(name = "")    
  private String custom8;
  @Excel(name = "")    
  private String custom9;
  @Excel(name = "")    
  private String custom10;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 租户名称
  @Excel(name = "租户名称")    
  private String tenantname;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 主表id
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 工装具id
    public String getToolid() {
        return toolid;
    }
    
    public void setToolid(String toolid) {
        this.toolid = toolid;
    }
        
   // 检验结果
    public String getResult() {
        return result;
    }
    
    public void setResult(String result) {
        this.result = result;
    }
        
   // 实际测量值
    public Double getMeasvalue() {
        return measvalue;
    }
    
    public void setMeasvalue(Double measvalue) {
        this.measvalue = measvalue;
    }
        
   // 本次检验完成日期
    public Date getCompdate() {
        return compdate;
    }
    
    public void setCompdate(Date compdate) {
        this.compdate = compdate;
    }
        
   // 使用的检验设备
    public String getEquipused() {
        return equipused;
    }
    
    public void setEquipused(String equipused) {
        this.equipused = equipused;
    }
        
   // 关联的检验规程
    public String getProcref() {
        return procref;
    }
    
    public void setProcref(String procref) {
        this.procref = procref;
    }
        
   // 不合格处理措施
    public String getNchandling() {
        return nchandling;
    }
    
    public void setNchandling(String nchandling) {
        this.nchandling = nchandling;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

