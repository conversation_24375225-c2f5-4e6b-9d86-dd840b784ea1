package inks.service.std.eam.mqtt.constants;

/**
 * MQTT相关常量定义
 * 统一管理所有MQTT相关的常量，便于维护和修改
 * 
 * <AUTHOR>
 */
public final class MqttConstants {
    
    /**
     * 私有构造函数，防止实例化
     */
    private MqttConstants() {
        throw new UnsupportedOperationException("常量类不允许实例化");
    }
    
    // ==================== Redis Key 前缀 ====================
    
    /**
     * MQTT Token在Redis中的Key前缀
     * 格式：mqtt:token:{token} -> deviceId
     */
    public static final String REDIS_TOKEN_PREFIX = "mqtt:token:";
    
    /**
     * MQTT设备在Redis中的Key前缀  
     * 格式：mqtt:device:{deviceId} -> token
     */
    public static final String REDIS_DEVICE_PREFIX = "mqtt:device:";
    
    /**
     * Redis健康检查Key
     * 用于验证Redis连接是否正常，避免在Redis不可用时进行数据同步
     */
    public static final String REDIS_HEALTH_CHECK_KEY = "mqtt:health:check";
    
    // ==================== MQTT 认证相关 ====================
    
    /**
     * 默认用户名（兼容模式）
     */
    public static final String DEFAULT_USERNAME = "inks";
    
    /**
     * 默认密码（兼容模式）
     */
    public static final String DEFAULT_PASSWORD = "8866";
    
    // ==================== 连接管理相关 ====================
    
    /**
     * 连接断开原因：Token已变更
     */
    public static final String DISCONNECT_REASON_TOKEN_CHANGED = "设备token已变更，旧连接已失效";
    
    /**
     * 连接断开原因：设备已删除
     */
    public static final String DISCONNECT_REASON_DEVICE_DELETED = "设备已被删除";
    
    /**
     * 连接断开原因：Token已失效
     */
    public static final String DISCONNECT_REASON_TOKEN_INVALID = "Token已失效";
    
    // ==================== 日志相关 ====================
    
    /**
     * 健康检查值
     */
    public static final String HEALTH_CHECK_VALUE = "ok";
    
    /**
     * 未知IP地址标识
     */
    public static final String UNKNOWN_IP = "unknown";
    
    // ==================== 业务相关 ====================
    
    /**
     * Token生成格式分隔符
     */
    public static final String TOKEN_SEPARATOR = "_";
    
    /**
     * Token随机部分长度
     */
    public static final int TOKEN_RANDOM_LENGTH = 8;
}
