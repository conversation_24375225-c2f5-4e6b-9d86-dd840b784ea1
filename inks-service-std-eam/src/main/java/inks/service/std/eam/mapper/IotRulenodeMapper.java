package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotRulenodePojo;
import inks.service.std.eam.domain.IotRulenodeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 规则链节点定义表(Iot_RuleNode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:45
 */
@Mapper
public interface IotRulenodeMapper {

    IotRulenodePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<IotRulenodePojo> getPageList(QueryParam queryParam);

    int insert(IotRulenodeEntity iotRulenodeEntity);

    int update(IotRulenodeEntity iotRulenodeEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    List<IotRulenodePojo> getListByRulechainId(String rulechainid);
}

