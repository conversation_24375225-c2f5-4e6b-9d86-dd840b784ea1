package inks.service.std.eam.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.DmToolinspectitemPojo;
import inks.service.std.eam.domain.DmToolinspectitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工装具校验记录子表(DmToolinspectitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-19 10:17:17
 */
 @Mapper
public interface DmToolinspectitemMapper {

    DmToolinspectitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<DmToolinspectitemPojo> getPageList(QueryParam queryParam);

    List<DmToolinspectitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(DmToolinspectitemEntity dmToolinspectitemEntity);

    int update(DmToolinspectitemEntity dmToolinspectitemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

}

