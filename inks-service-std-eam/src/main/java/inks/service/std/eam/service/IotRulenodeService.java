package inks.service.std.eam.service;

import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotRulenodePojo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 规则链节点定义表(Iot_RuleNode)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:46
 */
public interface IotRulenodeService {

    IotRulenodePojo getEntity(String key,String tid);

    PageInfo<IotRulenodePojo> getPageList(QueryParam queryParam);

    IotRulenodePojo insert(IotRulenodePojo iotRulenodePojo);

    IotRulenodePojo update(IotRulenodePojo iotRulenodepojo);

    int delete(String key,String tid);

    List<IotRulenodePojo> getListByChainid(String chainid, String tenantid);
}
