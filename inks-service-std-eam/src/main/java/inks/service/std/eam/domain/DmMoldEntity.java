package inks.service.std.eam.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 模具管理(DmMold)实体类
 *
 * <AUTHOR>
 * @since 2024-12-13 14:12:19
 */
public class DmMoldEntity implements Serializable {
    private static final long serialVersionUID = -92266477245615362L;
     // id
    private String id;
     // 模具编码
    private String moldcode;
     // 模具名称
    private String moldname;
     // 模具描述
    private String description;
     // 供应商id
    private String groupid;
     // 图纸号
    private String drawingnumber;
     // 数量
    private Double quantity;
     // 异常数量
    private Double abnormalqty;
     // 最后使用日期
    private Date lastuseddate;
     // 最大加工次数
    private Integer maxprocessing;
     // 当前加工次数
    private Integer currentprocessing;
     // 状态编码
    private String statuscode;
     // 行号
    private Integer rownum;
     // 摘要
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 模具编码
    public String getMoldcode() {
        return moldcode;
    }
    
    public void setMoldcode(String moldcode) {
        this.moldcode = moldcode;
    }
        
// 模具名称
    public String getMoldname() {
        return moldname;
    }
    
    public void setMoldname(String moldname) {
        this.moldname = moldname;
    }
        
// 模具描述
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
        
// 供应商id
    public String getGroupid() {
        return groupid;
    }
    
    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }
        
// 图纸号
    public String getDrawingnumber() {
        return drawingnumber;
    }
    
    public void setDrawingnumber(String drawingnumber) {
        this.drawingnumber = drawingnumber;
    }
        
// 数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
// 异常数量
    public Double getAbnormalqty() {
        return abnormalqty;
    }
    
    public void setAbnormalqty(Double abnormalqty) {
        this.abnormalqty = abnormalqty;
    }
        
// 最后使用日期
    public Date getLastuseddate() {
        return lastuseddate;
    }
    
    public void setLastuseddate(Date lastuseddate) {
        this.lastuseddate = lastuseddate;
    }
        
// 最大加工次数
    public Integer getMaxprocessing() {
        return maxprocessing;
    }
    
    public void setMaxprocessing(Integer maxprocessing) {
        this.maxprocessing = maxprocessing;
    }
        
// 当前加工次数
    public Integer getCurrentprocessing() {
        return currentprocessing;
    }
    
    public void setCurrentprocessing(Integer currentprocessing) {
        this.currentprocessing = currentprocessing;
    }
        
// 状态编码
    public String getStatuscode() {
        return statuscode;
    }
    
    public void setStatuscode(String statuscode) {
        this.statuscode = statuscode;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 摘要
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

