package inks.service.std.eam.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备分类(DmDevicetype)实体类
 *
 * <AUTHOR>
 * @since 2023-06-28 09:32:52
 */
public class DmDevicetypePojo implements Serializable {
    private static final long serialVersionUID = -85627890241067159L;
    // 设备类型ID
    @Excel(name = "设备类型ID")
    private String id;
    // 设备类型编码
    @Excel(name = "设备类型编码")
    private String typecode;
    // 设备类型名称
    @Excel(name = "设备类型名称")
    private String typename;
    // 父类型ID
    @Excel(name = "父类型ID")
    private String parentid;
    // 所有父节点ID
    @Excel(name = "所有父节点ID")
    private String ancestors;
    // 有效标识
    @Excel(name = "有效标识")
    private Integer enabledmark;
    // 排列序号
    @Excel(name = "排列序号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 前缀
    @Excel(name = "前缀")
    private String prefix;
    // 后缀
    @Excel(name = "后缀")
    private String suffix;
    // 序号位
    @Excel(name = "序号位")
    private String sncode;
    // 允许设备建立
    @Excel(name = "允许设备建立")
    private Integer allowitem;
    // 分组状态
    @Excel(name = "分组状态")
    private String statecode;
    // SVG微标
    @Excel(name = "SVG微标")
    private String groupsvg;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 分类子数
    @Excel(name = "分类子数")
    private Integer childcount;

    // 设备类型ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 设备类型编码
    public String getTypecode() {
        return typecode;
    }

    public void setTypecode(String typecode) {
        this.typecode = typecode;
    }

    // 设备类型名称
    public String getTypename() {
        return typename;
    }

    public void setTypename(String typename) {
        this.typename = typename;
    }

    // 父类型ID
    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    // 所有父节点ID
    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 排列序号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 前缀
    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    // 后缀
    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    // 序号位
    public String getSncode() {
        return sncode;
    }

    public void setSncode(String sncode) {
        this.sncode = sncode;
    }

    // 允许设备建立
    public Integer getAllowitem() {
        return allowitem;
    }

    public void setAllowitem(Integer allowitem) {
        this.allowitem = allowitem;
    }

    // 分组状态
    public String getStatecode() {
        return statecode;
    }

    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }

    // SVG微标
    public String getGroupsvg() {
        return groupsvg;
    }

    public void setGroupsvg(String groupsvg) {
        this.groupsvg = groupsvg;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    // 分类子数
    public Integer getChildcount() {
        return childcount;
    }

    public void setChildcount(Integer childcount) {
        this.childcount = childcount;
    }


}

