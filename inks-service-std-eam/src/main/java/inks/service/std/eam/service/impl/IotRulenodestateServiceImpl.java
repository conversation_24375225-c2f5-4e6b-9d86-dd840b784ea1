package inks.service.std.eam.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.eam.domain.pojo.IotRulenodestatePojo;
import inks.service.std.eam.domain.IotRulenodestateEntity;
import inks.service.std.eam.mapper.IotRulenodestateMapper;
import inks.service.std.eam.service.IotRulenodestateService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 规则节点运行状态表(IotRulenodestate)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:48
 */
@Service("iotRulenodestateService")
public class IotRulenodestateServiceImpl implements IotRulenodestateService {
    @Resource
    private IotRulenodestateMapper iotRulenodestateMapper;

    @Override
    public IotRulenodestatePojo getEntity(String key, String tid) {
        return this.iotRulenodestateMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<IotRulenodestatePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<IotRulenodestatePojo> lst = iotRulenodestateMapper.getPageList(queryParam);
            PageInfo<IotRulenodestatePojo> pageInfo = new PageInfo<IotRulenodestatePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public IotRulenodestatePojo insert(IotRulenodestatePojo iotRulenodestatePojo) {
        //初始化NULL字段
        cleanNull(iotRulenodestatePojo);
        IotRulenodestateEntity iotRulenodestateEntity = new IotRulenodestateEntity(); 
        BeanUtils.copyProperties(iotRulenodestatePojo,iotRulenodestateEntity);
          //生成雪花id
          iotRulenodestateEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          iotRulenodestateEntity.setRevision(1);  //乐观锁
          this.iotRulenodestateMapper.insert(iotRulenodestateEntity);
        return this.getEntity(iotRulenodestateEntity.getId(),iotRulenodestateEntity.getTenantid());
    }


    @Override
    public IotRulenodestatePojo update(IotRulenodestatePojo iotRulenodestatePojo) {
        IotRulenodestateEntity iotRulenodestateEntity = new IotRulenodestateEntity(); 
        BeanUtils.copyProperties(iotRulenodestatePojo,iotRulenodestateEntity);
        this.iotRulenodestateMapper.update(iotRulenodestateEntity);
        return this.getEntity(iotRulenodestateEntity.getId(),iotRulenodestateEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.iotRulenodestateMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(IotRulenodestatePojo iotRulenodestatePojo) {
        if(iotRulenodestatePojo.getRulenodeid()==null) iotRulenodestatePojo.setRulenodeid("");
        if(iotRulenodestatePojo.getEntitytype()==null) iotRulenodestatePojo.setEntitytype("");
        if(iotRulenodestatePojo.getEntityid()==null) iotRulenodestatePojo.setEntityid("");
        if(iotRulenodestatePojo.getStatedata()==null) iotRulenodestatePojo.setStatedata("");
        if(iotRulenodestatePojo.getRemark()==null) iotRulenodestatePojo.setRemark("");
        if(iotRulenodestatePojo.getRownum()==null) iotRulenodestatePojo.setRownum(0);
        if(iotRulenodestatePojo.getCreateby()==null) iotRulenodestatePojo.setCreateby("");
        if(iotRulenodestatePojo.getCreatebyid()==null) iotRulenodestatePojo.setCreatebyid("");
        if(iotRulenodestatePojo.getCreatedate()==null) iotRulenodestatePojo.setCreatedate(new Date());
        if(iotRulenodestatePojo.getLister()==null) iotRulenodestatePojo.setLister("");
        if(iotRulenodestatePojo.getListerid()==null) iotRulenodestatePojo.setListerid("");
        if(iotRulenodestatePojo.getModifydate()==null) iotRulenodestatePojo.setModifydate(new Date());
        if(iotRulenodestatePojo.getCustom1()==null) iotRulenodestatePojo.setCustom1("");
        if(iotRulenodestatePojo.getCustom2()==null) iotRulenodestatePojo.setCustom2("");
        if(iotRulenodestatePojo.getCustom3()==null) iotRulenodestatePojo.setCustom3("");
        if(iotRulenodestatePojo.getCustom4()==null) iotRulenodestatePojo.setCustom4("");
        if(iotRulenodestatePojo.getCustom5()==null) iotRulenodestatePojo.setCustom5("");
        if(iotRulenodestatePojo.getCustom6()==null) iotRulenodestatePojo.setCustom6("");
        if(iotRulenodestatePojo.getCustom7()==null) iotRulenodestatePojo.setCustom7("");
        if(iotRulenodestatePojo.getCustom8()==null) iotRulenodestatePojo.setCustom8("");
        if(iotRulenodestatePojo.getCustom9()==null) iotRulenodestatePojo.setCustom9("");
        if(iotRulenodestatePojo.getCustom10()==null) iotRulenodestatePojo.setCustom10("");
        if(iotRulenodestatePojo.getTenantid()==null) iotRulenodestatePojo.setTenantid("");
        if(iotRulenodestatePojo.getTenantname()==null) iotRulenodestatePojo.setTenantname("");
        if(iotRulenodestatePojo.getRevision()==null) iotRulenodestatePojo.setRevision(0);
   }

}
