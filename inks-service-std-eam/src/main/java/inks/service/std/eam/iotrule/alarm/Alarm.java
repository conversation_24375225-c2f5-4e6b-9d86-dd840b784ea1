package inks.service.std.eam.iotrule.alarm;

public class Alarm {
    // 告警基本信息
    private String id;
    private String alarmType;
    private String originatorId;      // 设备/资产ID
    private String status;            // ACTIVE_UNACK, ACTIVE_ACK, CLEARED_UNACK, CLEARED_ACK
    private String severity;          // CRITICAL, MAJOR, MINOR, WARNING, INDETERMINATE
    
    // 时间信息
    private long startTime;
    private long endTime;             // 结束时间，未结束时为0
    private long ackTime;             // 确认时间，未确认时为0
    private long clearTime;           // 清除时间，未清除时为0
    
    // 告警规则和详情
    private AlarmRule rule;           // 告警规则
    private String details;           // 告警详情(可存储JSON)
    
    // 关联仪表盘
    private String dashboardId;
    
    // 构造函数
    public Alarm(String alarmType, String originatorId, String severity, AlarmRule rule) {
        this.id = generateId();
        this.alarmType = alarmType;
        this.originatorId = originatorId;
        this.severity = severity;
        this.status = "ACTIVE_UNACK";
        this.startTime = System.currentTimeMillis();
        this.rule = rule;
    }
    
    // 生成唯一ID的方法
    private String generateId() {
        return java.util.UUID.randomUUID().toString();
    }
    
    // Getters and Setters...
    
    // 确认告警
    public void acknowledge() {
        this.ackTime = System.currentTimeMillis();
        if (this.status.startsWith("ACTIVE")) {
            this.status = "ACTIVE_ACK";
        } else if (this.status.startsWith("CLEARED")) {
            this.status = "CLEARED_ACK";
        }
    }
    
    // 清除告警
    public void clear() {
        this.clearTime = System.currentTimeMillis();
        this.endTime = System.currentTimeMillis();
        if (this.status.endsWith("UNACK")) {
            this.status = "CLEARED_UNACK";
        } else {
            this.status = "CLEARED_ACK";
        }
    }
    
    @Override
    public String toString() {
        return "Alarm{" +
                "id='" + id + '\'' +
                ", type='" + alarmType + '\'' +
                ", severity='" + severity + '\'' +
                ", status='" + status + '\'' +
                '}';
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAlarmType() {
        return alarmType;
    }

    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }

    public String getOriginatorId() {
        return originatorId;
    }

    public void setOriginatorId(String originatorId) {
        this.originatorId = originatorId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSeverity() {
        return severity;
    }

    public void setSeverity(String severity) {
        this.severity = severity;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getAckTime() {
        return ackTime;
    }

    public void setAckTime(long ackTime) {
        this.ackTime = ackTime;
    }

    public long getClearTime() {
        return clearTime;
    }

    public void setClearTime(long clearTime) {
        this.clearTime = clearTime;
    }

    public AlarmRule getRule() {
        return rule;
    }

    public void setRule(AlarmRule rule) {
        this.rule = rule;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getDashboardId() {
        return dashboardId;
    }

    public void setDashboardId(String dashboardId) {
        this.dashboardId = dashboardId;
    }
}