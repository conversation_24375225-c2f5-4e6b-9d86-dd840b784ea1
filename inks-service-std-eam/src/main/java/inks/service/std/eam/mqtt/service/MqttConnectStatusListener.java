package inks.service.std.eam.mqtt.service;

import org.dromara.mica.mqtt.spring.server.event.MqttClientOfflineEvent;
import org.dromara.mica.mqtt.spring.server.event.MqttClientOnlineEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * MQTT客户端连接状态监听器
 * 监听客户端上线和下线事件，更新统计信息
 * 
 * <AUTHOR>
 */
@Service
public class MqttConnectStatusListener {
    
    private static final Logger logger = LoggerFactory.getLogger(MqttConnectStatusListener.class);
    
    @Autowired
    private MqttStatsService mqttStatsService;

    /**
     * 客户端上线事件监听
     */
    @EventListener
    public void onClientOnline(MqttClientOnlineEvent event) {
        String clientId = event.getClientId();
        String username = event.getUsername(); // 修正方法名
        String ipAddress = extractIpAddress(event);
        
        logger.info("MQTT客户端上线 - ClientId: {}, Username: {}, IP: {}", clientId, username, ipAddress);
        
        // 更新统计信息
        mqttStatsService.onClientConnect(clientId, username, ipAddress);
        
        System.out.println("=== MQTT Client Online ===");
        System.out.println("ClientId: " + clientId);
        System.out.println("Username: " + username);
        System.out.println("IP Address: " + ipAddress);
        System.out.println("Timestamp: " + System.currentTimeMillis());
        System.out.println("===========================");
    }

    /**
     * 客户端下线事件监听
     */
    @EventListener
    public void onClientOffline(MqttClientOfflineEvent event) {
        String clientId = event.getClientId();
        String username = event.getUsername(); // 修正方法名
        
        logger.info("MQTT客户端下线 - ClientId: {}, Username: {}", clientId, username);
        
        // 更新统计信息
        mqttStatsService.onClientDisconnect(clientId);
        
        System.out.println("=== MQTT Client Offline ===");
        System.out.println("ClientId: " + clientId);
        System.out.println("Username: " + username);
        System.out.println("Timestamp: " + System.currentTimeMillis());
        System.out.println("============================");
    }
    
    /**
     * 从事件中提取IP地址
     */
    private String extractIpAddress(MqttClientOnlineEvent event) {
        try {
            // 暂时返回unknown，避免编译错误
            // 后续可以根据实际的mica-mqtt版本调整获取方式
            return "unknown";
        } catch (Exception e) {
            logger.warn("无法获取客户端IP地址: {}", e.getMessage());
        }
        return "unknown";
    }
}
