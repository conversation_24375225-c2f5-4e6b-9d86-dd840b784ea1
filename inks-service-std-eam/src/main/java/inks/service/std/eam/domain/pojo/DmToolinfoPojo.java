package inks.service.std.eam.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 工装具基础信息(DmToolinfo)实体类
 *
 * <AUTHOR>
 * @since 2025-06-09 16:30:35
 */
public class DmToolinfoPojo implements Serializable {
    private static final long serialVersionUID = 458696204790828352L;
     // id
    @Excel(name = "id") 
    private String id;
     // 工装具编码
    @Excel(name = "工装具编码") 
    private String toolcode;
     // 工装具名称
    @Excel(name = "工装具名称") 
    private String toolname;
     // 类别id
    @Excel(name = "类别id") 
    private String categoryid;
     // 启用日期
    @Excel(name = "启用日期")
    private Date activatedate;
     // 停用日期
    @Excel(name = "停用日期")
    private Date discarddate;
     // 存放位置
    @Excel(name = "存放位置") 
    private String storelocation;
     // 当前状态(1:在库, 2:使用中, 3:保养中, 4:已报废)
    @Excel(name = "当前状态(1:在库, 2:使用中, 3:保养中, 4:已报废)") 
    private Integer status;
     // 上次保养日期
    @Excel(name = "上次保养日期") 
    private Date lastmaintdate;
     // 已用量(保养周期)
    @Excel(name = "已用量(保养周期)") 
    private Integer maintusedqty;
     // 预警量(保养)
    @Excel(name = "预警量(保养)") 
    private Integer maintwarnqty;
     // 限制量(保养)
    @Excel(name = "限制量(保养)") 
    private Integer maintlimitqty;
     // 已保养次数
    @Excel(name = "已保养次数")
    private Integer maintcount;
     // 已用量(累计)
    @Excel(name = "已用量(累计)") 
    private Integer scrapusedqty;
     // 预警量(报废)
    @Excel(name = "预警量(报废)") 
    private Integer scrapwarnqty;
     // 限制量(报废)
    @Excel(name = "限制量(报废)") 
    private Integer scraplimitqty;
     // 上次检验日期
    @Excel(name = "上次检验日期")
    private Date lastinspdate;
     // 检验状态 (1:正常, 2:待检验, 3:检验中, 4:已过期, 5:不合格/停用)
    @Excel(name = "检验状态 (1:正常, 2:待检验, 3:检验中, 4:已过期, 5:不合格/停用)")
    private Integer inspstatus;
     // 校验类型 (0:内部校验, 1:外发校验)
    @Excel(name = "校验类型 (0:内部校验, 1:外发校验)")
    private Integer insptype;
     // 有效性(1:有效, 0:无效)
    @Excel(name = "有效性(1:有效, 0:无效)") 
    private Integer enabledmark;
     // 图片
    @Excel(name = "图片")
    private String picture;
     // 规格型号
    @Excel(name = "规格型号") 
    private String spec;
     // 品牌
    @Excel(name = "品牌") 
    private String brand;
     // 供应商
    @Excel(name = "供应商") 
    private String groupid;
     // 购买价格
    @Excel(name = "购买价格") 
    private Double price;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 自定义6
    @Excel(name = "自定义6") 
    private String custom6;
     // 自定义7
    @Excel(name = "自定义7") 
    private String custom7;
     // 自定义8
    @Excel(name = "自定义8") 
    private String custom8;
     // 自定义9
    @Excel(name = "自定义9") 
    private String custom9;
     // 自定义10
    @Excel(name = "自定义10") 
    private String custom10;
     // 部门id
    @Excel(name = "部门id")
    private String deptid;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;

    //Dm_ToolCategory.CateName,MaintDays
    //               App_Workgroup.GroupName
    private String catename; // 类别名称
    private Integer maintdays; // 保养警告周期(天)
    private String groupname; // 供应商名称
    //@Excel(name = "下次保养日期 后端计算")
    private Date nextmaintdate;
   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public Date getNextmaintdate() {
        return nextmaintdate;
    }

    public void setNextmaintdate(Date nextmaintdate) {
        this.nextmaintdate = nextmaintdate;
    }

    public Integer getMaintdays() {
        return maintdays;
    }

    public void setMaintdays(Integer maintdays) {
        this.maintdays = maintdays;
    }

    public String getCatename() {
        return catename;
    }

    public void setCatename(String catename) {
        this.catename = catename;
    }

    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    // 工装具编码
    public String getToolcode() {
        return toolcode;
    }
    
    public void setToolcode(String toolcode) {
        this.toolcode = toolcode;
    }
        
   // 工装具名称
    public String getToolname() {
        return toolname;
    }
    
    public void setToolname(String toolname) {
        this.toolname = toolname;
    }
        
   // 类别id
    public String getCategoryid() {
        return categoryid;
    }
    
    public void setCategoryid(String categoryid) {
        this.categoryid = categoryid;
    }
        
   // 启用日期
    public Date getActivatedate() {
        return activatedate;
    }

    public void setActivatedate(Date activatedate) {
        this.activatedate = activatedate;
    }

   // 停用日期
    public Date getDiscarddate() {
        return discarddate;
    }
    
    public void setDiscarddate(Date discarddate) {
        this.discarddate = discarddate;
    }
        
   // 存放位置
    public String getStorelocation() {
        return storelocation;
    }
    
    public void setStorelocation(String storelocation) {
        this.storelocation = storelocation;
    }
        
   // 当前状态(1:在库, 2:使用中, 3:保养中, 4:已报废)
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
        
   // 上次保养日期
    public Date getLastmaintdate() {
        return lastmaintdate;
    }
    
    public void setLastmaintdate(Date lastmaintdate) {
        this.lastmaintdate = lastmaintdate;
    }
        
   // 已用量(保养周期)
    public Integer getMaintusedqty() {
        return maintusedqty;
    }
    
    public void setMaintusedqty(Integer maintusedqty) {
        this.maintusedqty = maintusedqty;
    }
        
   // 预警量(保养)
    public Integer getMaintwarnqty() {
        return maintwarnqty;
    }
    
    public void setMaintwarnqty(Integer maintwarnqty) {
        this.maintwarnqty = maintwarnqty;
    }
        
   // 限制量(保养)
    public Integer getMaintlimitqty() {
        return maintlimitqty;
    }
    
    public void setMaintlimitqty(Integer maintlimitqty) {
        this.maintlimitqty = maintlimitqty;
    }
        
   // 已保养次数
    public Integer getMaintcount() {
        return maintcount;
    }

    public void setMaintcount(Integer maintcount) {
        this.maintcount = maintcount;
    }

   // 已用量(累计)
    public Integer getScrapusedqty() {
        return scrapusedqty;
    }
    
    public void setScrapusedqty(Integer scrapusedqty) {
        this.scrapusedqty = scrapusedqty;
    }
        
   // 预警量(报废)
    public Integer getScrapwarnqty() {
        return scrapwarnqty;
    }
    
    public void setScrapwarnqty(Integer scrapwarnqty) {
        this.scrapwarnqty = scrapwarnqty;
    }
        
   // 限制量(报废)
    public Integer getScraplimitqty() {
        return scraplimitqty;
    }
    
    public void setScraplimitqty(Integer scraplimitqty) {
        this.scraplimitqty = scraplimitqty;
    }
        
   // 上次检验日期
    public Date getLastinspdate() {
        return lastinspdate;
    }

    public void setLastinspdate(Date lastinspdate) {
        this.lastinspdate = lastinspdate;
    }

   // 检验状态 (1:正常, 2:待检验, 3:检验中, 4:已过期, 5:不合格/停用)
    public Integer getInspstatus() {
        return inspstatus;
    }

    public void setInspstatus(Integer inspstatus) {
        this.inspstatus = inspstatus;
    }

   // 校验类型 (0:内部校验, 1:外发校验)
    public Integer getInsptype() {
        return insptype;
    }

    public void setInsptype(Integer insptype) {
        this.insptype = insptype;
    }

   // 有效性(1:有效, 0:无效)
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 图片
    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

   // 规格型号
    public String getSpec() {
        return spec;
    }
    
    public void setSpec(String spec) {
        this.spec = spec;
    }
        
   // 品牌
    public String getBrand() {
        return brand;
    }
    
    public void setBrand(String brand) {
        this.brand = brand;
    }
        
   // 供应商
    public String getGroupid() {
        return groupid;
    }
    
    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

   // 购买价格
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 所属部门id
    public String getDeptid() {
        return deptid;
    }
    
    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

