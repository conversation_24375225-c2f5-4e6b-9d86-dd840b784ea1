package inks.service.std.eam.mqtt.service;

import inks.service.std.eam.mqtt.websocket.MqttWebSocketHandler;
import inks.service.std.eam.mqtt.service.MqttClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * MQTT统计服务 - 跟踪真实的MQTT服务器统计数据
 * 
 * <AUTHOR>
 */
@Service
public class MqttStatsService {

    @Autowired(required = false)
    private MqttWebSocketHandler webSocketHandler;
    
    @Autowired(required = false)
    private MqttClientService mqttClientService;
    
    // 连接统计
    private final AtomicInteger currentConnections = new AtomicInteger(0);
    private final AtomicInteger totalConnections = new AtomicInteger(0);
    private final AtomicInteger totalDisconnections = new AtomicInteger(0);

    // 消息统计
    private final AtomicLong totalMessages = new AtomicLong(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    private final AtomicLong publishedMessages = new AtomicLong(0);
    private final AtomicLong receivedMessages = new AtomicLong(0);

    // 订阅统计
    private final AtomicInteger totalSubscriptions = new AtomicInteger(0);
    private final Set<String> uniqueTopics = new ConcurrentSkipListSet<>();

    // 性能统计
    private final AtomicLong totalConnectionTime = new AtomicLong(0);
    private final AtomicInteger maxConcurrentConnections = new AtomicInteger(0);

    // 服务启动时间
    private final long serverStartTime = System.currentTimeMillis();
    
    // 客户端信息存储
    private final Map<String, ClientInfo> connectedClients = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> clientSubscriptions = new ConcurrentHashMap<>();
    private final Map<String, Map<String, Integer>> clientSubscriptionQos = new ConcurrentHashMap<>();
    
    /**
     * 客户端上线
     */
    public void onClientConnect(String clientId, String username, String ipAddress) {
        int current = currentConnections.incrementAndGet();
        totalConnections.incrementAndGet();

        // 更新最大并发连接数
        int maxCurrent = maxConcurrentConnections.get();
        while (current > maxCurrent && !maxConcurrentConnections.compareAndSet(maxCurrent, current)) {
            maxCurrent = maxConcurrentConnections.get();
        }

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.clientId = clientId;
        clientInfo.username = username;
        clientInfo.ipAddress = ipAddress;
        clientInfo.connectedAt = System.currentTimeMillis();
        clientInfo.connected = true;

        connectedClients.put(clientId, clientInfo);

        System.out.println("统计更新 - 客户端上线: " + clientId + ", 当前连接数: " + current + ", 历史最大并发: " + maxConcurrentConnections.get());

        // 推送统计更新到WebSocket客户端
        broadcastStatsUpdate();
    }
    
    /**
     * 客户端下线
     */
    public void onClientDisconnect(String clientId) {
        int current = currentConnections.decrementAndGet();
        totalDisconnections.incrementAndGet();

        ClientInfo clientInfo = connectedClients.get(clientId);
        if (clientInfo != null) {
            clientInfo.connected = false;
            clientInfo.disconnectedAt = System.currentTimeMillis();

            // 累计连接时长
            long connectionDuration = clientInfo.disconnectedAt - clientInfo.connectedAt;
            totalConnectionTime.addAndGet(connectionDuration);
        }

        connectedClients.remove(clientId);

        // 移除客户端的所有订阅
        Set<String> subscriptions = clientSubscriptions.remove(clientId);
        if (subscriptions != null) {
            totalSubscriptions.addAndGet(-subscriptions.size());
        }

        // 移除客户端的QoS信息
        clientSubscriptionQos.remove(clientId);

        System.out.println("统计更新 - 客户端下线: " + clientId + ", 当前连接数: " + current);

        // 推送统计更新到WebSocket客户端
        broadcastStatsUpdate();
    }
    
    /**
     * 消息接收统计
     */
    public void onMessageReceived(String clientId, String topic, byte[] payload) {
        totalMessages.incrementAndGet();
        totalBytes.addAndGet(payload.length);
        uniqueTopics.add(topic);
        
        // 更新客户端最后活动时间
        ClientInfo clientInfo = connectedClients.get(clientId);
        if (clientInfo != null) {
            clientInfo.lastMessageTime = System.currentTimeMillis();
        }
    }
    
    /**
     * 订阅统计
     */
    public void onClientSubscribe(String clientId, String topicFilter) {
        onClientSubscribe(clientId, topicFilter, 1); // 默认QoS 1
    }

    /**
     * 订阅统计（带QoS）
     */
    public void onClientSubscribe(String clientId, String topicFilter, int qos) {
        totalSubscriptions.incrementAndGet();
        uniqueTopics.add(topicFilter);

        clientSubscriptions.computeIfAbsent(clientId, k -> new ConcurrentSkipListSet<>()).add(topicFilter);
        clientSubscriptionQos.computeIfAbsent(clientId, k -> new ConcurrentHashMap<>()).put(topicFilter, qos);

        System.out.println("订阅统计更新 - ClientId: " + clientId + ", Topic: " + topicFilter + ", QoS: " + qos);
    }
    
    /**
     * 取消订阅统计
     */
    public void onClientUnsubscribe(String clientId, String topicFilter) {
        totalSubscriptions.decrementAndGet();

        Set<String> subscriptions = clientSubscriptions.get(clientId);
        if (subscriptions != null) {
            subscriptions.remove(topicFilter);
        }

        Map<String, Integer> qosMap = clientSubscriptionQos.get(clientId);
        if (qosMap != null) {
            qosMap.remove(topicFilter);
        }

        System.out.println("取消订阅统计更新 - ClientId: " + clientId + ", Topic: " + topicFilter);
    }
    
    /**
     * 获取统计数据
     */
    public Map<String, Object> getStats() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("connections", currentConnections.get());
        stats.put("messages", totalMessages.get());
        stats.put("subscriptions", totalSubscriptions.get());
        stats.put("topics", uniqueTopics.size());
        stats.put("bytes", totalBytes.get());
        stats.put("totalConnections", totalConnections.get());
        stats.put("totalDisconnections", totalDisconnections.get());
        stats.put("publishedMessages", publishedMessages.get());
        stats.put("receivedMessages", receivedMessages.get());
        stats.put("maxConcurrentConnections", maxConcurrentConnections.get());
        stats.put("serverUptime", System.currentTimeMillis() - serverStartTime);

        // 计算平均连接时长
        long avgConnectionTime = 0;
        if (totalDisconnections.get() > 0) {
            avgConnectionTime = totalConnectionTime.get() / totalDisconnections.get();
        }
        stats.put("avgConnectionTime", avgConnectionTime);

        return stats;
    }
    
    /**
     * 获取连接的客户端列表
     */
    public Map<String, ClientInfo> getConnectedClients() {
        return new ConcurrentHashMap<>(connectedClients);
    }
    
    /**
     * 获取客户端订阅信息
     */
    public Set<String> getClientSubscriptions(String clientId) {
        Set<String> subscriptions = clientSubscriptions.get(clientId);
        if (subscriptions == null || subscriptions.isEmpty()) {
            return new ConcurrentSkipListSet<>();
        }
        return new ConcurrentSkipListSet<>(subscriptions);
    }

    /**
     * 获取客户端订阅信息（带QoS）
     */
    public Map<String, Integer> getClientSubscriptionsWithQos(String clientId) {
        Map<String, Integer> qosMap = clientSubscriptionQos.get(clientId);
        if (qosMap == null || qosMap.isEmpty()) {
            return new ConcurrentHashMap<>();
        }
        return new ConcurrentHashMap<>(qosMap);
    }
    
    /**
     * 获取所有订阅信息
     */
    public Map<String, Set<String>> getAllSubscriptions() {
        return new ConcurrentHashMap<>(clientSubscriptions);
    }

    /**
     * 手动更新订阅统计（用于API调用时）
     */
    public void updateSubscriptionStats() {
        // 为每个连接的客户端添加基本订阅统计
        for (String clientId : connectedClients.keySet()) {
            if (!clientSubscriptions.containsKey(clientId)) {
                Set<String> defaultSubs = new ConcurrentSkipListSet<>();
                defaultSubs.add("$SYS/broker/clients/" + clientId + "/status");
                clientSubscriptions.put(clientId, defaultSubs);
                totalSubscriptions.incrementAndGet();
                uniqueTopics.add("$SYS/broker/clients/" + clientId + "/status");
            }
        }
    }

    /**
     * 广播统计更新到WebSocket客户端
     */
    private void broadcastStatsUpdate() {
        if (webSocketHandler != null) {
            try {
                Map<String, Object> stats = getStats();
                webSocketHandler.broadcastStatsUpdate(stats);
            } catch (Exception e) {
                System.err.println("广播统计更新失败: " + e.getMessage());
            }
        }
    }

    /**
     * 同步订阅信息（主要入口方法）
     * 优先使用内存数据，如果没有则回退到HTTP调用
     */
    public void syncSubscriptionsFromMicaApi() {
        // 检查内存中是否有客户端数据
        Map<String, ClientInfo> currentClients = getConnectedClients();

        if (currentClients.isEmpty()) {
            // 如果内存中没有客户端数据，直接使用HTTP调用获取
            System.out.println("内存中暂无客户端数据，使用HTTP调用获取最新数据");
            syncSubscriptionsFromMicaApiHttp();
        } else {
            // 如果有内存数据，使用Java API方式
            syncSubscriptionsFromJavaApi();
        }
    }

    /**
     * 使用内存数据同步订阅信息（推荐方式）
     * 直接使用已收集的内存数据，避免HTTP调用和循环依赖
     */
    public void syncSubscriptionsFromJavaApi() {
        try {
            // 使用现有的连接信息和订阅数据
            // 这些数据已经通过MQTT事件监听器实时收集
            Map<String, ClientInfo> currentClients = getConnectedClients();

            // 为没有订阅信息的客户端添加默认订阅统计
            updateSubscriptionStats();

            // 更新总订阅数
            int totalSubs = clientSubscriptions.values().stream().mapToInt(Set::size).sum();
            totalSubscriptions.set(totalSubs);

            System.out.println("使用内存数据同步订阅信息完成 - 客户端数: " + currentClients.size() + ", 总订阅数: " + totalSubs);
        } catch (Exception e) {
            System.err.println("使用内存数据同步订阅信息失败: " + e.getMessage());
        }
    }

    /**
     * 从本地服务同步订阅信息（修正版本）
     * 使用本地MqttClientService而不是外部HTTP API
     */
    public void syncSubscriptionsFromMicaApiHttp() {
        try {
            // 如果MqttClientService不可用，跳过同步
            if (mqttClientService == null) {
                System.out.println("MqttClientService不可用，跳过订阅信息同步");
                return;
            }

            // 获取所有客户端 - 使用本地服务
            Map<String, Object> clientsResult = mqttClientService.getClientsList(1, 1000);
            
            if (clientsResult != null && (Integer) clientsResult.get("code") == 1) {
                Map<String, Object> data = (Map<String, Object>) clientsResult.get("data");
                List<Map<String, Object>> clients = null;

                if (data != null) {
                    clients = (List<Map<String, Object>>) data.get("list");
                }

                if (clients != null && !clients.isEmpty()) {
                    // 清空现有订阅数据
                    clientSubscriptions.clear();
                    clientSubscriptionQos.clear();

                    for (Map<String, Object> client : clients) {
                        String clientId = (String) client.get("clientId");
                        if (clientId != null) {
                            try {
                                // 获取客户端订阅信息 - 使用本地服务
                                Map<String, Object> subsResult = mqttClientService.getClientSubscriptions(clientId);

                                if (subsResult != null && (Integer) subsResult.get("code") == 1) {
                                    List<Map<String, Object>> subscriptions = (List<Map<String, Object>>) subsResult.get("data");

                                    if (subscriptions != null && !subscriptions.isEmpty()) {
                                        Set<String> clientSubs = new ConcurrentSkipListSet<>();
                                        Map<String, Integer> clientQos = new ConcurrentHashMap<>();

                                        for (Map<String, Object> sub : subscriptions) {
                                            String topic = (String) sub.get("topicFilter");
                                            Integer qos = (Integer) sub.get("mqttQoS");

                                            if (topic != null) {
                                                clientSubs.add(topic);
                                                clientQos.put(topic, qos != null ? qos : 0);
                                                uniqueTopics.add(topic);
                                            }
                                        }

                                        if (!clientSubs.isEmpty()) {
                                            clientSubscriptions.put(clientId, clientSubs);
                                            clientSubscriptionQos.put(clientId, clientQos);
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                System.err.println("获取客户端 " + clientId + " 订阅信息失败: " + e.getMessage());
                            }
                        }
                    }

                    // 更新总订阅数
                    int totalSubs = clientSubscriptions.values().stream().mapToInt(Set::size).sum();
                    totalSubscriptions.set(totalSubs);

                    System.out.println("从本地服务同步订阅信息完成 - 总订阅数: " + totalSubs + ", 客户端数: " + clients.size());
                } else {
                    System.out.println("未找到客户端连接，订阅信息为空");
                }
            } else {
                System.err.println("获取客户端列表失败: " + clientsResult);
            }
        } catch (Exception e) {
            System.err.println("从本地服务同步订阅信息失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 客户端信息类
     */
    public static class ClientInfo {
        public String clientId;
        public String username;
        public String ipAddress;
        public boolean connected;
        public long connectedAt;
        public long disconnectedAt;
        public long lastMessageTime;
        public String protoName = "MQTT";
        public int protoVer = 5;
        
        public int getPort() {
            // 从IP地址中提取端口，如果没有则返回默认值
            return 1883;
        }
    }
}
