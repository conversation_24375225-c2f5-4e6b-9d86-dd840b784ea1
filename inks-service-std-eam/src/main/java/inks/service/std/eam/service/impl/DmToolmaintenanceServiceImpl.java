package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmToolinfoEntity;
import inks.service.std.eam.domain.DmToolmaintenanceEntity;
import inks.service.std.eam.domain.DmToolmaintenanceitemEntity;
import inks.service.std.eam.domain.pojo.*;
import inks.service.std.eam.mapper.*;
import inks.service.std.eam.service.DmToolmaintenanceService;
import inks.service.std.eam.service.DmToolmaintenanceitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 工装具保养记录表(DmToolmaintenance)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-09 16:49:20
 */
@Service("dmToolmaintenanceService")
public class DmToolmaintenanceServiceImpl implements DmToolmaintenanceService {
    @Resource
    private DmToolmaintenanceMapper dmToolmaintenanceMapper;
    @Resource
    private DmToolinfoMapper dmToolinfoMapper;
    @Resource
    private DmToolcategoryMapper dmToolcategoryMapper;
    @Resource
    private DmToolmaintenanceitemMapper dmToolmaintenanceitemMapper;
    @Resource
    private DmToolusageitemMapper dmToolusageitemMapper;

    @Resource
    private DmToolmaintenanceitemService dmToolmaintenanceitemService;

    @Override
    public DmToolmaintenancePojo getEntity(String key, String tid) {
        return this.dmToolmaintenanceMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<DmToolmaintenanceitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolmaintenanceitemdetailPojo> lst = dmToolmaintenanceMapper.getPageList(queryParam);
            PageInfo<DmToolmaintenanceitemdetailPojo> pageInfo = new PageInfo<DmToolmaintenanceitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public DmToolmaintenancePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            DmToolmaintenancePojo dmToolmaintenancePojo = this.dmToolmaintenanceMapper.getEntity(key, tid);
            //读取子表
            dmToolmaintenancePojo.setItem(dmToolmaintenanceitemMapper.getList(dmToolmaintenancePojo.getId(), tid));
            return dmToolmaintenancePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<DmToolmaintenancePojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolmaintenancePojo> lst = dmToolmaintenanceMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (DmToolmaintenancePojo item : lst) {
                item.setItem(dmToolmaintenanceitemMapper.getList(item.getId(), tid));
            }
            PageInfo<DmToolmaintenancePojo> pageInfo = new PageInfo<DmToolmaintenancePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<DmToolmaintenancePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolmaintenancePojo> lst = dmToolmaintenanceMapper.getPageTh(queryParam);
            PageInfo<DmToolmaintenancePojo> pageInfo = new PageInfo<DmToolmaintenancePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public DmToolmaintenancePojo insert(DmToolmaintenancePojo dmToolmaintenancePojo) {
        String tid = dmToolmaintenancePojo.getTenantid();
        //初始化NULL字段
        cleanNull(dmToolmaintenancePojo);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmToolmaintenanceEntity dmToolmaintenanceEntity = new DmToolmaintenanceEntity();
        BeanUtils.copyProperties(dmToolmaintenancePojo, dmToolmaintenanceEntity);

        //设置id和新建日期
        dmToolmaintenanceEntity.setId(id);
        dmToolmaintenanceEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmToolmaintenanceMapper.insert(dmToolmaintenanceEntity);
        //Item子表处理
        List<DmToolmaintenanceitemPojo> lst = dmToolmaintenancePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (DmToolmaintenanceitemPojo item : lst) {
                //初始化item的NULL
                DmToolmaintenanceitemPojo itemPojo = this.dmToolmaintenanceitemService.clearNull(item);
                DmToolmaintenanceitemEntity dmToolmaintenanceitemEntity = new DmToolmaintenanceitemEntity();
                BeanUtils.copyProperties(itemPojo, dmToolmaintenanceitemEntity);
                //设置id和Pid
                dmToolmaintenanceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                dmToolmaintenanceitemEntity.setPid(id);
                dmToolmaintenanceitemEntity.setTenantid(tid);
                dmToolmaintenanceitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.dmToolmaintenanceitemMapper.insert(dmToolmaintenanceitemEntity);
                // 保养次数校验
                DmToolinfoPojo toolDB = dmToolinfoMapper.getEntity(item.getToolid(), tid);
                DmToolcategoryPojo toolcategoryDB = dmToolcategoryMapper.getEntity(toolDB.getCategoryid(), tid);
                if (toolcategoryDB.getMaintlimitcount() > 0 && toolDB.getMaintcount() + 1 > toolcategoryDB.getMaintlimitcount()) {
                    throw new BaseBusinessException("工装具[" + toolDB.getToolname() + "]保养次数超过限制次数(" + toolcategoryDB.getMaintlimitcount() + ")");
                }
                // 同步工装具保养使用量 清0
                DmToolinfoEntity dmToolinfoEntity = new DmToolinfoEntity();
                dmToolinfoEntity.setId(item.getToolid());
                dmToolinfoEntity.setMaintusedqty(0);
                dmToolinfoEntity.setMaintcount(toolDB.getMaintcount() + 1);
                dmToolinfoEntity.setLastmaintdate(dmToolmaintenanceitemEntity.getMaintdate());
                dmToolinfoEntity.setStatus(3);// 当前状态(1:在库, 2:使用中, 3:保养中, 4:已报废)
                dmToolinfoEntity.setTenantid(tid);
                dmToolinfoMapper.update(dmToolinfoEntity);
            }

        }
        //返回Bill实例
        return this.getBillEntity(dmToolmaintenanceEntity.getId(), tid);
    }


    @Override
    @Transactional
    public DmToolmaintenancePojo update(DmToolmaintenancePojo dmToolmaintenancePojo) {
        String tid = dmToolmaintenancePojo.getTenantid();
        //主表更改
        DmToolmaintenanceEntity dmToolmaintenanceEntity = new DmToolmaintenanceEntity();
        BeanUtils.copyProperties(dmToolmaintenancePojo, dmToolmaintenanceEntity);
        this.dmToolmaintenanceMapper.update(dmToolmaintenanceEntity);
        if (dmToolmaintenancePojo.getItem() != null) {
            //Item子表处理
            List<DmToolmaintenanceitemPojo> lst = dmToolmaintenancePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = dmToolmaintenanceMapper.getDelItemIds(dmToolmaintenancePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String delId : lstDelIds) {
                    DmToolmaintenanceitemPojo item = dmToolmaintenanceitemMapper.getEntity(delId, tid);
                    // 只允许删除最新保养
                    DmToolmaintenanceitemPojo leastMaint = dmToolmaintenanceitemMapper.getLeastEntity(item.getToolid(), tid);
                    if (!Objects.equals(leastMaint.getId(), delId)) {
                        throw new BaseBusinessException("只允许删除最新保养项：" + leastMaint.getRefno());
                    }
                    this.dmToolmaintenanceitemMapper.delete(delId, tid);
                    // 找到上次保养时间 即删掉本次后的最新的保养
                    DmToolmaintenanceitemPojo leastMaintAfterDelete = dmToolmaintenanceitemMapper.getLeastEntity(item.getToolid(), tid);
                    int sumUsedQtyAfterDate = dmToolusageitemMapper.getSumUsedQtyAfterDate(item.getToolid(), leastMaintAfterDelete.getMaintdate(), tid);
                    // 同步工装具保养使用量 还原从上次保养开始使用量
                    DmToolinfoEntity dmToolinfoEntity = new DmToolinfoEntity();
                    dmToolinfoEntity.setId(item.getToolid());
                    dmToolinfoEntity.setMaintusedqty(sumUsedQtyAfterDate);
                    DmToolinfoPojo toolDB = dmToolinfoMapper.getEntity(item.getToolid(), tid);
                    dmToolinfoEntity.setMaintcount(toolDB.getMaintcount() - 1);
                    dmToolinfoEntity.setLastmaintdate(leastMaintAfterDelete.getMaintdate());
                    dmToolinfoEntity.setStatus(2);// 当前状态(1:在库, 2:使用中, 3:保养中, 4:已报废)
                    dmToolinfoEntity.setTenantid(tid);
                    dmToolinfoMapper.update(dmToolinfoEntity);
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (DmToolmaintenanceitemPojo item : lst) {
                    DmToolmaintenanceitemEntity dmToolmaintenanceitemEntity = new DmToolmaintenanceitemEntity();
                    if ("".equals(item.getId()) || item.getId() == null) {
                        //初始化item的NULL
                        DmToolmaintenanceitemPojo itemPojo = this.dmToolmaintenanceitemService.clearNull(item);
                        BeanUtils.copyProperties(itemPojo, dmToolmaintenanceitemEntity);
                        //设置id和Pid
                        dmToolmaintenanceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        dmToolmaintenanceitemEntity.setPid(dmToolmaintenanceEntity.getId());  // 主表 id
                        dmToolmaintenanceitemEntity.setTenantid(tid);   // 租户id
                        dmToolmaintenanceitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.dmToolmaintenanceitemMapper.insert(dmToolmaintenanceitemEntity);
                        // 保养次数校验 （只在新插入的保养同步清空保养使用量，因为修改不会再清空保养使用量了）
                        DmToolinfoPojo toolDB = dmToolinfoMapper.getEntity(item.getToolid(), tid);
                        DmToolcategoryPojo toolcategoryDB = dmToolcategoryMapper.getEntity(toolDB.getCategoryid(), tid);
                        if (toolcategoryDB.getMaintlimitcount() > 0 && toolDB.getMaintcount() + 1 > toolcategoryDB.getMaintlimitcount()) {
                            throw new BaseBusinessException("工装具[" + toolDB.getToolname() + "]保养次数超过限制次数(" + toolcategoryDB.getMaintlimitcount() + ")");
                        }
                        // 同步工装具保养使用量 清0
                        DmToolinfoEntity dmToolinfoEntity = new DmToolinfoEntity();
                        dmToolinfoEntity.setId(item.getToolid());
                        dmToolinfoEntity.setMaintusedqty(0);
                        dmToolinfoEntity.setMaintcount(toolDB.getMaintcount() + 1);
                        dmToolinfoEntity.setLastmaintdate(dmToolmaintenanceitemEntity.getMaintdate());
                        dmToolinfoEntity.setStatus(3);// 当前状态(1:在库, 2:使用中, 3:保养中, 4:已报废)
                        dmToolinfoEntity.setTenantid(tid);
                        dmToolinfoMapper.update(dmToolinfoEntity);
                    } else {
                        BeanUtils.copyProperties(item, dmToolmaintenanceitemEntity);
                        dmToolmaintenanceitemEntity.setTenantid(tid);
                        this.dmToolmaintenanceitemMapper.update(dmToolmaintenanceitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(dmToolmaintenanceEntity.getId(), tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
        DmToolmaintenancePojo dmToolmaintenancePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<DmToolmaintenanceitemPojo> lst = dmToolmaintenancePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (DmToolmaintenanceitemPojo item : lst) {
                // 只允许删除最新保养
                DmToolmaintenanceitemPojo leastMaint = dmToolmaintenanceitemMapper.getLeastEntity(item.getToolid(), tid);
                if (!Objects.equals(leastMaint.getId(), key)) {
                    throw new BaseBusinessException("只允许删除最新保养项：" + leastMaint.getRefno());
                }
                this.dmToolmaintenanceitemMapper.delete(item.getId(), tid);
                // 找到上次保养时间 即删掉本次后的最新的保养
                DmToolmaintenanceitemPojo leastMaintAfterDelete = dmToolmaintenanceitemMapper.getLeastEntity(item.getToolid(), tid);
                int sumUsedQtyAfterDate = dmToolusageitemMapper.getSumUsedQtyAfterDate(item.getToolid(), leastMaintAfterDelete.getMaintdate(), tid);
                // 同步工装具保养使用量 还原从上次保养开始使用量
                DmToolinfoEntity dmToolinfoEntity = new DmToolinfoEntity();
                dmToolinfoEntity.setId(item.getToolid());
                dmToolinfoEntity.setMaintusedqty(sumUsedQtyAfterDate);
                DmToolinfoPojo toolDB = dmToolinfoMapper.getEntity(item.getToolid(), tid);
                dmToolinfoEntity.setMaintcount(toolDB.getMaintcount() - 1);
                dmToolinfoEntity.setLastmaintdate(leastMaintAfterDelete.getMaintdate());
                dmToolinfoEntity.setStatus(2);// 当前状态(1:在库, 2:使用中, 3:保养中, 4:已报废)
                dmToolinfoEntity.setTenantid(tid);
                dmToolinfoMapper.update(dmToolinfoEntity);
            }
        }
        return this.dmToolmaintenanceMapper.delete(key, tid);
    }


    private static void cleanNull(DmToolmaintenancePojo dmToolmaintenancePojo) {
        if (dmToolmaintenancePojo.getRefno() == null) dmToolmaintenancePojo.setRefno("");
        if (dmToolmaintenancePojo.getBilldate() == null) dmToolmaintenancePojo.setBilldate(new Date());
        if (dmToolmaintenancePojo.getBilltype() == null) dmToolmaintenancePojo.setBilltype("");
        if (dmToolmaintenancePojo.getBilltitle() == null) dmToolmaintenancePojo.setBilltitle("");
        if (dmToolmaintenancePojo.getOperatorid() == null) dmToolmaintenancePojo.setOperatorid("");
        if (dmToolmaintenancePojo.getOperator() == null) dmToolmaintenancePojo.setOperator("");
        if (dmToolmaintenancePojo.getItemcount() == null) dmToolmaintenancePojo.setItemcount(0);
        if (dmToolmaintenancePojo.getFinishcount() == null) dmToolmaintenancePojo.setFinishcount(0);
        if (dmToolmaintenancePojo.getSummary() == null) dmToolmaintenancePojo.setSummary("");
        if (dmToolmaintenancePojo.getRownum() == null) dmToolmaintenancePojo.setRownum(0);
        if (dmToolmaintenancePojo.getRemark() == null) dmToolmaintenancePojo.setRemark("");
        if (dmToolmaintenancePojo.getCreateby() == null) dmToolmaintenancePojo.setCreateby("");
        if (dmToolmaintenancePojo.getCreatebyid() == null) dmToolmaintenancePojo.setCreatebyid("");
        if (dmToolmaintenancePojo.getCreatedate() == null) dmToolmaintenancePojo.setCreatedate(new Date());
        if (dmToolmaintenancePojo.getLister() == null) dmToolmaintenancePojo.setLister("");
        if (dmToolmaintenancePojo.getListerid() == null) dmToolmaintenancePojo.setListerid("");
        if (dmToolmaintenancePojo.getModifydate() == null) dmToolmaintenancePojo.setModifydate(new Date());
        if (dmToolmaintenancePojo.getCustom1() == null) dmToolmaintenancePojo.setCustom1("");
        if (dmToolmaintenancePojo.getCustom2() == null) dmToolmaintenancePojo.setCustom2("");
        if (dmToolmaintenancePojo.getCustom3() == null) dmToolmaintenancePojo.setCustom3("");
        if (dmToolmaintenancePojo.getCustom4() == null) dmToolmaintenancePojo.setCustom4("");
        if (dmToolmaintenancePojo.getCustom5() == null) dmToolmaintenancePojo.setCustom5("");
        if (dmToolmaintenancePojo.getCustom6() == null) dmToolmaintenancePojo.setCustom6("");
        if (dmToolmaintenancePojo.getCustom7() == null) dmToolmaintenancePojo.setCustom7("");
        if (dmToolmaintenancePojo.getCustom8() == null) dmToolmaintenancePojo.setCustom8("");
        if (dmToolmaintenancePojo.getCustom9() == null) dmToolmaintenancePojo.setCustom9("");
        if (dmToolmaintenancePojo.getCustom10() == null) dmToolmaintenancePojo.setCustom10("");
        if (dmToolmaintenancePojo.getTenantid() == null) dmToolmaintenancePojo.setTenantid("");
        if (dmToolmaintenancePojo.getTenantname() == null) dmToolmaintenancePojo.setTenantname("");
        if (dmToolmaintenancePojo.getRevision() == null) dmToolmaintenancePojo.setRevision(0);
    }

}
