package inks.service.std.eam.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.std.eam.domain.DmUpkeepplanitemEntity;
import inks.service.std.eam.domain.pojo.DmUpkeepplanitemPojo;
import inks.service.std.eam.mapper.DmUpkeepplanitemMapper;
import inks.service.std.eam.service.DmUpkeepplanitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备清单(DmUpkeepplanitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-06 14:36:10
 */
@Service("dmUpkeepplanitemService")
public class DmUpkeepplanitemServiceImpl implements DmUpkeepplanitemService {
    @Resource
    private DmUpkeepplanitemMapper dmUpkeepplanitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public DmUpkeepplanitemPojo getEntity(String key, String tid) {
        return this.dmUpkeepplanitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<DmUpkeepplanitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmUpkeepplanitemPojo> lst = dmUpkeepplanitemMapper.getPageList(queryParam);
            PageInfo<DmUpkeepplanitemPojo> pageInfo = new PageInfo<DmUpkeepplanitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DmUpkeepplanitemPojo> getList(String Pid, String tid) {
        try {
            List<DmUpkeepplanitemPojo> lst = dmUpkeepplanitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param dmUpkeepplanitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeepplanitemPojo insert(DmUpkeepplanitemPojo dmUpkeepplanitemPojo) {
        //初始化item的NULL
        DmUpkeepplanitemPojo itempojo = this.clearNull(dmUpkeepplanitemPojo);
        DmUpkeepplanitemEntity dmUpkeepplanitemEntity = new DmUpkeepplanitemEntity();
        BeanUtils.copyProperties(itempojo, dmUpkeepplanitemEntity);
        //生成雪花id
        dmUpkeepplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        dmUpkeepplanitemEntity.setRevision(1);  //乐观锁
        this.dmUpkeepplanitemMapper.insert(dmUpkeepplanitemEntity);
        return this.getEntity(dmUpkeepplanitemEntity.getId(), dmUpkeepplanitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param dmUpkeepplanitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeepplanitemPojo update(DmUpkeepplanitemPojo dmUpkeepplanitemPojo) {
        DmUpkeepplanitemEntity dmUpkeepplanitemEntity = new DmUpkeepplanitemEntity();
        BeanUtils.copyProperties(dmUpkeepplanitemPojo, dmUpkeepplanitemEntity);
        this.dmUpkeepplanitemMapper.update(dmUpkeepplanitemEntity);
        return this.getEntity(dmUpkeepplanitemEntity.getId(), dmUpkeepplanitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.dmUpkeepplanitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param dmUpkeepplanitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public DmUpkeepplanitemPojo clearNull(DmUpkeepplanitemPojo dmUpkeepplanitemPojo) {
        //初始化NULL字段
        if (dmUpkeepplanitemPojo.getPid() == null) dmUpkeepplanitemPojo.setPid("");
        if (dmUpkeepplanitemPojo.getDeviceid() == null) dmUpkeepplanitemPojo.setDeviceid("");
        if (dmUpkeepplanitemPojo.getRemark() == null) dmUpkeepplanitemPojo.setRemark("");
        if (dmUpkeepplanitemPojo.getRownum() == null) dmUpkeepplanitemPojo.setRownum(0);
        if (dmUpkeepplanitemPojo.getCustom1() == null) dmUpkeepplanitemPojo.setCustom1("");
        if (dmUpkeepplanitemPojo.getCustom2() == null) dmUpkeepplanitemPojo.setCustom2("");
        if (dmUpkeepplanitemPojo.getCustom3() == null) dmUpkeepplanitemPojo.setCustom3("");
        if (dmUpkeepplanitemPojo.getCustom4() == null) dmUpkeepplanitemPojo.setCustom4("");
        if (dmUpkeepplanitemPojo.getCustom5() == null) dmUpkeepplanitemPojo.setCustom5("");
        if (dmUpkeepplanitemPojo.getCustom6() == null) dmUpkeepplanitemPojo.setCustom6("");
        if (dmUpkeepplanitemPojo.getCustom7() == null) dmUpkeepplanitemPojo.setCustom7("");
        if (dmUpkeepplanitemPojo.getCustom8() == null) dmUpkeepplanitemPojo.setCustom8("");
        if (dmUpkeepplanitemPojo.getTenantid() == null) dmUpkeepplanitemPojo.setTenantid("");
        if (dmUpkeepplanitemPojo.getRevision() == null) dmUpkeepplanitemPojo.setRevision(0);
        return dmUpkeepplanitemPojo;
    }
}
