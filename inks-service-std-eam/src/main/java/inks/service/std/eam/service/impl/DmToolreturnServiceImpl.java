package inks.service.std.eam.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.std.eam.domain.pojo.DmToolreturnPojo;
import inks.service.std.eam.domain.pojo.DmToolreturnitemPojo;
import inks.service.std.eam.domain.pojo.DmToolreturnitemdetailPojo;
import inks.service.std.eam.domain.DmToolreturnEntity;
import inks.service.std.eam.domain.DmToolreturnitemEntity;
import inks.service.std.eam.mapper.DmToolreturnMapper;
import inks.service.std.eam.service.DmToolreturnService;
import inks.service.std.eam.service.DmToolreturnitemService;
import inks.service.std.eam.mapper.DmToolreturnitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 工装具归还主表(DmToolreturn)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-19 10:16:37
 */
@Service("dmToolreturnService")
public class DmToolreturnServiceImpl implements DmToolreturnService {
    @Resource
    private DmToolreturnMapper dmToolreturnMapper;
    
    @Resource
    private DmToolreturnitemMapper dmToolreturnitemMapper;
    

    @Resource
    private DmToolreturnitemService dmToolreturnitemService;

    @Override
    public DmToolreturnPojo getEntity(String key, String tid) {
        return this.dmToolreturnMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<DmToolreturnitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolreturnitemdetailPojo> lst = dmToolreturnMapper.getPageList(queryParam);
            PageInfo<DmToolreturnitemdetailPojo> pageInfo = new PageInfo<DmToolreturnitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public DmToolreturnPojo getBillEntity(String key, String tid) {
       try {
           //读取主表
           DmToolreturnPojo dmToolreturnPojo = this.dmToolreturnMapper.getEntity(key,tid);
           //读取子表
           dmToolreturnPojo.setItem(dmToolreturnitemMapper.getList(dmToolreturnPojo.getId(),tid));
           return dmToolreturnPojo;
       }catch (Exception e){
          throw new BaseBusinessException(e.getMessage());
       } 
    }


    @Override
    public PageInfo<DmToolreturnPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolreturnPojo> lst = dmToolreturnMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(DmToolreturnPojo item : lst){
                item.setItem(dmToolreturnitemMapper.getList(item.getId(), tid));
            }
            PageInfo<DmToolreturnPojo> pageInfo = new PageInfo<DmToolreturnPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<DmToolreturnPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<DmToolreturnPojo> lst = dmToolreturnMapper.getPageTh(queryParam);
            PageInfo<DmToolreturnPojo> pageInfo = new PageInfo<DmToolreturnPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public DmToolreturnPojo insert(DmToolreturnPojo dmToolreturnPojo) {
    String tid = dmToolreturnPojo.getTenantid();
        //初始化NULL字段
        cleanNull(dmToolreturnPojo);
         //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        DmToolreturnEntity dmToolreturnEntity = new DmToolreturnEntity(); 
        BeanUtils.copyProperties(dmToolreturnPojo,dmToolreturnEntity);
      
        //设置id和新建日期
        dmToolreturnEntity.setId(id);
        dmToolreturnEntity.setRevision(1);  //乐观锁
        //插入主表
        this.dmToolreturnMapper.insert(dmToolreturnEntity);
        //Item子表处理
        List<DmToolreturnitemPojo> lst = dmToolreturnPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(DmToolreturnitemPojo item : lst){
               //初始化item的NULL
               DmToolreturnitemPojo itemPojo =this.dmToolreturnitemService.clearNull(item);
               DmToolreturnitemEntity dmToolreturnitemEntity = new DmToolreturnitemEntity(); 
               BeanUtils.copyProperties(itemPojo,dmToolreturnitemEntity);
               //设置id和Pid
               dmToolreturnitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               dmToolreturnitemEntity.setPid(id);
               dmToolreturnitemEntity.setTenantid(tid);
               dmToolreturnitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.dmToolreturnitemMapper.insert(dmToolreturnitemEntity);
            }
            
        } 
        //返回Bill实例
        return this.getBillEntity(dmToolreturnEntity.getId(),tid);
    }


    @Override
    @Transactional
    public DmToolreturnPojo update(DmToolreturnPojo dmToolreturnPojo) {
        String tid = dmToolreturnPojo.getTenantid();
        //主表更改
        DmToolreturnEntity dmToolreturnEntity = new DmToolreturnEntity(); 
        BeanUtils.copyProperties(dmToolreturnPojo,dmToolreturnEntity);
        this.dmToolreturnMapper.update(dmToolreturnEntity);
        if (dmToolreturnPojo.getItem() != null) {
        //Item子表处理
        List<DmToolreturnitemPojo> lst = dmToolreturnPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =dmToolreturnMapper.getDelItemIds(dmToolreturnPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for (String delId : lstDelIds) {
             this.dmToolreturnitemMapper.delete(delId, tid);
            }
        }
        if (lst != null){
            //循环每个item子表
            for(DmToolreturnitemPojo item : lst){
               DmToolreturnitemEntity dmToolreturnitemEntity = new DmToolreturnitemEntity(); 
               if ("".equals(item.getId()) || item.getId() == null){
                //初始化item的NULL
               DmToolreturnitemPojo itemPojo =this.dmToolreturnitemService.clearNull(item);
               BeanUtils.copyProperties(itemPojo,dmToolreturnitemEntity);
               //设置id和Pid
               dmToolreturnitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               dmToolreturnitemEntity.setPid(dmToolreturnEntity.getId());  // 主表 id
               dmToolreturnitemEntity.setTenantid(tid);   // 租户id
               dmToolreturnitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.dmToolreturnitemMapper.insert(dmToolreturnitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(item,dmToolreturnitemEntity);       
                dmToolreturnitemEntity.setTenantid(tid);        
               this.dmToolreturnitemMapper.update(dmToolreturnitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(dmToolreturnEntity.getId(),tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
       DmToolreturnPojo dmToolreturnPojo =  this.getBillEntity(key,tid);
        //Item子表处理
        List<DmToolreturnitemPojo> lst = dmToolreturnPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(DmToolreturnitemPojo item : lst){
              this.dmToolreturnitemMapper.delete(item.getId(),tid);
            }
        }        
        return this.dmToolreturnMapper.delete(key,tid) ;
    }
    

    

    private static void cleanNull(DmToolreturnPojo dmToolreturnPojo) {
        if(dmToolreturnPojo.getRefno()==null) dmToolreturnPojo.setRefno("");
        if(dmToolreturnPojo.getBilldate()==null) dmToolreturnPojo.setBilldate(new Date());
        if(dmToolreturnPojo.getBilltype()==null) dmToolreturnPojo.setBilltype("");
        if(dmToolreturnPojo.getBilltitle()==null) dmToolreturnPojo.setBilltitle("");
        if(dmToolreturnPojo.getReturnerid()==null) dmToolreturnPojo.setReturnerid("");
        if(dmToolreturnPojo.getReturnername()==null) dmToolreturnPojo.setReturnername("");
        if(dmToolreturnPojo.getItemcount()==null) dmToolreturnPojo.setItemcount(0);
        if(dmToolreturnPojo.getSummary()==null) dmToolreturnPojo.setSummary("");
        if(dmToolreturnPojo.getCreateby()==null) dmToolreturnPojo.setCreateby("");
        if(dmToolreturnPojo.getCreatebyid()==null) dmToolreturnPojo.setCreatebyid("");
        if(dmToolreturnPojo.getCreatedate()==null) dmToolreturnPojo.setCreatedate(new Date());
        if(dmToolreturnPojo.getLister()==null) dmToolreturnPojo.setLister("");
        if(dmToolreturnPojo.getListerid()==null) dmToolreturnPojo.setListerid("");
        if(dmToolreturnPojo.getModifydate()==null) dmToolreturnPojo.setModifydate(new Date());
        if(dmToolreturnPojo.getCustom1()==null) dmToolreturnPojo.setCustom1("");
        if(dmToolreturnPojo.getCustom2()==null) dmToolreturnPojo.setCustom2("");
        if(dmToolreturnPojo.getCustom3()==null) dmToolreturnPojo.setCustom3("");
        if(dmToolreturnPojo.getCustom4()==null) dmToolreturnPojo.setCustom4("");
        if(dmToolreturnPojo.getCustom5()==null) dmToolreturnPojo.setCustom5("");
        if(dmToolreturnPojo.getCustom6()==null) dmToolreturnPojo.setCustom6("");
        if(dmToolreturnPojo.getCustom7()==null) dmToolreturnPojo.setCustom7("");
        if(dmToolreturnPojo.getCustom8()==null) dmToolreturnPojo.setCustom8("");
        if(dmToolreturnPojo.getCustom9()==null) dmToolreturnPojo.setCustom9("");
        if(dmToolreturnPojo.getCustom10()==null) dmToolreturnPojo.setCustom10("");
        if(dmToolreturnPojo.getTenantid()==null) dmToolreturnPojo.setTenantid("");
        if(dmToolreturnPojo.getTenantname()==null) dmToolreturnPojo.setTenantname("");
        if(dmToolreturnPojo.getRevision()==null) dmToolreturnPojo.setRevision(0);
   }

}
