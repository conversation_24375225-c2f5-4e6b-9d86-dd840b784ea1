package inks.service.std.eam.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.IotDeviceprofilePojo;
import inks.service.std.eam.service.IotDeviceprofileService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 设备配置模板表(Iot_DeviceProfile)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:20
 */
//@RestController
//@RequestMapping("iotDeviceprofile")
public class IotDeviceprofileController {

    @Resource
    private IotDeviceprofileService iotDeviceprofileService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(IotDeviceprofileController.class);


    @ApiOperation(value = " 获取设备配置模板表详细信息", notes = "获取设备配置模板表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_DeviceProfile.List")
    public R<IotDeviceprofilePojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotDeviceprofileService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_DeviceProfile.List")
    public R<PageInfo<IotDeviceprofilePojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Iot_DeviceProfile.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.iotDeviceprofileService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增设备配置模板表", notes = "新增设备配置模板表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_DeviceProfile.Add")
    public R<IotDeviceprofilePojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            IotDeviceprofilePojo iotDeviceprofilePojo = JSONArray.parseObject(json, IotDeviceprofilePojo.class);
            iotDeviceprofilePojo.setCreateby(loginUser.getRealName());   // 创建者
            iotDeviceprofilePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            iotDeviceprofilePojo.setCreatedate(new Date());   // 创建时间
            iotDeviceprofilePojo.setLister(loginUser.getRealname());   // 制表
            iotDeviceprofilePojo.setListerid(loginUser.getUserid());    // 制表id  
            iotDeviceprofilePojo.setModifydate(new Date());   //修改时间
            iotDeviceprofilePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.iotDeviceprofileService.insert(iotDeviceprofilePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改设备配置模板表", notes = "修改设备配置模板表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_DeviceProfile.Edit")
    public R<IotDeviceprofilePojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            IotDeviceprofilePojo iotDeviceprofilePojo = JSONArray.parseObject(json, IotDeviceprofilePojo.class);
            iotDeviceprofilePojo.setLister(loginUser.getRealname());   // 制表
            iotDeviceprofilePojo.setListerid(loginUser.getUserid());    // 制表id  
            iotDeviceprofilePojo.setTenantid(loginUser.getTenantid());   //租户id
            iotDeviceprofilePojo.setModifydate(new Date());   //修改时间
//            iotDeviceprofilePojo.setAssessor(""); // 审核员
//            iotDeviceprofilePojo.setAssessorid(""); // 审核员id
//            iotDeviceprofilePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.iotDeviceprofileService.update(iotDeviceprofilePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除设备配置模板表", notes = "删除设备配置模板表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_DeviceProfile.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotDeviceprofileService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_DeviceProfile.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        IotDeviceprofilePojo iotDeviceprofilePojo = this.iotDeviceprofileService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(iotDeviceprofilePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

