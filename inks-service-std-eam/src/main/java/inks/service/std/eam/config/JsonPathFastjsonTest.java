package inks.service.std.eam.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import org.junit.Test;

import java.util.List;

public class JsonPathFastjsonTest {

    private final String json = "{\n" +
            "  \"msg\": {\n" +
            "    \"info\": {\n" +
            "      \"code\": \"ds01\",\n" +
            "      \"date\": \"2024-06-08 13:42:00\",\n" +
            "      \"content\": [\n" +
            "        {\"key\": \"temp\",   \"value\": 37},\n" +
            "        {\"key\": \"dianya\", \"value\": \"220V\"}\n" +
            "      ]\n" +
            "    }\n" +
            "  },\n" +
            "  \"modulecode\": \"saiot\"\n" +
            "}";

    @Test
    public void testAllJsonPathWithVariable() {
        // 1. 直接取普通属性
        String path1 = "msg.info.code";
        Object code = JSONPath.eval(JSON.parse(json), path1);
        System.out.println(path1 + " => " + code);
        // 2. 取数组中指定下标元素的某个字段
        String path2 = "msg.info.content[0].key";
        Object firstKey = JSONPath.eval(JSON.parse(json), path2);
        System.out.println(path2 + " => " + firstKey);
        // 3. 用过滤器取 value 列表
        String path3 = "msg.info.content[?(@.key=='temp')].value";
        List<?> temps = (List<?>) JSONPath.eval(JSON.parse(json), path3);
        System.out.println(path3 + " => " + temps);

        // 4. 只关心过滤后第一个值
        String path4 = "msg.info.content[?(@.key=='dianya')].value";
        List<?> dianyaList = (List<?>) JSONPath.eval(JSON.parse(json), path4);
        Object dianya = dianyaList.isEmpty() ? null : dianyaList.get(0);
        System.out.println(path4 + " => " + dianya);

        // 5. 通配符，取所有 value
        String path5 = "msg.info.content[*].value";
        List<?> allValues = (List<?>) JSONPath.eval(JSON.parse(json), path5);
        System.out.println(path5 + " => " + allValues);
    }
}
