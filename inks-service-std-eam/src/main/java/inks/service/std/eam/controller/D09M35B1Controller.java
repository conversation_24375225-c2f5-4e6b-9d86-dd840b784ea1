package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.service.std.eam.domain.pojo.IotRelationPojo;
import inks.service.std.eam.service.IotRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 实体关系映射表(Iot_Relation)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-17 11:02:49
 */
@RestController
@RequestMapping("D09M35B1")
@Api(tags = "D09M35B1:实体关系映射表")
public class D09M35B1Controller extends IotRelationController {

    @Resource
    private IotRelationService iotRelationService;
    @Resource
    private TokenService tokenService;


    @ApiOperation(value = " 获取实体关系映射表详细信息", notes = "获取实体关系映射表详细信息", produces = "application/json")
    @RequestMapping(value = "/getList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_Relation.List")
    public R<List<IotRelationPojo>> getList(String fromid, String fromtype, String toid, String totype) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotRelationService.getList(fromid, fromtype, toid, totype, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
