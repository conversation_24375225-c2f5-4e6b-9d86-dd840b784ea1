package inks.service.std.eam.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.std.eam.domain.pojo.IotRulenodestatePojo;
import inks.service.std.eam.service.IotRulenodestateService;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 规则节点运行状态表(Iot_RuleNodeState)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-17 10:43:48
 */
//@RestController
//@RequestMapping("iotRulenodestate")
public class IotRulenodestateController {

    @Resource
    private IotRulenodestateService iotRulenodestateService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(IotRulenodestateController.class);


    @ApiOperation(value=" 获取规则节点运行状态表详细信息", notes="获取规则节点运行状态表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_RuleNodeState.List")
    public R<IotRulenodestatePojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotRulenodestateService.getEntity(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_RuleNodeState.List")
    public R<PageInfo<IotRulenodestatePojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Iot_RuleNodeState.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.iotRulenodestateService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value=" 新增规则节点运行状态表", notes="新增规则节点运行状态表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_RuleNodeState.Add")
    public R<IotRulenodestatePojo> create(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
       IotRulenodestatePojo iotRulenodestatePojo = JSONArray.parseObject(json,IotRulenodestatePojo.class);       
            iotRulenodestatePojo.setCreateby(loginUser.getRealName());   // 创建者
            iotRulenodestatePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            iotRulenodestatePojo.setCreatedate(new Date());   // 创建时间
            iotRulenodestatePojo.setLister(loginUser.getRealname());   // 制表
            iotRulenodestatePojo.setListerid(loginUser.getUserid());    // 制表id  
            iotRulenodestatePojo.setModifydate(new Date());   //修改时间
            iotRulenodestatePojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.iotRulenodestateService.insert(iotRulenodestatePojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }


    @ApiOperation(value="修改规则节点运行状态表", notes="修改规则节点运行状态表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "Iot_RuleNodeState.Edit")
    public R<IotRulenodestatePojo> update(@RequestBody String json) {
       LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
         IotRulenodestatePojo iotRulenodestatePojo = JSONArray.parseObject(json,IotRulenodestatePojo.class);
            iotRulenodestatePojo.setLister(loginUser.getRealname());   // 制表
            iotRulenodestatePojo.setListerid(loginUser.getUserid());    // 制表id  
            iotRulenodestatePojo.setTenantid(loginUser.getTenantid());   //租户id
            iotRulenodestatePojo.setModifydate(new Date());   //修改时间
//            iotRulenodestatePojo.setAssessor(""); // 审核员
//            iotRulenodestatePojo.setAssessorid(""); // 审核员id
//            iotRulenodestatePojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.iotRulenodestateService.update(iotRulenodestatePojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除规则节点运行状态表", notes="删除规则节点运行状态表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_RuleNodeState.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.iotRulenodestateService.delete(key, loginUser.getTenantid()));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Iot_RuleNodeState.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        IotRulenodestatePojo iotRulenodestatePojo = this.iotRulenodestateService.getEntity(key,loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(iotRulenodestatePojo);
                // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
      //从redis中获取Reprot内容
     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
     String content ;
     if (reportsPojo != null ) {
         content = reportsPojo.getRptdata();
     } else {
         throw new BaseBusinessException("未找到报表");
     }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

